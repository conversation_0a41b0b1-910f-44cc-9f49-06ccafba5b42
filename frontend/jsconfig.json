{"include": ["vite.config.*", "src/**/*", "src/**/*.vue", "themeConfig.js"], "exclude": ["dist", "node_modules", "src/@iconify/*"], "compilerOptions": {"baseUrl": "./", "target": "esnext", "module": "esnext", "moduleResolution": "node", "jsx": "preserve", "paths": {"@/*": ["src/*"], "@themeConfig": ["themeConfig.js"], "@layouts/*": ["src/@layouts/*"], "@layouts": ["src/@layouts"], "@core/*": ["src/@core/*"], "@core": ["src/@core"], "@images/*": ["src/assets/images/*"], "@styles/*": ["src/styles/*"], "@axios": ["src/plugins/axios"], "@validators": ["src/@core/utils/validators"]}, "types": ["vite/client", "unplugin-vue-define-options/macros-global"]}}