{"script": {"prefix": "vue-sfc", "body": ["<script setup>", "", "</script>", "", "<template>", "  ", "</template>", "", "<style lang=\"scss\">", "", "</style>", ""], "description": "Vue SFC Typescript"}, "template": {"scope": "vue", "prefix": "template", "body": ["<template>", "  $1", "</template>"], "description": "Create <template> block"}, "Script setup": {"prefix": "script-setup", "body": ["<script setup>", "${1}", "</script>"], "description": "Script setup"}, "style": {"scope": "vue", "prefix": "style", "body": ["<style lang=\"scss\">", "$1", "</style>"], "description": "Create <style> block"}, "use composable": {"prefix": "use-composable", "body": ["const { $2 } = ${1:useComposable}()"], "description": "We frequently uses composable in our components and writing const {} = useModule() is tedious. This snippet helps you to write it quickly."}, "template interpolation": {"prefix": "cc", "body": ["{{ ${1} }}"], "description": "We are just making writing template interpolation easier."}}