[{"paths": ["ssd_mobilenetv1_model-shard1", "ssd_mobilenetv1_model-shard2"], "weights": [{"dtype": "float32", "shape": [1, 1, 512, 9], "quantization": {"scale": 0.0026856216729856004, "min": -0.34107395246917127, "dtype": "uint8"}, "name": "Prediction/BoxPredictor_0/ClassPredictor/weights"}, {"dtype": "float32", "shape": [9], "quantization": {"scale": 0.00198518248165355, "min": -0.32159956202787515, "dtype": "uint8"}, "name": "Prediction/BoxPredictor_0/ClassPredictor/biases"}, {"dtype": "float32", "shape": [1, 1, 1024, 18], "quantization": {"scale": 0.003060340296988394, "min": -0.489654447518143, "dtype": "uint8"}, "name": "Prediction/BoxPredictor_1/ClassPredictor/weights"}, {"dtype": "float32", "shape": [18], "quantization": {"scale": 0.0008040678851744708, "min": -0.12221831854651957, "dtype": "uint8"}, "name": "Prediction/BoxPredictor_1/ClassPredictor/biases"}, {"dtype": "float32", "shape": [1, 1, 512, 18], "quantization": {"scale": 0.0012513800578958848, "min": -0.16017664741067325, "dtype": "uint8"}, "name": "Prediction/BoxPredictor_2/ClassPredictor/weights"}, {"dtype": "float32", "shape": [18], "quantization": {"scale": 0.000338070518245884, "min": -0.05510549447407909, "dtype": "uint8"}, "name": "Prediction/BoxPredictor_2/ClassPredictor/biases"}, {"dtype": "float32", "shape": [1, 1, 256, 18], "quantization": {"scale": 0.0011819932975021064, "min": -0.1453851755927591, "dtype": "uint8"}, "name": "Prediction/BoxPredictor_3/ClassPredictor/weights"}, {"dtype": "float32", "shape": [18], "quantization": {"scale": 0.00015985782386041154, "min": -0.026536398760828316, "dtype": "uint8"}, "name": "Prediction/BoxPredictor_3/ClassPredictor/biases"}, {"dtype": "float32", "shape": [1, 1, 256, 18], "quantization": {"scale": 0.0007035591438704846, "min": -0.08513065640832863, "dtype": "uint8"}, "name": "Prediction/BoxPredictor_4/ClassPredictor/weights"}, {"dtype": "float32", "shape": [18], "quantization": {"scale": 8.793946574716008e-05, "min": -0.013190919862074012, "dtype": "uint8"}, "name": "Prediction/BoxPredictor_4/ClassPredictor/biases"}, {"dtype": "float32", "shape": [1, 1, 128, 18], "quantization": {"scale": 0.00081320781918133, "min": -0.11059626340866088, "dtype": "uint8"}, "name": "Prediction/BoxPredictor_5/ClassPredictor/weights"}, {"dtype": "float32", "shape": [18], "quantization": {"scale": 9.80533805547976e-05, "min": -0.014609953702664841, "dtype": "uint8"}, "name": "Prediction/BoxPredictor_5/ClassPredictor/biases"}, {"dtype": "int32", "shape": [], "quantization": {"scale": 1, "min": 3, "dtype": "uint8"}, "name": "Prediction/BoxPredictor_0/stack_1/2"}, {"dtype": "int32", "shape": [3], "quantization": {"scale": 0.00392156862745098, "min": 0, "dtype": "uint8"}, "name": "Postprocessor/Slice/begin"}, {"dtype": "int32", "shape": [3], "quantization": {"scale": 1, "min": -1, "dtype": "uint8"}, "name": "Postprocessor/Slice/size"}, {"dtype": "float32", "shape": [1, 1, 512, 12], "quantization": {"scale": 0.003730384859384275, "min": -0.4327246436885759, "dtype": "uint8"}, "name": "Prediction/BoxPredictor_0/BoxEncodingPredictor/weights"}, {"dtype": "float32", "shape": [12], "quantization": {"scale": 0.0018744708568442102, "min": -0.3917644090804399, "dtype": "uint8"}, "name": "Prediction/BoxPredictor_0/BoxEncodingPredictor/biases"}, {"dtype": "int32", "shape": [], "quantization": {"scale": 1, "min": 3072, "dtype": "uint8"}, "name": "Prediction/BoxPredictor_0/stack_1/1"}, {"dtype": "float32", "shape": [1, 1, 1024, 24], "quantization": {"scale": 0.00157488017689948, "min": -0.20000978246623397, "dtype": "uint8"}, "name": "Prediction/BoxPredictor_1/BoxEncodingPredictor/weights"}, {"dtype": "float32", "shape": [24], "quantization": {"scale": 0.0002823906713256649, "min": -0.043488163384152394, "dtype": "uint8"}, "name": "Prediction/BoxPredictor_1/BoxEncodingPredictor/biases"}, {"dtype": "int32", "shape": [], "quantization": {"scale": 1, "min": 1536, "dtype": "uint8"}, "name": "Prediction/BoxPredictor_1/stack_1/1"}, {"dtype": "float32", "shape": [1, 1, 512, 24], "quantization": {"scale": 0.0007974451663447361, "min": -0.11004743295557358, "dtype": "uint8"}, "name": "Prediction/BoxPredictor_2/BoxEncodingPredictor/weights"}, {"dtype": "float32", "shape": [24], "quantization": {"scale": 0.0001350417988849621, "min": -0.02039131163162928, "dtype": "uint8"}, "name": "Prediction/BoxPredictor_2/BoxEncodingPredictor/biases"}, {"dtype": "int32", "shape": [], "quantization": {"scale": 1, "min": 384, "dtype": "uint8"}, "name": "Prediction/BoxPredictor_2/stack_1/1"}, {"dtype": "float32", "shape": [1, 1, 256, 24], "quantization": {"scale": 0.0007113990246080885, "min": -0.0860792819775787, "dtype": "uint8"}, "name": "Prediction/BoxPredictor_3/BoxEncodingPredictor/weights"}, {"dtype": "float32", "shape": [24], "quantization": {"scale": 5.0115815418608046e-05, "min": -0.007617603943628423, "dtype": "uint8"}, "name": "Prediction/BoxPredictor_3/BoxEncodingPredictor/biases"}, {"dtype": "int32", "shape": [], "quantization": {"scale": 1, "min": 96, "dtype": "uint8"}, "name": "Prediction/BoxPredictor_3/stack_1/1"}, {"dtype": "float32", "shape": [1, 1, 256, 24], "quantization": {"scale": 0.000590049314732645, "min": -0.06903576982371946, "dtype": "uint8"}, "name": "Prediction/BoxPredictor_4/BoxEncodingPredictor/weights"}, {"dtype": "float32", "shape": [24], "quantization": {"scale": 3.513663861097074e-05, "min": -0.006359731588585704, "dtype": "uint8"}, "name": "Prediction/BoxPredictor_4/BoxEncodingPredictor/biases"}, {"dtype": "int32", "shape": [], "quantization": {"scale": 1, "min": 24, "dtype": "uint8"}, "name": "Prediction/BoxPredictor_4/stack_1/1"}, {"dtype": "float32", "shape": [1, 1, 128, 24], "quantization": {"scale": 0.0005990567744946948, "min": -0.07907549423329971, "dtype": "uint8"}, "name": "Prediction/BoxPredictor_5/BoxEncodingPredictor/weights"}, {"dtype": "float32", "shape": [24], "quantization": {"scale": 3.392884288640583e-05, "min": -0.006039334033780238, "dtype": "uint8"}, "name": "Prediction/BoxPredictor_5/BoxEncodingPredictor/biases"}, {"dtype": "float32", "shape": [], "quantization": {"scale": 1, "min": 0.007843137718737125, "dtype": "uint8"}, "name": "Preprocessor/mul/x"}, {"dtype": "int32", "shape": [2], "quantization": {"scale": 1, "min": 512, "dtype": "uint8"}, "name": "Preprocessor/ResizeImage/size"}, {"dtype": "float32", "shape": [], "quantization": {"scale": 1, "min": 1, "dtype": "uint8"}, "name": "Preprocessor/sub/y"}, {"dtype": "float32", "shape": [3, 3, 3, 32], "quantization": {"scale": 0.03948551065781537, "min": -5.014659853542552, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_0_pointwise/weights"}, {"dtype": "float32", "shape": [32], "quantization": {"scale": 0.0498106133704092, "min": -7.371970778820562, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_0_pointwise/convolution_bn_offset"}, {"dtype": "float32", "shape": [3, 3, 32, 1], "quantization": {"scale": 0.036833542468501075, "min": -4.714693435968138, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_1_depthwise/depthwise_weights"}, {"dtype": "float32", "shape": [32], "quantization": {"scale": 0.012173276705046495, "min": -0.012173276705046495, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_1_depthwise/BatchNorm/gamma"}, {"dtype": "float32", "shape": [32], "quantization": {"scale": 0.032182769214405736, "min": -2.4780732295092416, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_1_depthwise/BatchNorm/beta"}, {"dtype": "float32", "shape": [32], "quantization": {"scale": 0.028287527607936486, "min": -3.366215785344442, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_1_depthwise/BatchNorm/moving_mean"}, {"dtype": "float32", "shape": [32], "quantization": {"scale": 0.04716738532571232, "min": 3.9071404665769224e-36, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_1_depthwise/BatchNorm/moving_variance"}, {"dtype": "float32", "shape": [1, 1, 32, 64], "quantization": {"scale": 0.04010109433940812, "min": -4.290817094316669, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_1_pointwise/weights"}, {"dtype": "float32", "shape": [64], "quantization": {"scale": 0.2212210038129021, "min": -34.51047659481273, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_1_pointwise/convolution_bn_offset"}, {"dtype": "float32", "shape": [3, 3, 64, 1], "quantization": {"scale": 0.010024750933927648, "min": -1.343316625146305, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_2_depthwise/depthwise_weights"}, {"dtype": "float32", "shape": [64], "quantization": {"scale": 0.006120916675118839, "min": 0.5227176547050476, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_2_depthwise/BatchNorm/gamma"}, {"dtype": "float32", "shape": [64], "quantization": {"scale": 0.02317035385206634, "min": -0.7646216771181892, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_2_depthwise/BatchNorm/beta"}, {"dtype": "float32", "shape": [64], "quantization": {"scale": 0.04980821422502106, "min": -5.8275610643274645, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_2_depthwise/BatchNorm/moving_mean"}, {"dtype": "float32", "shape": [64], "quantization": {"scale": 0.051751047022202436, "min": 3.916113799002297e-36, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_2_depthwise/BatchNorm/moving_variance"}, {"dtype": "float32", "shape": [1, 1, 64, 128], "quantization": {"scale": 0.021979344124887504, "min": -2.1319963801140878, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_2_pointwise/weights"}, {"dtype": "float32", "shape": [128], "quantization": {"scale": 0.09958663267247816, "min": -11.054116226645077, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_2_pointwise/convolution_bn_offset"}, {"dtype": "float32", "shape": [3, 3, 128, 1], "quantization": {"scale": 0.01943492702409333, "min": -2.6237151482525993, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_3_depthwise/depthwise_weights"}, {"dtype": "float32", "shape": [128], "quantization": {"scale": 0.017852897737540452, "min": 0.40204083919525146, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_3_depthwise/BatchNorm/gamma"}, {"dtype": "float32", "shape": [128], "quantization": {"scale": 0.029888209174661076, "min": -1.972621805527631, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_3_depthwise/BatchNorm/beta"}, {"dtype": "float32", "shape": [128], "quantization": {"scale": 0.029319268581913967, "min": -5.130872001834945, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_3_depthwise/BatchNorm/moving_mean"}, {"dtype": "float32", "shape": [128], "quantization": {"scale": 0.014018708584355373, "min": 3.9083178263362604e-36, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_3_depthwise/BatchNorm/moving_variance"}, {"dtype": "float32", "shape": [1, 1, 128, 128], "quantization": {"scale": 0.020776657964669022, "min": -2.5347522716896207, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_3_pointwise/weights"}, {"dtype": "float32", "shape": [128], "quantization": {"scale": 0.14383157094319662, "min": -9.636715253194174, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_3_pointwise/convolution_bn_offset"}, {"dtype": "float32", "shape": [3, 3, 128, 1], "quantization": {"scale": 0.004463558571011412, "min": -0.5981168485155293, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_4_depthwise/depthwise_weights"}, {"dtype": "float32", "shape": [128], "quantization": {"scale": 0.006487431245691636, "min": 0.47910428047180176, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_4_depthwise/BatchNorm/gamma"}, {"dtype": "float32", "shape": [128], "quantization": {"scale": 0.026542164297664865, "min": -1.2209395576925839, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_4_depthwise/BatchNorm/beta"}, {"dtype": "float32", "shape": [128], "quantization": {"scale": 0.05119945675719018, "min": -8.60150873520795, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_4_depthwise/BatchNorm/moving_mean"}, {"dtype": "float32", "shape": [128], "quantization": {"scale": 0.03081628388049556, "min": 3.911508751095344e-36, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_4_depthwise/BatchNorm/moving_variance"}, {"dtype": "float32", "shape": [1, 1, 128, 256], "quantization": {"scale": 0.010758659886378868, "min": -1.0328313490923713, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_4_pointwise/weights"}, {"dtype": "float32", "shape": [256], "quantization": {"scale": 0.08058219610476026, "min": -9.34753474815219, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_4_pointwise/convolution_bn_offset"}, {"dtype": "float32", "shape": [3, 3, 256, 1], "quantization": {"scale": 0.01145936741548426, "min": -1.3292866201961742, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_5_depthwise/depthwise_weights"}, {"dtype": "float32", "shape": [256], "quantization": {"scale": 0.0083988838336047, "min": 0.36280909180641174, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_5_depthwise/BatchNorm/gamma"}, {"dtype": "float32", "shape": [256], "quantization": {"scale": 0.02858148649627087, "min": -3.6584302715226715, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_5_depthwise/BatchNorm/beta"}, {"dtype": "float32", "shape": [256], "quantization": {"scale": 0.03988401375564874, "min": -7.099354448505476, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_5_depthwise/BatchNorm/moving_mean"}, {"dtype": "float32", "shape": [256], "quantization": {"scale": 0.009090481683904049, "min": 0.020878996700048447, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_5_depthwise/BatchNorm/moving_variance"}, {"dtype": "float32", "shape": [1, 1, 256, 256], "quantization": {"scale": 0.008951201625898773, "min": -1.1189002032373465, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_5_pointwise/weights"}, {"dtype": "float32", "shape": [256], "quantization": {"scale": 0.051758006974762565, "min": -5.745138774198645, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_5_pointwise/convolution_bn_offset"}, {"dtype": "float32", "shape": [3, 3, 256, 1], "quantization": {"scale": 0.004110433190476661, "min": -0.6042336790000691, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_6_depthwise/depthwise_weights"}, {"dtype": "float32", "shape": [256], "quantization": {"scale": 0.013170199768216002, "min": 0.3386639356613159, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_6_depthwise/BatchNorm/gamma"}, {"dtype": "float32", "shape": [256], "quantization": {"scale": 0.03599378548416437, "min": -3.70735990486893, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_6_depthwise/BatchNorm/beta"}, {"dtype": "float32", "shape": [256], "quantization": {"scale": 0.026967673208199296, "min": -3.748506575939702, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_6_depthwise/BatchNorm/moving_mean"}, {"dtype": "float32", "shape": [256], "quantization": {"scale": 0.012615410486857097, "min": 3.9111388979838637e-36, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_6_depthwise/BatchNorm/moving_variance"}, {"dtype": "float32", "shape": [1, 1, 256, 512], "quantization": {"scale": 0.00822840648538926, "min": -1.1848905338960536, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_6_pointwise/weights"}, {"dtype": "float32", "shape": [512], "quantization": {"scale": 0.06608965817619772, "min": -7.468131373910342, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_6_pointwise/convolution_bn_offset"}, {"dtype": "float32", "shape": [3, 3, 512, 1], "quantization": {"scale": 0.008801074355256323, "min": -0.9593171047229393, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_7_depthwise/depthwise_weights"}, {"dtype": "float32", "shape": [512], "quantization": {"scale": 0.030577416513480393, "min": 0.3285980224609375, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_7_depthwise/BatchNorm/gamma"}, {"dtype": "float32", "shape": [512], "quantization": {"scale": 0.04778536441279393, "min": -8.935863145192464, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_7_depthwise/BatchNorm/beta"}, {"dtype": "float32", "shape": [512], "quantization": {"scale": 0.04331884945140165, "min": -9.660103427662568, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_7_depthwise/BatchNorm/moving_mean"}, {"dtype": "float32", "shape": [512], "quantization": {"scale": 0.04126455444367785, "min": 0.000604183878749609, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_7_depthwise/BatchNorm/moving_variance"}, {"dtype": "float32", "shape": [1, 1, 512, 512], "quantization": {"scale": 0.009305818408143287, "min": -1.1446156642016243, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_7_pointwise/weights"}, {"dtype": "float32", "shape": [512], "quantization": {"scale": 0.04640720217835669, "min": -4.733534622192383, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_7_pointwise/convolution_bn_offset"}, {"dtype": "float32", "shape": [3, 3, 512, 1], "quantization": {"scale": 0.008138792655047248, "min": -0.9766551186056698, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_8_depthwise/depthwise_weights"}, {"dtype": "float32", "shape": [512], "quantization": {"scale": 0.027351748358969596, "min": 0.34030041098594666, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_8_depthwise/BatchNorm/gamma"}, {"dtype": "float32", "shape": [512], "quantization": {"scale": 0.04415061053107767, "min": -7.019947074441349, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_8_depthwise/BatchNorm/beta"}, {"dtype": "float32", "shape": [512], "quantization": {"scale": 0.02476683784933651, "min": -2.9224868662217083, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_8_depthwise/BatchNorm/moving_mean"}, {"dtype": "float32", "shape": [512], "quantization": {"scale": 0.02547598832684076, "min": 0.00026032101595774293, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_8_depthwise/BatchNorm/moving_variance"}, {"dtype": "float32", "shape": [1, 1, 512, 512], "quantization": {"scale": 0.01083052625843123, "min": -1.2563410459780227, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_8_pointwise/weights"}, {"dtype": "float32", "shape": [512], "quantization": {"scale": 0.06360894371481503, "min": -7.951117964351878, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_8_pointwise/convolution_bn_offset"}, {"dtype": "float32", "shape": [3, 3, 512, 1], "quantization": {"scale": 0.006704086883395326, "min": -0.8648272079579971, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_9_depthwise/depthwise_weights"}, {"dtype": "float32", "shape": [512], "quantization": {"scale": 0.015343831567203297, "min": 0.2711026668548584, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_9_depthwise/BatchNorm/gamma"}, {"dtype": "float32", "shape": [512], "quantization": {"scale": 0.03378283930759804, "min": -4.797163181678922, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_9_depthwise/BatchNorm/beta"}, {"dtype": "float32", "shape": [512], "quantization": {"scale": 0.021910778213949763, "min": -3.987761634938857, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_9_depthwise/BatchNorm/moving_mean"}, {"dtype": "float32", "shape": [512], "quantization": {"scale": 0.009284070410007296, "min": 2.1581046894425526e-05, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_9_depthwise/BatchNorm/moving_variance"}, {"dtype": "float32", "shape": [1, 1, 512, 512], "quantization": {"scale": 0.012783036979974485, "min": -1.9046725100161983, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_9_pointwise/weights"}, {"dtype": "float32", "shape": [512], "quantization": {"scale": 0.07273082733154297, "min": -9.52773838043213, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_9_pointwise/convolution_bn_offset"}, {"dtype": "float32", "shape": [3, 3, 512, 1], "quantization": {"scale": 0.006126228033327589, "min": -0.7351473639993107, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_10_depthwise/depthwise_weights"}, {"dtype": "float32", "shape": [512], "quantization": {"scale": 0.029703759212119908, "min": 0.28687000274658203, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_10_depthwise/BatchNorm/gamma"}, {"dtype": "float32", "shape": [512], "quantization": {"scale": 0.04394429898729511, "min": -6.3279790541704966, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_10_depthwise/BatchNorm/beta"}, {"dtype": "float32", "shape": [512], "quantization": {"scale": 0.016566915605582443, "min": -2.7501079905266854, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_10_depthwise/BatchNorm/moving_mean"}, {"dtype": "float32", "shape": [512], "quantization": {"scale": 0.012152872833551145, "min": 3.913338286370366e-36, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_10_depthwise/BatchNorm/moving_variance"}, {"dtype": "float32", "shape": [1, 1, 512, 512], "quantization": {"scale": 0.01354524388032801, "min": -1.7473364605623134, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_10_pointwise/weights"}, {"dtype": "float32", "shape": [512], "quantization": {"scale": 0.08566816367355047, "min": -9.937506986131854, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_10_pointwise/convolution_bn_offset"}, {"dtype": "float32", "shape": [3, 3, 512, 1], "quantization": {"scale": 0.006012305558896532, "min": -0.7876120282154457, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_11_depthwise/depthwise_weights"}, {"dtype": "float32", "shape": [512], "quantization": {"scale": 0.01469323155926723, "min": 0.29223933815956116, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_11_depthwise/BatchNorm/gamma"}, {"dtype": "float32", "shape": [512], "quantization": {"scale": 0.030889174517463234, "min": -3.2433633243336395, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_11_depthwise/BatchNorm/beta"}, {"dtype": "float32", "shape": [512], "quantization": {"scale": 0.014836942448335536, "min": -2.047498057870304, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_11_depthwise/BatchNorm/moving_mean"}, {"dtype": "float32", "shape": [512], "quantization": {"scale": 0.007234466105343445, "min": 0.00013165915152058005, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_11_depthwise/BatchNorm/moving_variance"}, {"dtype": "float32", "shape": [1, 1, 512, 512], "quantization": {"scale": 0.016261722527298274, "min": -1.4798167499841428, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_11_pointwise/weights"}, {"dtype": "float32", "shape": [512], "quantization": {"scale": 0.091437328563017, "min": -14.172785927267636, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_11_pointwise/convolution_bn_offset"}, {"dtype": "float32", "shape": [3, 3, 512, 1], "quantization": {"scale": 0.004750356487199372, "min": -0.650798838746314, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_12_depthwise/depthwise_weights"}, {"dtype": "float32", "shape": [512], "quantization": {"scale": 0.008174965545242907, "min": 0.3120670020580292, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_12_depthwise/BatchNorm/gamma"}, {"dtype": "float32", "shape": [512], "quantization": {"scale": 0.030133422215779623, "min": -2.41067377726237, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_12_depthwise/BatchNorm/beta"}, {"dtype": "float32", "shape": [512], "quantization": {"scale": 0.006088157261119169, "min": -0.7853722866843729, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_12_depthwise/BatchNorm/moving_mean"}, {"dtype": "float32", "shape": [512], "quantization": {"scale": 0.003668997334498985, "min": 3.9124486300013356e-36, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_12_depthwise/BatchNorm/moving_variance"}, {"dtype": "float32", "shape": [1, 1, 512, 1024], "quantization": {"scale": 0.010959514449624454, "min": -1.4028178495519301, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_12_pointwise/weights"}, {"dtype": "float32", "shape": [1024], "quantization": {"scale": 0.10896045834410424, "min": -14.818622334798176, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_12_pointwise/convolution_bn_offset"}, {"dtype": "float32", "shape": [3, 3, 1024, 1], "quantization": {"scale": 0.004633033509347953, "min": -0.5652300881404502, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_13_depthwise/depthwise_weights"}, {"dtype": "float32", "shape": [1024], "quantization": {"scale": 0.022285057224479377, "min": 0.23505790531635284, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_13_depthwise/BatchNorm/gamma"}, {"dtype": "float32", "shape": [1024], "quantization": {"scale": 0.0324854850769043, "min": -3.9957146644592285, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_13_depthwise/BatchNorm/beta"}, {"dtype": "float32", "shape": [1024], "quantization": {"scale": 0.014760061806323482, "min": -2.125448900110581, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_13_depthwise/BatchNorm/moving_mean"}, {"dtype": "float32", "shape": [1024], "quantization": {"scale": 0.0036057423142825855, "min": 3.9067056828997994e-36, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_13_depthwise/BatchNorm/moving_variance"}, {"dtype": "float32", "shape": [1, 1, 1024, 1024], "quantization": {"scale": 0.017311988157384536, "min": -2.094750567043529, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_13_pointwise/weights"}, {"dtype": "float32", "shape": [1024], "quantization": {"scale": 0.16447528764313343, "min": -25.658144872328815, "dtype": "uint8"}, "name": "MobilenetV1/Conv2d_13_pointwise/convolution_bn_offset"}, {"dtype": "float32", "shape": [1, 1, 1024, 256], "quantization": {"scale": 0.0026493051472832175, "min": -0.36825341547236723, "dtype": "uint8"}, "name": "Prediction/Conv2d_0_pointwise/weights"}, {"dtype": "float32", "shape": [256], "quantization": {"scale": 0.012474596734140433, "min": -2.3078003958159803, "dtype": "uint8"}, "name": "Prediction/Conv2d_0_pointwise/convolution_bn_offset"}, {"dtype": "float32", "shape": [3, 3, 256, 512], "quantization": {"scale": 0.014533351449405445, "min": -1.8166689311756807, "dtype": "uint8"}, "name": "Prediction/Conv2d_1_pointwise/weights"}, {"dtype": "float32", "shape": [512], "quantization": {"scale": 0.024268776762719248, "min": -2.4754152297973633, "dtype": "uint8"}, "name": "Prediction/Conv2d_1_pointwise/convolution_bn_offset"}, {"dtype": "float32", "shape": [1, 1, 512, 128], "quantization": {"scale": 0.002208403746287028, "min": -0.28709248701731366, "dtype": "uint8"}, "name": "Prediction/Conv2d_2_pointwise/weights"}, {"dtype": "float32", "shape": [128], "quantization": {"scale": 0.012451349052728392, "min": -1.5937726787492341, "dtype": "uint8"}, "name": "Prediction/Conv2d_2_pointwise/convolution_bn_offset"}, {"dtype": "float32", "shape": [3, 3, 128, 256], "quantization": {"scale": 0.026334229637594783, "min": -2.8967652601354263, "dtype": "uint8"}, "name": "Prediction/Conv2d_3_pointwise/weights"}, {"dtype": "float32", "shape": [256], "quantization": {"scale": 0.02509917792151956, "min": -1.4055539636050953, "dtype": "uint8"}, "name": "Prediction/Conv2d_3_pointwise/convolution_bn_offset"}, {"dtype": "float32", "shape": [1, 1, 256, 128], "quantization": {"scale": 0.004565340046789132, "min": -0.3971845840706545, "dtype": "uint8"}, "name": "Prediction/Conv2d_4_pointwise/weights"}, {"dtype": "float32", "shape": [128], "quantization": {"scale": 0.017302456556581983, "min": -2.5953684834872974, "dtype": "uint8"}, "name": "Prediction/Conv2d_4_pointwise/convolution_bn_offset"}, {"dtype": "float32", "shape": [3, 3, 128, 256], "quantization": {"scale": 0.025347338470758176, "min": -3.8527954475552426, "dtype": "uint8"}, "name": "Prediction/Conv2d_5_pointwise/weights"}, {"dtype": "float32", "shape": [256], "quantization": {"scale": 0.033134659598855414, "min": -2.9158500446992766, "dtype": "uint8"}, "name": "Prediction/Conv2d_5_pointwise/convolution_bn_offset"}, {"dtype": "float32", "shape": [1, 1, 256, 64], "quantization": {"scale": 0.002493104397081861, "min": -0.2817207968702503, "dtype": "uint8"}, "name": "Prediction/Conv2d_6_pointwise/weights"}, {"dtype": "float32", "shape": [64], "quantization": {"scale": 0.011383360974928912, "min": -1.2749364291920382, "dtype": "uint8"}, "name": "Prediction/Conv2d_6_pointwise/convolution_bn_offset"}, {"dtype": "float32", "shape": [3, 3, 64, 128], "quantization": {"scale": 0.020821522731407017, "min": -2.7484410005457263, "dtype": "uint8"}, "name": "Prediction/Conv2d_7_pointwise/weights"}, {"dtype": "float32", "shape": [128], "quantization": {"scale": 0.052144218893612135, "min": -3.5979511036592373, "dtype": "uint8"}, "name": "Prediction/Conv2d_7_pointwise/convolution_bn_offset"}, {"dtype": "int32", "shape": [], "quantization": {"scale": 1, "min": 6, "dtype": "uint8"}, "name": "Prediction/BoxPredictor_5/stack_1/1"}, {"dtype": "int32", "shape": [], "quantization": {"scale": 1, "min": 1, "dtype": "uint8"}, "name": "concat_1/axis"}, {"dtype": "int32", "shape": [1], "quantization": {"scale": 1, "min": 0, "dtype": "uint8"}, "name": "Prediction/BoxPredictor_0/strided_slice/stack"}, {"dtype": "int32", "shape": [1], "quantization": {"scale": 1, "min": 1, "dtype": "uint8"}, "name": "Prediction/BoxPredictor_0/strided_slice/stack_1"}, {"dtype": "int32", "shape": [], "quantization": {"scale": 1, "min": 5118, "dtype": "uint8"}, "name": "Postprocessor/stack/1"}, {"dtype": "int32", "shape": [], "quantization": {"scale": 1, "min": 4, "dtype": "uint8"}, "name": "Prediction/BoxPredictor_0/stack/3"}, {"dtype": "float32", "shape": [1, 5118, 4], "name": "Output/extra_dim"}]}]