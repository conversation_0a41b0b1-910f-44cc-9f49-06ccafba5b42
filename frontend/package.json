{"name": "whitelblrx", "version": "8.0.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview --port 5050", "lint": "eslint . -c .eslintrc.js --fix --ext .ts,.js,.vue,.tsx,.jsx", "build:icons": "node src/@iconify/build-icons.js", "postinstall": "npm run build:icons"}, "dependencies": {"@casl/ability": "6.5.0", "@casl/vue": "2.2.1", "@floating-ui/dom": "1.2.7", "@mediapipe/face_detection": "^0.4.1646425229", "@paypal/paypal-js": "^8.2.0", "@tabler/icons": "2.17.0", "@tabler/icons-vue": "^3.1.0", "@tensorflow-models/face-detection": "^1.0.3", "@tensorflow/tfjs-backend-webgl": "^4.22.0", "@tensorflow/tfjs-converter": "^4.22.0", "@tensorflow/tfjs-core": "^4.22.0", "@tiptap/extension-highlight": "^2.1.12", "@tiptap/extension-image": "^2.1.12", "@tiptap/extension-link": "^2.1.12", "@tiptap/extension-text-align": "^2.1.12", "@tiptap/pm": "^2.1.12", "@tiptap/starter-kit": "^2.1.12", "@tiptap/vue-3": "^2.1.12", "@vueuse/core": "10.1.0", "@vueuse/math": "10.1.0", "apexcharts-clevision": "3.28.5", "axios": "^1.7.3", "chart.js": "4.3.0", "copy-to-clipboard": "^3.3.3", "date-fns": "^3.6.0", "dayjs": "^1.11.13", "eslint-import-resolver-alias": "^1.1.2", "flowbite": "^2.3.0", "maska": "^2.1.9", "pinia": "2.0.35", "posthog-js": "^1.154.5", "primevue": "^3.30.0", "prismjs": "1.29.0", "qrcode.vue": "^3.4.1", "sass": "1.62.1", "unplugin-vue-define-options": "1.3.4", "uuid": "^9.0.1", "vee-validate": "^4.8.6", "vue": "3.2.47", "vue-chartjs": "5.2.0", "vue-confetti-explosion": "^1.0.2", "vue-cookies": "^1.8.3", "vue-flatpickr-component": "11.0.3", "vue-i18n": "9.14.3", "vue-markdown-render": "^2.0.1", "vue-prism-component": "2.0.0", "vue-recaptcha-v3": "^2.0.1", "vue-router": "4.1.6", "vue-sonner": "^1.1.2", "vue-sweetalert2": "^5.0.5", "vue3-apexcharts": "1.4.1", "vue3-perfect-scrollbar": "1.6.1", "vuetify": "3.2.1", "webfontloader": "1.6.28", "yup": "^1.2.0"}, "devDependencies": {"@antfu/eslint-config-vue": "0.38.5", "@iconify-json/fa": "1.1.4", "@iconify-json/tabler": "1.1.74", "@iconify/tools": "2.2.6", "@iconify/vue": "4.1.1", "@intlify/unplugin-vue-i18n": "0.10.0", "@tailwindcss/typography": "^0.5.14", "@tiptap/extension-character-count": "^2.1.12", "@tiptap/extension-placeholder": "^2.1.12", "@tiptap/extension-subscript": "^2.1.12", "@tiptap/extension-superscript": "^2.1.12", "@tiptap/extension-underline": "^2.1.12", "@vitejs/plugin-vue": "4.2.1", "@vitejs/plugin-vue-jsx": "3.0.1", "autoprefixer": "^10.4.19", "eslint": "8.39.0", "eslint-config-airbnb-base": "15.0.0", "eslint-plugin-import": "2.27.5", "eslint-plugin-promise": "6.1.1", "eslint-plugin-regex": "1.10.0", "eslint-plugin-sonarjs": "0.19.0", "eslint-plugin-unicorn": "46.0.0", "eslint-plugin-vue": "9.11.0", "postcss": "^8.4.38", "postcss-html": "1.5.0", "stylelint": "14.15.0", "stylelint-config-idiomatic-order": "9.0.0", "stylelint-config-standard-scss": "6.1.0", "stylelint-use-logical-spec": "4.1.0", "tailwindcss": "^3.4.3", "unplugin-auto-import": "0.15.3", "unplugin-vue-components": "0.24.1", "vite": "^4.3.9", "vite-plugin-vuetify": "1.0.2"}, "resolutions": {"postcss": "8", "@tiptap/core": "^2"}}