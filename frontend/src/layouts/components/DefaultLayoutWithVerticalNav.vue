<script setup>
import navItems from '@/navigation/vertical'
import { useThemeConfig } from '@core/composable/useThemeConfig'

// Components
import Footer from '@/layouts/components/Footer.vue'
import NavbarThemeSwitcher from '@/layouts/components/NavbarThemeSwitcher.vue'
import UserProfile from '@/layouts/components/UserProfile.vue'

// @layouts plugin
import { VerticalNavLayout } from '@layouts'
import router from '@/router'

const { appRouteTransition, isLessThanOverlayNavBreakpoint } = useThemeConfig()
const { width: windowWidth } = useWindowSize()
</script>

<template>
  <VerticalNavLayout :nav-items="navItems">
    <!-- 👉 navbar -->
    <template #navbar="{ toggleVerticalOverlayNavActive }">
      <div class="d-flex h-100 align-center">
        <IconBtn
          v-if="isLessThanOverlayNavBreakpoint(windowWidth)"
          id="vertical-nav-toggle-btn"
          class="ms-n3"
          @click="toggleVerticalOverlayNavActive(true)"
        >
          <VIcon
            size="26"
            icon="tabler-menu-2"
          />
        </IconBtn>

        <div class="d-flex align-center gap-4">
          <!-- 👉 back button -->
          <VBtn
            v-if="router.currentRoute.value.meta.showBackButton"
            variant="tonal"
            size="small"
            @click="router.go(-1)"
          >
            <VIcon
              size="18"
              icon="tabler-arrow-left"
              start
            />
            Back
          </VBtn>
          <!-- 👉 Page title -->
          <h5 class="text-h5">
            {{ router.currentRoute.value.meta.pageTitle }}
          </h5>
          <!--
            <VBreadcrumbs
            :items="router.currentRoute.value.meta.breadcrumbs"
            class="m-0 p-0 text-caption"
            ></VBreadcrumbs>
          -->
        </div>

        <VSpacer />

        <NavbarThemeSwitcher class="me-3" />

        <UserProfile />
      </div>
    </template>

    <!-- 👉 Pages -->
    <RouterView v-slot="{ Component }">
      <Transition
        :name="appRouteTransition"
        mode="out-in"
      >
        <Component :is="Component" />
      </Transition>
    </RouterView>

    <!-- 👉 Footer -->
    <template #footer>
      <Footer />
    </template>

    <!-- 👉 Customizer -->
    <!-- <TheCustomizer /> -->
  </VerticalNavLayout>
</template>
<!--
  <style lang="scss">
  .v-breadcrumbs-item {
  padding-right: 0 !important;
  padding-left: 0 !important;
  }
  </style>
-->
