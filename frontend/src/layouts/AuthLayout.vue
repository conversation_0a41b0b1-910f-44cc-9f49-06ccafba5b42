<script setup>
import NavHeader from '@/views/user/components/NavHeader.vue'
import PendingVisitBanner from '@/views/user/components/PendingVisitBanner.vue'
import PendingDocumentUpload from '@/views/user/components/PendingDocumentUploadItem.vue'
import useUserDetails from '@/composables/useUserDetails'
import { onMounted } from 'vue'
import { isEmpty } from '@/@core/utils'
import { toast } from 'vue-sonner'
import { processErrors } from '@/utils/errorHandler'
import { useRoute } from 'vue-router'

const { fetchUserDetails, errors } = useUserDetails()
const route = useRoute()

onMounted(async () => {
  if (route.name !== 'user-profile') {
    await fetchUserDetails()
    if (!isEmpty(errors.value)) {
      toast.error(processErrors(errors.value)[0])
    }
  }
})
</script>

<template>
  <div class="bg-gray-200 min-h-screen">
    <NavHeader />
    <div class="fixed w-full z-[9] top-[56px] lg:top-[63px]">
      <PendingVisitBanner />
    </div>
    <RouterView class="py-20 pt-[5.5rem] lg:pt-[6.5rem]" />
  </div>
</template>

<style lang="scss">
@import '@/assets/user/css/style.css';
</style>
