<script setup>
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { processErrors } from '@/utils/errorHandler'
import { PerfectScrollbar } from 'vue3-perfect-scrollbar'

const props = defineProps({
  isDrawerOpen: {
    type: Boolean,
    required: true,
  },
  subscriptionId: {
    type: String,
    required: true,
  },
})

const emit = defineEmits(['update:isDrawerOpen', 'updated'])

const { showSnackbar } = useGlobalData()
const selectedDate = ref()
const isLoading = ref(false)

const handleDrawerModelValueUpdate = val => {
  emit('update:isDrawerOpen', val)
}

async function changeNextFollowupDate() {
  try {
    isLoading.value = true

    const postData = {
      id: props.subscriptionId,
      next_follow_up_visit_date: selectedDate.value,
    }

    const { data } = await ApiService.post('/admin/update-wl-next-follow-up-visit-date', postData)

    if (data.status === 200) {
      showSnackbar(data.message)
      emit('updated', true)
      emit('update:isDrawerOpen', false)
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    isLoading.value = false
  }
}
</script>

<template>
  <VNavigationDrawer
    :model-value="props.isDrawerOpen"
    temporary
    location="end"
    width="400"
    class="category-navigation-drawer scrollable-content"
    @update:model-value="handleDrawerModelValueUpdate"
  >
    <!-- 👉 Header -->
    <AppDrawerHeaderSection
      title="Change Next Follow-up Date"
      @cancel="$emit('update:isDrawerOpen', false)"
    />

    <VDivider />

    <PerfectScrollbar :options="{ wheelPropagation: false }">
      <VCard flat>
        <VCardText>
          <AppDateTimePicker
            v-model="selectedDate"
            label="Choose Date"
            placeholder="MM/DD/YYYY"
            :config="{
              dateFormat: 'm/d/Y',
            }"
          />

          <VBtn
            block
            class="mt-6"
            :loading="isLoading"
            @click="changeNextFollowupDate"
          >
            Save
          </VBtn>
        </VCardText>
      </VCard>
    </PerfectScrollbar>
  </VNavigationDrawer>
</template>

<style lang="scss">
.category-navigation-drawer{
  .ProseMirror {
    padding: 0.5rem;
    min-block-size: 15vh;

    p {
      margin-block-end: 0;
    }

    p.is-editor-empty:first-child::before {
      block-size: 0;
      color: #adb5bd;
      content: attr(data-placeholder);
      float: inline-start;
      pointer-events: none;
    }
  }

  .is-active {
    border-color: rgba(var(--v-theme-primary), var(--v-border-opacity)) !important;
    background-color: rgba(var(--v-theme-primary), var(--v-activated-opacity));
    color: rgb(var(--v-theme-primary));
  }

  .ProseMirror-focused{
    outline: none !important;
  }
}
</style>
