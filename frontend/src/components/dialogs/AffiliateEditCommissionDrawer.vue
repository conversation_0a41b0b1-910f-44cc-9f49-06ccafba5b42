<script setup>
import { requiredValidator } from '@/@core/utils/validators'
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { processErrors } from '@/utils/errorHandler'
import { computed, ref, watch } from 'vue'
import { isEmptyObject, isNullOrUndefined } from '@/@core/utils'
import { formatCurrency } from '@/utils/helpers'
import { storeToRefs } from 'pinia'
import { useAffiliateUserDetailsStore } from '@/store/admin/affiliateUserDetails'
import { PerfectScrollbar } from 'vue3-perfect-scrollbar'

const props = defineProps({
  isDrawerOpen: {
    type: Boolean,
    required: true,
  },
  userId: {
    type: String,
    required: true,
  },
})

const emit = defineEmits(['update:isDrawerOpen', 'updated'])

const userDetailsStore = useAffiliateUserDetailsStore()
const { userData } = storeToRefs(userDetailsStore)
const { showSnackbar } = useGlobalData()
const formRef = ref(null)
const isLoading = ref(false)
const serverErrors = ref([])
const inputErrors = ref({})
const commissionTiers = ref([])
const selectedTierId = ref(null)

onMounted(async () => {
  await getTiers()
})

async function getTiers() {
  try {
    const { data } = await ApiService.get('/admin/get-commission-tiers')
    if (data.status === 200) {
      commissionTiers.value = data.commissionTiers
    } else {
      throw data
    }
  } catch (error) {
    showSnackbar(processErrors(error)[0], 'error')
    emit('update:isDrawerOpen', false)
  }
}

const selectedTier = computed(() => {
  return commissionTiers.value?.find(tier => tier.id === selectedTierId.value)
})

const currentTier = computed(() => {
  return userData.value?.commission_tier_detail?.current_commission_tier_level || null
})

const currentTierStructure = computed(() => {
  return userData.value?.commission_tier_detail?.current_commission_tier_structure || null
})

const resetForm = () => {
  emit('update:isDrawerOpen', false)
}

watch(props, () => {
  selectedTierId.value = currentTierStructure.value?.id || null
})

const onFormSubmit = async () => {
  formRef.value?.validate().then(async valid => {
    if (valid.valid) {
      await update()
    }
  })
}

async function update() {
  try {
    isLoading.value = true

    const formData = {
      id: props.userId,
      region_commission_tier_id: selectedTierId.value,
    }

    const { data } = await ApiService.post('/admin/update-affiliate-user-commission-tiers', formData)

    if (data.status === 200) {
      showSnackbar(data.message)
      emit('update:isDrawerOpen', false)
      emit('updated', true)
      userDetailsStore.setProfileUpdated()
    } else {
      serverErrors.value = processErrors(data)
    }
  } catch (error) {
    serverErrors.value = processErrors(error)
    console.error(error)
  } finally {
    isLoading.value = false
  }
}

const handleDrawerModelValueUpdate = val => {
  emit('update:isDrawerOpen', val)
}
</script>

<template>
  <VNavigationDrawer
    :model-value="props.isDrawerOpen"
    temporary
    location="end"
    width="400"
    class="category-navigation-drawer scrollable-content"
    @update:model-value="handleDrawerModelValueUpdate"
  >
    <!-- 👉 Header -->
    <AppDrawerHeaderSection
      title="Update Affiliate Commission"
      @cancel="$emit('update:isDrawerOpen', false)"
    />

    <VDivider />

    <PerfectScrollbar :options="{ wheelPropagation: false }">
      <VCard flat>
        <VCardText>
          <!-- 👉 Alert -->
          <VAlert
            v-if="isEmptyObject(inputErrors) && serverErrors.length > 0"
            type="error"
            variant="tonal"
            title="Error!"
            class="mb-6"
          >
            <ul class="mb-0">
              <li
                v-for="error in serverErrors"
                :key="error"
                class="mb-0"
              >
                {{ error }}
              </li>
            </ul>
          </VAlert>

          <!-- 👉 Form -->
          <VForm
            ref="formRef"
            @submit.prevent="onFormSubmit"
          >
            <VRow>
              <!-- 👉 Commission Tier -->
              <VCol cols="12">
                <AppSelect
                  v-model="selectedTierId"
                  label="Commission Tier"
                  placeholder="Select Commission Tier"
                  :rules="[requiredValidator]"
                  :items="commissionTiers"
                  item-title="tier_name"
                  item-value="id"
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['region_commission_tier_id'])"
                  class="text-sm text-error mb-0 ms-3"
                >
                  {{ inputErrors['region_commission_tier_id'][0] }}
                </p>
              </VCol>

              <!-- Overview -->
              <VCol
                v-if="selectedTier"
                cols="12"
              >
                <VCard
                  variant="tonal"
                  flat
                >
                  <VCardText class="pa-3 px-4 text-high-emphasis">
                    <div class="mb-3">
                      {{ selectedTier?.tier_name }}
                    </div>
                    <div
                      v-for="(tierItem, index) in selectedTier?.tiers"
                      :key="index"
                      class="border rounded-lg pa-3 mb-3"
                      style="flex: 1; min-width: 280px;"
                    >
                      <div class="text-caption text-uppercase mb-2">
                        Tier {{ index + 1 }}
                      </div>
                      <div v-if="tierItem.max === '-1'">
                        Range: Above {{ formatCurrency(tierItem.min) }}
                      </div>
                      <div v-else>
                        Range: {{ formatCurrency(tierItem.min) }} to {{ formatCurrency(tierItem.max) }}
                      </div>
                      <div>Commission: {{ tierItem.commission_type === 'percentage' ? `${tierItem.commission_value}%` : formatCurrency(tierItem.commission_value) }}</div>
                    </div>
                  </VCardText>
                </VCard>
              </VCol>

              <!-- 👉 Submit and Cancel button -->
              <VCol
                cols="12"
                class="text-center"
              >
                <VBtn
                  type="submit"
                  class="me-3"
                  :loading="isLoading"
                >
                  Submit
                </VBtn>

                <VBtn
                  variant="tonal"
                  color="secondary"
                  :disabled="isLoading"
                  @click="resetForm"
                >
                  Cancel
                </VBtn>
              </VCol>
            </VRow>
          </VForm>
        </VCardText>
      </VCard>
    </PerfectScrollbar>
  </VNavigationDrawer>
</template>
