<script setup>
import { isEmpty } from '@/@core/utils'
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { useUpdatePaymentMethod } from '@/store/updatePaymentMethod'
import { processErrors } from '@/utils/errorHandler'
import { onMounted, ref } from 'vue'

const props = defineProps({
  subscriptionId: {
    type: String,
    required: true,
  },
})

const emit = defineEmits(['updatePaymentMethod'])

const { showSnackbar } = useGlobalData()
const { setSubscriptionRefillId } = useUpdatePaymentMethod()

const skeletonLoading = ref(false)
const notifications = ref([])
const currentPage = ref(1)
const totalPage = ref(0)
const totalRecords = ref(0)

onMounted(async () => {
  await fetchNotifications()
})

async function fetchNotifications() {
  try {
    skeletonLoading.value = true

    const postData = {
      id: props.subscriptionId,
      page: currentPage.value,
    }

    const { data } = await ApiService.post('/admin/ed-subscription-notification', postData)

    if (data.status === 200) {
      if (currentPage.value === 1) {
        notifications.value = data.subscriptionsNotifications.records
      } else {
        notifications.value = [...notifications.value, ...data.subscriptionsNotifications.records]
      }
      totalPage.value = data.subscriptionsNotifications.totalPage
      totalRecords.value = data.subscriptionsNotifications.totalRecords
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    console.error(error)
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    skeletonLoading.value = false
  }
}

async function handlePagination() {
  if (!skeletonLoading.value && currentPage.value < totalPage.value) {
    currentPage.value++
    await fetchNotifications()
  }
}

function actionResolver(notification) {
  const type = notification.notification_type

  if (type === 9) {
    return {
      buttonLabel: 'Retry Payment',
      onClick: () => {
        setSubscriptionRefillId(notification.subscription_refill_id)
        emit('updatePaymentMethod')
      },
    }
  }

  return null
}

function reload() {
  currentPage.value = 1
  notifications.value = []
  fetchNotifications()
}

defineExpose({ reload })
</script>

<template>
  <VCard title="Recent Events">
    <VCardText v-if="notifications.length === 0 && !skeletonLoading">
      <div class="d-flex flex-column align-center">
        <VIcon
          icon="tabler-timeline-event"
          size="48"
        />
        <p class="text-body-1 mt-4">
          No recent events
        </p>
      </div>
    </VCardText>

    <VCardText>
      <div class="notifications-wrapper scroll-container">
        <div v-if="notifications.length > 0">
          <div
            v-for="notification in notifications"
            :key="notification"
            class="rounded-lg border border-dashed px-3 py-2 d-flex justify-space-between align-center mb-3"
          >
            <div class="event-content">
              <h6 class="text-h6">
                {{ notification.title }}
              </h6>
              <div class="text-sm mt-1">
                <address v-if="notification.location">
                  {{ notification.location }}
                  <span v-if="notification.ip_address">
                    ({{ notification.ip_address }})
                  </span>
                </address>
              </div>
              <v-tooltip
                :text="notification.action_date"
                content-class="event-tooltip"
                open-on-click
              >
                <template #activator="{ props: tooltip_props }">
                  <time
                    v-bind="tooltip_props"
                    class="text-xs event-time"
                  >{{ notification.date }}</time>
                </template>
              </v-tooltip>
              <span
                v-if="notification.action_by_user_full_name"
                class="text-xs ms-2"
              >
                <span class="text-high-emphasis">Updated by: </span>
                <span v-if="notification.action_by_user_full_name">{{ notification.action_by_user_full_name + " " }}</span>
                <span v-if="notification.role">({{ notification.role }})</span>
              </span>
            </div>
            <VBtn
              v-if="notification.is_action === 1 && !isEmpty(actionResolver(notification))"
              size="small"
              @click="actionResolver(notification).onClick"
            >
              {{ actionResolver(notification).buttonLabel }}
            </VBtn>
          </div>
        </div>

        <div v-if="skeletonLoading">
          <div
            v-for="i in 3"
            :key="i"
            class="rounded-lg border border-dashed px-3 py-2 d-flex justify-space-between align-center mb-3"
          >
            <div>
              <Skeleton
                height="1rem"
                width="20rem"
                class="mb-2"
              />
              <Skeleton
                height="0.75rem"
                width="80px"
              />
            </div>
          </div>
        </div>

        <div
          v-if="currentPage < totalPage && !skeletonLoading"
          class="text-center"
        >
          <VBtn
            size="small"
            @click="handlePagination"
          >
            See More
          </VBtn>
        </div>
      </div>
    </VCardText>
  </VCard>
</template>

<style lang="scss">
.notifications-wrapper {
  max-height: 320px;
  overflow-y: auto;
}
.scroll-container::-webkit-scrollbar {
  width: 4px;
}

.scroll-container::-webkit-scrollbar-track {
  background: white;
}

.scroll-container::-webkit-scrollbar-thumb {
  background: #a7a8a9;
}

.scroll-container::-webkit-scrollbar-thumb:hover {
  background: #888;
}
.event-time {
  &:hover {
    text-decoration: underline;
    cursor: pointer;
  }
}
.event-tooltip {
  max-width: 200px;
  font-size: 12px !important;
}
</style>
