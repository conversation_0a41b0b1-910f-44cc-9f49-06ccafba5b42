<script setup>
import { requiredValidator } from '@/@core/utils/validators'
import ApiService from '@/services/ApiService'
import { useAffiliateUserDetailsStore } from '@/store/admin/affiliateUserDetails'
import { useGlobalData } from '@/store/global'
import { processErrors } from '@/utils/errorHandler'
import { isNumber, plainPhoneNumber } from '@/utils/helpers'
import { vMaska } from 'maska'
import { computed, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import { isEmptyObject, isNullOrUndefined } from '@/@core/utils'
import { storeToRefs } from 'pinia'

const props = defineProps({
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits([
  'update:isDialogVisible',
  'updated',
])

const route = useRoute()
const globalStore = useGlobalData()
const { showSnackbar } = globalStore
const userDetailsStore = useAffiliateUserDetailsStore()
const { userData } = storeToRefs(userDetailsStore)

const formRef = ref(null)
const serverErrors = ref([])
const inputErrors = ref({})

const userFormData = ref({
  'first_name': '',
  'last_name': '',
  'email': '',
  'phone_number': '',
  'website': '',
  'company_name': '',
})

const isLoading = ref(false)
const userId = computed(() => route.params.userId)

watch(userData, () => {
  userFormData.value = {
    first_name: userData.value?.first_name,
    last_name: userData.value?.last_name,
    email: userData.value?.email,
    phone_number: userData?.value.phone_number,
    website: userData.value?.user_additional_information?.website,
    company_name: userData.value?.user_additional_information?.company_name,
  }
})

onMounted(async () => {
  //
})

const onFormSubmit = async () => {
  formRef.value?.validate().then(async valid => {
    if (valid.valid) {
      await updateUserDetails()
    }
  })
}

async function updateUserDetails() {
  try {
    isLoading.value = true

    const formData = {
      ...userFormData.value,
      phone_number: plainPhoneNumber(userFormData.value.phone_number),
      id: userId.value,
    }

    const { data } = await ApiService.post('/admin/update-affiliate-user-personal-information', formData)

    if (data.status === 200) {
      emit('update:isDialogVisible', false)
      showSnackbar(data.message)
      userDetailsStore.setProfileUpdated()
      await userDetailsStore.fetchUserDetails(userId.value)
    } else {
      if (data.errors) {
        inputErrors.value = data.errors
      } else {
        showSnackbar(data.message)
      }
    }
  } catch (error) {
    serverErrors.value = processErrors(error)
    console.error(error)
  } finally {
    isLoading.value = false
  }
}

const onFormReset = () => {
  userFormData.value = {
    first_name: userData.value?.first_name,
    last_name: userData.value?.last_name,
    email: userData.value?.email,
    phone_number: userData?.value.phone_number,
    website: userData.value?.user_additional_information?.website,
    company_name: userData.value?.user_additional_information?.company_name,
  }
  emit('update:isDialogVisible', false)
}

const dialogModelValueUpdate = val => {
  emit('update:isDialogVisible', val)
}
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 677"
    :model-value="props.isDialogVisible"
    @update:model-value="dialogModelValueUpdate"
  >
    <!-- Dialog close btn -->
    <DialogCloseBtn @click="onFormReset" />

    <VCard class="pa-sm-8 pa-5">
      <VCardItem class="text-center">
        <VCardTitle class="text-h3 mb-3">
          Edit User Details
        </VCardTitle>
      </VCardItem>

      <VCardText>
        <!-- 👉 Alert -->
        <VAlert
          v-if="isEmptyObject(inputErrors) && serverErrors.length > 0"
          type="error"
          variant="tonal"
          title="Validation failed!"
          class="mb-6"
        >
          <ul class="mb-0">
            <li
              v-for="error in serverErrors"
              :key="error"
              class="mb-0"
            >
              {{ error }}
            </li>
          </ul>
        </VAlert>

        <!-- 👉 Form -->
        <VForm
          ref="formRef"
          @submit.prevent="onFormSubmit"
        >
          <VRow>
            <!-- 👉 First Name -->
            <VCol
              cols="12"
              md="6"
            >
              <AppTextField
                v-model="userFormData.first_name"
                label="First Name"
                :rules="[requiredValidator]"
                required
              />
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['first_name'])"
                class="text-sm text-error mb-0 ms-3"
              >
                {{ inputErrors['first_name'][0] }}
              </p>
            </VCol>

            <!-- 👉 Last Name -->
            <VCol
              cols="12"
              md="6"
            >
              <AppTextField
                v-model="userFormData.last_name"
                label="Last Name"
                :rules="[requiredValidator]"
                required
              />
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['last_name'])"
                class="text-sm text-error mb-0 ms-3"
              >
                {{ inputErrors['last_name'][0] }}
              </p>
            </VCol>

            <!-- 👉 Email -->
            <VCol
              cols="12"
              md="6"
            >
              <AppTextField
                v-model="userFormData.email"
                label="Email"
                :rules="[requiredValidator]"
                required
              />
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['email'])"
                class="text-sm text-error mb-0 ms-3"
              >
                {{ inputErrors['email'][0] }}
              </p>
            </VCol>

            <!-- 👉 Status -->
            <VCol
              cols="12"
              md="6"
            >
              <AppTextField
                v-model="userFormData.phone_number"
                label="Phone Number"
                :rules="[requiredValidator]"
                required
                @keydown="isNumber"
              />
              <input
                v-model="userFormData.phone_number"
                v-maska
                type="hidden"
                data-maska="(###) ###-####"
              />
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['phone_number'])"
                class="text-sm text-error mb-0 ms-3"
              >
                {{ inputErrors['phone_number'][0] }}
              </p>
            </VCol>

            <!-- 👉 Company Name -->
            <VCol
              cols="12"
              md="6"
            >
              <AppTextField
                v-model="userFormData.company_name"
                label="Company Name"
              />
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['company_name'])"
                class="text-sm text-error mb-0 ms-3"
              >
                {{ inputErrors['company_name'][0] }}
              </p>
            </VCol>

            <!-- 👉 Website -->
            <VCol
              cols="12"
              md="6"
            >
              <AppTextField
                v-model="userFormData.website"
                label="Website"
              />
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['website'])"
                class="text-sm text-error mb-0 ms-3"
              >
                {{ inputErrors['website'][0] }}
              </p>
            </VCol>

            <!-- 👉 Submit and Cancel -->
            <VCol
              cols="12"
              class="d-flex flex-wrap justify-center gap-4"
            >
              <VBtn
                type="submit"
                :loading="isLoading"
              >
                Submit
              </VBtn>

              <VBtn
                color="secondary"
                variant="tonal"
                :disabled="isLoading"
                @click="onFormReset"
              >
                Cancel
              </VBtn>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>
