<script setup>
import { requiredValidator } from '@/@core/utils/validators'
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { processErrors } from '@/utils/errorHandler'
import { isNumber } from '@/utils/helpers'
import { onMounted, watch } from 'vue'

const props = defineProps({
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
  refundId: {
    type: String,
    required: true,
  },
  refundAmount: {
    type: Number,
    required: true,
  },
})

const emit = defineEmits([
  'update:isDialogVisible',
  'updated',
])

const globalStore = useGlobalData()
const { showSnackbar } = globalStore

const formRef = ref(null)
const isLoading = ref(false)
const serverErrors = ref([])
const refundAmount = ref(0)

watch(props, () => {
  refundAmount.value = props.refundAmount
})

onMounted(() => {
  refundAmount.value = props.refundAmount
})

const resetForm = () => {
  emit('update:isDialogVisible', false)
}

const onFormSubmit = async () => {
  formRef.value?.validate().then(async valid => {
    if (valid.valid) {
      await update()
    }
  })
}

async function update() {
  try {
    isLoading.value = true

    const formData = {
      refund_id: props.refundId,
      refund_amount: refundAmount.value,
    }

    const { data } = await ApiService.post('/admin/edit-refund-requested-amount', formData)

    if (data.status === 200) {
      emit('update:isDialogVisible', false)
      emit('updated', true)
      showSnackbar(data.message)
    } else {
      serverErrors.value = processErrors(data)
    }
  } catch (error) {
    serverErrors.value = processErrors(error)
    console.error(error)
  } finally {
    isLoading.value = false
  }
}
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 610"
    :model-value="props.isDialogVisible"
    @update:model-value="val => $emit('update:isDialogVisible', val)"
  >
    <!-- 👉 Dialog close btn -->
    <DialogCloseBtn @click="$emit('update:isDialogVisible', false)" />

    <VCard class="pa-sm-8 pa-5">
      <!-- 👉 Title -->
      <VCardItem>
        <VCardTitle class="text-h4 text-center">
          Edit Requested Refund Amount
        </VCardTitle>
      </VCardItem>

      <VCardText>
        <!-- 👉 Alert -->
        <VAlert
          v-if="serverErrors.length > 0"
          type="error"
          variant="tonal"
          title="Validation failed!"
          class="mb-6"
        >
          <ul class="mb-0">
            <li
              v-for="error in serverErrors"
              :key="error"
              class="mb-0"
            >
              {{ error }}
            </li>
          </ul>
        </VAlert>

        <!-- 👉 Form -->
        <VForm
          ref="formRef"
          @submit.prevent="onFormSubmit"
        >
          <VRow>
            <VCol cols="12">
              <VTextField
                v-model="refundAmount"
                label="Refund Amount"
                prefix="$"
                :rules="[requiredValidator]"
                @keydown="isNumber($event)"
              />
            </VCol>

            <!-- 👉 Submit and Cancel button -->
            <VCol
              cols="12"
              class="text-center"
            >
              <VBtn
                type="submit"
                class="me-3"
                :loading="isLoading"
              >
                submit
              </VBtn>

              <VBtn
                variant="tonal"
                color="secondary"
                :disabled="isLoading"
                @click="resetForm"
              >
                Cancel
              </VBtn>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>
