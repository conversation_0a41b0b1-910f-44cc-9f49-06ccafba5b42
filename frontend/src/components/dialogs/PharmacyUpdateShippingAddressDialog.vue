<script setup>
import { requiredValidator } from '@/@core/utils/validators'
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { processErrors } from '@/utils/errorHandler'
import { onMounted, watch } from 'vue'
import { isEmptyObject, isNullOrUndefined } from '@/@core/utils'
import { vMaska } from 'maska'
import { removeKeyFromObject } from '@/utils/helpers'
import { PerfectScrollbar } from 'vue3-perfect-scrollbar'

const props = defineProps({
  isDrawerOpen: {
    type: Boolean,
    required: true,
  },
  refillId: {
    type: String,
    required: true,
  },
  shippingAddress: {
    type: Object,
    required: false,
    default: () => ({
      address_line_1: '',
      address_line_2: '',
      city: '',
      state: '',
      zip_code: '',
    }),
  },
})

const emit = defineEmits(['update:isDrawerOpen', 'updated'])

const { showSnackbar } = useGlobalData()

const formRef = ref(null)
const isLoading = ref(false)
const serverErrors = ref([])
const inputErrors = ref({})
const states = ref([])
const shippingAddress = ref(structuredClone(toRaw(props.shippingAddress)))

onMounted(async () => {
  await fetchStates()
})

const fetchStates = async () => {
  try {
    const { data } = await ApiService.get('/state-list')

    if (data.status === 200) {
      states.value = data.stateData ?? []
    } else {
      console.error(data)
    }
  } catch (error) {
    console.error(error)
  }
}

const stateItems = computed(() => {
  const statesList = states.value.filter(item => item.type === 'state')
  const territoriesList = states.value.filter(item => item.type === 'territory')

  return [
    { props: { header: 'States' } },
    ...statesList,
    { props: { header: 'Territories' } },
    ...territoriesList,
  ]
})

const resetForm = () => {
  emit('update:isDrawerOpen', false)
  shippingAddress.value = structuredClone(toRaw(props.shippingAddress))
}

const handleDrawerModelValueUpdate = val => {
  emit('update:isDrawerOpen', val)
}

watch(props, () => {
  shippingAddress.value = structuredClone(toRaw(props.shippingAddress))
})

const onFormSubmit = async () => {
  formRef.value?.validate().then(async valid => {
    if (valid.valid) {
      await updateShippingAddress()
    }
  })
}

async function updateShippingAddress () {
  try {
    isLoading.value = true

    const formData = {
      refill_id: props.refillId,
      address_line_1: shippingAddress.value.address_line_1,
      address_line_2: shippingAddress.value.address_line_2,
      city: shippingAddress.value.city,
      state: shippingAddress.value.state,
      zipcode: shippingAddress.value.zip_code,
    }

    const { data } = await ApiService.post('/admin/update-pharmacy-order-shipping-address', formData)

    if (data.status === 200) {
      emit('update:isDrawerOpen', false)
      emit('updated', true)
      showSnackbar(data.message)
    } else {
      if (data.errors) {
        inputErrors.value = data.errors
      }
    }
  } catch (error) {
    serverErrors.value = processErrors(error)
    console.error(error)
  } finally {
    isLoading.value = false
  }
}

function removeKeyFromInputErrors(key) {
  inputErrors.value = removeKeyFromObject(inputErrors.value, key)
}
</script>

<template>
  <VNavigationDrawer
    :model-value="props.isDrawerOpen"
    temporary
    location="end"
    width="400"
    class="category-navigation-drawer scrollable-content"
    @update:model-value="handleDrawerModelValueUpdate"
  >
    <!-- 👉 Header -->
    <AppDrawerHeaderSection
      title="Update Shipping Address"
      @cancel="$emit('update:isDrawerOpen', false)"
    />

    <VDivider />

    <PerfectScrollbar :options="{ wheelPropagation: false }">
      <VCard flat>
        <VCardText>
          <!-- 👉 Alert -->
          <VAlert
            v-if="isEmptyObject(inputErrors) && serverErrors.length > 0"
            type="error"
            variant="tonal"
            title="Error!"
            class="mb-6"
          >
            <ul class="mb-0">
              <li
                v-for="error in serverErrors"
                :key="error"
                class="mb-0"
              >
                {{ error }}
              </li>
            </ul>
          </VAlert>

          <!-- 👉 Form -->
          <VForm
            ref="formRef"
            @submit.prevent="onFormSubmit"
          >
            <VRow>
              <!-- 👉 Address 1 -->
              <VCol cols="12">
                <AppTextField
                  v-model="shippingAddress.address_line_1"
                  label="Address Line 1"
                  :rules="[requiredValidator]"
                  required
                  @keyup="removeKeyFromInputErrors('address_line_1')"
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['address_line_1'])"
                  class="text-sm text-error mb-0 ms-3"
                >
                  {{ inputErrors['address_line_1'][0] }}
                </p>
              </VCol>

              <!-- 👉 Address 2 -->
              <VCol cols="12">
                <AppTextField
                  v-model="shippingAddress.address_line_2"
                  label="Address Line 2"
                  @keyup="removeKeyFromInputErrors('address_line_2')"
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['address_line_2'])"
                  class="text-sm text-error mb-0 ms-3"
                >
                  {{ inputErrors['address_line_2'][0] }}
                </p>
              </VCol>

              <!-- 👉 City -->
              <VCol cols="12">
                <AppTextField
                  v-model="shippingAddress.city"
                  label="City"
                  :rules="[requiredValidator]"
                  required
                  @keyup="removeKeyFromInputErrors('city')"
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['city'])"
                  class="text-sm text-error mb-0 ms-3"
                >
                  {{ inputErrors['city'][0] }}
                </p>
              </VCol>

              <!-- 👉 State / Territory -->
              <VCol cols="12">
                <AppSelect
                  v-model="shippingAddress.state"
                  label="State / Territory"
                  :rules="[requiredValidator]"
                  :items="stateItems"
                  item-title="name"
                  item-value="code"
                  @keyup="removeKeyFromInputErrors('state')"
                >
                  <!-- eslint-disable-next-line vue/no-unused-vars -->
                  <template #item="{ props: sProps, item }">
                    <v-list-subheader v-if="sProps.header">
                      <span class="text-sm">{{ sProps.header }}</span>
                    </v-list-subheader>
                    <v-list-item
                      v-else
                      v-bind="sProps"
                    ></v-list-item>
                  </template>
                </AppSelect>
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['state'])"
                  class="text-sm text-error mb-0 ms-3"
                >
                  {{ inputErrors['state'][0] }}
                </p>
              </VCol>

              <!-- 👉 Zip Code -->
              <VCol cols="12">
                <AppTextField
                  v-model="shippingAddress.zip_code"
                  label="Zip Code"
                  :rules="[requiredValidator]"
                  required
                  @keyup="removeKeyFromInputErrors('zipcode')"
                />
                <input
                  v-model="shippingAddress.zip_code"
                  v-maska
                  type="hidden"
                  data-maska="#####-####"
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['zipcode'])"
                  class="text-sm text-error mb-0 ms-3"
                >
                  {{ inputErrors['zipcode'][0] }}
                </p>
              </VCol>

              <!-- 👉 Submit and Cancel button -->
              <VCol
                cols="12"
                class="text-center"
              >
                <VBtn
                  type="submit"
                  class="me-3"
                  :loading="isLoading"
                >
                  submit
                </VBtn>

                <VBtn
                  variant="tonal"
                  color="secondary"
                  :disabled="isLoading"
                  @click="resetForm"
                >
                  Cancel
                </VBtn>
              </VCol>
            </VRow>
          </VForm>
        </VCardText>
      </VCard>
    </PerfectScrollbar>
  </VNavigationDrawer>
</template>

<style lang="scss">
.category-navigation-drawer{
  .ProseMirror {
    padding: 0.5rem;
    min-block-size: 15vh;

    p {
      margin-block-end: 0;
    }

    p.is-editor-empty:first-child::before {
      block-size: 0;
      color: #adb5bd;
      content: attr(data-placeholder);
      float: inline-start;
      pointer-events: none;
    }
  }

  .is-active {
    border-color: rgba(var(--v-theme-primary), var(--v-border-opacity)) !important;
    background-color: rgba(var(--v-theme-primary), var(--v-activated-opacity));
    color: rgb(var(--v-theme-primary));
  }

  .ProseMirror-focused{
    outline: none !important;
  }
}
</style>
