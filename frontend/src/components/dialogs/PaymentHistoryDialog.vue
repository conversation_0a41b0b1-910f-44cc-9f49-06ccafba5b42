<script setup>
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { processErrors } from '@/utils/errorHandler'
import { onMounted, watch } from 'vue'
import { isEmpty } from '@/@core/utils'
import { formattedPhoneNumber } from '@/utils/helpers'
import { resolvePaymentStatusColor, resolveTransactionTypeColor } from '@/utils/admin'

const props = defineProps({
  paymentHistoryId: {
    type: String,
    required: false,
  },
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits([
  'update:isDialogVisible',
  'updated',
])

const globalStore = useGlobalData()
const { showSnackbar } = globalStore

const isLoading = ref(true)
const paymentDetails = ref([])

watch(props, () => {
  if (props.isDialogVisible && !isEmpty(props.paymentHistoryId)) {
    fetchDetails()
  }
})

const fetchDetails = async () => {
  try {
    isLoading.value = true

    const { data } = await ApiService.get(`/admin/payment-history/${props.paymentHistoryId}`)

    if (data.status === 200) {
      paymentDetails.value = data.paymentDetails
    } else {
      showSnackbar(data.message, 'error')
      emit('update:isDialogVisible', false)
    }
  } catch (error) {
    console.error(error)
    showSnackbar(processErrors(error)[0], 'error')
    emit('update:isDialogVisible', false)
  } finally {
    isLoading.value = false
  }
}
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 610 "
    :model-value="props.isDialogVisible"
    @update:model-value="val => $emit('update:isDialogVisible', val)"
  >
    <!-- 👉 Dialog close btn -->
    <DialogCloseBtn @click="$emit('update:isDialogVisible', false)" />

    <VCard class="pa-sm-8 pa-5">
      <!-- 👉 Title -->

      <VCardItem>
        <VCardTitle class="text-h4 text-center">
          Payment Details
        </VCardTitle>
      </VCardItem>

      <VCardText v-if="isLoading">
        <VRow class="mb-6">
          <VCol
            cols="6"
            lg="4"
          >
            <Skeleton
              width="5rem"
              height="1rem"
              class="mb-2"
            ></Skeleton>
            <Skeleton
              width="7rem"
              height="1rem"
            ></Skeleton>
          </VCol>
          <VCol
            cols="6"
            lg="4"
          >
            <Skeleton
              width="5rem"
              height="1rem"
              class="mb-2"
            ></Skeleton>
            <Skeleton
              width="7rem"
              height="1rem"
            ></Skeleton>
          </VCol>
          <VCol
            cols="6"
            lg="4"
          >
            <Skeleton
              width="5rem"
              height="1rem"
              class="mb-2"
            ></Skeleton>
            <Skeleton
              width="7rem"
              height="1rem"
            ></Skeleton>
          </VCol>
          <VCol
            cols="6"
            lg="4"
          >
            <Skeleton
              width="5rem"
              height="1rem"
              class="mb-2"
            ></Skeleton>
            <Skeleton
              width="7rem"
              height="1rem"
            ></Skeleton>
          </VCol>
          <VCol
            cols="6"
            lg="4"
          >
            <Skeleton
              width="5rem"
              height="1rem"
              class="mb-2"
            ></Skeleton>
            <Skeleton
              width="7rem"
              height="1rem"
            ></Skeleton>
          </VCol>
          <VCol
            cols="6"
            lg="4"
          >
            <Skeleton
              width="5rem"
              height="1rem"
              class="mb-2"
            ></Skeleton>
            <Skeleton
              width="7rem"
              height="1rem"
            ></Skeleton>
          </VCol>
        </VRow>
      </VCardText>

      <VCardText v-else>
        <VRow class="pb-6">
          <VCol
            v-if="!isEmpty(paymentDetails?.user_details)"
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              User
            </div>
            <div class="font-weight-medium text-body-2">
              <div
                v-if="!isEmpty(paymentDetails?.user_details?.full_name)"
                class="text-body-1"
              >
                {{ paymentDetails?.user_details?.full_name }}
              </div>
              <div v-if="!isEmpty(paymentDetails?.user_details?.email)">
                {{ paymentDetails?.user_details?.email }}
              </div>
              <div v-if="!isEmpty(paymentDetails?.user_details?.phone_number)">
                {{ formattedPhoneNumber(paymentDetails?.user_details?.phone_number) }}
              </div>
            </div>
          </VCol>

          <VCol
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Order Type
            </div>
            <div class="font-weight-medium text-body-2">
              {{ paymentDetails.refill_number === 0 ? paymentDetails.refill_type : `${paymentDetails.refill_type} ${paymentDetails.refill_number}` }}
            </div>
          </VCol>

          <VCol
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Amount
            </div>
            <div class="font-weight-medium text-body-2">
              {{ formatCurrency(paymentDetails.amount) }}
            </div>
          </VCol>

          <VCol
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Transaction Type
            </div>
            <VChip
              :color="resolveTransactionTypeColor(paymentDetails.transaction_type)"
              size="small"
              label
            >
              {{ paymentDetails.transaction_type_text }}
            </VChip>
          </VCol>

          <VCol
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Status
            </div>
            <VChip
              :color="resolvePaymentStatusColor(paymentDetails.status)"
              size="small"
              label
            >
              {{ paymentDetails.payment_status_text }}
            </VChip>
          </VCol>

          <VCol
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Action By
            </div>
            <div class="font-weight-medium text-body-2">
              <span v-if="!isEmpty(paymentDetails.action_by_user_name) && !isEmpty(paymentDetails.action_by_role)">
                {{ paymentDetails.action_by_user_name }} <br>
                {{ paymentDetails.action_by_role }}
              </span>
              <span v-else-if="!isEmpty(paymentDetails.action_by_user_name) && isEmpty(paymentDetails.action_by_role)">
                {{ paymentDetails.action_by_user_name }}
              </span>
              <span v-else-if="isEmpty(paymentDetails.action_by_user_name) && !isEmpty(paymentDetails.action_by_role)">
                {{ paymentDetails.action_by_role }}
              </span>
            </div>
          </VCol>

          <VCol
            v-if="paymentDetails.transaction_id"
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Transaction ID
            </div>
            <div class="font-weight-medium text-body-2">
              {{ paymentDetails.transaction_id ?? '-' }}
            </div>
          </VCol>

          <VCol
            v-if="paymentDetails.payment_authorize_id"
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Payment Authorize ID
            </div>
            <div class="font-weight-medium text-body-2">
              {{ paymentDetails.payment_authorize_id ?? '-' }}
            </div>
          </VCol>

          <VCol
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Payment Method
            </div>
            <div class="text-body-2">
              <div>{{ paymentDetails.card_brand_type }}</div>
              <div>xxxx xxxx xxxx {{ paymentDetails.card_number }}</div>
            </div>
          </VCol>

          <VCol
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              {{ paymentDetails.status === 2 ? 'Failed' : 'Transaction' }} Date
            </div>
            <div class="font-weight-medium text-body-2">
              {{ paymentDetails.created_date_time }}
            </div>
          </VCol>
          <VCol
            v-if="!isEmpty(paymentDetails.failed_reason)"
            cols="12"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Payment Fail Reason
            </div>
            <div class="font-weight-medium text-body-2">
              {{ paymentDetails.failed_reason }}
            </div>
          </VCol>
        </VRow>
      </VCardText>
    </VCard>
  </VDialog>
</template>
