<script setup>
import { isEmpty } from '@/@core/utils'
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { processErrors } from '@/utils/errorHandler'
import { PerfectScrollbar } from 'vue3-perfect-scrollbar'

const props = defineProps({
  isDrawerOpen: {
    type: Boolean,
    required: true,
  },
  subscriptionId: {
    type: String,
    required: true,
  },
  refillOptions: {
    type: Array,
    required: true,
  },
})

const emit = defineEmits(['update:isDrawerOpen', 'updated'])

const { showSnackbar } = useGlobalData()
const selectedDate = ref()
const customSelectedDate = ref(null)
const isLoading = ref(false)

const handleDrawerModelValueUpdate = val => {
  emit('update:isDrawerOpen', val)
}

watch(selectedDate, newVal => {
  if (newVal !== null) {
    customSelectedDate.value = null
  }
})

async function changeNextRefillDate() {
  try {
    isLoading.value = true

    const postData = {
      id: props.subscriptionId,
      date: isEmpty(selectedDate.value) ? customSelectedDate.value : selectedDate.value,
      is_selected_custom_date: isEmpty(selectedDate.value) ? 1 : 0,
    }

    const { data } = await ApiService.post('/admin/change-ed-subscription-refill-date', postData)

    if (data.status === 200) {
      showSnackbar(data.message)
      emit('updated', true)
      emit('update:isDrawerOpen', false)
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    isLoading.value = false
  }
}
</script>

<template>
  <VNavigationDrawer
    :model-value="props.isDrawerOpen"
    temporary
    location="end"
    width="400"
    class="category-navigation-drawer scrollable-content"
    @update:model-value="handleDrawerModelValueUpdate"
  >
    <!-- 👉 Header -->
    <AppDrawerHeaderSection
      title="Change Next Refill Date"
      @cancel="$emit('update:isDrawerOpen', false)"
    />

    <VDivider />

    <PerfectScrollbar :options="{ wheelPropagation: false }">
      <VCard flat>
        <VCardText>
          <VRadioGroup
            v-if="props.refillOptions"
            v-model="selectedDate"
            class="mb-5"
          >
            <VRow>
              <VCol
                v-for="item in props.refillOptions"
                :key="item.date"
                cols="12"
                class="py-2"
              >
                <VLabel :class="`custom-input custom-radio rounded cursor-pointer w-100 ${selectedDate === item.date ? 'active' : ''}`">
                  <div>
                    <VRadio :value="item.date" />
                  </div>
                  <slot :item="item">
                    <div class="flex-grow-1">
                      <div class="d-flex align-center mb-0">
                        <h6 class="cr-title text-base">
                          {{ item.title }}
                        </h6>
                        <VSpacer />
                        <span class="text-sm">
                          {{ item.formatted_date }}
                        </span>
                      </div>
                      <p class="text-sm mb-0">
                        {{ item.sub_title }}
                      </p>
                    </div>
                  </slot>
                </VLabel>
              </VCol>
            </VRow>
          </VRadioGroup>

          <AppDateTimePicker
            v-if="selectedDate === null"
            v-model="customSelectedDate"
            label="Choose Custom Date"
            placeholder="yyyy-mm-dd"
            :config="{
              dateFormat: 'Y-m-d',
            }"
          />

          <VBtn
            block
            class="mt-6"
            :loading="isLoading"
            @click="changeNextRefillDate"
          >
            Save
          </VBtn>
        </VCardText>
      </VCard>
    </PerfectScrollbar>
  </VNavigationDrawer>
</template>

<style lang="scss">
.category-navigation-drawer{
  .ProseMirror {
    padding: 0.5rem;
    min-block-size: 15vh;

    p {
      margin-block-end: 0;
    }

    p.is-editor-empty:first-child::before {
      block-size: 0;
      color: #adb5bd;
      content: attr(data-placeholder);
      float: inline-start;
      pointer-events: none;
    }
  }

  .is-active {
    border-color: rgba(var(--v-theme-primary), var(--v-border-opacity)) !important;
    background-color: rgba(var(--v-theme-primary), var(--v-activated-opacity));
    color: rgb(var(--v-theme-primary));
  }

  .ProseMirror-focused{
    outline: none !important;
  }
}
</style>
