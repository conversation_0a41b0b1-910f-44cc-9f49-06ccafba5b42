<script setup>
import { isEmpty } from '@/@core/utils'
import { getShippingActivityColor } from '@/utils/admin'

const props = defineProps({
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
  shippingActivities: {
    type: String,
    required: false,
  },
})

const emit = defineEmits(['update:isDialogVisible'])
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 768 "
    :model-value="props.isDialogVisible"
    @update:model-value="val => $emit('update:isDialogVisible', val)"
  >
    <!-- 👉 Dialog close btn -->
    <DialogCloseBtn @click="$emit('update:isDialogVisible', false)" />

    <VCard class="pa-sm-8 pa-5">
      <!-- 👉 Title -->
      <VCardItem>
        <VCardTitle class="text-h4 text-center">
          Shipping Activity
        </VCardTitle>
      </VCardItem>

      <VCardText>
        <VTimeline
          v-if="!isEmpty(shippingActivities)"
          truncate-line="both"
          align="start"
          side="end"
          line-color="primary"
          density="compact"
          class="v-timeline-density-compact"
        >
          <VTimelineItem
            v-for="(item, index) in props.shippingActivities"
            :key="index"
            dot-color="primary"
            size="x-small"
          >
            <div class="d-flex justify-space-between align-center">
              <div class="app-timeline-title">
                <VChip
                  :color="getShippingActivityColor(item.EventStatus)"
                  label
                  class="text-capitalize mb-1"
                >
                  {{ item.EventStatusText }}
                </VChip>
                <div v-if="item.EventDescription">
                  {{ item.EventDescription }}
                </div>
              </div>
              <div class="app-timeline-meta text-right">
                <div v-if="item.EventDate">
                  {{ item.EventDate }}
                </div>
                <div v-if="item.action_by_user_name">
                  <span class="text-medium-emphasis">Updated by: </span>
                  <span v-if="item.action_by_user_name">{{ item.action_by_user_name + ' ' }}</span>
                  <span v-if="item.action_by_user_role">({{ item.action_by_user_role }})</span>
                </div>
              </div>
            </div>
            <p
              v-if="item.EventLocation"
              class="app-timeline-text mb-0"
            >
              {{ item.EventLocation }}
            </p>
          </VTimelineItem>
        </VTimeline>

        <div
          v-else
          class="d-flex flex-column align-center justify-center"
        >
          <VIcon
            size="x-large"
            icon="mdi-truck"
          />
          <span class="text-h6 ms-2 mt-1">Not shipped</span>
        </div>
      </VCardText>
    </VCard>
  </VDialog>
</template>
