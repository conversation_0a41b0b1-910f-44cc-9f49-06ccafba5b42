<script setup>
import { requiredValidator, dobValidator } from '@/@core/utils/validators'
import ApiService from '@/services/ApiService'
import { useUserDetailsStore } from '@/store/admin/userDetails'
import { useGlobalData } from '@/store/global'
import { processErrors } from '@/utils/errorHandler'
import { isNumber, plainPhoneNumber } from '@/utils/helpers'
import { vMaska } from 'maska'
import { computed, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import { isEmptyObject, isNullOrUndefined } from '@/@core/utils'
import { storeToRefs } from 'pinia'
import { PerfectScrollbar } from 'vue3-perfect-scrollbar'

const props = defineProps({
  isDrawerOpen: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits([
  'update:isDrawerOpen',
  'updated',
])

const route = useRoute()
const globalStore = useGlobalData()
const { showSnackbar } = globalStore
const userDetailsStore = useUserDetailsStore()
const { userData } = storeToRefs(userDetailsStore)

const formRef = ref(null)
const serverErrors = ref([])
const inputErrors = ref({})
const userFormData = ref(JSON.parse(JSON.stringify(userData.value)))
const states = ref([])
const isLoading = ref(false)

const sexOptions = [
  { value: 'Male', label: 'Male' },
  { value: 'Female', label: 'Female' },
  { value: 'Other', label: 'Other' },
]

const userId = computed(() => route.params.userId)

watch(userData, () => {
  userFormData.value = JSON.parse(JSON.stringify(userData.value))
})

onMounted(async () => {
  await fetchStates()
})

const fetchStates = async () => {
  try {
    const { data } = await ApiService.get('/state-list')

    if (data.status === 200) {
      states.value = data.stateData ?? []
    } else {
      console.error(data)
    }
  } catch (error) {
    console.error(error)
  }
}

const stateItems = computed(() => {
  const statesList = states.value.filter(item => item.type === 'state')
  const territoriesList = states.value.filter(item => item.type === 'territory')

  return [
    { props: { header: 'States' } },
    ...statesList,
    { props: { header: 'Territories' } },
    ...territoriesList,
  ]
})

const onFormSubmit = async () => {
  formRef.value?.validate().then(async valid => {
    if (valid.valid) {
      await updateUserDetails()
    }
  })
}

async function updateUserDetails() {
  try {
    isLoading.value = true

    const formData = {
      ...userFormData.value,
      phone_number: plainPhoneNumber(userFormData.value.phone_number),
      user_id: userId.value,
    }

    const { data } = await ApiService.post('/admin/update-user-profile', formData)

    if (data.status === 200) {
      emit('update:isDrawerOpen', false)
      showSnackbar(data.message)
      userDetailsStore.setProfileUpdated()
      await userDetailsStore.fetchUserDetails(userId.value)
    } else {
      if (data.errors) {
        inputErrors.value = data.errors
      }
    }
  } catch (error) {
    serverErrors.value = processErrors(error)
    console.error(error)
  } finally {
    isLoading.value = false
  }
}

const onFormReset = () => {
  userFormData.value = JSON.parse(JSON.stringify(userData.value))
  emit('update:isDrawerOpen', false)
}

const handleDrawerModelValueUpdate = val => {
  emit('update:isDrawerOpen', val)
}
</script>

<template>
  <VNavigationDrawer
    :model-value="props.isDrawerOpen"
    temporary
    location="end"
    width="400"
    class="category-navigation-drawer scrollable-content"
    @update:model-value="handleDrawerModelValueUpdate"
  >
    <!-- 👉 Header -->
    <AppDrawerHeaderSection
      title="Edit User Details"
      @cancel="$emit('update:isDrawerOpen', false)"
    />

    <VDivider />

    <PerfectScrollbar :options="{ wheelPropagation: false }">
      <VCard class="pa-0">
        <VCardText>
          <!-- 👉 Alert -->
          <VAlert
            v-if="isEmptyObject(inputErrors) && serverErrors.length > 0"
            type="error"
            variant="tonal"
            title="Validation failed!"
            class="mb-6"
          >
            <ul class="mb-0">
              <li
                v-for="error in serverErrors"
                :key="error"
                class="mb-0"
              >
                {{ error }}
              </li>
            </ul>
          </VAlert>

          <!-- 👉 Form -->
          <VForm
            ref="formRef"
            @submit.prevent="onFormSubmit"
          >
            <VRow>
              <!-- 👉 First Name -->
              <VCol cols="12">
                <AppTextField
                  v-model="userFormData.first_name"
                  label="First Name"
                  :rules="[requiredValidator]"
                  required
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['first_name'])"
                  class="text-sm text-error mb-0 ms-3"
                >
                  {{ inputErrors['first_name'][0] }}
                </p>
              </VCol>

              <!-- 👉 Last Name -->
              <VCol cols="12">
                <AppTextField
                  v-model="userFormData.last_name"
                  label="Last Name"
                  :rules="[requiredValidator]"
                  required
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['last_name'])"
                  class="text-sm text-error mb-0 ms-3"
                >
                  {{ inputErrors['last_name'][0] }}
                </p>
              </VCol>

              <!-- 👉 Email -->
              <VCol cols="12">
                <AppTextField
                  v-model="userFormData.email"
                  label="Email"
                  :rules="[requiredValidator]"
                  required
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['email'])"
                  class="text-sm text-error mb-0 ms-3"
                >
                  {{ inputErrors['email'][0] }}
                </p>
              </VCol>

              <!-- 👉 Status -->
              <VCol cols="12">
                <AppTextField
                  v-model="userFormData.phone_number"
                  label="Phone Number"
                  :rules="[requiredValidator]"
                  required
                  @keydown="isNumber"
                />
                <input
                  v-model="userFormData.phone_number"
                  v-maska
                  type="hidden"
                  data-maska="(###) ###-####"
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['phone_number'])"
                  class="text-sm text-error mb-0 ms-3"
                >
                  {{ inputErrors['phone_number'][0] }}
                </p>
              </VCol>

              <!-- 👉 Date of Birth -->
              <VCol cols="12">
                <AppTextField
                  v-model="userFormData.dob"
                  label="Date of Birth"
                  :rules="[requiredValidator, dobValidator]"
                  required
                  placeholder="mm/dd/yyyy"
                  @keydown="isNumber"
                />
                <input
                  v-model="userFormData.dob"
                  v-maska
                  type="hidden"
                  data-maska="##/##/####"
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['dob'])"
                  class="text-sm text-error mb-0 ms-3"
                >
                  {{ inputErrors['dob'][0] }}
                </p>
              </VCol>

              <!-- 👉 Gender -->
              <VCol cols="12">
                <AppSelect
                  v-model="userFormData.sex"
                  label="Sex assigned at birth"
                  :rules="[requiredValidator]"
                  :items="sexOptions"
                  item-title="label"
                  item-value="value"
                  required
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['sex'])"
                  class="text-sm text-error mb-0 ms-3"
                >
                  {{ inputErrors['sex'][0] }}
                </p>
              </VCol>

              <!-- 👉 State / Territory -->
              <VCol cols="12">
                <AppSelect
                  v-model="userFormData.state"
                  label="State / Territory"
                  :rules="[requiredValidator]"
                  :items="stateItems"
                  item-title="name"
                  item-value="code"
                  required
                >
                  <!-- eslint-disable-next-line vue/no-unused-vars -->
                  <template #item="{ props: sProps, item }">
                    <v-list-subheader v-if="sProps.header">
                      <span class="text-sm">{{ sProps.header }}</span>
                    </v-list-subheader>
                    <v-list-item
                      v-else
                      v-bind="sProps"
                    ></v-list-item>
                  </template>
                </AppSelect>
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['state'])"
                  class="text-sm text-error mb-0 ms-3"
                >
                  {{ inputErrors['state'][0] }}
                </p>
              </VCol>

              <!-- 👉 Submit and Cancel -->
              <VCol
                cols="12"
                class="d-flex flex-wrap justify-center gap-4"
              >
                <VBtn
                  type="submit"
                  :loading="isLoading"
                >
                  Submit
                </VBtn>

                <VBtn
                  color="secondary"
                  variant="tonal"
                  :disabled="isLoading"
                  @click="onFormReset"
                >
                  Cancel
                </VBtn>
              </VCol>
            </VRow>
          </VForm>
        </VCardText>
      </VCard>
    </PerfectScrollbar>
  </VNavigationDrawer>
</template>
