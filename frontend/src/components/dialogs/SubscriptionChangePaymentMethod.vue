<script setup>
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { useUpdatePaymentMethod } from '@/store/updatePaymentMethod'
import { processErrors } from '@/utils/errorHandler'
import { isCardExpired } from '@/utils/helpers'
import { storeToRefs } from 'pinia'
import { PerfectScrollbar } from 'vue3-perfect-scrollbar'
import { isEmpty } from '@/@core/utils'

const props = defineProps({
  isDrawerOpen: {
    type: Boolean,
    required: true,
  },
  subscriptionId: {
    type: String,
    required: true,
  },
  paymentMethods: {
    type: Array,
    required: true,
  },
  currentPaymentMethodId: {
    type: String,
  },
})

const emit = defineEmits(['update:isDrawerOpen', 'updated'])

const { showSnackbar } = useGlobalData()
const { subscriptionRefillId } = storeToRefs(useUpdatePaymentMethod())

const selectedPaymentMethod = ref(null)
const isLoading = ref(false)

const handleDrawerModelValueUpdate = val => {
  emit('update:isDrawerOpen', val)
}

watch(props, () => {
  selectedPaymentMethod.value = props.currentPaymentMethodId
})

async function updatePaymentMethod() {
  try {
    isLoading.value = true

    const postData = {
      id: props.subscriptionId,
      payment_method_id: selectedPaymentMethod.value,
    }

    if (!isEmpty(subscriptionRefillId.value)) {
      postData.subscription_refill_id = subscriptionRefillId.value
    }

    const { data } = await ApiService.post('/admin/update-ed-subscription-payment-method', postData)

    if (data.status === 200) {
      showSnackbar(data.message)
      emit('updated', true)
      emit('update:isDrawerOpen', false)
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    isLoading.value = false
  }
}
</script>

<template>
  <VNavigationDrawer
    :model-value="props.isDrawerOpen"
    temporary
    location="end"
    width="400"
    class="category-navigation-drawer scrollable-content"
    @update:model-value="handleDrawerModelValueUpdate"
  >
    <!-- 👉 Header -->
    <AppDrawerHeaderSection
      :title="!isEmpty(subscriptionRefillId) ? 'Retry Payment' : 'Change Payment Method'"
      @cancel="$emit('update:isDrawerOpen', false)"
    />

    <VDivider />

    <PerfectScrollbar :options="{ wheelPropagation: false }">
      <VCard flat>
        <VCardText>
          <VRadioGroup
            v-if="props.paymentMethods"
            v-model="selectedPaymentMethod"
          >
            <VRow>
              <VCol
                v-for="item in props.paymentMethods"
                :key="item.id"
                cols="12"
                class="py-2"
              >
                <VLabel :class="`custom-input custom-radio rounded cursor-pointer w-100 ${selectedPaymentMethod === item.id ? 'active' : ''} ${isCardExpired(item.card_expiry_month, item.card_expiry_year) ? 'border border-error' : ''}`">
                  <div>
                    <VRadio
                      :value="item.id"
                      :disabled="isCardExpired(item.card_expiry_month, item.card_expiry_year)"
                    />
                  </div>
                  <slot :item="item">
                    <div class="flex-grow-1">
                      <div class="d-flex align-center mb-0">
                        <h6 class="cr-title text-sm">
                          {{ item.card_brand_type }}
                        </h6>
                        <VSpacer />
                        <span class="text-disabled text-sm">
                          <VChip
                            v-if="isCardExpired(item.card_expiry_month, item.card_expiry_year)"
                            size="x-small"
                            color="error"
                            label
                          >Expired</VChip>
                          <span v-else>
                            {{ item.card_expiry_month }}/{{ item.card_expiry_year }}
                          </span>
                        </span>
                      </div>
                      <p class="text-base mb-0">
                        xxxx xxxx xxxx {{ item.card_last_4_digit }}
                      </p>
                    </div>
                  </slot>
                </VLabel>
              </VCol>
            </VRow>
          </VRadioGroup>

          <VBtn
            block
            class="mt-6"
            :loading="isLoading"
            @click="updatePaymentMethod"
          >
            {{ !isEmpty(subscriptionRefillId) ? 'Retry' : "Save" }}
          </VBtn>
        </VCardText>
      </VCard>
    </PerfectScrollbar>
  </VNavigationDrawer>
</template>

<style lang="scss">
.category-navigation-drawer{
  .ProseMirror {
    padding: 0.5rem;
    min-block-size: 15vh;

    p {
      margin-block-end: 0;
    }

    p.is-editor-empty:first-child::before {
      block-size: 0;
      color: #adb5bd;
      content: attr(data-placeholder);
      float: inline-start;
      pointer-events: none;
    }
  }

  .is-active {
    border-color: rgba(var(--v-theme-primary), var(--v-border-opacity)) !important;
    background-color: rgba(var(--v-theme-primary), var(--v-activated-opacity));
    color: rgb(var(--v-theme-primary));
  }

  .ProseMirror-focused{
    outline: none !important;
  }
}
</style>
