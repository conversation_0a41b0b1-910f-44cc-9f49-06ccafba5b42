<script setup>
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { processErrors } from '@/utils/errorHandler'
import { watch } from 'vue'
import { isEmpty } from '@/@core/utils'
import { formattedPhoneNumber } from '@/utils/helpers'

const props = defineProps({
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
  refundRequestId: {
    type: String,
    required: false,
  },
})

const emit = defineEmits([
  'update:isDialogVisible',
  'updated',
  'approve',
  'decline',
])

const globalStore = useGlobalData()
const { showSnackbar } = globalStore

const isLoading = ref(true)
const details = ref([])

watch(props, () => {
  if (props.isDialogVisible && !isEmpty(props.refundRequestId)) {
    fetchDetails()
  }
})

const fetchDetails = async () => {
  try {
    isLoading.value = true

    const { data } = await ApiService.get(`/admin/view-refund-request/${props.refundRequestId}`)

    if (data.status === 200) {
      details.value = data.refundRequestDetails
    } else {
      showSnackbar(data.message, 'error')
      emit('update:isDialogVisible', false)
    }
  } catch (error) {
    console.error(error)
    showSnackbar(processErrors(error)[0], 'error')
    emit('update:isDialogVisible', false)
  } finally {
    isLoading.value = false
  }
}
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 610 "
    :model-value="props.isDialogVisible"
    @update:model-value="val => $emit('update:isDialogVisible', val)"
  >
    <!-- 👉 Dialog close btn -->
    <DialogCloseBtn @click="$emit('update:isDialogVisible', false)" />

    <VCard class="pa-sm-8 pa-5">
      <!-- 👉 Title -->

      <VCardItem>
        <VCardTitle class="text-h4 text-center">
          Refund Details
        </VCardTitle>
      </VCardItem>

      <VCardText v-if="isLoading">
        <VRow class="mb-6">
          <VCol
            cols="6"
            lg="4"
          >
            <Skeleton
              width="5rem"
              height="1rem"
              class="mb-2"
            ></Skeleton>
            <Skeleton
              width="7rem"
              height="1rem"
            ></Skeleton>
          </VCol>
          <VCol
            cols="6"
            lg="4"
          >
            <Skeleton
              width="5rem"
              height="1rem"
              class="mb-2"
            ></Skeleton>
            <Skeleton
              width="7rem"
              height="1rem"
            ></Skeleton>
          </VCol>
          <VCol
            cols="6"
            lg="4"
          >
            <Skeleton
              width="5rem"
              height="1rem"
              class="mb-2"
            ></Skeleton>
            <Skeleton
              width="7rem"
              height="1rem"
            ></Skeleton>
          </VCol>
          <VCol
            cols="6"
            lg="4"
          >
            <Skeleton
              width="5rem"
              height="1rem"
              class="mb-2"
            ></Skeleton>
            <Skeleton
              width="7rem"
              height="1rem"
            ></Skeleton>
          </VCol>
          <VCol
            cols="6"
            lg="4"
          >
            <Skeleton
              width="5rem"
              height="1rem"
              class="mb-2"
            ></Skeleton>
            <Skeleton
              width="7rem"
              height="1rem"
            ></Skeleton>
          </VCol>
          <VCol
            cols="6"
            lg="4"
          >
            <Skeleton
              width="5rem"
              height="1rem"
              class="mb-2"
            ></Skeleton>
            <Skeleton
              width="7rem"
              height="1rem"
            ></Skeleton>
          </VCol>
        </VRow>
      </VCardText>

      <VCardText v-else>
        <VRow>
          <VCol
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Order #
            </div>
            <div class="font-weight-medium text-body-2">
              {{ details.order_no }}
            </div>
          </VCol>

          <VCol
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Product
            </div>
            <div class="d-flex align-center py-1">
              <div>
                <h6 class="text-base">
                  {{ details.product_name }}
                </h6>
                <div
                  v-if="details.category_name?.toLowerCase() === 'wl'"
                  class="d-flex flex-column"
                >
                  <span class="d-block text-body-2">
                    {{ details.strength }} {{ details.strength_unit }} / weekly
                  </span>
                </div>
                <div
                  v-else
                  class="d-flex flex-column"
                >
                  <span class="d-block text-body-2">
                    {{ details.strength }} {{ details.strength_unit }} x {{ details.qty * details.subscription_interval }} units
                  </span>
                  <span class="d-block text-body-2">({{ details.subscription_interval * 30 }}-day supply)</span>
                </div>
              </div>
            </div>
          </VCol>

          <VCol
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              User
            </div>
            <div class="font-weight-medium text-body-2">
              <div
                v-if="!isEmpty(details.full_name)"
                class="text-body-1"
              >
                {{ details.full_name }}
              </div>
              <div v-if="!isEmpty(details.email)">
                {{ details.email }}
              </div>
              <div v-if="!isEmpty(details.phone_number)">
                {{ formattedPhoneNumber(details.phone_number) }}
              </div>
            </div>
          </VCol>

          <VCol
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Refund Amount
            </div>
            <div class="font-weight-medium text-body-2">
              {{ formatCurrency(details.refund_amount) }}
            </div>
          </VCol>

          <VCol
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Refund Status
            </div>
            <VChip
              label
              :color="details.refund_status_color_code"
              size="small"
              class="text-capitalize"
            >
              {{ details.refund_status_text }}
            </VChip>
          </VCol>

          <VCol
            v-if="!isEmpty(details.refund_transaction_id)"
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Transaction ID
            </div>
            <div class="font-weight-medium text-body-2">
              {{ details.refund_transaction_id }}
            </div>
          </VCol>

          <VCol
            v-if="!isEmpty(details.action_perform_by_username) || !isEmpty(details.action_perform_by_user_role)"
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Action By
            </div>
            <div class="font-weight-medium text-body-2">
              <span v-if="!isEmpty(details.action_perform_by_username) && !isEmpty(details.action_perform_by_user_role)">
                {{ details.action_perform_by_username }} <br>
                {{ details.action_perform_by_user_role }}
              </span>
              <span v-else-if="!isEmpty(details.action_perform_by_username) && isEmpty(details.action_perform_by_user_role)">
                {{ details.action_perform_by_username }}
              </span>
              <span v-else-if="isEmpty(details.action_perform_by_username) && !isEmpty(details.action_perform_by_user_role)">
                {{ details.action_perform_by_user_role }}
              </span>
            </div>
          </VCol>

          <VCol
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Created At
            </div>
            <div class="font-weight-medium text-body-2">
              {{ details.created_date_time }}
            </div>
          </VCol>

          <VCol
            v-if="details.action_perform_by_date"
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Action At
            </div>
            <div class="font-weight-medium text-body-2">
              {{ details.action_perform_by_date }}
            </div>
          </VCol>

          <VCol
            v-if="!isEmpty(details.cancel_reason)"
            cols="12"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Cancelation Reason
            </div>
            <div class="font-weight-medium text-body-2">
              {{ details.cancel_reason }}
            </div>
          </VCol>

          <VCol
            cols="12"
            class="pa-0"
          >
            <div
              v-if="[0, 3].includes(details.refund_status)"
              class="d-flex justify-center gap-3 mt-4"
            >
              <VBtn
                color="success"
                variant="tonal"
                @click="emit('approve', details.id)"
              >
                Approve Refund
              </VBtn>
              <VBtn
                color="error"
                variant="tonal"
                @click="emit('decline', details.id)"
              >
                Cancel Refund
              </VBtn>
            </div>
          </VCol>
        </VRow>
      </VCardText>
    </VCard>
  </VDialog>
</template>
