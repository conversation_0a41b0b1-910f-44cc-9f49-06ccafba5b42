<script setup>
import { requiredValidator } from '@/@core/utils/validators'
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { processErrors } from '@/utils/errorHandler'
import { PerfectScrollbar } from 'vue3-perfect-scrollbar'

const props = defineProps({
  isDrawerOpen: {
    type: Boolean,
    required: true,
  },
  refillId: {
    type: String,
    required: true,
  },
  formData: {
    type: Object,
    required: false,
    default: () => ({
      trackingNo: '',
      trackingUrl: '',
      deliveryProvider: '',
    }),
  },
})

const emit = defineEmits(['update:isDrawerOpen', 'updated'])

const globalStore = useGlobalData()
const { showSnackbar } = globalStore
const formRef = ref(null)
const isLoading = ref(false)
const serverErrors = ref([])
const selectedStatus = ref(null)
const shipDateTime = ref(null)
const trackingNo = ref(null)
const trackingUrl = ref(null)
const deliveryProvider = ref(null)

const showShipDateTime = computed(() => {
  return [7, 8, 9, 10].includes(selectedStatus.value)
})

const shipmentStatusList = ref([
  {
    title: 'New',
    value: 11,
  },
  {
    title: 'Processing',
    value: 12,
  },
  {
    title: 'Complete',
    value: 13,
  },
  {
    title: 'Preparing for Shipment',
    value: 7,
  },
  {
    title: 'In Transit',
    value: 8,
  },
  {
    title: 'Out for Delivery',
    value: 9,
  },
  {
    title: 'Delivered',
    value: 10,
  },
])

watch(props, () => {
  formRef.value?.reset()
  clearForm()
})

function clearForm() {
  selectedStatus.value = null
  shipDateTime.value = null
  trackingNo.value = props.formData.trackingNo || null
  trackingUrl.value = props.formData.trackingUrl || null
  deliveryProvider.value = props.formData.deliveryProvider || null
  serverErrors.value = []
}

const resetForm = () => {
  clearForm()
  emit('update:isDrawerOpen', false)
}

const onFormSubmit = async () => {
  formRef.value?.validate().then(async valid => {
    if (valid.valid) {
      await updateOrderShipmentStatus()
    }
  })
}

async function updateOrderShipmentStatus() {
  try {
    isLoading.value = true

    const formData = {
      refill_id: props.refillId,
      status: selectedStatus.value,
      ship_date_time: shipDateTime.value,
    }

    if (selectedStatus.value === 8) { // 8 - In-Transit
      formData['tracking_no'] = trackingNo.value
      formData['tracking_url'] = trackingUrl.value
      formData['delivery_provider'] = deliveryProvider.value
    }

    const { data } = await ApiService.post('/admin/update-pharmacy-order-status', formData)

    if (data.status === 200) {
      emit('update:isDrawerOpen', false)
      emit('updated', true)
      showSnackbar(data.message)
    } else {
      serverErrors.value = processErrors(data)
    }
  } catch (error) {
    serverErrors.value = processErrors(error)
    console.error(error)
  } finally {
    isLoading.value = false
  }
}

const handleDrawerModelValueUpdate = val => {
  emit('update:isDrawerOpen', val)
}
</script>

<template>
  <VNavigationDrawer
    :model-value="props.isDrawerOpen"
    temporary
    location="end"
    width="400"
    class="category-navigation-drawer scrollable-content"
    @update:model-value="handleDrawerModelValueUpdate"
  >
    <!-- 👉 Header -->
    <AppDrawerHeaderSection
      title="Edit Order Shipment Status"
      @cancel="$emit('update:isDrawerOpen', false)"
    />

    <VDivider />

    <PerfectScrollbar :options="{ wheelPropagation: false }">
      <VCard flat>
        <VCardText>
          <!-- 👉 Alert -->
          <VAlert
            v-if="serverErrors.length > 0"
            type="error"
            variant="tonal"
            title="Validation failed!"
            class="mb-6"
          >
            <ul class="mb-0">
              <li
                v-for="error in serverErrors"
                :key="error"
                class="mb-0"
              >
                {{ error }}
              </li>
            </ul>
          </VAlert>

          <!-- 👉 Form -->
          <VForm
            ref="formRef"
            @submit.prevent="onFormSubmit"
          >
            <VRow>
              <!-- 👉 Status -->
              <VCol cols="12">
                <AppSelect
                  v-model="selectedStatus"
                  label="Order Status"
                  :rules="[requiredValidator]"
                  :items="shipmentStatusList"
                  placeholder="Select Status"
                />
              </VCol>

              <!-- 👉 Tracking No -->
              <VCol
                v-if="selectedStatus === 8"
                cols="12"
              >
                <AppTextField
                  v-model="trackingNo"
                  label="Tracking No"
                  placeholder="Enter tracking no"
                />
              </VCol>

              <!-- 👉 Tracking Url -->
              <VCol
                v-if="selectedStatus === 8"
                cols="12"
              >
                <AppTextField
                  v-model="trackingUrl"
                  label="Tracking URL"
                  placeholder="Enter tracking url"
                />
              </VCol>

              <!-- 👉 Delivery Provider -->
              <VCol
                v-if="selectedStatus === 8"
                cols="12"
              >
                <AppTextField
                  v-model="deliveryProvider"
                  label="Delivery Provider"
                  placeholder="Enter delivery provider"
                />
              </VCol>

              <!-- 👉 Status -->
              <VCol
                v-if="showShipDateTime"
                cols="12"
              >
                <AppDateTimePicker
                  v-model="shipDateTime"
                  label="Shipment Date and Time"
                  placeholder="Select date and time"
                  :rules="[requiredValidator]"
                  :config="{
                    enableTime: true,
                    // enableSeconds: true,
                    time_24hr: true,
                    dateFormat: 'm/d/Y H:i',
                  }"
                />
              </VCol>

              <!-- 👉 Submit and Cancel button -->
              <VCol
                cols="12"
                class="text-center"
              >
                <VBtn
                  type="submit"
                  class="me-3"
                  :loading="isLoading"
                >
                  submit
                </VBtn>

                <VBtn
                  variant="tonal"
                  color="secondary"
                  :disabled="isLoading"
                  @click="resetForm"
                >
                  Cancel
                </VBtn>
              </VCol>
            </VRow>
          </VForm>
        </VCardText>
      </VCard>
    </PerfectScrollbar>
  </VNavigationDrawer>
</template>

<style lang="scss">
.category-navigation-drawer{
  .ProseMirror {
    padding: 0.5rem;
    min-block-size: 15vh;

    p {
      margin-block-end: 0;
    }

    p.is-editor-empty:first-child::before {
      block-size: 0;
      color: #adb5bd;
      content: attr(data-placeholder);
      float: inline-start;
      pointer-events: none;
    }
  }

  .is-active {
    border-color: rgba(var(--v-theme-primary), var(--v-border-opacity)) !important;
    background-color: rgba(var(--v-theme-primary), var(--v-activated-opacity));
    color: rgb(var(--v-theme-primary));
  }

  .ProseMirror-focused{
    outline: none !important;
  }
}
</style>
