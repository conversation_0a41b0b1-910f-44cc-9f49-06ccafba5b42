<script setup>
import { requiredValidator } from '@/@core/utils/validators'
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { processErrors } from '@/utils/errorHandler'
import { watch } from 'vue'
import { isEmptyObject, isNullOrUndefined } from '@/@core/utils'
import { removeKeyFromObject } from '@/utils/helpers'

const props = defineProps({
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
  subscriptionId: {
    type: String,
    required: true,
  },
})

const emit = defineEmits(['update:isDialogVisible', 'canceled'])

const { showSnackbar } = useGlobalData()

const formRef = ref(null)
const isLoading = ref(false)
const serverErrors = ref([])
const inputErrors = ref({})
const cancelReason = ref('')
const provideRefund = ref('0')

const resetForm = () => {
  emit('update:isDialogVisible', false)
}

watch(props, () => {
  cancelReason.value = ''
  provideRefund.value = '0'
})

const onFormSubmit = async () => {
  formRef.value?.validate().then(async valid => {
    if (valid.valid) {
      await cancel()
    }
  })
}

async function cancel() {
  try {
    isLoading.value = true

    const formData = {
      id: props.subscriptionId,
      cancel_reason: cancelReason.value,
    }

    const { data } = await ApiService.post('/admin/cancel-ed-subscription', formData)

    if (data.status === 200) {
      emit('update:isDialogVisible', false)
      emit('canceled', true)
      showSnackbar(data.message)
    } else {
      serverErrors.value = processErrors(data)
    }
  } catch (error) {
    serverErrors.value = processErrors(error)
    console.error(error)
  } finally {
    isLoading.value = false
  }
}

function removeKeyFromInputErrors(key) {
  inputErrors.value = removeKeyFromObject(inputErrors.value, key)
}

const dialogModelValueUpdate = val => {
  emit('update:isDialogVisible', val)
}
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 677"
    :model-value="props.isDialogVisible"
    @update:model-value="dialogModelValueUpdate"
  >
    <!-- Dialog close btn -->
    <DialogCloseBtn @click="dialogModelValueUpdate(false)" />

    <VCard flat>
      <VCardItem class="text-center">
        <VCardTitle class="text-h3 mb-3">
          Cancel Subscription
        </VCardTitle>
      </VCardItem>

      <VCardText>
        <!-- 👉 Alert -->
        <VAlert
          v-if="isEmptyObject(inputErrors) && serverErrors.length > 0"
          type="error"
          variant="tonal"
          title="Error!"
          class="mb-6"
        >
          <ul class="mb-0">
            <li
              v-for="error in serverErrors"
              :key="error"
              class="mb-0"
            >
              {{ error }}
            </li>
          </ul>
        </VAlert>

        <!-- 👉 Form -->
        <VForm
          ref="formRef"
          @submit.prevent="onFormSubmit"
        >
          <VRow>
            <VCol cols="12">
              <AppTextarea
                v-model="cancelReason"
                label="Cancelation reason"
                placeholder="Write here..."
                :rules="[requiredValidator]"
                rows="3"
                @keyup="removeKeyFromInputErrors('cancel_reason')"
              />
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['cancel_reason'])"
                class="text-sm text-error mb-0 ms-3"
              >
                {{ inputErrors['cancel_reason'][0] }}
              </p>
            </VCol>

            <!-- 👉 Submit and Cancel button -->
            <VCol
              cols="12"
              class="text-center"
            >
              <VBtn
                type="submit"
                class="me-3"
                :loading="isLoading"
              >
                submit
              </VBtn>

              <VBtn
                variant="tonal"
                color="secondary"
                :disabled="isLoading"
                @click="resetForm"
              >
                Cancel
              </VBtn>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>
  </VDialog>
</template>

<style lang="scss">
.category-navigation-drawer{
  .ProseMirror {
    padding: 0.5rem;
    min-block-size: 15vh;

    p {
      margin-block-end: 0;
    }

    p.is-editor-empty:first-child::before {
      block-size: 0;
      color: #adb5bd;
      content: attr(data-placeholder);
      float: inline-start;
      pointer-events: none;
    }
  }

  .is-active {
    border-color: rgba(var(--v-theme-primary), var(--v-border-opacity)) !important;
    background-color: rgba(var(--v-theme-primary), var(--v-activated-opacity));
    color: rgb(var(--v-theme-primary));
  }

  .ProseMirror-focused{
    outline: none !important;
  }
}
</style>
