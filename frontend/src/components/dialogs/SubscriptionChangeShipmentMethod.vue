<script setup>
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { processErrors } from '@/utils/errorHandler'
import { PerfectScrollbar } from 'vue3-perfect-scrollbar'

const props = defineProps({
  isDrawerOpen: {
    type: Boolean,
    required: true,
  },
  subscriptionId: {
    type: String,
    required: true,
  },
  shipmentMethods: {
    type: Array,
    required: true,
  },
  currentShipmentMethodId: {
    type: Number,
  },
})

const emit = defineEmits(['update:isDrawerOpen', 'updated'])

const { showSnackbar } = useGlobalData()
const selectedShipmentMethod = ref(null)
const isLoading = ref(false)

const handleDrawerModelValueUpdate = val => {
  emit('update:isDrawerOpen', val)
}

watch(props, () => {
  selectedShipmentMethod.value = props.currentShipmentMethodId
  selectedShipmentMethod.value = props.shipmentMethods.find(
    m => m.id === props.currentShipmentMethodId,
  )?.id
})

async function updateShipmentMethod() {
  try {
    isLoading.value = true

    const postData = {
      id: props.subscriptionId,
      pharmacy_shipment_method_id: selectedShipmentMethod.value,
    }

    const { data } = await ApiService.post('/admin/update-ed-subscription-pharmacy-shipment-method', postData)

    if (data.status === 200) {
      showSnackbar(data.message)
      emit('updated', true)
      emit('update:isDrawerOpen', false)
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    isLoading.value = false
  }
}
</script>

<template>
  <VNavigationDrawer
    :model-value="props.isDrawerOpen"
    temporary
    location="end"
    width="400"
    class="category-navigation-drawer scrollable-content"
    @update:model-value="handleDrawerModelValueUpdate"
  >
    <!-- 👉 Header -->
    <AppDrawerHeaderSection
      title="Change Shipment Method"
      @cancel="$emit('update:isDrawerOpen', false)"
    />

    <VDivider />

    <PerfectScrollbar :options="{ wheelPropagation: false }">
      <VCard flat>
        <VCardText>
          <VRadioGroup
            v-if="props.shipmentMethods"
            v-model="selectedShipmentMethod"
          >
            <VRow>
              <VCol
                v-for="item in props.shipmentMethods"
                :key="item.id"
                cols="12"
                class="py-2"
              >
                <VLabel :class="`custom-input custom-radio rounded cursor-pointer w-100 ${selectedShipmentMethod === item.id ? 'active' : ''}`">
                  <div>
                    <VRadio :value="item.id" />
                  </div>
                  <slot :item="item">
                    <div class="flex-grow-1">
                      <div class="d-flex align-center mb-0">
                        <h6 class="cr-title text-sm">
                          {{ item.title }}
                        </h6>
                        <VSpacer />
                        <span class="text-sm">
                          <span v-if="item.cost > 0">${{ item.cost }}</span>
                          <span v-else>FREE</span>
                        </span>
                      </div>
                      <p class="text-base mb-0">
                        {{ item.description }}
                      </p>
                    </div>
                  </slot>
                </VLabel>
              </VCol>
            </VRow>
          </VRadioGroup>

          <VBtn
            block
            class="mt-6"
            :loading="isLoading"
            @click="updateShipmentMethod"
          >
            Save
          </VBtn>
        </VCardText>
      </VCard>
    </PerfectScrollbar>
  </VNavigationDrawer>
</template>

<style lang="scss">
.category-navigation-drawer{
  .ProseMirror {
    padding: 0.5rem;
    min-block-size: 15vh;

    p {
      margin-block-end: 0;
    }

    p.is-editor-empty:first-child::before {
      block-size: 0;
      color: #adb5bd;
      content: attr(data-placeholder);
      float: inline-start;
      pointer-events: none;
    }
  }

  .is-active {
    border-color: rgba(var(--v-theme-primary), var(--v-border-opacity)) !important;
    background-color: rgba(var(--v-theme-primary), var(--v-activated-opacity));
    color: rgb(var(--v-theme-primary));
  }

  .ProseMirror-focused{
    outline: none !important;
  }
}
</style>
