<script setup>
import { requiredValidator } from '@/@core/utils/validators'
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { processErrors } from '@/utils/errorHandler'
import { PerfectScrollbar } from 'vue3-perfect-scrollbar'

const props = defineProps({
  isDrawerOpen: {
    type: Boolean,
    required: true,
  },
  refillId: {
    type: String,
    required: true,
  },
})

const emit = defineEmits(['update:isDrawerOpen', 'updated'])

const globalStore = useGlobalData()
const { showSnackbar } = globalStore

const formRef = ref(null)
const isLoading = ref(false)
const serverErrors = ref([])
const newAffiliateOrderNo = ref(null)

watch(props, () => {
  formRef.value?.reset()
  newAffiliateOrderNo.value = null
  serverErrors.value = []
})

const resetForm = () => {
  newAffiliateOrderNo.value = null
  emit('update:isDrawerOpen', false)
}

const onFormSubmit = async () => {
  formRef.value?.validate().then(async valid => {
    if (valid.valid) {
      await updateOrderShipmentStatus()
    }
  })
}

async function updateOrderShipmentStatus() {
  try {
    isLoading.value = true

    const { data } = await ApiService.post(
      `/admin/update-hallandale-order-id/${props.refillId}`,
      {
        affiliate_order_no: newAffiliateOrderNo.value,
      },
    )

    if (data.status === 200) {
      emit('update:isDrawerOpen', false)
      emit('updated', true)
      showSnackbar(data.message)
    } else {
      serverErrors.value = processErrors(data)
    }
  } catch (error) {
    serverErrors.value = processErrors(error)
    console.error(error)
  } finally {
    isLoading.value = false
  }
}

const handleDrawerModelValueUpdate = val => {
  emit('update:isDrawerOpen', val)
}
</script>

<template>
  <VNavigationDrawer
    :model-value="props.isDrawerOpen"
    temporary
    location="end"
    width="400"
    class="category-navigation-drawer scrollable-content"
    @update:model-value="handleDrawerModelValueUpdate"
  >
    <!-- 👉 Header -->
    <AppDrawerHeaderSection
      title="Update Hallandale Order ID"
      @cancel="$emit('update:isDrawerOpen', false)"
    />

    <VDivider />

    <PerfectScrollbar :options="{ wheelPropagation: false }">
      <VCard flat>
        <VCardText>
          <!-- 👉 Alert -->
          <VAlert
            v-if="serverErrors.length > 0"
            type="error"
            variant="tonal"
            title="Validation failed!"
            class="mb-6"
          >
            <ul class="mb-0">
              <li
                v-for="error in serverErrors"
                :key="error"
                class="mb-0"
              >
                {{ error }}
              </li>
            </ul>
          </VAlert>

          <!-- 👉 Form -->
          <VForm
            ref="formRef"
            @submit.prevent="onFormSubmit"
          >
            <VRow>
              <!-- 👉 Status -->
              <VCol cols="12">
                <AppTextField
                  v-model="newAffiliateOrderNo"
                  label="Hallandale Order ID"
                  :rules="[requiredValidator]"
                  hint="Enter Hallandale Order ID"
                  persistent-hint
                />
              </VCol>

              <!-- 👉 Submit and Cancel button -->
              <VCol
                cols="12"
                class="text-center"
              >
                <VBtn
                  type="submit"
                  class="me-3"
                  :loading="isLoading"
                >
                  submit
                </VBtn>

                <VBtn
                  variant="tonal"
                  color="secondary"
                  :disabled="isLoading"
                  @click="resetForm"
                >
                  Cancel
                </VBtn>
              </VCol>
            </VRow>
          </VForm>
        </VCardText>
      </VCard>
    </PerfectScrollbar>
  </VNavigationDrawer>
</template>
