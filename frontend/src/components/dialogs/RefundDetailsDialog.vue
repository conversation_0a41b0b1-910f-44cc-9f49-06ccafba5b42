<script setup>
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { processErrors } from '@/utils/errorHandler'
import { onMounted, watch } from 'vue'
import { isEmpty } from '@/@core/utils'
import { formattedPhoneNumber } from '@/utils/helpers'
import { resolveRefundStatusColor } from '@/utils/admin'

const props = defineProps({
  refundHistoryId: {
    type: String,
    required: false,
  },
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits([
  'update:isDialogVisible',
  'updated',
])

const globalStore = useGlobalData()
const { showSnackbar } = globalStore

const isLoading = ref(true)
const details = ref([])

watch(props, () => {
  if (props.isDialogVisible && !isEmpty(props.refundHistoryId)) {
    fetchDetails()
  }
})

const fetchDetails = async () => {
  try {
    isLoading.value = true

    const { data } = await ApiService.get(`/admin/refund-details/${props.refundHistoryId}`)

    if (data.status === 200) {
      details.value = data.refundDetails
    } else {
      showSnackbar(data.message, 'error')
      emit('update:isDialogVisible', false)
    }
  } catch (error) {
    console.error(error)
    showSnackbar(processErrors(error)[0], 'error')
    emit('update:isDialogVisible', false)
  } finally {
    isLoading.value = false
  }
}
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 610 "
    :model-value="props.isDialogVisible"
    @update:model-value="val => $emit('update:isDialogVisible', val)"
  >
    <!-- 👉 Dialog close btn -->
    <DialogCloseBtn @click="$emit('update:isDialogVisible', false)" />

    <VCard class="pa-sm-8 pa-5">
      <!-- 👉 Title -->

      <VCardItem>
        <VCardTitle class="text-h4 text-center">
          Refund Details
        </VCardTitle>
      </VCardItem>

      <VCardText v-if="isLoading">
        <VRow class="mb-6">
          <VCol
            cols="6"
            lg="4"
          >
            <Skeleton
              width="5rem"
              height="1rem"
              class="mb-2"
            ></Skeleton>
            <Skeleton
              width="7rem"
              height="1rem"
            ></Skeleton>
          </VCol>
          <VCol
            cols="6"
            lg="4"
          >
            <Skeleton
              width="5rem"
              height="1rem"
              class="mb-2"
            ></Skeleton>
            <Skeleton
              width="7rem"
              height="1rem"
            ></Skeleton>
          </VCol>
          <VCol
            cols="6"
            lg="4"
          >
            <Skeleton
              width="5rem"
              height="1rem"
              class="mb-2"
            ></Skeleton>
            <Skeleton
              width="7rem"
              height="1rem"
            ></Skeleton>
          </VCol>
          <VCol
            cols="6"
            lg="4"
          >
            <Skeleton
              width="5rem"
              height="1rem"
              class="mb-2"
            ></Skeleton>
            <Skeleton
              width="7rem"
              height="1rem"
            ></Skeleton>
          </VCol>
          <VCol
            cols="6"
            lg="4"
          >
            <Skeleton
              width="5rem"
              height="1rem"
              class="mb-2"
            ></Skeleton>
            <Skeleton
              width="7rem"
              height="1rem"
            ></Skeleton>
          </VCol>
          <VCol
            cols="6"
            lg="4"
          >
            <Skeleton
              width="5rem"
              height="1rem"
              class="mb-2"
            ></Skeleton>
            <Skeleton
              width="7rem"
              height="1rem"
            ></Skeleton>
          </VCol>
        </VRow>
      </VCardText>

      <VCardText v-else>
        <VRow class="pb-6">
          <VCol
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Order Type
            </div>
            <div class="font-weight-medium text-body-2">
              {{ details.refill_number === 0 ? details.refill_type : `${details.refill_type} ${details.refill_number}` }}
            </div>
          </VCol>

          <VCol
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Refund Amount
            </div>
            <div class="font-weight-medium text-body-2">
              {{ formatCurrency(details.refund_amount) }}
            </div>
          </VCol>

          <VCol
            v-if="!isEmpty(details.refund_transaction_id)"
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Transaction ID
            </div>
            <div class="font-weight-medium text-body-2">
              {{ details.refund_transaction_id }}
            </div>
          </VCol>

          <VCol
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Refund Status
            </div>
            <VChip
              :color="resolveRefundStatusColor(details.refund_status)"
              size="small"
              label
            >
              {{ details.refund_status_text }}
            </VChip>
          </VCol>

          <VCol
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Action By
            </div>
            <div class="font-weight-medium text-body-2">
              <span v-if="!isEmpty(details.action_by_user_name) && !isEmpty(details.action_by_user_role)">
                {{ details.action_by_user_name }} <br>
                {{ details.action_by_user_role }}
              </span>
              <span v-else-if="!isEmpty(details.action_by_user_name) && isEmpty(details.action_by_user_role)">
                {{ details.action_by_user_name }}
              </span>
              <span v-else-if="isEmpty(details.action_by_user_name) && !isEmpty(details.action_by_user_role)">
                {{ details.action_by_user_role }}
              </span>
            </div>
          </VCol>

          <VCol
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Created At
            </div>
            <div class="font-weight-medium text-body-2">
              {{ details.created_date }} <br />
              {{ details.created_time }}
            </div>
          </VCol>

          <VCol
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Updated At
            </div>
            <div class="font-weight-medium text-body-2">
              {{ details.updated_date }}<br />
              {{ details.updated_time }}
            </div>
          </VCol>

          <VCol
            v-if="!isEmpty(details.cancel_reason)"
            cols="12"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Cancelation Reason
            </div>
            <div class="font-weight-medium text-body-2">
              {{ details.cancel_reason }}
            </div>
          </VCol>
        </VRow>
      </VCardText>
    </VCard>
  </VDialog>
</template>
