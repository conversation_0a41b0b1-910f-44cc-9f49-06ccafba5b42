<script setup>
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { processErrors } from '@/utils/errorHandler'
import { ref, watch } from 'vue'
import { isEmpty } from '@/@core/utils'
import 'prismjs'
import 'prismjs/themes/prism.css'
import Prism from 'vue-prism-component'

const props = defineProps({
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
  eventId: {
    type: String,
    required: false,
  },
})

const emit = defineEmits([
  'update:isDialogVisible',
  'updated',
])

const globalStore = useGlobalData()
const { showSnackbar } = globalStore

const isLoading = ref(true)
const details = ref({})

watch(props, () => {
  if (props.isDialogVisible && !isEmpty(props.eventId)) {
    fetchDetails()
  }
})

const fetchDetails = async () => {
  try {
    isLoading.value = true

    const { data } = await ApiService.get(`/admin/view-hallandale-event-logs/${props.eventId}`)

    if (data.status === 200) {
      details.value = data.hallandaleEventLog
    } else {
      showSnackbar(data.message, 'error')
      emit('update:isDialogVisible', false)
    }
  } catch (error) {
    console.error(error)
    showSnackbar(processErrors(error)[0], 'error')
    emit('update:isDialogVisible', false)
  } finally {
    isLoading.value = false
  }
}
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 768 "
    :model-value="props.isDialogVisible"
    @update:model-value="val => $emit('update:isDialogVisible', val)"
  >
    <!-- 👉 Dialog close btn -->
    <DialogCloseBtn @click="$emit('update:isDialogVisible', false)" />

    <VCard class="pa-sm-8 pa-5">
      <!-- 👉 Title -->
      <VCardItem>
        <VCardTitle class="text-h4 text-center">
          Event Details
        </VCardTitle>
      </VCardItem>

      <VCardText v-if="isLoading">
        <div
          class="d-flex justify-center align-center"
          style="height: 5rem"
        >
          <VProgressCircular
            indeterminate
            size="48"
          />
        </div>
      </VCardText>

      <VCardText v-else>
        <prism
          v-if="details"
          language="javascript"
          class="rounded-lg"
        >
          {{ details }}
        </prism>
        <div
          v-else
          class="text-center"
        >
          <span class="text-disabled">No Data Available</span>
        </div>
      </VCardText>
    </VCard>
  </VDialog>
</template>
