<script setup>
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { processErrors } from '@/utils/errorHandler'
import { watch } from 'vue'
import { isEmpty } from '@/@core/utils'
import { formattedPhoneNumber, formatCurrency, resolveInitials } from '@/utils/helpers'

const props = defineProps({
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
  payoutRequestId: {
    type: String,
    required: false,
  },
})

const emit = defineEmits([
  'update:isDialogVisible',
  'updated',
  'approve',
  'decline',
])

const globalStore = useGlobalData()
const { showSnackbar } = globalStore

const isLoading = ref(true)
const details = ref([])

watch(props, () => {
  if (props.isDialogVisible && !isEmpty(props.payoutRequestId)) {
    fetchDetails()
  }
})

const fetchDetails = async () => {
  try {
    isLoading.value = true

    const { data } = await ApiService.get(`/admin/view-payout-request/${props.payoutRequestId}`)

    if (data.status === 200) {
      details.value = data.payoutDetail
    } else {
      showSnackbar(data.message, 'error')
      emit('update:isDialogVisible', false)
    }
  } catch (error) {
    console.error(error)
    showSnackbar(processErrors(error)[0], 'error')
    emit('update:isDialogVisible', false)
  } finally {
    isLoading.value = false
  }
}

function resolveStatus(status) {
  if (status === 0) {
    return { label: 'Pending', color: 'warning' }
  } else if (status === 1) {
    return { label: 'Approved', color: 'success' }
  } else if (status === 2) {
    return { label: 'Rejected', color: 'error' }
  }
}
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 610 "
    :model-value="props.isDialogVisible"
    @update:model-value="val => $emit('update:isDialogVisible', val)"
  >
    <!-- 👉 Dialog close btn -->
    <DialogCloseBtn @click="$emit('update:isDialogVisible', false)" />

    <VCard class="pa-sm-8 pa-5">
      <!-- 👉 Title -->

      <VCardItem>
        <VCardTitle class="text-h4 text-center">
          Payout Request Details
        </VCardTitle>
      </VCardItem>

      <VCardText v-if="isLoading">
        <VRow class="mb-6">
          <VCol
            cols="6"
            lg="4"
          >
            <Skeleton
              width="5rem"
              height="1rem"
              class="mb-2"
            ></Skeleton>
            <Skeleton
              width="7rem"
              height="1rem"
            ></Skeleton>
          </VCol>
          <VCol
            cols="6"
            lg="4"
          >
            <Skeleton
              width="5rem"
              height="1rem"
              class="mb-2"
            ></Skeleton>
            <Skeleton
              width="7rem"
              height="1rem"
            ></Skeleton>
          </VCol>
          <VCol
            cols="6"
            lg="4"
          >
            <Skeleton
              width="5rem"
              height="1rem"
              class="mb-2"
            ></Skeleton>
            <Skeleton
              width="7rem"
              height="1rem"
            ></Skeleton>
          </VCol>
          <VCol
            cols="6"
            lg="4"
          >
            <Skeleton
              width="5rem"
              height="1rem"
              class="mb-2"
            ></Skeleton>
            <Skeleton
              width="7rem"
              height="1rem"
            ></Skeleton>
          </VCol>
          <VCol
            cols="6"
            lg="4"
          >
            <Skeleton
              width="5rem"
              height="1rem"
              class="mb-2"
            ></Skeleton>
            <Skeleton
              width="7rem"
              height="1rem"
            ></Skeleton>
          </VCol>
          <VCol
            cols="6"
            lg="4"
          >
            <Skeleton
              width="5rem"
              height="1rem"
              class="mb-2"
            ></Skeleton>
            <Skeleton
              width="7rem"
              height="1rem"
            ></Skeleton>
          </VCol>
        </VRow>
      </VCardText>

      <VCardText v-else>
        <VRow>
          <VCol cols="6">
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Affiliate User
            </div>
            <div class="font-weight-medium text-body-2">
              <div class="d-flex align-center py-1">
                <VAvatar
                  v-if="details.user?.profile_picture"
                  variant="tonal"
                  size="40"
                  class="me-3"
                  :image="details.user?.profile_picture"
                  rounded
                />
                <VAvatar
                  v-else
                  variant="tonal"
                  size="40"
                  class="me-3"
                  rounded
                >
                  {{ resolveInitials(details.user?.first_name + ' ' + details.user?.last_name) }}
                </VAvatar>
                <div>
                  <h6 class="text-base">
                    {{ details.user?.first_name + ' ' + details.user?.last_name }}
                  </h6>
                  <div class="d-flex flex-column">
                    <span class="text-body-2">{{ details.user?.email }}</span>
                    <span class="text-body-2">{{ formattedPhoneNumber(details.user?.phone_number) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </VCol>

          <VCol
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Amount
            </div>
            <div class="font-weight-medium text-body-2">
              {{ formatCurrency(details.amount) }}
            </div>
          </VCol>

          <VCol
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Status
            </div>
            <VChip
              label
              :color="resolveStatus(details.status).color"
              size="small"
              class="text-capitalize"
            >
              {{ resolveStatus(details.status).label }}
            </VChip>
          </VCol>

          <VCol
            v-if="!isEmpty(details.reference_id)"
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Reference ID
            </div>
            <div class="font-weight-medium text-body-2">
              {{ details.reference_id }}
            </div>
          </VCol>

          <VCol
            v-if="!isEmpty(details.payout_method)"
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Payout Method
            </div>
            <div class="font-weight-medium text-body-2">
              {{ details.payout_method }}
            </div>
          </VCol>

          <VCol
            v-if="!isEmpty(details.paypal_email)"
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              PayPal Email Address
            </div>
            <div class="font-weight-medium text-body-2">
              {{ details.paypal_email }}
            </div>
          </VCol>

          <!--
            <VCol
            v-if="!isEmpty(details.beneficiary_name)"
            cols="6"
            lg="4"
            >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
            Account Holder Name
            </div>
            <div class="font-weight-medium text-body-2">
            {{ details.beneficiary_name }}
            </div>
            </VCol>
          -->

          <VCol
            v-if="!isEmpty(details.bank_name)"
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Bank Name
            </div>
            <div class="font-weight-medium text-body-2">
              {{ details.bank_name }}
            </div>
          </VCol>

          <VCol
            v-if="!isEmpty(details.beneficiary_account_no)"
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Account No
            </div>
            <div class="font-weight-medium text-body-2">
              {{ details.beneficiary_account_no }}
            </div>
          </VCol>

          <VCol
            v-if="!isEmpty(details.beneficiary_routing_no)"
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Routing No
            </div>
            <div class="font-weight-medium text-body-2">
              {{ details.beneficiary_routing_no }}
            </div>
          </VCol>

          <VCol
            v-if="details.ip_address || details.location"
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Location
            </div>
            <div class="font-weight-medium text-body-2">
              {{ details.location }} <br>
              <span class="font-italic">IP: {{ details.ip_address }}</span>
            </div>
          </VCol>

          <VCol
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Created At
            </div>
            <div class="font-weight-medium text-body-2">
              {{ details.created_date }}
            </div>
          </VCol>

          <VCol
            v-if="!isEmpty(details.action_by_user_name) || !isEmpty(details.action_by_user_role)"
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Action By
            </div>
            <div class="font-weight-medium text-body-2">
              <span v-if="!isEmpty(details.action_by_user_name) && !isEmpty(details.action_by_user_role)">
                {{ details.action_by_user_name }} <br>
                {{ details.action_by_user_role }}
              </span>
              <span v-else-if="!isEmpty(details.action_by_user_name) && isEmpty(details.action_by_user_role)">
                {{ details.action_by_user_name }}
              </span>
              <span v-else-if="isEmpty(details.action_by_user_name) && !isEmpty(details.action_by_user_role)">
                {{ details.action_by_user_role }}
              </span>
            </div>
          </VCol>

          <VCol
            v-if="details.action_perform_at"
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Action At
            </div>
            <div class="font-weight-medium text-body-2">
              {{ details.action_perform_at }}
            </div>
          </VCol>

          <VCol
            v-if="!isEmpty(details.cancel_reason)"
            cols="12"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Reason for Rejection
            </div>
            <div class="font-weight-medium text-body-2">
              {{ details.cancel_reason }}
            </div>
          </VCol>

          <VCol
            cols="12"
            class="pa-0"
          >
            <div
              v-if="details.status === 0"
              class="d-flex justify-center gap-3 mt-4"
            >
              <VBtn
                color="success"
                variant="tonal"
                @click="emit('approve', details.id)"
              >
                Approve Payout
              </VBtn>
              <VBtn
                color="error"
                variant="tonal"
                @click="emit('decline', details.id)"
              >
                Reject Payout
              </VBtn>
            </div>
          </VCol>
        </VRow>
      </VCardText>
    </VCard>
  </VDialog>
</template>
