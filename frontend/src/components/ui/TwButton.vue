<script setup>
import { computed, ref, watch } from 'vue'
import { IconLoader2 } from '@tabler/icons-vue'

const props = defineProps({
  type: {
    type: String,
    default: 'button',
  },
  loading: {
    type: Boolean,
    default: false,
  },
  loadingText: {
    type: String,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  variant: {
    type: String,
    default: 'primary',
  },
})

const emit = defineEmits(['click'])

const isLoadingProp = Boolean(props.loading)
const isDisabledProp = Boolean(props.disabled)

const isLoading = ref(isLoadingProp)
const isDisabled = ref(isDisabledProp)

const btnClasses = computed(() => {
  if (props.variant === 'success') {
    return 'inline-flex items-center justify-center text-white bg-green-500 hover:bg-green-600 focus:outline-none focus:ring-1 focus:ring-green-700 font-medium rounded-full text-sm px-5 py-3 text-center disabled:bg-gray-500 disabled:cursor-not-allowed transition-all shadow-sm'
  } else if (props.variant === 'secondary') {
    return 'inline-flex items-center justify-center text-black border !border-black hover:bg-gray-100 focus:outline-none focus:ring-1 focus:ring-zinc-600 font-medium rounded-full text-sm px-5 py-3 text-center disabled:bg-gray-200 disabled:text-gray-600 disabled:!border-gray-400 disabled:cursor-not-allowed transition-all'
  } else if (props.variant === 'accent') {
    return 'inline-flex items-center justify-center bg-[#ffef08] text-black hover:bg-[#e6d807] focus:outline-none focus:ring-1 focus:ring-[#d6c907] font-medium rounded-full text-sm px-5 py-3 text-center disabled:bg-[#fff7a3] disabled:text-[#a39e07] disabled:cursor-not-allowed transition-all shadow-sm'
  } else {
    return 'inline-flex items-center justify-center text-white bg-black hover:bg-zinc-800 focus:outline-none focus:ring-1 focus:ring-zinc-600 font-medium rounded-full text-sm px-5 py-3 text-center disabled:bg-zinc-600 disabled:cursor-not-allowed transition-all shadow-sm'
  }
})

watch(props, () => {
  isLoading.value = Boolean(props.loading)
  isDisabled.value = Boolean(props.disabled)
})

const handleClick = () => {
  if (!isLoading.value) {
    emit('click')
  }
}
</script>

<template>
  <button
    :type="type"
    :class="btnClasses"
    :disabled="isLoading || isDisabled"
    @click="handleClick"
  >
    <div
      v-if="isLoading"
      class="inline-flex items-center justify-center"
    >
      <IconLoader2
        class="w-5 h-5 text-gray-200 animate-spin"
        stroke-width="2"
      />
      <span
        v-if="loadingText"
        class="ms-2"
      >{{ loadingText }}</span>
    </div>
    <div
      v-else
      class="inline-flex items-center justify-center"
    >
      <slot></slot>
    </div>
  </button>
</template>

