<script setup>
import logoWhite from '@/assets/logo/logo-light.svg'
import logoDark from '@/assets/logo/logo-dark.svg'

const props = defineProps({
  variant: {
    type: String, // 'light' or 'dark'
    default: 'black',
  },
})
</script>

<template>
  <img
    :src="
      variant === 'white'
        ? logoWhite
        : logoDark
    "
    class="h-12 w-auto"
    alt="White Label Rx Logo"
  >
</template>
