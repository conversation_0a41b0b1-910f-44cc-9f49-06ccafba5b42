<template>
  <div
    v-if="globalData.loadingProgress"
    class="loader-container"
  >
    <div class="loader-progress-bar">
      <div class="loader-progress-bar-value"></div>
    </div>
  </div>
</template>

<script setup>
import { useGlobalData } from '@/store/global'

const globalData = useGlobalData()
</script>

<style scoped>
.loader-container {
  position: relative;
  top: 0%;
  z-index: 99999;
}
.loader-progress-bar {
  height: 2px;
  background-color: rgba(21, 101, 255, 0.25);
  width: 100%;
  overflow: hidden;
  position: fixed;
}

.loader-progress-bar-value {
  width: 100%;
  height: 100%;
  background-color: #1565FF;
  animation: indeterminateAnimation 1s infinite linear;
  transform-origin: 0 50%;
}

@keyframes indeterminateAnimation {
  0% {
    transform: translateX(0) scaleX(0);
  }
  40% {
    transform: translateX(0) scaleX(0.4);
  }
  100% {
    transform: translateX(100%) scaleX(0.5);
  }
}
</style>
