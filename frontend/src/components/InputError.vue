<script setup>
import { isEmptyObject, isNullOrUndefined } from '@/@core/utils'
import { watch } from 'vue'

const props = defineProps({
  errors: {
    type: Object,
    required: true,
  },
  name: {
    type: String,
    required: true,
  },
})

watch(props, () => {
  console.log(props.errors)
})
</script>

<template>
  <p
    v-if="!isEmptyObject(errors) && !isNullOrUndefined(errors[name])"
    class="text-sm text-error mb-0"
  >
    {{ errors[name][0] }}
  </p>
</template>
