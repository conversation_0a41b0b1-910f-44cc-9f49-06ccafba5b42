<template>
  <div
    v-if="globalData.circularProgress"
    class="blockui-custom__wrapper"
  >
    <div class="blockui-custom__spinner"></div>
  </div>
</template>

<script setup>
import { useGlobalData } from '@/store/global'

const globalData = useGlobalData()
</script>

<style scoped>
.blockui-custom__wrapper {
  z-index: 999999999;
  transition: all 0.3s ease;
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.32);
}
.blockui-custom__spinner {
  width: 50px;
  aspect-ratio: 1;
  border-radius: 50%;
  background: radial-gradient(farthest-side, #7367f0 94%, #0000) top/8px 8px
      no-repeat,
    conic-gradient(#0000 30%, #7367f0);
  -webkit-mask: radial-gradient(farthest-side, #0000 calc(100% - 8px), #000 0);
  animation: s3 1s infinite linear;
}

@keyframes s3 {
  100% {
    transform: rotate(1turn);
  }
}
</style>
