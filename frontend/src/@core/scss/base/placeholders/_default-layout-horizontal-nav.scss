@use "@configured-variables" as variables;
@use "misc";
@use "@core/scss/base/mixins";

%default-layout-horizontal-nav-navbar-and-nav-container {
  @include mixins.elevation(3);

  // ℹ️ 1000 is v-window z-index
  z-index: 1001;
  background-color: rgb(var(--v-theme-surface));

  &.header-blur {
    @extend %blurry-bg;
  }
}

%default-layout-horizontal-nav-navbar {
  border-block-end: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
}

%default-layout-horizontal-nav-nav {
  padding-block: variables.$horizontal-nav-padding;
}

%default-layout-horizontal-nav-nav-items-list {
  gap: variables.$horizontal-nav-top-level-items-gap;
}
