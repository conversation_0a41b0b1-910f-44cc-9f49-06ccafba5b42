@forward "@core/scss/base/variables" with (
  $default-layout-with-vertical-nav-navbar-footer-roundness: 6px !default,

  $vertical-nav-navbar-style: "floating" !default, // options: elevated, floating

  // 👉 Vertical nav
  $vertical-nav-background-color-rgb: var(--v-theme-surface) !default,
  // ℹ️ This is used to keep consistency between nav items and nav header left & right margin
  // This is used by nav items & nav header
  $vertical-nav-horizontal-spacing: 0.875rem !default,

  // Section title margin top (when its not first child)
  $vertical-nav-section-title-mt: 1.25rem !default,
  $vertical-nav-navbar-elevation: 2 !default,

  // Move logo when vertical nav is mini (collapsed but not hovered)
  $vertical-nav-header-logo-translate-x-when-vertical-nav-mini: -1px !default,

  // Section title margin bottom
  $vertical-nav-section-title-mb: 0.375rem !default,

  // Vertical nav icons
  $vertical-nav-items-icon-size: 1.375rem !default,
  $vertical-nav-items-nested-icon-size: 0.425rem !default,

  //  👉Footer
  $layout-vertical-nav-footer-height: 48px !default,

  // Font sizes
  $font-sizes: (
    "xs": 0.6875rem,
    "sm": 0.8125rem,
    "base": 0.9375rem,
    "lg": 1.125rem,
    "xl": 1.375rem,
    "2xl": 1.625rem,
    "3xl": 2rem,
    "4xl": 2.375rem,
    "5xl": 3rem,
    "6xl": 3.5rem,
    "7xl": 4rem,
    "8xl": 4.5rem,
    "9xl": 5.25rem,
  ) !default,

  // Line heights
  $font-line-height: (
    "xs": 0.9375rem,
    "sm": 1.25rem,
    "base": 1.3125rem,
    "lg": 1.5rem,
    "xl": 1.875rem,
    "2xl": 2.25rem,
    "3xl": 2.75rem,
    "4xl": 3.25rem,
    "5xl": 1,
    "6xl": 1,
    "7xl": 1,
    "8xl": 1,
    "9xl": 1
  ) !default,
);
