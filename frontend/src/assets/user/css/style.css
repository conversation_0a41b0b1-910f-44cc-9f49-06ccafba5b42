@import "../fonts/konnect/stylesheet.css";

@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: "Konnect", system-ui, -apple-system, BlinkMacSystemFont,
    "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue",
    sans-serif;
  background: white !important;
  color: black !important;
}

.p-tooltip {
  font-family: "Konnect", system-ui, -apple-system, BlinkMacSystemFont,
    "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue",
    sans-serif;
  padding-left: 4px !important;
  padding-right: 4px !important;
}

.p-tooltip .p-tooltip-arrow {
  /* border-top-color: black !important; */
}
.p-tooltip .p-tooltip-text {
  font-size: 14px !important;
  background: black !important;
  color: white !important;
  /* width: max-content; */
  /* max-width: 320px; */
}

.custom-prose ul li {
  @apply list-disc ms-3;
}

.custom-prose ol li {
  @apply list-decimal ms-3;
}

.btn-primary {
  @apply inline-flex items-center justify-center text-white bg-black hover:bg-zinc-800 focus:outline-none focus:ring-1 focus:ring-zinc-600 font-medium rounded-full text-sm px-5 py-3 text-center disabled:bg-zinc-600 disabled:cursor-not-allowed;
}

.btn-secondary {
  @apply inline-flex items-center justify-center text-black border !border-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-1 focus:ring-zinc-600 font-medium rounded-full text-sm px-5 py-3 text-center disabled:bg-gray-200 disabled:text-gray-600 disabled:!border-gray-400 disabled:cursor-not-allowed;
}

@layer utilities {
  /* Hide scrollbar for Chrome, Safari and Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
  /* Hide scrollbar for IE, Edge and Firefox */
  .no-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }
}

/* start: Questions page slide animation */
.slide-left-enter-active,
.slide-right-enter-active {
  transition: all 0.3s ease-out;
}

.slide-left-leave-active,
.slide-right-leave-active {
  transition: all 0.3s cubic-bezier(1, 0.5, 0.8, 1);
}

.slide-left-enter-from {
  transform: translateX(50px);
  opacity: 0;
}

.slide-left-leave-to {
  transform: translateX(-50px);
  opacity: 0;
}

.slide-right-enter-from {
  transform: translateX(-50px);
  opacity: 0;
}

.slide-right-leave-to {
  transform: translateX(50px);
  opacity: 0;
}
/* end: Questions page slide animation */
