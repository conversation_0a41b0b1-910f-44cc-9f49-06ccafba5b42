export default [
  {
    title: 'Dashboard',
    to: { name: 'admin-dashboard' },
    icon: { icon: 'tabler-smart-home' },
  },
  {
    title: 'Users',
    icon: { icon: 'tabler-users' },
    children: [
      {
        title: 'Registered',
        to: 'admin-users',
      },
      {
        title: 'Interested',
        to: 'admin-interested-users',
      },
    ],
  },
  {
    title: 'Sales',
    icon: { icon: 'tabler-chart-line' },
    children: [
      {
        title: 'Subscriptions',
        to: 'admin-subscriptions',
      },
      {
        title: 'Orders',
        to: 'admin-orders',
      },
    ],
  },
  {
    title: 'ED',
    icon: { icon: 'tabler-cactus' },
    children: [
      {
        title: 'Products',
        to: 'admin-ed-products',
      },
      {
        title: 'Product-State Mapping',
        to: 'admin-ed-product-state-mapping',
      },
      {
        title: 'Recommendations',
        to: 'admin-ed-recommendations',
      },
      {
        title: 'Subscriptions',
        to: 'admin-ed-subscriptions',
      },
      {
        title: 'Orders',
        to: 'admin-ed-orders',
      },
    ],
  },
  {
    title: 'Hair Treatment',
    icon: { icon: 'tabler-growth' },
    children: [
      {
        title: 'Products',
        to: 'admin-hl-products',
      },
      {
        title: 'Product-State Mapping',
        to: 'admin-hl-product-state-mapping',
      },
      {
        title: 'Recommendations',
        to: 'admin-hl-recommendations',
      },
      {
        title: 'Subscriptions',
        to: 'admin-hl-subscriptions',
      },
      {
        title: 'Orders',
        to: 'admin-hl-orders',
      },
    ],
  },
  {
    title: 'Weight Loss',
    icon: { icon: 'tabler-scale-outline' },
    children: [
      {
        title: 'Products',
        to: 'admin-wl-products',
      },
      {
        title: 'Product-State Mapping',
        to: 'admin-wl-product-state-mapping',
      },

      // {
      //   title: 'State Recommendations',
      //   to: 'admin-wl-recommendations',
      // },
      {
        title: 'Subscriptions',
        to: 'admin-wl-subscriptions',
      },
      {
        title: 'Orders',
        to: 'admin-wl-orders',
      },
    ],
  },
  {
    title: 'OTC',
    icon: { icon: 'tabler-pills' },
    children: [
      {
        title: 'Product Category',
        to: 'admin-otc-category',
      },
      {
        title: 'Products',
        to: 'admin-otc-products',
      },
      {
        title: 'Orders',
        to: 'admin-otc-orders',
      },
    ],
  },
  {
    title: 'Order Logs',
    icon: { icon: 'tabler-logs' },
    children: [
      {
        title: 'Visit Logs',
        to: { name: 'admin-visit-logs' },
      },
      {
        title: 'Pharmacy Logs',
        to: { name: 'admin-pharmacy-logs' },
      },

      // {
      //   title: 'Hallandale Logs',
      //   to: { name: 'admin-hallandale-logs' },
      // },
      {
        title: 'Pending Visits',
        to: { name: 'admin-pending-visits' },
      },
    ],
  },
  {
    title: 'Refund Requests',
    to: { name: 'admin-refund-requests' },
    icon: { icon: 'tabler-credit-card-refund' },
  },
  {
    title: 'Promo Codes',
    to: { name: 'admin-promo-codes' },
    icon: { icon: 'tabler-discount' },
  },
  {
    title: 'Affiliate',
    icon: { icon: 'tabler-affiliate' },
    children: [
      {
        title: 'Approved Users',
        to: 'admin-affiliate-users',
      },
      {
        title: 'Pending Approval',
        to: 'admin-affiliate-pending-approval',
      },
      {
        title: 'Pending Transactions',
        to: 'admin-affiliate-pending-transactions',
      },
      {
        title: 'Transactions',
        to: 'admin-affiliate-transactions',
      },
      {
        title: 'Payout Requests',
        to: 'admin-affiliate-payout-requests',
      },
      {
        title: 'Commission Tiers',
        to: 'admin-affiliate-commission-tiers',
      },
    ],
  },
  {
    title: 'Settings',
    icon: { icon: 'tabler-settings' },
    children: [
      {
        title: 'Global Settings',
        to: 'admin-global-settings',
      },
      {
        title: 'Shipping Methods',
        to: 'admin-shipping-methods',
      },
      {
        title: 'States',
        to: 'admin-states',
      },
    ],
  },
]
