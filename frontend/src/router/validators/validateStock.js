import ApiService from '@/services/ApiService'
import { useAuthStore } from '@/store/auth'
import { clearRetrySession } from '@/utils/sessionHelpers'

export const validateWLStock = async (to, from, next) => {
  try {
    const authStore = useAuthStore()
    if (!authStore.isAuthenticated) {
      return next()
    }

    const { data } = await ApiService.get('/wl-product-available')

    if (data.status === 200) {
      clearRetrySession()
      window.sessionStorage.removeItem('isFullBodyImgLatest')

      return next() // Proceed to the route
    } else {
      return next({ name: 'wl-out-of-stock' }) // Redirect to WL product unavailable page
    }
  } catch (error) {
    return next({ name: 'user-subscription' }) // Redirect to user subscription page
  }
}
