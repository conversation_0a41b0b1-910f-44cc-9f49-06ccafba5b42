import ApiService from '@/services/ApiService'
import { useSessionStorage } from '@vueuse/core'
import { toast } from 'vue-sonner'

export const validatePendingVisit = async (to, from, next) => {
  try {
    const { category, id } = to.params

    const edVisitSession = useSessionStorage('edVisitSession', {})
    const hlVisitSession = useSessionStorage('hlVisitSession', {})
    const wlVisitSession = useSessionStorage('wlVisitSession', {})

    const { data } = await ApiService.get(`/validate-visit/${category}/${id}`)

    if (data.status === 200) {
      if (category.toLowerCase() === 'ed') {
        edVisitSession.value = {}
        edVisitSession.value['questionSessionId'] = id
        next({ name: 'ed-visit-product-recommended' })
      } else if (category.toLowerCase() === 'hl') {
        hlVisitSession.value = {}
        hlVisitSession.value['questionSessionId'] = id
        next({ name: 'hl-visit-product-recommended' })
      } else if (category.toLowerCase() === 'wl') {
        wlVisitSession.value = {}
        wlVisitSession.value['questionSessionId'] = id
        next({ name: 'wl-products' })
      } else {
        toast.error('Invalid treatment category')

        return next({ name: 'user-subscription' })
      }
    } else {
      toast.error('Invalid visit or treatment category')

      return next({ name: 'user-subscription' })
    }
  } catch (error) {
    return next({ name: 'user-subscription' })
  }
}
