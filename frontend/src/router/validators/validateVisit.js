import { useAuthStore } from '@/store/auth'
import { validateWLStock } from './validateStock'
import { clearRetrySession, resolveVisitSessionKey } from '@/utils/sessionHelpers'
import { useActiveSubscriptionPlan } from '@/composables/useActiveSubscriptionPlan'
import { useSessionStorage } from '@vueuse/core'

export const validateActiveVisit = async (category, to, from, next) => {
  try {
    const authStore = useAuthStore()
    if (!authStore.isAuthenticated) {
      return next()
    }

    const {
      validateActiveSubscriptionPlan,
      isSubscriptionActive,
      isPrescriptionActive,
      isTreatmentAvailable,
      getTreatmentType,
      getLastSubscriptionId,
    } = useActiveSubscriptionPlan()

    const data = await validateActiveSubscriptionPlan()

    if (data.status === 200) {
      const categoryMap = {
        ed: {
          visitExist: 'ed-visit-exists',
          notAvailable: 'ed-treatment-not-available',
        },
        hl: {
          visitExist: 'hl-visit-exists',
          notAvailable: 'hl-treatment-not-available',
        },
        wl: {
          visitExist: 'wl-visit-exists',
          notAvailable: 'wl-treatment-not-available',
        },
      }

      const categoryData = categoryMap[category]

      if (categoryData && !getTreatmentType(category)) {
        if (isSubscriptionActive(category)) {
          return next({ name: categoryData.visitExist })
        } else if (isPrescriptionActive(category) && !sessionStorage.getItem(`start_new_${category}_visit`)) {
          return next({ name: 'visit-reactivate', params: { visitType: category } })
        } else if (!isTreatmentAvailable(category)) {
          return next({ name: categoryData.notAvailable })
        }
      }
    }

    if (category === 'wl') {
      return await validateWLStock(to, from, next)
    } else {
      clearRetrySession()

      const visitSession = useSessionStorage(resolveVisitSessionKey(category), {})

      if (getTreatmentType(category) === 'followup') {
        visitSession.value['subscriptionId'] = getLastSubscriptionId(category)
        visitSession.value['visitType'] = 'followup'

        return next({ name: `${category}-followup-questions`, params: { slug: 'start' } })

      } else if (getTreatmentType(category) === 'new') {
        return next()
      } else {
        return next({ name: 'user-subscription' })
      }
    }
  } catch (error) {
    return next({ name: 'user-subscription' })
  }
}
