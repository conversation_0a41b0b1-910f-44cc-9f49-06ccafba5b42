import ApiService from '@/services/ApiService'
import router from '@/router'

export const validateResetPasswordForAdmin = async (to, from) => {
  try {
    const postData = {
      token: to.query.token,
      email: to.query.email,
    }

    const { data } = await ApiService.post('/admin/reset-password-link-valid', postData)

    if (data.status === 200) {
      return true
    } else {
      sessionStorage.setItem('resetLinkExpired', '1')
      router.push({ name: 'admin-login' })

      return false
    }
  } catch (error) {
    console.log(error)
    sessionStorage.setItem('resetLinkExpired', '1')
    router.push({ name: 'admin-login' })

    return false
  }
}

export const validateResetPasswordForUser = async (to, from) => {
  try {
    const postData = {
      token: to.query.token,
      email: to.query.email,
    }

    const { data } = await ApiService.post('/reset-password-link-valid', postData)

    if (data.status === 200) {
      return true
    } else {
      sessionStorage.setItem('resetLinkExpired', '1')
      router.push({ name: 'user-login' })

      return false
    }
  } catch (error) {
    console.log(error)
    sessionStorage.setItem('resetLinkExpired', '1')
    router.push({ name: 'user-login' })

    return false
  }
}
