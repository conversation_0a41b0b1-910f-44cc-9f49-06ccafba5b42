import ApiService from '@/services/ApiService'
import { useAuthStore } from '@/store/auth'
import { clearRetrySession } from '@/utils/sessionHelpers'
import { useSessionStorage } from '@vueuse/core'

export function handleStartWLFollowUpFromMail(to, from, next) {
  const { subscriptionId } = to.params

  if (!subscriptionId) {
    return next({ name: 'user-subscription' })
  }

  clearRetrySession()
  window.sessionStorage.removeItem('isFullBodyImgLatest')

  const wlVisitSession = useSessionStorage('wlVisitSession', {})

  wlVisitSession.value['subscriptionId'] = subscriptionId

  next({ name: 'wl-followup-questions', params: { slug: 'start' } })
}

export const validateWLFollowupVisit = async (to, from, next) => {
  try {
    const authStore = useAuthStore()
    if (!authStore.isAuthenticated) {
      return next()
    }

    const wlVisitSession = useSessionStorage('wlVisitSession', {})

    const subscriptionId = to.params?.subscriptionId || wlVisitSession.value['subscriptionId']

    if (!subscriptionId) {
      return next({ name: 'user-subscription' })
    }

    clearRetrySession()
    window.sessionStorage.removeItem('isFullBodyImgLatest')

    const { data } = await ApiService.get(`/validate-wl-follow-up-visit/${subscriptionId}`)

    if (data.status === 200) {
      return next()
    } else {
      throw new Error('WL follow up visit not found')
    }
  } catch (error) {
    return next({ name: 'user-subscription' })
  }
}
