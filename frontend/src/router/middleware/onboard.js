import { useAuthStore } from '@/store/auth'

export default function onboard({ to, from, next }) {
  const authStore = useAuthStore()
  const userData = authStore.userData

  if (authStore.isAuthenticated && userData.role === 'patient') {
    if (to.name == 'phone-verification' && userData.phone_verified) {
      return next({ name: 'basic-details' })
    }

    if (
      to.name == 'basic-details' &&
      userData.dob &&
      userData.gender &&
      userData.state
    ) {
      return next({ name: 'physical-details' })
    }

    if (to.name == 'physical-details' && userData.height && userData.weight) {
      return next({ name: 'user-subscription' })
    }

    return next()
  } else {
    return next({ name: 'login' })
  }
}
