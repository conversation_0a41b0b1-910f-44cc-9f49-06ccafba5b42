import { useAuthStore } from '@/store/auth'

export default function guest({ to, from, next }) {
  const authStore = useAuthStore()

  if (authStore.isAuthenticated) {
    const userData = authStore.userData

    if (userData.role === 'superadmin') {
      return next({ name: 'admin-dashboard' })
    } else if (userData.role === 'patient') {
      return next({ name: 'user-subscription' })
    } else {
      return next({ name: 'home' })
    }
  }

  return next()
}
