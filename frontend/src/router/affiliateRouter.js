import { useAuthStore } from '@/store/auth'
import user from './middleware/user'
import ApiService from '@/services/ApiService'

async function redirectIfAuthenticated() {
  const authStore = useAuthStore()
  if (authStore.isAuthenticated) {
    return { name: 'affiliate-dashboard' }
  }
}

async function validateResetPassword(to, from) {
  try {
    const authStore = useAuthStore()
    if (authStore.isAuthenticated) {
      return { name: 'affiliate-dashboard' }
    }

    const postData = {
      token: to.query.token,
      email: to.query.email,
    }

    const { data } = await ApiService.post('/affiliate/reset-password-link-valid', postData)

    if (data.status === 200) {
      return true
    } else {
      sessionStorage.setItem('resetLinkExpired', '1')

      return { name: 'affiliate-login' }
    }
  } catch (error) {
    console.log(error)
    sessionStorage.setItem('resetLinkExpired', '1')

    return { name: 'affiliate-login' }
  }
}

const routes = [
  {
    path: '/affiliate',
    children: [
      // auth routes
      {
        path: '/affiliate',
        component: () => import('@/layouts/LandingLayout.vue'),
        children: [
          {
            path: '/affiliate',
            name: 'affiliate-home',
            component: () => import('@/views/affiliate/AffiliateLandingPage.vue'),
            meta: {
              pageTitle: 'White Label Rx Affiliate Program',
            },
          },
          {
            path: '/affiliate/signup',
            name: 'affiliate-signup',
            redirect: '/affiliate/signup/user-details',
            component: () => import('@/views/affiliate/auth/signup/AffiliateSignUp.vue'),
            meta: {
              pageTitle: 'Create your White Label Rx affiliate account',
            },
            children: [
              {
                path: '/affiliate/signup/user-details',
                name: 'affiliate-signup-user-details',
                component: () => import('@/views/affiliate/auth/signup/Step1UserDetails.vue'),
              },
              {
                path: '/affiliate/signup/address-details',
                name: 'affiliate-signup-address-details',
                component: () => import('@/views/affiliate/auth/signup/Step2AddressDetails.vue'),
              },
              {
                path: '/affiliate/signup/additional-questions',
                name: 'affiliate-signup-additional-questions',
                component: () => import('@/views/affiliate/auth/signup/Step3AdditionalQuestions.vue'),
              },
              {
                path: '/affiliate/signup/payout-methods',
                name: 'affiliate-signup-payout-methods',
                component: () => import('@/views/affiliate/auth/signup/Step4PayoutMethods.vue'),
              },
            ],
          },
          {
            path: '/affiliate/signup/success',
            name: 'affiliate-signup-success',
            component: () => import('@/views/affiliate/auth/signup/SignUpSuccess.vue'),
            meta: {
              pageTitle: 'Thank you for creating your White Label Rx affiliate account',
            },
          },
          {
            path: '/affiliate/login',
            name: 'affiliate-login',
            component: () => import('@/views/affiliate/auth/Login.vue'),
            meta: {
              pageTitle: 'Sign in to your White Label Rx affiliate account',
            },
            beforeEnter: redirectIfAuthenticated,
          },
          {
            path: '/affiliate/forgot-password',
            name: 'affiliate-forgot-password',
            component: () => import('@/views/affiliate/auth/ForgotPassword.vue'),
            meta: {
              pageTitle: 'Forgot your Password?',
            },
            beforeEnter: redirectIfAuthenticated,
          },
          {
            path: '/affiliate/reset-password',
            name: 'affiliate-reset-password',
            component: () => import('@/views/affiliate/auth/ResetPassword.vue'),
            meta: {
              pageTitle: 'Create a new password for your affiliate account',
            },
            beforeEnter: validateResetPassword,
          },
        ],
      },

      // protected routes
      {
        path: '/affiliate',
        component: () => import('@/views/affiliate/components/Layout.vue'),
        children: [
          {
            path: '/affiliate/home',
            name: 'affiliate-dashboard',
            component: () => import('@/views/affiliate/dashboard/Home.vue'),
            meta: {
              pageTitle: 'Home - Affiliate',
              middleware: [user],
            },
          },
          {
            path: '/affiliate/wallet',
            name: 'affiliate-wallet',
            component: () => import('@/views/affiliate/dashboard/Wallet.vue'),
            meta: {
              pageTitle: 'Wallet - Affiliate',
              middleware: [user],
            },
          },
          {
            path: '/affiliate/profile',
            name: 'affiliate-profile',
            component: () => import('@/views/affiliate/dashboard/Profile.vue'),
            meta: {
              pageTitle: 'Profile - Affiliate',
              middleware: [user],
            },
          },
        ],
      },
    ],
  },
]

export default routes
