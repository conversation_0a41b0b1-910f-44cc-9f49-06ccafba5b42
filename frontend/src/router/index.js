import adminRoutes from '@/router/adminRouter'
import userRoutes from '@/router/userRouter'
import affiliateRoutes from '@/router/affiliateRouter'
import { useGlobalData } from '@/store/global'
import { createRouter, createWebHistory } from 'vue-router'
import { verifyToken } from './validators/verifyToken'
import { getToken } from '@/services/JwtService'
import { nextTick } from 'vue'
import { usePosthog } from '@/store/posthog'
import { useClarity } from '@/composables/useClarity'
import { useFacebookPixel } from '@/composables/useFacebookPixel'

const routes = [
  ...userRoutes,
  ...adminRoutes,
  ...affiliateRoutes,

  // error routes
  {
    path: '/404',
    component: () => import('@/layouts/LandingLayout.vue'),
    children: [
      {
        path: '/404',
        name: '404',
        component: () => import('@/views/error/Error404.vue'),
        meta: {
          pageTitle: 'Error 404',
        },
      },
      {
        path: '/500',
        name: '500',
        component: () => import('@/views/error/Error500.vue'),
        meta: {
          pageTitle: 'Error 500',
        },
      },
    ],
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404',
  },
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else if (to.hash) {
      return { el: to.hash, behavior: 'smooth' }
    } else if (from.path === to.path) {
      return { top: 0 }
    } else {
      return { top: 0 }
    }
  },
})

function nextFactory(context, middlewares, index) {
  const nextMiddleware = middlewares[index]

  if (!nextMiddleware) {
    return context.next
  }

  return nextMiddleware({
    ...context,
    next: nextFactory(context, middlewares, index + 1),
  })
}

router.beforeEach(async (to, from, next) => {
  // Start page loading progress
  const globalData = useGlobalData()

  if (to.name !== from.name && to.name.includes('admin')) {
    globalData.loadingProgress = true

    if (!!getToken()) await verifyToken()
  }

  if (!to.meta.middleware) {
    return next()
  }

  const middlewares = Array.isArray(to.meta.middleware)
    ? to.meta.middleware
    : [to.meta.middleware]

  const context = { to, from, next, router }

  return middlewares[0]({
    ...context,
    next: nextFactory(context, middlewares, 1),
  })
})

router.afterEach((to, from, failure) => {
  let globalData = useGlobalData()
  globalData.loadingProgress = false

  document.title = to.meta.pageTitle ? to.meta.pageTitle + ' | White Label Rx' : 'White Label Rx'

  // posthog setup
  const { $posthog } = usePosthog()

  if ($posthog) {
    if (to.fullPath.includes('admin')) {
      $posthog.opt_out_capturing()
    } else if (!failure) {
      nextTick(() => {
        $posthog.capture('$pageview', { path: to.fullPath })
      })
    }
  }

  // analytics config
  const { addScript, removeScript } = useClarity()
  const { loadPixel, removePixel } = useFacebookPixel()

  if (to.fullPath.includes('admin')) {
    removeScript()
    removePixel()
  } else if (!failure) {
    addScript()
    loadPixel()
  }
})

export default router
