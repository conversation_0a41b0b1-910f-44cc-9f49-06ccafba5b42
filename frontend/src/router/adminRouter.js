import AdminLayout from '@/layouts/AdminLayout.vue'
import BlankLayout from '@/layouts/blank.vue'
import admin from './middleware/admin'
import guest from './middleware/guest'
import { validateResetPasswordForAdmin } from './validators/validateResetPassword'

const routes = [
  {
    path: '/admin/login',
    component: BlankLayout,
    children: [
      {
        path: '/admin/login',
        name: 'admin-login',
        component: () => import('@/views/admin/auth/Login.vue'),
        meta: {
          pageTitle: 'Admin Login',
          middleware: [guest],
          action: 'read',
          subject: 'Auth',
        },
      },
      {
        path: '/admin/forgot-password',
        name: 'admin-forgot-password',
        component: () => import('@/views/admin/auth/ForgotPassword.vue'),
        meta: {
          pageTitle: 'Forgot your Password?',
          middleware: [guest],
        },
      },
      {
        path: '/admin/reset-password',
        name: 'admin-reset-password',
        component: () => import('@/views/admin/auth/ResetPassword.vue'),
        meta: {
          pageTitle: 'Create a new password for your account',
          middleware: [guest],
        },
        beforeEnter: validateResetPasswordForAdmin,
      },

      // error routes
      {
        path: '/admin/404',
        name: 'admin-404',
        component: () => import('@/views/error/404.vue'),
        meta: {
          pageTitle: 'Error 404',
        },
      },
      {
        path: '/admin/500',
        name: 'admin-500',
        component: () => import('@/views/error/500.vue'),
        meta: {
          pageTitle: 'Error 500',
        },
      },
    ],
  },
  {
    path: '/admin',
    redirect: '/admin/dashboard',
    component: AdminLayout,
    meta: {
      middleware: [admin],
    },
    children: [
      // dashboard
      {
        path: '/admin/dashboard',
        name: 'admin-dashboard',
        component: () => import('@/views/admin/dashboard/Dashboard.vue'),
        meta: {
          pageTitle: 'Dashboard',
          activeMenu: 'admin-dashboard',
        },
      },

      // users
      {
        path: '/admin/users',
        name: 'admin-users',
        component: () => import('@/views/admin/users/UserList.vue'),
        meta: {
          pageTitle: 'Registered Users',
          activeMenu: 'admin-users',
        },
      },
      {
        path: '/admin/users/:userId',
        name: 'admin-user-details',
        component: () => import('@/views/admin/users/UserDetails.vue'),
        meta: {
          pageTitle: 'User Details',
          activeMenu: 'admin-users',
          showBackButton: true,
        },
      },
      {
        path: '/admin/interested-users',
        name: 'admin-interested-users',
        component: () => import('@/views/admin/users/InterestedUserList.vue'),
        meta: {
          pageTitle: 'Interested Users',
          activeMenu: 'admin-interested-users',
        },
      },

      // sales
      {
        path: '/admin/subscriptions',
        name: 'admin-subscriptions',
        component: () => import('@/views/admin/sales/SubscriptionList.vue'),
        meta: {
          pageTitle: 'All Subscriptions',
          activeMenu: 'admin-subscriptions',
        },
      },
      {
        path: '/admin/orders',
        name: 'admin-orders',
        component: () => import('@/views/admin/sales/OrderList.vue'),
        meta: {
          pageTitle: 'All Orders',
          activeMenu: 'admin-orders',
        },
      },

      // ed products
      {
        path: '/admin/ed/products',
        name: 'admin-ed-products',
        component: () => import('@/views/admin/ed/products/ProductList.vue'),
        meta: {
          pageTitle: 'Products',
          activeMenu: 'admin-ed-products',
        },
      },
      {
        path: '/admin/ed/products/add',
        name: 'admin-ed-products-add',
        component: () => import('@/views/admin/ed/products/AddProduct.vue'),
        meta: {
          pageTitle: 'Add Product',
          activeMenu: 'admin-ed-products',
          showBackButton: true,
        },
      },
      {
        path: '/admin/ed/products/edit/:product_id',
        name: 'admin-ed-products-edit',
        component: () => import('@/views/admin/ed/products/EditProduct.vue'),
        meta: {
          pageTitle: 'Edit Product',
          activeMenu: 'admin-ed-products',
          showBackButton: true,
        },
      },
      {
        path: '/admin/ed/products/:product_id/overview',
        name: 'admin-ed-product-overview',
        component: () => import('@/views/admin/ed/products/components/ProductOverview.vue'),
        meta: {
          pageTitle: 'Edit Product Overview',
          activeMenu: 'admin-ed-products',
          showBackButton: true,
        },
      },

      // ed product-state mapping
      {
        path: '/admin/ed/product-state-mapping',
        name: 'admin-ed-product-state-mapping',
        component: () => import('@/views/admin/ed/products/ProductStateMapping.vue'),
        meta: {
          pageTitle: 'ED Product State Mapping',
          activeMenu: 'admin-ed-product-state-mapping',
        },
      },

      // ed recommendations
      {
        path: '/admin/ed/recommendations',
        name: 'admin-ed-recommendations',
        component: () => import('@/views/admin/ed/recommended/EDRecommendations.vue'),
        meta: {
          pageTitle: 'ED Product Recommendations',
          activeMenu: 'admin-ed-recommendations',
        },
      },

      // ed subscriptions
      {
        path: '/admin/ed/subscriptions',
        name: 'admin-ed-subscriptions',
        component: () => import('@/views/admin/ed/subscription/SubscriptionList.vue'),
        meta: {
          pageTitle: 'ED Subscriptions',
          activeMenu: 'admin-ed-subscriptions',
        },
      },
      {
        path: '/admin/ed/subscriptions/:subscriptionId',
        name: 'admin-ed-manage-subscription',
        component: () => import('@/views/admin/ed/subscription/SubscriptionDetails.vue'),
        meta: {
          pageTitle: 'Manage ED Subscription',
          activeMenu: 'admin-ed-subscriptions',
          showBackButton: true,
        },
      },

      // ed orders
      {
        path: '/admin/ed/orders',
        name: 'admin-ed-orders',
        component: () => import('@/views/admin/ed/orders/OrderList.vue'),
        meta: {
          pageTitle: 'ED Orders',
          activeMenu: 'admin-ed-orders',
        },
      },
      {
        path: '/admin/ed/orders/:orderId',
        name: 'admin-ed-order-details',
        component: () => import('@/views/admin/ed/orders/OrderDetails.vue'),
        meta: {
          pageTitle: 'ED Order Details',
          activeMenu: 'admin-ed-orders',
          showBackButton: true,
        },
      },

      // HL products
      {
        path: '/admin/hl/products',
        name: 'admin-hl-products',
        component: () => import('@/views/admin/hl/products/ProductList.vue'),
        meta: {
          pageTitle: 'Products',
          activeMenu: 'admin-hl-products',
        },
      },
      {
        path: '/admin/hl/products/add',
        name: 'admin-hl-products-add',
        component: () => import('@/views/admin/hl/products/AddProduct.vue'),
        meta: {
          pageTitle: 'Add Product',
          activeMenu: 'admin-hl-products',
          showBackButton: true,
        },
      },
      {
        path: '/admin/hl/products/edit/:product_id',
        name: 'admin-hl-products-edit',
        component: () => import('@/views/admin/hl/products/EditProduct.vue'),
        meta: {
          pageTitle: 'Edit Product',
          activeMenu: 'admin-hl-products',
          showBackButton: true,
        },
      },
      {
        path: '/admin/hl/products/:product_id/overview',
        name: 'admin-hl-product-overview',
        component: () => import('@/views/admin/hl/products/components/ProductOverview.vue'),
        meta: {
          pageTitle: 'Edit Product Overview',
          activeMenu: 'admin-hl-products',
          showBackButton: true,
        },
      },

      // hl product-state mapping
      {
        path: '/admin/hl/product-state-mapping',
        name: 'admin-hl-product-state-mapping',
        component: () => import('@/views/admin/hl/products/ProductStateMapping.vue'),
        meta: {
          pageTitle: 'Hair Product State Mapping',
          activeMenu: 'admin-hl-product-state-mapping',
        },
      },

      // hl recommendations
      {
        path: '/admin/hl/recommendations',
        name: 'admin-hl-recommendations',
        component: () => import('@/views/admin/hl/recommended/HLRecommendations.vue'),
        meta: {
          pageTitle: 'HL Product Recommendations',
          activeMenu: 'admin-hl-recommendations',
        },
      },

      // HL subscriptions
      {
        path: '/admin/hl/subscriptions',
        name: 'admin-hl-subscriptions',
        component: () => import('@/views/admin/hl/subscription/SubscriptionList.vue'),
        meta: {
          pageTitle: 'HL Subscriptions',
          activeMenu: 'admin-hl-subscriptions',
        },
      },
      {
        path: '/admin/hl/subscriptions/:subscriptionId',
        name: 'admin-hl-manage-subscription',
        component: () => import('@/views/admin/hl/subscription/SubscriptionDetails.vue'),
        meta: {
          pageTitle: 'Manage HL Subscription',
          activeMenu: 'admin-hl-subscriptions',
          showBackButton: true,
        },
      },

      // HL orders
      {
        path: '/admin/hl/orders',
        name: 'admin-hl-orders',
        component: () => import('@/views/admin/hl/orders/OrderList.vue'),
        meta: {
          pageTitle: 'HL Orders',
          activeMenu: 'admin-hl-orders',
        },
      },
      {
        path: '/admin/hl/orders/:orderId',
        name: 'admin-hl-order-details',
        component: () => import('@/views/admin/hl/orders/OrderDetails.vue'),
        meta: {
          pageTitle: 'HL Order Details',
          activeMenu: 'admin-hl-orders',
          showBackButton: true,
        },
      },

      // WL products
      {
        path: '/admin/wl/products',
        name: 'admin-wl-products',
        component: () => import('@/views/admin/wl/products/ProductList.vue'),
        meta: {
          pageTitle: 'Products',
          activeMenu: 'admin-wl-products',
        },
      },
      {
        path: '/admin/wl/products/add',
        name: 'admin-wl-products-add',
        component: () => import('@/views/admin/wl/products/AddProduct.vue'),
        meta: {
          pageTitle: 'Add Product',
          activeMenu: 'admin-wl-products',
          showBackButton: true,
        },
      },
      {
        path: '/admin/wl/products/edit/:product_id',
        name: 'admin-wl-products-edit',
        component: () => import('@/views/admin/wl/products/EditProduct.vue'),
        meta: {
          pageTitle: 'Edit Product',
          activeMenu: 'admin-wl-products',
          showBackButton: true,
        },
      },
      {
        path: '/admin/wl/products/:product_id/overview',
        name: 'admin-wl-product-attachments',
        component: () => import('@/views/admin/wl/products/components/ProductAttachments.vue'),
        meta: {
          pageTitle: 'Product Attachments',
          activeMenu: 'admin-wl-products',
          showBackButton: true,
        },
      },

      // wl product-state mapping
      {
        path: '/admin/wl/product-state-mapping',
        name: 'admin-wl-product-state-mapping',
        component: () => import('@/views/admin/wl/products/ProductStateMapping.vue'),
        meta: {
          pageTitle: 'Weight Loss Product State Mapping',
          activeMenu: 'admin-wl-product-state-mapping',
        },
      },

      // wl recommendations
      // {
      //   path: '/admin/wl/state-recommendations',
      //   name: 'admin-wl-recommendations',
      //   component: () => import('@/views/admin/wl/recommended/WLRecommendations.vue'),
      //   meta: {
      //     pageTitle: 'Weight Loss Product State Recommendations',
      //     activeMenu: 'admin-wl-recommendations',
      //   },
      // },

      // wl subscriptions
      {
        path: '/admin/wl/subscriptions',
        name: 'admin-wl-subscriptions',
        component: () => import('@/views/admin/wl/subscription/SubscriptionList.vue'),
        meta: {
          pageTitle: 'Weight Loss Subscriptions',
          activeMenu: 'admin-wl-subscriptions',
        },
      },
      {
        path: '/admin/wl/subscriptions/:subscriptionId',
        name: 'admin-wl-manage-subscription',
        component: () => import('@/views/admin/wl/subscription/SubscriptionDetails.vue'),
        meta: {
          pageTitle: 'Manage Weight Loss Subscription',
          activeMenu: 'admin-wl-subscriptions',
          showBackButton: true,
        },
      },

      // wl orders
      {
        path: '/admin/wl/orders',
        name: 'admin-wl-orders',
        component: () => import('@/views/admin/wl/orders/OrderList.vue'),
        meta: {
          pageTitle: 'Weight Loss Orders',
          activeMenu: 'admin-wl-orders',
        },
      },
      {
        path: '/admin/wl/orders/:orderId',
        name: 'admin-wl-order-details',
        component: () => import('@/views/admin/wl/orders/OrderDetails.vue'),
        meta: {
          pageTitle: 'Weight Loss Order Details',
          activeMenu: 'admin-wl-orders',
          showBackButton: true,
        },
      },

      // otc category
      {
        path: '/admin/otc/category',
        name: 'admin-otc-category',
        component: () => import('@/views/admin/otc/category/CategoryList.vue'),
        meta: {
          pageTitle: 'Product Category',
          activeMenu: 'admin-otc-category',
        },
      },

      // otc products
      {
        path: '/admin/otc/products',
        name: 'admin-otc-products',
        component: () => import('@/views/admin/otc/products/ProductList.vue'),
        meta: {
          pageTitle: 'Products',
          activeMenu: 'admin-otc-products',
        },
      },
      {
        path: '/admin/otc/products/add',
        name: 'admin-otc-products-add',
        component: () => import('@/views/admin/otc/products/AddProduct.vue'),
        meta: {
          pageTitle: 'Add Product',
          activeMenu: 'admin-otc-products',
          showBackButton: true,
        },
      },
      {
        path: '/admin/otc/products/edit/:product_id',
        name: 'admin-otc-products-edit',
        component: () => import('@/views/admin/otc/products/EditProduct.vue'),
        meta: {
          pageTitle: 'Edit Product',
          activeMenu: 'admin-otc-products',
          showBackButton: true,
        },
      },

      // otc orders
      {
        path: '/admin/otc/orders',
        name: 'admin-otc-orders',
        component: () => import('@/views/admin/otc/orders/OrderList.vue'),
        meta: {
          pageTitle: 'OTC Orders',
          activeMenu: 'admin-otc-orders',
        },
      },
      {
        path: '/admin/otc/orders/:orderId',
        name: 'admin-otc-order-details',
        component: () => import('@/views/admin/otc/orders/OrderDetails.vue'),
        meta: {
          pageTitle: 'OTC Order Details',
          activeMenu: 'admin-otc-orders',
          showBackButton: true,
        },
      },

      // visit logs
      {
        path: '/admin/visit/logs',
        name: 'admin-visit-logs',
        component: () => import('@/views/admin/order-logs/visits/VisitLogList.vue'),
        meta: {
          pageTitle: 'Visit Logs',
          activeMenu: 'admin-visit-logs',
        },
      },
      {
        path: '/admin/visit/logs/:logId',
        name: 'admin-visit-log-details',
        component: () => import('@/views/admin/order-logs/visits/VisitLogDetails.vue'),
        meta: {
          pageTitle: 'Visit Log Details',
          activeMenu: 'admin-visit-logs',
          showBackButton: true,
        },
      },
      {
        path: '/admin/visit/logs/:logId/medical-qa',
        name: 'admin-visit-medical-qa',
        component: () => import('@/views/admin/order-logs/visits/VisitMedicalQA.vue'),
        meta: {
          pageTitle: 'Visit Medical Question Answers',
          activeMenu: 'admin-visit-logs',
          showBackButton: true,
        },
      },

      // pharmacy logs
      {
        path: '/admin/pharmacy/logs',
        name: 'admin-pharmacy-logs',
        component: () => import('@/views/admin/order-logs/pharmacy/PharmacyLogList.vue'),
        meta: {
          pageTitle: 'Pharmacy Logs',
          activeMenu: 'admin-pharmacy-logs',
        },
      },
      {
        path: '/admin/pharmacy/logs/:logId',
        name: 'admin-pharmacy-log-details',
        component: () => import('@/views/admin/order-logs/pharmacy/PharmacyLogDetails.vue'),
        meta: {
          pageTitle: 'Pharmacy Log Details',
          activeMenu: 'admin-pharmacy-logs',
          showBackButton: true,
        },
      },

      // hallandale logs
      // {
      //   path: '/admin/hallandale/logs',
      //   name: 'admin-hallandale-logs',
      //   component: () => import('@/views/admin/order-logs/hallandale/HallandaleLogList.vue'),
      //   meta: {
      //     pageTitle: 'Hallandale Logs',
      //     activeMenu: 'admin-hallandale-logs',
      //   },
      // },
      // {
      //   path: '/admin/hallandale/logs/:logId',
      //   name: 'admin-hallandale-log-details',
      //   component: () => import('@/views/admin/order-logs/hallandale/HallandaleLogDetails.vue'),
      //   meta: {
      //     pageTitle: 'Hallandale Log Details',
      //     activeMenu: 'admin-hallandale-logs',
      //     showBackButton: true,
      //   },
      // },

      // Refund Requests
      {
        path: '/admin/refund-requests',
        name: 'admin-refund-requests',
        component: () => import('@/views/admin/refund/RefundRequestList.vue'),
        meta: {
          pageTitle: 'Refund Requests',
          activeMenu: 'admin-refund-requests',
        },
      },

      // Promo Code
      {
        path: '/admin/promo-codes',
        name: 'admin-promo-codes',
        component: () => import('@/views/admin/promo/PromoList.vue'),
        meta: {
          pageTitle: 'Promo Codes',
          activeMenu: 'admin-promo-codes',
        },
      },
      {
        path: '/admin/promo-codes/add',
        name: 'admin-promo-add',
        component: () => import('@/views/admin/promo/PromoAdd.vue'),
        meta: {
          pageTitle: 'Add Promo Code',
          activeMenu: 'admin-promo-codes',
          showBackButton: true,
        },
      },
      {
        path: '/admin/promo-codes/edit/:id',
        name: 'admin-promo-edit',
        component: () => import('@/views/admin/promo/PromoEdit.vue'),
        meta: {
          pageTitle: 'Edit Promo Code',
          activeMenu: 'admin-promo-codes',
          showBackButton: true,
        },
      },

      // Pending Visits
      {
        path: '/admin/pending-visits',
        name: 'admin-pending-visits',
        component: () => import('@/views/admin/order-logs/pending-visit/PendingVisitList.vue'),
        meta: {
          pageTitle: 'Pending Visits',
          activeMenu: 'admin-pending-visits',
        },
      },
      {
        path: '/admin/pending-visits/:id',
        name: 'admin-pending-visit-details',
        component: () => import('@/views/admin/order-logs/pending-visit/PendingVisitDetails.vue'),
        meta: {
          pageTitle: 'Pending Visit Details',
          activeMenu: 'admin-pending-visits',
          showBackButton: true,
        },
      },

      // affiliate
      {
        path: '/admin/affiliate',
        children: [
          {
            path: 'pending-approval',
            name: 'admin-affiliate-pending-approval',
            component: () => import('@/views/admin/affiliate/users/InterestedUsersList.vue'),
            meta: {
              pageTitle: 'Pending Approval',
              activeMenu: 'admin-affiliate-pending-approval',
            },
          },
          {
            path: 'pending-approval/:userId',
            name: 'admin-affiliate-pending-user-details',
            component: () => import('@/views/admin/affiliate/users/InterestedUserDetails.vue'),
            meta: {
              pageTitle: 'Pending Approval User Details',
              activeMenu: 'admin-affiliate-pending-approval',
              showBackButton: true,
            },
          },

          {
            path: 'users',
            name: 'admin-affiliate-users',
            component: () => import('@/views/admin/affiliate/users/ApprovedUsersList.vue'),
            meta: {
              pageTitle: 'Affiliate Users',
              activeMenu: 'admin-affiliate-users',
            },
          },
          {
            path: 'users/:userId',
            name: 'admin-affiliate-user-details',
            component: () => import('@/views/admin/affiliate/users/ApprovedUserDetails.vue'),
            meta: {
              pageTitle: 'Affiliate User Details',
              activeMenu: 'admin-affiliate-users',
              showBackButton: true,
            },
          },

          // Pending Transactions
          {
            path: 'pending-transactions',
            name: 'admin-affiliate-pending-transactions',
            component: () => import('@/views/admin/affiliate/transactions/PendingTransactionsList.vue'),
            meta: {
              pageTitle: 'Pending Transactions',
              activeMenu: 'admin-affiliate-pending-transactions',
            },
          },

          // Transactions
          {
            path: 'transactions',
            name: 'admin-affiliate-transactions',
            component: () => import('@/views/admin/affiliate/transactions/TransactionsList.vue'),
            meta: {
              pageTitle: 'Transactions',
              activeMenu: 'admin-affiliate-transactions',
            },
          },

          // Payout Requests
          {
            path: 'payout-requests',
            name: 'admin-affiliate-payout-requests',
            component: () => import('@/views/admin/affiliate/payout/PayoutRequestList.vue'),
            meta: {
              pageTitle: 'Payout Requests | Affiliate',
              activeMenu: 'admin-affiliate-payout-requests',
            },
          },

          // Commission Tiers
          {
            path: 'commission-tiers',
            name: 'admin-affiliate-commission-tiers',
            component: () => import('@/views/admin/affiliate/commission-tiers/TierList.vue'),
            meta: {
              pageTitle: 'Commission Tiers',
              activeMenu: 'admin-affiliate-commission-tiers',
            },
          },
          {
            path: 'commission-tiers/add',
            name: 'admin-affiliate-commission-tier-add',
            component: () => import('@/views/admin/affiliate/commission-tiers/TierForm.vue'),
            meta: {
              pageTitle: 'Add Commission Tier',
              activeMenu: 'admin-affiliate-commission-tiers',
            },
          },
          {
            path: 'commission-tiers/edit/:id',
            name: 'admin-affiliate-commission-tier-edit',
            component: () => import('@/views/admin/affiliate/commission-tiers/TierForm.vue'),
            meta: {
              pageTitle: 'Edit Commission Tier',
              activeMenu: 'admin-affiliate-commission-tiers',
            },
          },
        ],
      },

      // settings
      {
        path: '/admin/setting',
        redirect: '/admin/setting/shipping-methods',
        children: [
          {
            path: 'shipping-methods',
            name: 'admin-shipping-methods',
            component: () => import('@/views/admin/settings/shipping/ShippingMethodList.vue'),
            meta: {
              pageTitle: 'Shipping Methods',
              activeMenu: 'admin-shipping-methods',
            },
          },
          {
            path: 'shipping-methods/add',
            name: 'admin-shipping-method-add',
            component: () => import('@/views/admin/settings/shipping/ShippingMethodAdd.vue'),
            meta: {
              pageTitle: 'Add Shipping Method',
              activeMenu: 'admin-shipping-methods',
            },
          },
          {
            path: 'shipping-methods/edit/:id',
            name: 'admin-shipping-method-edit',
            component: () => import('@/views/admin/settings/shipping/ShippingMethodEdit.vue'),
            meta: {
              pageTitle: 'Edit Shipping Method',
              activeMenu: 'admin-shipping-methods',
            },
          },
          {
            path: 'global',
            name: 'admin-global-settings',
            component: () => import('@/views/admin/settings/GlobalSettings.vue'),
            meta: {
              pageTitle: 'Edit Global Settings',
              activeMenu: 'admin-global-settings',
            },
          },

          // states
          {
            path: 'states',
            name: 'admin-states',
            component: () => import('@/views/admin/settings/states/StateList.vue'),
            meta: {
              pageTitle: 'States',
              activeMenu: 'admin-states',
            },
          },
        ],
      },

      // admin profile
      {
        path: '/admin/profile',
        name: 'admin-profile',
        component: () => import('@/views/admin/auth/Profile.vue'),
        meta: {
          pageTitle: 'Manage Profile',
          middleware: [admin],
        },
      },

      // admin change password
      {
        path: '/admin/change-password',
        name: 'admin-change-password',
        component: () => import('@/views/admin/auth/ChangePassword.vue'),
        meta: {
          pageTitle: 'Change your password',
          middleware: [admin],
        },
      },
    ],
  },
]

export default routes
