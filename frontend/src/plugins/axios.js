import router from '@/router'
import { getToken } from '@/services/JwtService'
import { destroyUserAbilities } from '@/services/UserService'
import { useAuthStore } from '@/store/auth'
import axios from 'axios'

const axiosIns = axios.create({
  baseURL: import.meta.env.VITE_APP_API_URL,
  headers: {
    Accept: 'application/json',
  },

  // timeout: 1000,
})

// ℹ️ Add request interceptor to send the authorization header on each subsequent request after login
axiosIns.interceptors.request.use(config => {
  const token = getToken()

  if (token) {
    config.headers = config.headers || {}
    config.headers.Authorization = `Bearer ${token}`
  }

  return config
})

// ℹ️ Add response interceptor to handle 401 response
axiosIns.interceptors.response.use(
  response => {
    if (response?.status === 401 || response?.data?.status === 401) {
      destroyUserSession()
    }
    if (response?.status === 404 || response?.data?.status === 404) {
      const authStore = useAuthStore()
      const userRole = authStore.userData?.role
      if (userRole === 'superadmin' || userRole === 'admin') {
        router.push({ name: 'admin-404' })
      } else {
        router.push({ name: '404' })
      }
    }
    if (response?.status === 500 || response?.data?.status === 500) {
      const authStore = useAuthStore()
      const userRole = authStore.userData?.role
      if (userRole === 'superadmin' || userRole === 'admin') {
        router.push({ name: 'admin-500' })
      } else {
        router.push({ name: '500' })
      }
    }

    return Promise.resolve(response)
  },
  error => {
    if (error.response?.status === 401 || error.response?.data?.status === 401) {
      destroyUserSession()
    }
    if (
      error.code === 'ERR_NETWORK' ||
      error.response?.status === 500 ||
      error.response?.data?.status === 500
    ) {
      // log error to slack
      const appEnv = import.meta.env.VITE_APP_ENV
      if (appEnv !== 'local') {
        axiosIns.post('/error-logger', {
          appUrl: window.location.href,
          error: error?.message,
          apiUrl: `${error?.config?.baseURL}${error?.config?.url}`,
          requestBody: error?.config?.data,
          response: error?.response?.data,
        })
      }

      const authStore = useAuthStore()
      const userRole = authStore.userData?.role
      if (userRole === 'superadmin' || userRole === 'admin') {
        router.push({ name: 'admin-500' })
      } else {
        router.push({ name: '500' })
      }
    }

    return Promise.reject(error)
  },
)

const destroyUserSession = () => {
  const authStore = useAuthStore()
  const userRole = authStore.userData?.role

  authStore.purgeAuth()
  destroyUserAbilities()

  if (userRole === 'superadmin' || userRole === 'admin') {
    router.replace({ name: 'admin-login' })
  } else {
    router.replace({ name: 'user-login' })
  }
}

export default axiosIns
