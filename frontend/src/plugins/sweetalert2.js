import Swal from 'sweetalert2'

// Modal alerts
const sweetAlertMixin = Swal.mixin({
  showConfirmButton: true,
  confirmButtonColor: '#39be81',
  cancelButtonColor: '#989898',
})

export const alertSuccess = sweetAlertMixin.mixin({
  icon: 'success',
})

export const alertError = sweetAlertMixin.mixin({
  icon: 'error',
})

export const alertWarning = sweetAlertMixin.mixin({
  icon: 'warning',
})

export const alertInfo = sweetAlertMixin.mixin({
  icon: 'info',
})

export const alertQuestion = sweetAlertMixin.mixin({
  icon: 'question',
})

// Toast alerts
const toastMixin = Swal.mixin({
  toast: true,
  timer: 5000,
  timerProgressBar: true,
  position: 'top',
  showConfirmButton: false,
  width: 400,
  didOpen: toast => {
    toast.addEventListener('mouseenter', Swal.stopTimer)
    toast.addEventListener('mouseleave', Swal.resumeTimer)
  },
})

export const toastSuccess = toastMixin.mixin({
  icon: 'success',
})

export const toastError = toastMixin.mixin({
  icon: 'error',
})

export const toastWarning = toastMixin.mixin({
  icon: 'warning',
})

export const toastInfo = toastMixin.mixin({
  icon: 'info',
})

export const toastQuestion = toastMixin.mixin({
  icon: 'question',
})

export default Swal
