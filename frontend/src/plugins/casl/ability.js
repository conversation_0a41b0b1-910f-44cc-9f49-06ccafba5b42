import { getUserAbilities } from "@/services/UserService"
import { Ability } from "@casl/ability"

export const initialAbility = [
  {
    action: "read",
    subject: "Auth",
  },
]

// 👉 Handles auto fetching previous abilities if already logged in user
// ℹ️ You can update this if you store user abilities to more secure place
const existingAbility = getUserAbilities() || null
export default new Ability(existingAbility || initialAbility)
