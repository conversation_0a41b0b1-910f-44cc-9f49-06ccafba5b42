<script setup>
import LoadingProgress from '@/components/LoadingProgress.vue'
import ScrollToTop from '@core/components/ScrollToTop.vue'
import { useThemeConfig } from '@core/composable/useThemeConfig'
import { useTheme } from 'vuetify'
import CircularProgress from './components/CircularProgress.vue'
import { useGlobalData } from './store/global'
import { Toaster } from 'vue-sonner'

const {
  syncInitialLoaderTheme,
  syncVuetifyThemeWithTheme: syncConfigThemeWithVuetifyTheme,
  isAppRtl,
  handleSkinChanges,
} = useThemeConfig()

const { global } = useTheme()

// ℹ️ Sync current theme with initial loader theme
syncInitialLoaderTheme()
syncConfigThemeWithVuetifyTheme()
handleSkinChanges()

const store = useGlobalData()

// For deployment: counter: 16
</script>

<template>
  <LoadingProgress />
  <CircularProgress />
  <RouterView />
  <!-- <ScrollToTop /> -->

  <VSnackbar
    v-model="store.snackbarVisible"
    :color="store.snackbarColor"
  >
    {{ store.snackbarMsg }}
    <template #actions>
      <v-btn
        color="pink"
        variant="text"
        @click="store.hideSnackbar"
      >
        Close
      </v-btn>
    </template>
  </VSnackbar>
  <Toaster
    position="bottom-center"
    theme="dark"
    rich-colors
  />
</template>
