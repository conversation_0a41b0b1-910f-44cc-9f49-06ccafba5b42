// Write your overrides

::-webkit-scrollbar {
  width: 0.4rem;
  height: 0.4rem;
  background-color: #c5c5c5;
  border-radius: 0;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(95, 103, 148, 0.7);
  border-radius: 0.5rem;
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgba(62, 67, 97, 0.7);
}

// start: fix for template

// body {
//   font-family: "Public Sans",sans-serif,-apple-system,blinkmacsystemfont,"Segoe UI",roboto,"Helvetica Neue",arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol";
// }

// ul, ol {
//   padding: 0;
// }

// .gap-4 {
//   gap: 1rem !important;
// }

// hr {
//   margin: 0;
// }

// .h1, .h2, .h3, .h4, .h5, .h6, h1, h2, h3, h4, h5, h6 {
//   margin: 0;
// }

// .layout-page-content {
//   padding-inline: 1.5rem;
// }

// .layout-page-content {
//   flex-grow: 1;
//   padding-block: 1.5rem;
// }

// .alert_box_wrap ul {
//   list-style: disc;
//   padding-left: 20px;
// }

// end: fix for template

// reusable css classes
.w-50px {
  width: 50px !important;
}

.w-75px {
  width: 75px !important;
}

.w-100px {
  width: 100px !important;
}

.w-125px {
  width: 125px !important;
}

.w-150px {
  width: 150px !important;
}

.w-175px {
  width: 175px !important;
}

.w-200px {
  width: 200px !important;
}

.w-225px {
  width: 225px !important;
}

.w-250px {
  width: 250px !important;
}

.v-theme--dark {
  .p-skeleton {
    background-color: rgba(255, 255, 255, 0.5) !important;
  }
}

.object-fit-cover {
  object-fit: cover !important;
}

.v-progress-circular__underlay {
  color: white;
}

.btn-link {
  text-decoration: underline;
  color: #1565ff;
  transition: all 3ms ease;

  &:hover {
    text-decoration: none;
    filter: brightness(0.9);
  }
}

.text-link {
  color: #1565ff;
  text-decoration: underline;
  transition: all 3ms ease;
  cursor: pointer;

  &:hover {
    text-decoration: none;
    filter: brightness(0.9);
  }
}

.dt-link {
  all: unset;
  color: #1565ff;
  text-decoration: underline;
  transition: all 3ms ease;
  cursor: pointer;

  &:hover {
    text-decoration: none;
  }
}

.admin-table {
  font-size: 14px;
  tr {
    border-bottom: thin solid rgba(var(--v-border-color), var(--v-border-opacity));

    &:last-child {
      border-bottom-width: 0;
    }

    td {
      padding-top: 0.5rem !important;
      padding-bottom: 0.5rem !important;

      &:first-child {
        padding-right: 0.5rem;
      }

      &:last-child {
        padding-left: 0.5rem;
        border-bottom: 0;
      }

      span {
        font-weight: 600;
      }
    }
  }
}

.list-text-fix {
  max-width: 180px;
  overflow: auto;
  text-wrap: auto;
}


.sub-info-container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  // justify-content: space-between;
  gap: 2rem;
  margin-bottom: 0.75rem;

  > div {
    // flex-grow: 1;
    min-width: 150px;
  }
}
