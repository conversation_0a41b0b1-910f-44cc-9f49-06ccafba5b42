import { defineStore } from 'pinia'
import { computed, ref } from 'vue'

export const useUpdatePaymentMethod = defineStore('updatePaymentMethod', () => {
  const _subscriptionRefillId = ref(null)

  const subscriptionRefillId = computed(() => _subscriptionRefillId.value)

  function setSubscriptionRefillId(id) {
    _subscriptionRefillId.value = id
  }

  return {
    setSubscriptionRefillId,
    subscriptionRefillId,
  }
})
