import { isEmpty } from '@/@core/utils'
import ApiService from '@/services/ApiService'
import { processErrors } from '@/utils/errorHandler'
import { useSessionStorage } from '@vueuse/core'
import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import { toast } from 'vue-sonner'

export const useChatStore = defineStore('chat', () => {
  const prompt = ref('')
  const isGenerating = ref(false)
  const messages = ref([])
  const isMessagesLoading = ref(false)
  const chatContainer = ref(null)
  const chatThreadId = useSessionStorage('chatThreadId', null)
  const chatRunId = useSessionStorage('chatRunId', null)
  const timerId = ref(undefined)

  const setPrompt = value => prompt.value = value

  async function sendPrompt() {
    try {
      isGenerating.value = true

      const newPrompt = prompt.value

      prompt.value = ''

      addMessage(newPrompt)

      const { data } = await ApiService.post('/assistant/chat', {
        prompt: newPrompt,
        thread_id: chatThreadId.value,
      })

      if (data.status === 200) {
        chatThreadId.value = data.response?.thread_id
        chatRunId.value = data.response?.run_id

        timerId.value = setInterval(getStatus, 3000) // fetch run status every 3 seconds
      } else {
        throw data
      }
    } catch (error) {
      console.error(error)
      isGenerating.value = false
      toast.error(processErrors(error)[0])
    }
  }

  async function getStatus() {
    try {
      if (isEmpty(chatRunId.value)) return

      const { data } = await ApiService.get('/assistant/chat/status', {
        thread_id: chatThreadId.value,
        run_id: chatRunId.value,
      })

      if (data.status === 200) {
        if (data.response.status === 'completed' && data.response.messages?.data) {
          // clear interval as request is completed
          clearInterval(timerId.value)
          chatRunId.value = null
          isGenerating.value = false

          // populate latest messages
          messages.value = data.response.messages?.data

          // scrollChatContainer()
        }
      } else {
        toast.error(data.message)
      }
    } catch (error) {
      console.error(error)
      toast.error(processErrors(error)[0])
    }
  }

  async function loadMessages() {
    try {
      isMessagesLoading.value = true

      const { data } = await ApiService.get('/assistant/chat/messages', {
        thread_id: chatThreadId.value,
      })

      if (data.status === 200) {
        if (messages.value.length !== data.messages?.data) {
          messages.value = data.messages?.data
          scrollChatContainer()
        }
      } else {
        messages.value = []
      }
    } catch (error) {
      console.error(error)
      toast.error(processErrors(error)[0])
    } finally {
      isMessagesLoading.value = false
    }
  }

  function addMessage(message) {
    messages.value.unshift({
      'id': 'msg_' + (new Date()).getTime(),
      'object': 'thread.message',
      'created_at': Math.floor((new Date()).getTime() / 1000), // time in epoch seconds
      'role': 'user',
      'content': [
        {
          'type': 'text',
          'text': {
            'value': message,
          },
        },
      ],
    })
    scrollChatContainer()
  }

  function scrollChatContainer() {
    if (chatContainer.value) {
      setTimeout(() => {
        // chatContainer.value?.scrollIntoView({
        //   behavior: 'smooth',
        //   block: 'end',
        // })
        chatContainer.value.scrollTo({
          top: chatContainer.value.scrollHeight,
          behavior: 'smooth',
        })
      }, 100)
    }
  }

  function clearChat() {
    messages.value = []
    chatThreadId.value = null
  }

  return {
    // state
    prompt,
    isLoading: computed(() => isGenerating.value),
    messages,
    isMessagesLoading,
    chatContainer,

    // actions
    sendPrompt,
    setPrompt,
    loadMessages,
    clearChat,
  }
})
