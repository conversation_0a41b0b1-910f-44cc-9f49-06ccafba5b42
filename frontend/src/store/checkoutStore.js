import { defineStore } from 'pinia'
import { useSessionStorage } from '@vueuse/core'
import { computed, ref } from 'vue'
import { toast } from 'vue-sonner'
import ApiService from '@/services/ApiService'
import { processErrors } from '@/utils/errorHandler'
import { useCartStore } from '@/store/cartStore'
import { useAuthStore } from '@/store/auth'
import router from '@/router'

export const useCheckoutStore = defineStore('checkout', () => {
  const authStore = useAuthStore()

  const currentStep = useSessionStorage('checkout_current_step', 1)
  const totalSteps = computed(() => authStore.isAuthenticated ? 2 : 3)
  const completedSteps = useSessionStorage('checkout_completed_steps', [])
  const isLoading = ref(false)

  const checkoutSession = useSessionStorage('otcCheckoutSession', {
    email: '',
    isGuest: true,
    shippingAddress: {
      firstName: '',
      lastName: '',
      phone: '',
      address1: '',
      address2: '',
      city: '',
      state: '',
      zipCode: '',
      country: 'USA',
    },
    paymentMethod: null,
    promoCode: null,
    paymentToken: null, // only for guest checkout
  })

  const serverErrors = ref([])

  // Getters
  const isFirstStep = computed(() => currentStep.value === 1)
  const isLastStep = computed(() => currentStep.value === totalSteps.value)
  const progress = computed(() => (currentStep.value / totalSteps.value) * 100)

  const stepTitle = computed(() => {
    if (authStore.isAuthenticated) {
      // For authenticated users (2 steps: Shipping and Payment)
      if (currentStep.value === 1) {
        return 'Shipping Information'
      } else if (currentStep.value === 2) {
        return 'Payment Information'
      } else {
        return 'Checkout'
      }
    } else {
      // For guest users (3 steps: Account, Shipping, and Payment)
      if (currentStep.value === 1) {
        return 'Account Information'
      } else if (currentStep.value === 2) {
        return 'Shipping Information'
      } else if (currentStep.value === 3) {
        return 'Payment Information'
      } else {
        return 'Checkout'
      }
    }
  })

  function goToNextStep() {
    if (currentStep.value < totalSteps.value) {
      currentStep.value++
    }
  }

  function goToPreviousStep() {
    if (currentStep.value > 1) {
      currentStep.value--
    }
  }

  function setStep(step) {
    if (step >= 1 && step <= totalSteps.value) {
      currentStep.value = step
    }
  }

  function updateEmail(email) {
    checkoutSession.value.email = email
  }

  function setGuestMode(isGuest) {
    checkoutSession.value.isGuest = isGuest
  }

  function updateShippingAddress(address) {
    checkoutSession.value.shippingAddress = {
      ...checkoutSession.value.shippingAddress,
      ...address,
    }
  }

  function setPaymentMethod(methodId) {
    checkoutSession.value.paymentMethod = methodId
  }

  function setPromoCode(code) {
    checkoutSession.value.promoCode = code
  }

  function setServerErrors(errors) {
    serverErrors.value = Array.isArray(errors) ? errors : [errors]
  }

  function clearServerErrors() {
    serverErrors.value = []
  }

  function resetCheckout() {
    currentStep.value = 1
    completedSteps.value = []
    checkoutSession.value = {
      email: '',
      isGuest: true,
      shippingAddress: {
        firstName: '',
        lastName: '',
        phone: '',
        address1: '',
        address2: '',
        city: '',
        state: '',
        zipCode: '',
        country: 'USA',
      },
      paymentMethod: null,
      promoCode: null,
      paymentToken: null,
    }
    serverErrors.value = []
  }

  function setStepCompleted(step) {
    if (!completedSteps.value.includes(step)) {
      completedSteps.value.push(step)
    }
  }

  function isStepCompleted(step) {
    return completedSteps.value.includes(step)
  }

  async function placeOrder() {
    const cartStore = useCartStore()

    try {
      isLoading.value = true
      clearServerErrors()

      // If attempting guest checkout, verify it's enabled
      if (checkoutSession.value.isGuest) {
        try {
          const { data } = await ApiService.get('/setting/guest-checkout')
          if (data.status !== 200 || data.info !== 1) {
            toast.error('Guest checkout is currently disabled. Please create an account or log in.')
            setServerErrors(['Guest checkout is currently disabled. Please create an account or log in.'])
            isLoading.value = false
            router.push({ name: 'otc-checkout-account' })

            return
          }
        } catch (error) {
          console.error('Error checking guest checkout status:', error)

          // Continue with the order, don't block if we can't check the setting
        }
      }

      const products = cartStore.cartItems.map(item => ({
        product_id: item.id,
        qty: item.quantity,
      }))

      // common for both guest and authenticated users
      const orderData = {
        products,
        is_guest_checkout: checkoutSession.value.isGuest ? 1 : 0,
      }

      if (checkoutSession.value.promoCode) {
        orderData.promocode = checkoutSession.value.promoCode
      }

      // for authenticated users
      if (!checkoutSession.value.isGuest) {
        orderData.payment_card_id = checkoutSession.value.paymentMethod
      } else {
        // for guest
        orderData.first_name = checkoutSession.value.shippingAddress.firstName
        orderData.last_name = checkoutSession.value.shippingAddress.lastName
        orderData.email = checkoutSession.value.email
        orderData.phone_number = checkoutSession.value.shippingAddress.phone.replace(/\D/g, '')
        orderData.address_line_1 = checkoutSession.value.shippingAddress.address1
        orderData.address_line_2 = checkoutSession.value.shippingAddress.address2 || ''
        orderData.city = checkoutSession.value.shippingAddress.city
        orderData.state = checkoutSession.value.shippingAddress.state
        orderData.zipcode = checkoutSession.value.shippingAddress.zipCode
        orderData.country = checkoutSession.value.shippingAddress.country
        orderData.payment_token = checkoutSession.value.paymentToken
      }

      const { data } = await ApiService.post('/otc/place-order', orderData)

      if (data.status === 200) {
        // Clear cart after successful order
        cartStore.clearCart()

        // Reset checkout state
        resetCheckout()

        router.push({
          name: 'otc-order-success',
          params: { orderId: data.id },
        })
      } else {
        toast.error(data.message || 'Failed to place order. Please try again.')
        setServerErrors([data.message || 'Failed to place order'])
        isLoading.value = false
      }
    } catch (error) {
      isLoading.value = false
      console.error(error)
      toast.error(processErrors(error)[0] || 'Failed to place order. Please try again.')
      setServerErrors(processErrors(error))
    }
  }

  // Return all state, getters, and actions
  return {
    // State
    currentStep,
    totalSteps,
    checkoutSession,
    serverErrors,
    isLoading,
    completedSteps,

    // Getters
    isFirstStep,
    isLastStep,
    progress,
    stepTitle,

    // Actions
    goToNextStep,
    goToPreviousStep,
    setStep,
    updateEmail,
    setGuestMode,
    updateShippingAddress,
    setPaymentMethod,
    setPromoCode,
    setServerErrors,
    clearServerErrors,
    resetCheckout,
    setStepCompleted,
    isStepCompleted,
    placeOrder,
  }
})
