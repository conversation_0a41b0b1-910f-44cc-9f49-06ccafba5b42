import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { toast } from 'vue-sonner'

export const useCartStore = defineStore('cart', () => {
  const cartItems = ref(JSON.parse(localStorage.getItem('cartItems')) || [])
  const isCartSidebarOpen = ref(false)

  const totalItems = computed(() => {
    return cartItems.value.reduce((total, item) => total + item.quantity, 0)
  })

  const totalPrice = computed(() => {
    return cartItems.value.reduce((total, item) => total + (item.price * item.quantity), 0)
  })

  const addToCart = (product, quantity, planType = 'one_time', buyNow = false) => {
    const existingItemIndex = cartItems.value.findIndex(
      item => item.id === product.id && item.planType === planType,
    )

    if (existingItemIndex !== -1) {
      isCartSidebarOpen.value = true

      // Update quantity if item already exists
      // const newQuantity = cartItems.value[existingItemIndex].quantity + quantity
      // const maxQuantity = product.max_qty_per_order || 10

      // if (newQuantity <= maxQuantity) {
      //   cartItems.value[existingItemIndex].quantity = newQuantity
      //   toast.success('Cart updated successfully')

      //   // isCartSidebarOpen.value = true
      // } else {
      //   toast.error(`You can't add more than ${maxQuantity} of this item`)

      //   return false
      // }
    } else {
      // Add new item
      cartItems.value.push({
        id: product.id,
        slug: product.slug,
        name: product.drug_name,
        image: product.image,
        price: product.product_price_after_discount,
        originalPrice: product.product_price,
        quantity: quantity,
        planType: planType,
        maxQuantity: product.max_qty_per_order || 10,
      })

      if (!buyNow) {
        toast.success('Product added to cart')
        isCartSidebarOpen.value = true
      }
    }

    saveCartToLocalStorage()

    return true
  }

  const updateQuantity = (itemIndex, newQuantity) => {
    if (newQuantity < 1) {
      return removeFromCart(itemIndex)
    }

    const item = cartItems.value[itemIndex]
    if (newQuantity > item.maxQuantity) {
      toast.error(`You can't add more than ${item.maxQuantity} of this item`)

      return false
    }

    cartItems.value[itemIndex].quantity = newQuantity
    saveCartToLocalStorage()

    // toast.success('Cart updated')

    return true
  }

  const removeFromCart = itemIndex => {
    cartItems.value.splice(itemIndex, 1)
    saveCartToLocalStorage()
    toast.success('Item removed from cart')

    return true
  }

  const clearCart = () => {
    cartItems.value = []
    saveCartToLocalStorage()
  }

  const toggleCartSidebar = () => {
    isCartSidebarOpen.value = !isCartSidebarOpen.value
  }

  const openCartSidebar = () => {
    isCartSidebarOpen.value = true
  }

  const closeCartSidebar = () => {
    isCartSidebarOpen.value = false
  }

  const saveCartToLocalStorage = () => {
    localStorage.setItem('cartItems', JSON.stringify(cartItems.value))
  }

  return {
    // State
    cartItems,
    isCartSidebarOpen,

    // Getters
    totalItems,
    totalPrice,

    // Actions
    addToCart,
    updateQuantity,
    removeFromCart,
    clearCart,
    toggleCartSidebar,
    openCartSidebar,
    closeCartSidebar,
  }
})
