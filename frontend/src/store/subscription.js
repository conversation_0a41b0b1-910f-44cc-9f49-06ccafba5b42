import router from '@/router'
import ApiService from '@/services/ApiService'
import { defineStore } from 'pinia'

export const useSubscriptionStore = defineStore('subscriptionStore', () => {
  const btnReactivateLoading = ref(false)

  async function reactivateSubscription(subscriptionId) {
    try {
      btnReactivateLoading.value = true

      const { data } = await ApiService.get(`/reactive-subscription/${subscriptionId}`)

      if (data.status === 200) {
        router.push({
          name: 'user-subscription-reactivation-status',
          params: {
            status: 'success',
            subscriptionId: subscriptionId,
          },
          query: {
            next_refill_date: data.next_refill_date ?? undefined,
            payment_success: data.is_payment_success ?? undefined,
          },
        })
      } else {
        throw data
      }
    } catch (error) {
      router.push({
        name: 'user-subscription-reactivation-status',
        params: {
          status: 'failure',
          subscriptionId: subscriptionId,
        },
        query: {
          r_id: error.subscription_refill_id ?? undefined,
          p_id: error.user_paypal_card_id ?? undefined,
        },
      })
    } finally {
      btnReactivateLoading.value = false
    }
  }

  return {
    btnReactivateLoading,
    reactivateSubscription,
  }
})