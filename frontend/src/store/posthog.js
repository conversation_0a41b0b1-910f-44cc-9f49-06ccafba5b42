import { defineStore } from 'pinia'
import posthog from 'posthog-js'

export const usePosthog = defineStore('usePosthog', () => {
  let $posthog = null

  const { VITE_POSTHOG_API_KEY, VITE_POSTHOG_API_HOST } = import.meta.env

  if (VITE_POSTHOG_API_HOST && VITE_POSTHOG_API_KEY) {
    $posthog = posthog.init(
      VITE_POSTHOG_API_KEY,
      {
        api_host: VITE_POSTHOG_API_HOST,
        capture_pageview: false,
        person_profiles: 'identified_only',
      },
    )
  }

  return { $posthog }
})
