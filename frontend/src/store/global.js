import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useGlobalData = defineStore('globalData', () => {
  // Loading state
  const loadingProgress = ref(false)
  const circularProgress = ref(false)

  // Snackbar state
  const snackbarVisible = ref(false)
  const snackbarMsg = ref(null)
  const snackbarColor = ref('primary')

  const showSnackbar = (msg, color = 'default') => {
    snackbarMsg.value = msg
    snackbarColor.value = color
    snackbarVisible.value = true
  }

  const hideSnackbar = () => {
    snackbarVisible.value = false
  }

  // Back button state
  const _backBtnDisabled = ref(false)

  const backBtnDisabled = computed(() => _backBtnDisabled.value)

  function toggleBackBtnDisabledState(state) {
    _backBtnDisabled.value = state
  }

  return {
    loadingProgress,
    circularProgress,
    snackbarVisible,
    snackbarMsg,
    snackbarColor,
    showSnackbar,
    hideSnackbar,
    backBtnDisabled,
    toggleBackBtnDisabledState,
  }
})
