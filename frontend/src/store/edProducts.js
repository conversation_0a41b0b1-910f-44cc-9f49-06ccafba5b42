import router from '@/router'
import ApiService from '@/services/ApiService'
import { processErrors } from '@/utils/errorHandler'
import { defineStore } from 'pinia'
import { computed, ref } from 'vue'

export const useEdProductsStore = defineStore('useEdProductsStore', () => {
  const _products = ref([])
  const _loading = ref(false)
  const _errors = ref(null)
  const _hasRecommendations = ref(false)

  const products = computed(() => _products.value)
  const loading = computed(() => _loading.value)
  const errors = computed(() => _errors.value)

  async function fetchProducts() {
    try {
      _loading.value = true

      const { data } = await ApiService.get('/ed-products')

      if (data.status === 200) {
        if (data.productData.length === 0) {
          await router.push({ name: 'ed-treatment-not-available' })
        }
        _products.value = data.productData
        _hasRecommendations.value = data.is_recommended_products
      } else {
        if (data.status === 400 && data.treatment_not_available) {
          router.push({ name: 'ed-treatment-not-available' })
        } else {
          _errors.value = processErrors(data)
          router.push({ name: 'error-something-wrong', query: { visit: 'ed' } })
        }
      }
    } catch (error) {
      _errors.value = processErrors(error)
      router.push({ name: 'error-something-wrong', query: { visit: 'ed' } })
    } finally {
      _loading.value = false
    }
  }

  return {
    products,
    loading,
    errors,
    fetchProducts,
    hasRecommendations: _hasRecommendations,
  }
})
