import router from '@/router'
import ApiService from '@/services/ApiService'
import { processErrors } from '@/utils/errorHandler'
import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useAffiliateUserDetailsStore = defineStore('affiliateUserDetails', () => {
  const userData = ref({})
  const loading = ref(false)
  const errors = ref([])
  const profileUpdated = ref(false)

  // Actions
  const fetchUserDetails = async userId => {
    loading.value = true
    userData.value = {}
    try {
      const { data } = await ApiService.get(`/admin/affiliate-user-details/${userId}`)
      if (data.status === 200) {
        userData.value = data.userDetails
      } else {
        handleError(data)
      }
    } catch (error) {
      handleError(error)
    } finally {
      loading.value = false
    }
  }

  const handleError = error => {
    errors.value = processErrors(error)
    router.back()
  }

  const setProfileUpdated = () => {
    profileUpdated.value = true
    setTimeout(() => {
      profileUpdated.value = false
    }, 3000)
  }

  return {
    userData,
    loading,
    errors,
    profileUpdated,

    fetchUserDetails,
    setProfileUpdated,
  }
})
