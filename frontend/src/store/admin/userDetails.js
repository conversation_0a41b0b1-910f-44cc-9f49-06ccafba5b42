import router from '@/router'
import ApiService from '@/services/ApiService'
import { processErrors } from '@/utils/errorHandler'
import { defineStore } from 'pinia'
import { computed, ref } from 'vue'

export const useUserDetailsStore = defineStore('useUserDetailsStore', () => {
  const _userData = ref({})
  const _loading = ref(false)
  const _errors = ref([])
  const isSMSNotificationsEnabled = ref(false)

  const userData = computed(() => _userData.value)
  const loading = computed(() => _loading.value)
  const errors = computed(() => _errors.value)

  async function fetchUserDetails(userId) {
    try {
      _loading.value = true
      _userData.value = {}

      const { data } = await ApiService.get(`/admin/user-details/${userId}`)

      if (data.status === 200) {
        _userData.value = data.userDetails
        isSMSNotificationsEnabled.value = Boolean(data.userDetails?.is_sms_notification_allowed)
      } else {
        _errors.value = processErrors(data)
        router.back()
      }
    } catch (error) {
      console.log(error)
      _errors.value = processErrors(error)
      router.back()
    } finally {
      _loading.value = false
    }
  }

  const _profileUpdated = ref(false)

  const profileUpdated = computed(() => _profileUpdated.value)

  function setProfileUpdated() {
    _profileUpdated.value = true
    setTimeout(() => {
      _profileUpdated.value = false
    }, 3000)
  }

  return { userData, loading, errors, fetchUserDetails, profileUpdated, setProfileUpdated, isSMSNotificationsEnabled }
})
