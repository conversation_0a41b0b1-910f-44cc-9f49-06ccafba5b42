import ApiService from '@/services/ApiService'
import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useAllProductsStore = defineStore('useAllProductsStore', () => {
  const products = ref([])
  const loading = ref(false)
  const errors = ref(null)
  const otcCategories = ref([])

  async function fetchProducts() {
    try {
      loading.value = true

      const { data } = await ApiService.get('/admin/all-product-list')

      if (data.status === 200) {
        products.value = data.productLists || []
      } else {
        throw data
      }
    } catch (error) {
      errors.value = error
    } finally {
      loading.value = false
    }
  }

  async function fetchCategories() {
    try {
      const { data } = await ApiService.get('/admin/main/sub/category/all')

      if (data.status === 200) {
        otcCategories.value = data.allCategories || []
      } else {
        throw data
      }
    } catch (error) {
      console.error(error)
      errors.value = error
    }
  }

  return {
    // state
    products,
    otcCategories,
    loading,
    errors,

    // actions
    fetchProducts,
    fetchCategories,
  }
})
