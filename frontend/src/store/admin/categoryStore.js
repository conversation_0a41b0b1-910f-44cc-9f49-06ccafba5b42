import { defineStore } from 'pinia'
import { ref } from 'vue'
import ApiService from '@/services/ApiService'
import { useGlobalData } from '../global'
import { processErrors } from '@/utils/errorHandler'

export const useCategoryStore = defineStore('productCategory', () => {
  const { showSnackbar } = useGlobalData()

  const parentCategories = ref([])

  async function fetchParentCategories() {
    try {
      const { data } = await ApiService.get('/admin/category/all')

      if (data.status === 200) {
        parentCategories.value = data.mainCategories || []
      } else {
        throw data
      }
    } catch (error) {
      console.error(error)
      showSnackbar(processErrors(error)[0], 'error')
    }
  }

  return {
    // state
    parentCategories,

    // actions
    fetchParentCategories,
  }
})
