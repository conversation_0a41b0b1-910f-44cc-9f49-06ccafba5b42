import { defineStore } from 'pinia'
import { ref } from 'vue'
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { processErrors } from '@/utils/errorHandler'

export const useOtcProductStore = defineStore('otcProductStore', () => {
  const { showSnackbar } = useGlobalData()

  const loading = ref(false)
  const errors = ref([])
  const categories = ref([])
  const productDetails = ref(null)
  const productLoading = ref(false)

  async function fetchCategories() {
    try {
      const { data } = await ApiService.get('/admin/main/sub/category/all')

      if (data.status === 200) {
        categories.value = data.allCategories || []
      } else {
        throw data
      }
    } catch (error) {
      console.error(error)
      showSnackbar(processErrors(error)[0], 'error')
    }
  }

  const fetchProductDetails = async productId => {
    try {
      productLoading.value = true
      errors.value = []

      const { data } = await ApiService.get(`/admin/otc/view-product/${productId}`)

      if (data.status === 200) {
        productDetails.value = data.productDetails || null

        return data.productDetails
      } else {
        errors.value = processErrors(data)
        showSnackbar(errors.value[0], 'error')

        return null
      }
    } catch (error) {
      errors.value = processErrors(error)
      showSnackbar(errors.value[0], 'error')

      return null
    } finally {
      productLoading.value = false
    }
  }

  const addProduct = async formData => {
    try {
      loading.value = true
      errors.value = []

      const { data } = await ApiService.post('/admin/otc/add-product', formData)

      if (data.status === 200) {
        showSnackbar(data.message || 'Product added successfully')

        return data.id
      } else {
        errors.value = processErrors(data)
        showSnackbar(errors.value[0], 'error')

        return null
      }
    } catch (error) {
      errors.value = processErrors(error)
      showSnackbar(errors.value[0], 'error')

      return null
    } finally {
      loading.value = false
    }
  }

  const updateProductDescription = async payload => {
    try {
      loading.value = true
      errors.value = []

      const { data } = await ApiService.post('/admin/otc/update-product-description', payload)

      if (data.status === 200) {
        showSnackbar(data.message || 'Product description updated successfully')

        return true
      } else {
        errors.value = processErrors(data)
        showSnackbar(errors.value[0], 'error')

        return false
      }
    } catch (error) {
      errors.value = processErrors(error)
      showSnackbar(errors.value[0], 'error')

      return false
    } finally {
      loading.value = false
    }
  }

  const updateProductPrice = async payload => {
    try {
      loading.value = true
      errors.value = []

      const { data } = await ApiService.post('/admin/otc/update-product-price', payload)

      if (data.status === 200) {
        showSnackbar(data.message || 'Product price updated successfully')

        return true
      } else {
        errors.value = processErrors(data)
        showSnackbar(errors.value[0], 'error')

        return false
      }
    } catch (error) {
      errors.value = processErrors(error)
      showSnackbar(errors.value[0], 'error')

      return false
    } finally {
      loading.value = false
    }
  }

  const editProduct = async formData => {
    try {
      loading.value = true
      errors.value = []

      const { data } = await ApiService.post('/admin/otc/edit-product', formData)

      if (data.status === 200) {
        showSnackbar(data.message || 'Product updated successfully')

        return true
      } else {
        errors.value = processErrors(data)
        showSnackbar(errors.value[0], 'error')

        return false
      }
    } catch (error) {
      errors.value = processErrors(error)
      showSnackbar(errors.value[0], 'error')

      return false
    } finally {
      loading.value = false
    }
  }

  const updateProductImage = async formData => {
    try {
      loading.value = true
      errors.value = []

      const { data } = await ApiService.post('/admin/otc/update-product-image', formData)

      if (data.status === 200) {
        showSnackbar(data.message || 'Product image updated successfully')

        return { success: true, url: data.image }
      } else {
        errors.value = processErrors(data)
        showSnackbar(errors.value[0], 'error')

        return { success: false }
      }
    } catch (error) {
      errors.value = processErrors(error)
      showSnackbar(errors.value[0], 'error')

      return { success: false }
    } finally {
      loading.value = false
    }
  }

  return {
    // State
    loading,
    errors,
    productDetails,
    productLoading,
    categories,

    // Actions
    fetchCategories,
    fetchProductDetails,
    addProduct,
    updateProductDescription,
    updateProductPrice,
    editProduct,
    updateProductImage,
  }
})
