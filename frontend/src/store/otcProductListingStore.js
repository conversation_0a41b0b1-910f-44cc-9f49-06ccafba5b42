import { defineStore } from 'pinia'
import { ref } from 'vue'
import ApiService from '@/services/ApiService'
import { processErrors } from '@/utils/errorHandler'
import { toast } from 'vue-sonner'

export const useOtcProductsStore = defineStore('otcProducts', () => {
  const categories = ref([])
  const categoriesLoading = ref(false)
  const products = ref([])
  const loading = ref(false)
  const productDetails = ref(null)
  const productLoading = ref(false)
  const errors = ref([])

  const pagination = ref({
    currentPage: 1,
    perPage: 12,
    totalPages: 1,
    totalRecords: 0,
  })

  const fetchCategories = async () => {
    try {
      categoriesLoading.value = true

      const { data } = await ApiService.get('/get-categories')

      if (data.status === 200) {
        categories.value = [
          { name: 'All', slug: null, subCategories: [] },
          ...data.mainCategories.map(category => ({
            name: category.name,
            slug: category.slug,
            subCategories: category.subCategories.map(subCategory => ({
              name: subCategory.name,
              slug: subCategory.slug,
            })),
          })),
        ]
      } else {
        throw data
      }
    } catch (error) {
      console.error(error)
      errors.value = processErrors(error)
      toast.error(errors.value[0])
    } finally {
      categoriesLoading.value = false
    }
  }

  const fetchProducts = async (params = {}) => {
    try {
      loading.value = true

      const { data } = await ApiService.post('/get-otc-products', {
        search: params.search || '',
        per_page: params.perPage || pagination.value.perPage,
        page: params.page || pagination.value.currentPage,
        main_category_slug: params.categorySlug || '',
        sub_category_slug: params.subCategorySlug || '',
      })

      if (data.status === 200) {
        products.value = data.products.records

        pagination.value = {
          currentPage: data.products.current_page,
          perPage: data.products.per_page,
          totalPages: data.products.totalPage,
          totalRecords: data.products.totalRecords,
        }
      } else {
        throw data
      }
    } catch (error) {
      console.error(error)
      errors.value = processErrors(error)
      toast.error(errors.value[0])
    } finally {
      loading.value = false
    }
  }

  const fetchProductDetails = async productId => {
    try {
      productLoading.value = true
      errors.value = []

      const { data } = await ApiService.get(`/product-details/${productId}`)

      if (data.status === 200) {
        productDetails.value = data.productDetails

        return data.productDetails
      } else {
        throw data
      }
    } catch (error) {
      console.error(error)
      errors.value = processErrors(error)
      toast.error(errors.value[0] || 'Failed to load product details')

      return null
    } finally {
      productLoading.value = false
    }
  }

  const clearProductDetails = () => {
    productDetails.value = null
  }

  return {
    // State
    categories,
    categoriesLoading,
    products,
    loading,
    errors,
    pagination,
    productDetails,
    productLoading,

    // Actions
    fetchCategories,
    fetchProducts,
    fetchProductDetails,
    clearProductDetails,
  }
})
