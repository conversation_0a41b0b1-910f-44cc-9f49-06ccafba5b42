import { destroyToken, getToken, setToken } from '@/services/JwtService'
import {
  destroyUserData,
  getUserData,
  setUserData,
} from '@/services/UserService'
import { defineStore } from 'pinia'
import { computed, ref } from 'vue'

export const useAuthStore = defineStore('auth', () => {
  // state
  const _isAuthenticated = ref(!!getToken())
  const _userData = ref(getUserData() || null)

  // getters
  const isAuthenticated = computed(() => _isAuthenticated.value)
  const userData = computed(() => _userData.value)

  // actions
  function setAuth(data) {
    _isAuthenticated.value = true
    setToken(data.accessToken)
    _userData.value = data.userData
    setUserData(data.userData)
  }

  function purgeAuth() {
    _isAuthenticated.value = false
    destroyToken()
    _userData.value = null
    destroyUserData()

    // clear storage
    localStorage.clear()
    sessionStorage.clear()
  }

  function updateUserData(userData) {
    setUserData(userData)
    _userData.value = getUserData()
  }

  return {
    isAuthenticated,
    userData,
    setAuth,
    purgeAuth,
    updateUserData,
  }
})
