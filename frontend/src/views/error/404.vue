<script setup>
import useBackNavigationOnError from '@/composables/useBackNavigationOnError'
import { useGenerateImageVariant } from '@core/composable/useGenerateImageVariant'
import misc404 from '@images/pages/404.png'
import miscMaskDark from '@images/pages/misc-mask-dark.png'
import miscMaskLight from '@images/pages/misc-mask-light.png'

const authThemeMask = useGenerateImageVariant(miscMaskLight, miscMaskDark)

const { handleClick, loading } = useBackNavigationOnError()
</script>

<template>
  <div class="misc-wrapper">
    <ErrorHeader
      error-title="Page Not Found :("
      error-description="We couldn't find the page you are looking for."
    />
    <VBtn
      class="mb-12"
      :loading="loading"
      @click="handleClick"
    >
      Back to Home
    </VBtn>

    <!-- 👉 Image -->
    <div class="misc-avatar w-100 text-center">
      <VImg
        :src="misc404"
        alt="Coming Soon"
        :max-width="200"
        class="mx-auto"
      />
    </div>

    <VImg
      :src="authThemeMask"
      class="misc-footer-img d-none d-md-block"
    />
  </div>
</template>

<style lang="scss">
@use "@core/scss/template/pages/misc.scss";
</style>
