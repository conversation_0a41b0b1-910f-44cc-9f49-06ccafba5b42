<script setup>
import useBackNavigationOnError from '@/composables/useBackNavigationOnError'
import Header from '@/views/user/components/Header.vue'
import { IconServerOff } from '@tabler/icons-vue'

const { handleClick, loading } = useBackNavigationOnError()
</script>

<template>
  <div>
    <Header />
    <div class="grid place-items-center">
      <div class="w-full sm:max-w-lg grid place-items-center h-[calc(100vh-15rem)]">
        <div class="px-4 py-12 sm:px-20 flex flex-col items-center">
          <IconServerOff
            class="w-28 mb-4 text-gray-800"
            stroke-width="1.5"
          />
          <div class="mb-6 text-center">
            <h1 class="text-2xl sm:text-3xl text-gray-800 font-semibold mb-4">
              Oops, an error occurred.
            </h1>
            <p class="text-base sm:text-lg text-gray-600">
              Please try again later. We're actively working to resolve the issue quickly.
            </p>
          </div>
          <TwButton
            class="px-8"
            :loading="loading"
            @click="handleClick"
          >
            Back to Home
          </TwButton>
        </div>
      </div>
    </div>
  </div>
</template>
