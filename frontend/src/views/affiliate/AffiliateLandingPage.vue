<script setup>
import { ref } from 'vue'
import Footer from '@/views/user/components/Footer.vue'
import { IconCurrencyDollar, IconChartBar, IconCalendarCheck, IconChevronDown, IconRocket, IconUsers, IconBrandTelegram } from '@tabler/icons-vue'
import { scrollIntoView } from '@/utils/helpers'

const frontendUrl = computed(() => import.meta.env.VITE_MARKETING_SITE_URL)

const features = [
  {
    name: 'Lifetime Commission',
    description: 'Receive up to 30% recurring commission for each sale you bring in.',
    icon: IconCurrencyDollar,
    color: 'bg-emerald-50 text-emerald-600',
  },
  {
    name: '60-Day Cookie Life',
    description: 'Earn credit for referrals up to 60 days after they click your link.',
    icon: IconCalendarCheck,
    color: 'bg-blue-50 text-blue-600',
  },
  {
    name: 'High Conversions',
    description: 'Benefit from high conversion rates and customer loyalty through our superior service.',
    icon: IconChartBar,
    color: 'bg-purple-50 text-purple-600',
  },
]

const steps = [
  {
    title: 'Sign Up',
    description: 'Create your free affiliate account in just a few minutes.',
    icon: IconRocket,
  },
  {
    title: 'Get Your Links',
    description: 'Access your unique affiliate links and promotional materials.',
    icon: IconBrandTelegram,
  },
  {
    title: 'Promote',
    description: 'Share your links through your website, social media, or email marketing.',
    icon: IconUsers,
  },
  {
    title: 'Earn',
    description: 'Start earning commissions on every qualified sale you generate.',
    icon: IconCurrencyDollar,
  },
]

const faqs = ref([
  {
    question: 'How does the Associates Program work?',
    answer: 'You can share products and available treatments on White Label Rx with your audience and earn money on qualifying purchases and customer actions.',
    isOpen: false,
  },
  {
    question: 'How do I qualify for this program?',
    answer: 'Bloggers, publishers and content creators with a qualifying website or mobile app can participate in this program.',
    isOpen: false,
  },
  {
    question: 'What products can I promote?',
    answer: 'You can promote all treatments we offer.',
    isOpen: false,
  },
  {
    question: 'How do I earn in this program?',
    answer: 'You earn from qualifying purchases through the traffic you drive to White Label Rx. Advertising fees for qualifying purchases differ based on treatment category.',
    isOpen: false,
  },
  {
    question: 'How do I join this program?',
    answer: `
      <p class="mb-1">
        You can join White Label Rx affiliate program by clicking <a href="/affiliate/signup" class="font-medium underline text-black hover:no-underline">here</a>.
      </p>
      <p>
        We will review your application and approve it if you meet the qualifying criteria.
      </p>`,
    isOpen: false,
  },
])
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white/80 backdrop-blur-md fixed w-full z-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex">
            <div class="flex-shrink-0 flex items-center">
              <a :href="frontendUrl ?? '#'">
                <AppLogo />
              </a>
            </div>
          </div>
          <div class="flex">
            <div class="flex-shrink-0 flex items-center space-x-4">
              <RouterLink
                :to="{ name: 'affiliate-login' }"
                class="text-gray-600 hover:text-black transition"
              >
                Login
              </RouterLink>
              <RouterLink
                :to="{ name: 'affiliate-signup' }"
                class="bg-black text-white px-6 py-2 rounded-full hover:bg-gray-800 transition"
              >
                Join Now
              </RouterLink>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <!-- Hero Section -->
    <section class="relative overflow-hidden pt-32 pb-24">
      <div class="absolute inset-0 bg-gradient-to-br from-yellow-100 via-yellow-200 to-[#ffef08] opacity-60"></div>
      <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
          <h1 class="text-5xl md:text-7xl font-bold bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 bg-clip-text text-transparent">
            Turn Your Influence Into Income
          </h1>
          <p class="mt-6 text-xl text-gray-600 max-w-3xl mx-auto">
            Join our network of successful affiliates and start earning commissions by promoting trusted healthcare solutions.
          </p>
          <div class="mt-10 flex justify-center gap-4">
            <RouterLink
              :to="{ name: 'affiliate-signup' }"
              class="inline-flex items-center px-8 py-4 border-2 !border-black text-lg font-medium rounded-full text-white bg-black hover:bg-gray-800 transition duration-300 ease-in-out transform hover:scale-105"
            >
              Get Started
            </RouterLink>
            <a
              href="#learn-more"
              class="inline-flex items-center px-8 py-4 border-2 !border-black text-lg font-medium rounded-full text-black hover:bg-black hover:text-white transition duration-300"
              @click.prevent="scrollIntoView('#learn-more')"
            >
              Learn More
            </a>
          </div>
        </div>
      </div>
    </section>

    <!-- Program Overview -->
    <section
      id="learn-more"
      class="py-24 bg-white"
    >
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-4xl font-bold text-black">
            Why Partner With Us?
          </h2>
          <p class="mt-4 text-xl text-gray-600">
            Join a program designed for your success
          </p>
        </div>
        <div class="grid gap-8 grid-cols-1 md:grid-cols-3">
          <div
            v-for="feature in features"
            :key="feature.name"
            class="group relative rounded-2xl p-8 hover:shadow-lg transition duration-300 border !border-gray-200"
          >
            <div
              :class="feature.color"
              class="inline-flex p-3 rounded-lg"
            >
              <component
                :is="feature.icon"
                class="h-8 w-8"
                stroke-width="1.5"
              />
            </div>
            <h3 class="mt-6 text-xl font-semibold text-black transition">
              {{ feature.name }}
            </h3>
            <p class="mt-4 text-gray-600">
              {{ feature.description }}
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- How it Works -->
    <section class="py-24 bg-gray-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-4xl font-bold text-black">
            How it Works
          </h2>
          <p class="mt-4 text-xl text-gray-600">
            Start earning in four simple steps
          </p>
        </div>
        <div class="grid gap-8 grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
          <div
            v-for="(step, index) in steps"
            :key="index"
            class="relative"
          >
            <div class="bg-white rounded-2xl p-8 h-full hover:shadow-lg transition duration-300">
              <div class="absolute -top-4 left-8 bg-[#ffef08] w-10 h-10 rounded-full flex items-center justify-center text-black font-bold">
                {{ index + 1 }}
              </div>
              <component
                :is="step.icon"
                class="h-10 w-10 text-gray-800 mb-6"
                stroke-width="1.5"
              />
              <h3 class="text-xl font-semibold text-black mb-4">
                {{ step.title }}
              </h3>
              <p class="text-gray-600">
                {{ step.description }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- FAQs -->
    <section class="py-24 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-4xl font-bold text-black">
            Frequently Asked Questions
          </h2>
          <p class="mt-4 text-xl text-gray-600">
            Everything you need to know about our affiliate program
          </p>
        </div>
        <div class="max-w-3xl mx-auto space-y-4">
          <div
            v-for="faq in faqs"
            :key="faq.question"
            class="bg-gray-50 rounded-xl overflow-hidden transition-all duration-300"
            :class="{ 'shadow-md': faq.isOpen }"
          >
            <button
              class="flex justify-between items-center w-full px-6 py-4 text-left focus:outline-none"
              @click="faq.isOpen = !faq.isOpen"
            >
              <span class="text-lg font-medium text-black">{{ faq.question }}</span>
              <IconChevronDown
                :class="{ 'transform rotate-180': faq.isOpen }"
                class="w-6 h-6 text-gray-500 transition-transform duration-200"
                stroke-width="2"
              />
            </button>
            <div
              v-show="faq.isOpen"
              class="px-6 pb-4 text-gray-600"
            >
              <div v-html="faq.answer"></div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Call to Action -->
    <section class="relative py-24 bg-black">
      <div class="absolute inset-0 bg-gradient-to-br from-[#ffef08]/20 via-transparent to-transparent"></div>
      <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
          <h2 class="text-4xl font-bold text-white mb-8">
            Ready to Start Your Journey?
          </h2>
          <RouterLink
            :to="{ name: 'affiliate-signup' }"
            class="inline-flex items-center px-8 py-4 text-lg font-medium rounded-full text-black bg-[#ffef08] hover:bg-yellow-300 transition duration-300 transform hover:scale-105"
          >
            Join Our Affiliate Program
          </RouterLink>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <Footer />
  </div>
</template>
