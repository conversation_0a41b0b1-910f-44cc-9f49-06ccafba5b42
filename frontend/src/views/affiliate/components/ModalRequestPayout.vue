<script setup>
import { computed, ref } from 'vue'
import { isEmpty, isEmptyObject, isNullOrUndefined } from '@/@core/utils'
import { toast } from 'vue-sonner'
import ApiService from '@/services/ApiService'
import { processErrors } from '@/utils/errorHandler'
import { ErrorMessage, Field, Form as VForm } from 'vee-validate'
import * as yup from 'yup'
import AlertError from '@/views/user/components/AlertError.vue'
import { vMaska } from 'maska'
import { formatCurrency } from '@/utils/helpers'
import { IconCheck } from '@tabler/icons-vue'

const props = defineProps({
  closeOnClickOutside: {
    type: Boolean,
    default: true,
  },
  maxAmount: {
    type: Number,
    required: true,
  },
})

const emit = defineEmits(['reload'])

const isOpen = ref(false)
const isLoading = ref(false)
const inputErrors = ref({})
const serverErrors = ref([])
const withdrawType = ref('full') // full, custom
const withdrawAmount = ref(undefined)
const maxAmount = computed(() => props.maxAmount)

function open() {
  isOpen.value = true
}

function close() {
  isOpen.value = false
  withdrawType.value = 'full'
  withdrawAmount.value = undefined
  serverErrors.value = []
  inputErrors.value = {}
  isLoading.value = false
}

function handleOverlayClick(event) {
  if (props.closeOnClickOutside && event.target === event.currentTarget) {
    close()
  }
}

const validationSchema = yup.object().shape({
  withdraw_type: yup.string()
    .required('Withdraw type is required')
    .oneOf(['full', 'custom'], 'Invalid withdraw type'),
  withdraw_amount: yup.string().nullable()
    .when('withdraw_type', {
      is: 'custom',
      then: schema => schema.required('Withdraw amount is required'),
      otherwise: schema => schema.nullable(),
    }),
})

async function handleFormSubmit() {
  try {
    isLoading.value = true
    inputErrors.value = {}
    serverErrors.value = []

    const postData = {
      withdraw_type: withdrawType.value,
    }

    if (withdrawType.value === 'custom') {
      postData['withdraw_amount'] = withdrawAmount.value
    }

    const { data } = await ApiService.post('/affiliate/withdraw-request', postData)

    if (data.status === 200) {
      toast.success(data.message)
      close()
      emit('reload')
    } else {
      isLoading.value = false
      if (data.errors) {
        inputErrors.value = data.errors
      } else {
        serverErrors.value = processErrors(data)
      }
    }
  } catch (error) {
    isLoading.value = false
    serverErrors.value = processErrors(error)
    console.error(error)
  }
}

defineExpose({ open, close })
</script>

<template>
  <div
    v-if="isOpen"
    class="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center overflow-y-auto overflow-x-hidden top-0 right-0 left-0 z-50 w-full md:inset-0 h-full max-h-full"
    @click="handleOverlayClick"
  >
    <div class="relative p-4 w-full max-w-2xl max-h-full">
      <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
        <div class="px-5 py-8 md:p-10">
          <h2 class="text-center text-2xl md:text:3xl font-bold leading-tight text-black mb-5">
            Request Payout
          </h2>

          <AlertError
            v-if="isEmptyObject(inputErrors) && !isEmpty(serverErrors)"
            title="Error!"
            :errors="serverErrors"
          />

          <VForm
            autocomplete="off"
            :validation-schema="validationSchema"
            @submit="handleFormSubmit"
          >
            <div class="mb-6">
              <label class="font-medium text-gray-500 duration-300 flex items-center mb-3">
                How much would you like to withdraw?
              </label>

              <ul class="grid w-full gap-4">
                <li>
                  <Field
                    id="type-full"
                    v-model="withdrawType"
                    type="radio"
                    name="withdraw_type"
                    value="full"
                    class="hidden peer"
                    required
                  />
                  <label
                    for="type-full"
                    class="inline-flex items-center justify-between w-full p-4 text-gray-500 bg-white border-2 !border-gray-200 rounded-lg cursor-pointer peer-checked:!border-black peer-checked:text-black hover:text-gray-600 hover:bg-gray-100"
                  >
                    <div>
                      <div class="text-base font-medium">Full Amount</div>
                      <div class="text-sm font-medium">{{ formatCurrency(maxAmount) }}</div>
                    </div>
                    <div>
                      <span
                        class="w-4 h-4 border-2 rounded-sm flex justify-center items-center"
                        :class="[
                          withdrawType === 'full' ? 'border-transparent bg-black' : 'border-gray-400'
                        ]"
                      >
                        <IconCheck
                          class="h-5 w-5 z-50"
                          :stroke-width="withdrawType === 'full' ? 4 : 1"
                          :class="[
                            withdrawType === 'full' ? 'text-white' : 'text-gray-900'
                          ]"
                        />
                      </span>
                    </div>
                  </label>
                </li>
                <li>
                  <Field
                    id="type-custom"
                    v-model="withdrawType"
                    type="radio"
                    name="withdraw_type"
                    value="custom"
                    class="hidden peer"
                  />
                  <label
                    for="type-custom"
                    class="inline-flex items-center justify-between w-full p-4 text-gray-500 bg-white border-2 !border-gray-200 rounded-lg cursor-pointer peer-checked:!border-black peer-checked:text-black hover:text-gray-600 hover:bg-gray-100"
                  >
                    <div class="text-base font-medium">Custom Amount</div>
                    <div>
                      <span
                        class="w-4 h-4 border-2 rounded-sm flex justify-center items-center"
                        :class="[
                          withdrawType === 'custom' ? 'border-transparent bg-black' : 'border-gray-400'
                        ]"
                      >
                        <IconCheck
                          class="h-5 w-5 z-50"
                          :stroke-width="withdrawType === 'custom' ? 4 : 1"
                          :class="[
                            withdrawType === 'custom' ? 'text-white' : 'text-gray-900'
                          ]"
                        />
                      </span>
                    </div>
                  </label>
                </li>
              </ul>
              <ErrorMessage
                name="withdraw_type"
                class="text-red-500 text-sm ms-5"
              />
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.withdraw_type)"
                class="text-red-500 text-sm ms-5"
              >
                {{ inputErrors.withdraw_type[0] }}
              </p>
            </div>

            <div
              v-if="withdrawType === 'custom'"
              class="relative"
            >
              <label
                for="withdraw_amount"
                class="block text-sm font-semibold text-gray-600 mb-1"
              >Withdraw amount</label>
              <span class="text-gray-800 absolute text-lg font-medium top-[34px] left-[12px]">$</span>
              <Field
                id="withdraw_amount"
                v-model="withdrawAmount"
                v-maska
                name="withdraw_amount"
                class="block w-full px-5 py-3 ps-7 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                placeholder=" "
                data-maska="0.99"
                data-maska-tokens="0:\d:multiple|9:\d:optional"
                validate-on-input
                required
              />
              <ErrorMessage
                name="withdraw_amount"
                class="text-red-500 text-sm"
              />
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.withdraw_amount)"
                class="text-red-500 text-sm"
              >
                {{ inputErrors.withdraw_amount[0] }}
              </p>
            </div>

            <div class="flex justify-center gap-3 mt-6">
              <TwButton
                type="button"
                class="w-48"
                variant="secondary"
                :disabled="isLoading"
                @click="close"
              >
                Cancel
              </TwButton>
              <TwButton
                type="submit"
                :loading="isLoading"
                class="w-48"
              >
                Request
              </TwButton>
            </div>
          </VForm>
        </div>
      </div>
    </div>
  </div>
</template>
