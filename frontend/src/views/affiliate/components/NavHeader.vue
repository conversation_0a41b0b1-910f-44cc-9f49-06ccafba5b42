<script setup>
import { ref } from 'vue'
import { Icon<PERSON>elp, IconMenu2, IconX } from '@tabler/icons-vue'
import useLogout from '@/composables/useLogout'
import { useRoute } from 'vue-router'
import ModalHelp from '@/views/user/components/ModalHelp.vue'
import ModalConfirmLogout from '@/views/user/components/ModalConfirmLogout.vue'

const { handleAffiliateLogout } = useLogout()
const route = useRoute()

const navLinks = [
  {
    name: 'Home',
    path: '/affiliate/home',
  },
  {
    name: 'Wallet',
    path: '/affiliate/wallet',
  },
  {
    name: 'Profile',
    path: '/affiliate/profile',
  },
]

const isMenuOpen = ref(false)

const toggleMenu = () => {
  isMenuOpen.value = !isMenuOpen.value
}

const modalHelpRef = ref(null)
const modalConfirmLogoutRef = ref(null)
const frontendUrl = computed(() => import.meta.env.VITE_MARKETING_SITE_URL)
</script>

<template>
  <div class="fixed w-full bg-zinc-900 z-10">
    <div class="mx-auto flex max-w-7xl items-center justify-between px-4 py-2 sm:px-6 lg:px-8">
      <div class="px-4">
        <a :href="frontendUrl ?? '#'">
          <AppLogo variant="white" />
        </a>
      </div>
      <div class="hidden grow items-start lg:flex">
        <ul class="ms-12 inline-flex space-x-8">
          <li
            v-for="link in navLinks"
            :key="link.name"
          >
            <RouterLink
              :to="link.path"
              class="text-sm font-semibold text-zinc-400 hover:text-zinc-200 pb-5"
              :class="{ 'border-b-[5px] border-[#ffef08] text-zinc-100': route.path === link.path || route.path.includes(link.path) }"
            >
              {{ link.name }}
            </RouterLink>
          </li>
        </ul>
      </div>
      <div class="hidden lg:block">
        <div class="flex align-center gap-4">
          <button
            type="button"
            class="rounded-sm px-4 py-2 text-sm font-semibold text-white border-2 border-solid !border-gray-300 hover:bg-zinc-600 transition-all duration-300"
            @click="modalConfirmLogoutRef.open()"
          >
            Logout
          </button>
          <button
            v-tooltip.bottom="'Help'"
            type="button"
            @click="modalHelpRef.open()"
          >
            <IconHelp
              class="h-6 w-6 cursor-pointer text-white"
              stroke-width="2"
            />
          </button>
        </div>
      </div>
      <div class="lg:hidden">
        <div class="flex align-center gap-4">
          <button
            v-tooltip.bottom="'Help'"
            type="button"
            @click="modalHelpRef.open()"
          >
            <IconHelp
              class="h-6 w-6 cursor-pointer text-white"
              stroke-width="2"
            />
          </button>
          <IconMenu2
            class="h-6 w-6 cursor-pointer text-white"
            stroke-width="2"
            @click="toggleMenu"
          />
        </div>
      </div>
      <transition name="fade">
        <div
          v-if="isMenuOpen"
          class="absolute inset-x-0 top-0 z-50 origin-top-right transform p-2 transition lg:hidden"
        >
          <div class="divide-y-2 divide-gray-50 rounded-lg bg-zinc-900 shadow-lg ring-1 ring-black ring-opacity-5">
            <div class="px-5 pb-6 pt-5">
              <div class="flex items-center justify-between">
                <div class="inline-flex items-center space-x-2">
                  <a :href="frontendUrl ?? '#'">
                    <AppLogo variant="white" />
                  </a>
                </div>
                <div class="-mr-2">
                  <button
                    type="button"
                    class="inline-flex items-center justify-center rounded-md p-2 text-gray-400 hover:bg-zinc-800 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-zinc-800"
                    @click="toggleMenu"
                  >
                    <span class="sr-only">Close menu</span>
                    <IconX
                      class="h-6 w-6 text-white"
                      stroke-width="2"
                      aria-hidden="true"
                    />
                  </button>
                </div>
              </div>
              <div class="my-6">
                <nav class="grid">
                  <RouterLink
                    v-for="link in navLinks"
                    :key="link.name"
                    :to="link.path"
                    class="flex items-center p-3 text-sm font-semibold text-zinc-400 hover:text-zinc-200 hover:bg-zinc-800"
                    :class="{ 'border-b-[5px] border-[#ffef08] text-zinc-100': route.path === link.path || route.path.includes(link.path) }"
                    @click="isMenuOpen = false"
                  >
                    <span class="ms-3 text-base font-semibold">
                      {{ link.name }}
                    </span>
                  </RouterLink>
                </nav>
              </div>
              <button
                type="button"
                class="rounded-sm px-4 py-2 text-sm font-semibold text-white border-2 border-solid !border-gray-300 hover:bg-zinc-600 transition-all duration-300"
                @click="modalConfirmLogoutRef.open()"
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      </transition>
    </div>

    <!-- Confirm logout -->
    <ModalConfirmLogout
      ref="modalConfirmLogoutRef"
      @confirmed="handleAffiliateLogout"
    />

    <!-- Help modal -->
    <ModalHelp ref="modalHelpRef" />
  </div>
</template>
