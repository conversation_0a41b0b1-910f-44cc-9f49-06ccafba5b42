<script setup>
import ApiService from '@/services/ApiService'
import AlertError from '@/views/user/components/AlertError.vue'
import { computed, ref } from 'vue'
import { processErrors } from '@/utils/errorHandler'
import { IconEdit, IconLoader2, IconPencil, IconUser } from '@tabler/icons-vue'
import { toast } from 'vue-sonner'
import { formattedPhoneNumber, plainPhoneNumber, preventSpacesFromStart, readAsDataURL, removeKeyFromObject } from '@/utils/helpers'
import { isEmptyObject, isNullOrUndefined } from '@/@core/utils'
import * as yup from 'yup'
import { Form as VForm, Field, ErrorMessage } from 'vee-validate'
import { vMaska } from 'maska'
import { useAuthStore } from '@/store/auth'
import { storeToRefs } from 'pinia'
import { useBase64 } from '@vueuse/core'

const props = defineProps({
  loading: {
    type: Boolean,
    default: true,
  },
})

const emit = defineEmits(['updateUserData'])

const authStore = useAuthStore()
const { userData } = storeToRefs(authStore)
const skeletonLoading = computed(() => props.loading)
const isLoading = ref(false)
const serverErrors = ref([])
const inputErrors = ref({})
const scrollToEle = ref(null)
const user = ref({})
const editMode = ref(false)

const formUser = ref({
  first_name: '',
  last_name: '',
  email: '',
  phone_number: '',
})

const fileInput = ref(null)
const profilePictureUploading = ref(false)

const validationSchema = yup.object().shape({
  first_name: yup.string().required('First name is required'),
  last_name: yup.string().required('Last name is required'),
  email: yup.string().required('Email address is required').email('Invalid email address'),
  phone_number: yup.string().required('Phone Number is required'),
})

watch(userData, () => {
  user.value = userData.value

  formUser.value = {
    first_name: userData.value?.first_name,
    last_name: userData.value?.last_name,
    phone_number: userData.value?.phone_number,
    email: userData.value?.email,
  }
})

function removeKeyFromInputErrors(key) {
  inputErrors.value = removeKeyFromObject(inputErrors.value, key)
}

async function handleSubmit(values) {
  try {
    isLoading.value = true
    inputErrors.value = {}
    serverErrors.value = []

    const postData = {
      ...values,
      phone_number: plainPhoneNumber(values.phone_number),
    }

    const { data } = await ApiService.post('/affiliate/update-personal-information', postData)

    if (data.status === 200) {
      toast.success(data.message)
      discardEdit()
      emit('updateUserData')
    } else {
      if (data.errors) {
        inputErrors.value = data.errors
      }
    }
  } catch (error) {
    if (error.response.errors) {
      inputErrors.value = error.response.errors
    }
    serverErrors.value = processErrors(error)
    console.error(error)
  } finally {
    isLoading.value = false
  }
}

function discardEdit() {
  editMode.value = false

  formUser.value = {
    first_name: user.value?.first_name,
    last_name: user.value?.last_name,
    email: user.value?.email,
    phone_number: user.value?.phone_number,
  }

  inputErrors.value = {}
  serverErrors.value = []
}

const triggerFileInput = () => {
  if (!profilePictureUploading.value) {
    fileInput.value.click()
  }
}

const uploadProfilePicture = async $event => {
  const { files } = $event.target
  if (!files || files.length === 0) return
  const file = files[0]

  if (file.type.slice(0, 6) !== 'image/') {
    toast.error('Only image files are allowed')

    return false
  }
  if (file.size > 3 * 1024 * 1024) {
    toast.error('Profile picture should be less than 3MB')

    return false
  }

  const url = await readAsDataURL(file)

  try {
    profilePictureUploading.value = true

    const formData = new FormData()

    formData.append('profile_picture', url)

    const { data } = await ApiService.post('/affiliate/update-profile-picture', formData)

    if (data.status === 200) {
      toast.success(data.message)

      if (data.profile_picture) {
        user.value['profile_picture'] = data.profile_picture
      } else {
        emit('updateUserData')
      }
    } else {
      toast.error(data.message)
    }
  } catch (error) {
    console.error('Error uploading profile picture:', error)
    toast.error('Error uploading profile picture')
  } finally {
    fileInput.value.value = ''
    profilePictureUploading.value = false
  }
}
</script>

<template>
  <div class="bg-white py-5 px-6 rounded-[20px]">
    <div class="flex justify-between mb-3">
      <div class="text-xl font-semibold">
        Account Details
      </div>
      <div>
        <button
          v-if="!editMode"
          class="inline-flex gap-x-1 text-sm font-medium border !border-zinc-800 px-4 py-1.5 rounded-full hover:bg-gray-100 transition-all duration-300 disabled:cursor-not-allowed"
          :disabled="skeletonLoading"
          @click="editMode = true"
        >
          <IconPencil
            class="h-4 w-[14px]"
            stroke-width="2"
          />
          <span>Edit</span>
        </button>
      </div>
    </div>

    <div
      ref="scrollToEle"
      class="h-0"
    ></div>

    <AlertError
      v-if="isEmptyObject(inputErrors) && serverErrors.length > 0"
      title="Error!"
      :errors="serverErrors"
    />

    <div
      v-if="skeletonLoading"
      class="space-y-3"
    >
      <div class="space-y-1">
        <div class="h-4 w-20 bg-gray-300 rounded animate-pulse"></div>
        <div class="h-5 w-25 bg-gray-300 rounded animate-pulse"></div>
      </div>
      <div class="space-y-1">
        <div class="h-4 w-20 bg-gray-300 rounded animate-pulse"></div>
        <div class="h-5 w-25 bg-gray-300 rounded animate-pulse"></div>
      </div>
      <div class="space-y-1">
        <div class="h-4 w-20 bg-gray-300 rounded animate-pulse"></div>
        <div class="h-5 w-25 bg-gray-300 rounded animate-pulse"></div>
      </div>
      <div class="space-y-1">
        <div class="h-4 w-20 bg-gray-300 rounded animate-pulse"></div>
        <div class="h-5 w-25 bg-gray-300 rounded animate-pulse"></div>
      </div>
    </div>

    <div v-else>
      <div
        v-if="!editMode"
        class="space-y-5"
      >
        <div class="relative flex flex-col sm:flex-row gap-5 sm:items-center">
          <!-- Profile Picture or Placeholder -->
          <div class="relative">
            <img
              v-if="user?.profile_picture"
              :src="user?.profile_picture"
              alt="profile picture"
              class="w-24 h-24 rounded-full object-cover"
            >
            <div
              v-else
              class="bg-gray-200 rounded-full w-24 h-24 flex items-center justify-center p-4"
            >
              <IconUser
                class="text-gray-500"
                stroke-width="1.5"
              />
            </div>
            <!-- Edit Button Overlay -->
            <button
              class="absolute bottom-0 right-0 bg-white p-1 rounded-full border border-gray-300 hover:bg-gray-100"
              aria-label="Edit profile picture"
              @click="triggerFileInput"
            >
              <IconLoader2
                v-if="profilePictureUploading"
                class="w-5 h-5 text-gray-600 animate-spin"
                stroke-width="1.6"
              />
              <IconEdit
                v-else
                class="w-5 h-5 text-gray-600"
                stroke-width="1.6"
              />
            </button>

            <!-- Hidden File Input -->
            <input
              ref="fileInput"
              type="file"
              class="hidden"
              accept="image/*"
              @change="uploadProfilePicture"
            />
          </div>

          <!-- User Info -->
          <div>
            <div class="text-xl font-semibold">
              {{ user?.first_name }} {{ user?.last_name }}
            </div>
            <div class="text-base text-gray-700">
              {{ user?.email }}
            </div>
            <div class="text-base text-gray-700">
              {{ formattedPhoneNumber(user?.phone_number) }}
            </div>
          </div>
        </div>
      </div>

      <div v-if="editMode">
        <VForm
          class="mt-8"
          autocomplete="off"
          :validation-schema="validationSchema"
          @submit="handleSubmit"
        >
          <div class="space-y-5">
            <div class="flex flex-col md:flex-row gap-5">
              <div class="relative flex-auto">
                <Field
                  id="first_name"
                  v-model="formUser.first_name"
                  type="text"
                  name="first_name"
                  class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                  placeholder=" "
                  autocomplete="off"
                  @keydown="preventSpacesFromStart($event)"
                  @keyup="removeKeyFromInputErrors('first_name')"
                />
                <label
                  for="first_name"
                  class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4 cursor-text"
                >First Name</label>
                <ErrorMessage
                  name="first_name"
                  class="text-red-500 text-sm ms-5"
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.first_name)"
                  class="text-red-500 text-sm ms-5"
                >
                  {{ inputErrors.first_name[0] }}
                </p>
              </div>

              <div class="relative flex-auto">
                <Field
                  id="last_name"
                  v-model="formUser.last_name"
                  type="text"
                  name="last_name"
                  class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                  placeholder=" "
                  autocomplete="off"
                  @keydown="preventSpacesFromStart($event)"
                  @keyup="removeKeyFromInputErrors('last_name')"
                />
                <label
                  for="last_name"
                  class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4 cursor-text"
                >Last Name</label>
                <ErrorMessage
                  name="last_name"
                  class="text-red-500 text-sm ms-5"
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.last_name)"
                  class="text-red-500 text-sm ms-5"
                >
                  {{ inputErrors.last_name[0] }}
                </p>
              </div>
            </div>

            <div>
              <div class="relative">
                <Field
                  id="email"
                  v-model="formUser.email"
                  type="email"
                  name="email"
                  class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                  placeholder=" "
                  autocomplete="off"
                  required
                  @keyup="removeKeyFromInputErrors('email')"
                />
                <label
                  for="email"
                  class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4 cursor-text"
                >Email</label>
              </div>
              <ErrorMessage
                name="email"
                class="text-red-500 text-sm ms-5"
              />
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.email)"
                class="text-red-500 text-sm ms-5"
              >
                {{ inputErrors.email[0] }}
              </p>
            </div>

            <div>
              <div class="relative">
                <Field
                  id="phone_number"
                  v-model="formUser.phone_number"
                  v-maska
                  type="text"
                  name="phone_number"
                  class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                  placeholder=" "
                  autocomplete="off"
                  data-maska="(###) ###-####"
                  required
                  @keyup="removeKeyFromInputErrors('phone_number')"
                />
                <label
                  for="phone_number"
                  class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4 cursor-text"
                >Phone Number</label>
              </div>
              <ErrorMessage
                name="phone_number"
                class="text-red-500 text-sm ms-5"
              />
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.phone_number)"
                class="text-red-500 text-sm ms-5"
              >
                {{ inputErrors.phone_number[0] }}
              </p>
            </div>

            <div class="mt-8 flex gap-3">
              <button
                type="button"
                class="inline-flex w-full items-center justify-center rounded-full border !border-black px-3.5 py-2.5 font-semibold leading-7 text-black hover:bg-gray-200 text-sm"
                :disabled="isLoading"
                @click="discardEdit"
              >
                Discard
              </button>
              <TwButton
                type="submit"
                class="w-full"
                :loading="isLoading"
              >
                Update
              </TwButton>
            </div>
          </div>
        </VForm>
      </div>
    </div>
  </div>
</template>
