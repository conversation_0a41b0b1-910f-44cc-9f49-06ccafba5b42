<script setup>
import ApiService from '@/services/ApiService'
import AlertError from '@/views/user/components/AlertError.vue'
import { computed, ref } from 'vue'
import { processErrors } from '@/utils/errorHandler'
import { IconPencil } from '@tabler/icons-vue'
import { toast } from 'vue-sonner'
import { removeKeyFromObject } from '@/utils/helpers'
import { isEmptyObject, isNullOrUndefined } from '@/@core/utils'
import * as yup from 'yup'
import { Form as VForm, Field, ErrorMessage } from 'vee-validate'
import { useAuthStore } from '@/store/auth'
import { storeToRefs } from 'pinia'

const props = defineProps({
  loading: {
    type: Boolean,
    default: true,
  },
})

const emit = defineEmits(['updateUserData'])

const authStore = useAuthStore()
const { userData } = storeToRefs(authStore)
const skeletonLoading = computed(() => props.loading)
const isLoading = ref(false)
const serverErrors = ref([])
const inputErrors = ref({})
const scrollToEle = ref(null)
const user = ref({})
const editMode = ref(false)

const formData = ref({
  payout_method: '', // PayPal, Direct Bank Transfer

  // required only if payout_method is PayPal
  payout_paypal_email: '',

  // required only if payout_method is Direct Bank Transfer
  account_holder_first_name: '',
  account_holder_last_name: '',
  bank_name: '',
  account_number: '',
  routing_number: '',
})

const validationSchema = yup.object().shape({
  payout_method: yup.string().required('Please select a payout method.'),

  // required only if payout_method is PayPal
  payout_paypal_email: yup.string().nullable().when('payout_method', {
    is: 'PayPal',
    then: schema => schema
      .required('Please enter your PayPal email address.')
      .email('Please enter a valid email address.'),
    otherwise: schema => schema.nullable(),
  }),

  // required only if payout_method is Direct Bank Transfer
  account_holder_first_name: yup.string().when('payout_method', {
    is: 'Direct Bank Transfer',
    then: schema => schema.required('Please enter your first name.'),
    otherwise: schema => schema.nullable(),
  }),
  account_holder_last_name: yup.string().when('payout_method', {
    is: 'Direct Bank Transfer',
    then: schema => schema.required('Please enter your last name.'),
    otherwise: schema => schema.nullable(),
  }),
  bank_name: yup.string().when('payout_method', {
    is: 'Direct Bank Transfer',
    then: schema => schema.required('Please enter the name of your bank.'),
    otherwise: schema => schema.nullable(),
  }),
  account_number: yup.string().when('payout_method', {
    is: 'Direct Bank Transfer',
    then: schema => schema.required('Please enter your account number.'),
    otherwise: schema => schema.nullable(),
  }),
  routing_number: yup.string().when('payout_method', {
    is: 'Direct Bank Transfer',
    then: schema => schema.required('Please enter your routing number.'),
    otherwise: schema => schema.nullable(),
  }),
})

watch(userData, () => {
  user.value = userData.value

  formData.value = {
    payout_method: userData.value?.payout_method,
    payout_paypal_email: userData.value?.payout_paypal_email || '',
    account_holder_first_name: userData.value?.account_holder_first_name || '',
    account_holder_last_name: userData.value?.account_holder_last_name || '',
    bank_name: userData.value?.bank_name || '',
    account_number: userData.value?.account_number || '',
    routing_number: userData.value?.routing_number || '',
  }
})

function removeKeyFromInputErrors(key) {
  inputErrors.value = removeKeyFromObject(inputErrors.value, key)
}

async function handleSubmit(values) {
  try {
    isLoading.value = true
    inputErrors.value = {}
    serverErrors.value = []

    const postData = {
      payout_method: formData.value.payout_method,
    }

    if (formData.value.payout_method === 'PayPal') {
      postData['payout_paypal_email'] = formData.value.payout_paypal_email
    } else if (formData.value.payout_method === 'Direct Bank Transfer') {
      postData['account_holder_first_name'] = formData.value.account_holder_first_name
      postData['account_holder_last_name'] = formData.value.account_holder_last_name
      postData['bank_name'] = formData.value.bank_name
      postData['account_number'] = formData.value.account_number
      postData['routing_number'] = formData.value.routing_number
    }

    const { data } = await ApiService.post('/affiliate/update-payout-method', postData)

    if (data.status === 200) {
      toast.success(data.message)
      discardEdit()
      emit('updateUserData')
    } else {
      if (data.errors) {
        inputErrors.value = data.errors
      }
    }
  } catch (error) {
    if (error.response.errors) {
      inputErrors.value = error.response.errors
    }
    serverErrors.value = processErrors(error)
    console.error(error)
  } finally {
    isLoading.value = false
  }
}

function discardEdit() {
  editMode.value = false

  formData.value = {
    payout_method: user.value?.payout_method,
    payout_paypal_email: user.value?.payout_paypal_email || '',
    account_holder_first_name: user.value?.account_holder_first_name || '',
    account_holder_last_name: user.value?.account_holder_last_name || '',
    bank_name: user.value?.bank_name || '',
    account_number: user.value?.account_number || '',
    routing_number: user.value?.routing_number || '',
  }

  inputErrors.value = {}
  serverErrors.value = []
}
</script>

<template>
  <div class="bg-white py-5 px-6 rounded-[20px]">
    <div class="flex justify-between mb-3">
      <div class="text-xl font-semibold">
        Payout Method
      </div>
      <div>
        <button
          v-if="!editMode"
          class="inline-flex gap-x-1 text-sm font-medium border !border-zinc-800 px-4 py-1.5 rounded-full hover:bg-gray-100 transition-all duration-300 disabled:cursor-not-allowed"
          :disabled="skeletonLoading"
          @click="editMode = true"
        >
          <IconPencil
            class="h-4 w-[14px]"
            stroke-width="2"
          />
          <span>Edit</span>
        </button>
      </div>
    </div>

    <div
      ref="scrollToEle"
      class="h-0"
    ></div>

    <AlertError
      v-if="isEmptyObject(inputErrors) && serverErrors.length > 0"
      title="Error!"
      :errors="serverErrors"
    />

    <div
      v-if="skeletonLoading"
      class="space-y-3"
    >
      <div class="space-y-1">
        <div class="h-4 w-20 bg-gray-300 rounded animate-pulse"></div>
        <div class="h-5 w-25 bg-gray-300 rounded animate-pulse"></div>
      </div>
      <div class="space-y-1">
        <div class="h-4 w-20 bg-gray-300 rounded animate-pulse"></div>
        <div class="h-5 w-25 bg-gray-300 rounded animate-pulse"></div>
      </div>
      <div class="space-y-1">
        <div class="h-4 w-20 bg-gray-300 rounded animate-pulse"></div>
        <div class="h-5 w-25 bg-gray-300 rounded animate-pulse"></div>
      </div>
      <div class="space-y-1">
        <div class="h-4 w-20 bg-gray-300 rounded animate-pulse"></div>
        <div class="h-5 w-25 bg-gray-300 rounded animate-pulse"></div>
      </div>
    </div>

    <div v-else>
      <div
        v-if="!editMode"
        class="space-y-5"
      >
        <div>
          <div class="text-sm text-zinc-700">
            Payout Method
          </div>
          <div class="font-medium text-zinc-900">
            {{ user?.payout_method }}
          </div>
        </div>
        <div v-if="user?.payout_paypal_email">
          <div class="text-sm text-zinc-700">
            PayPal Email Address
          </div>
          <div class="font-medium text-zinc-900">
            {{ user?.payout_paypal_email }}
          </div>
        </div>
        <div v-if="user?.account_holder_first_name && user?.account_holder_last_name">
          <div class="text-sm text-zinc-700">
            Account Holder Name
          </div>
          <div class="font-medium text-zinc-900">
            {{ user?.account_holder_first_name }} {{ user?.account_holder_last_name }}
          </div>
        </div>
        <div v-if="user?.bank_name">
          <div class="text-sm text-zinc-700">
            Bank Name
          </div>
          <div class="font-medium text-zinc-900">
            {{ user?.bank_name }}
          </div>
        </div>
        <div v-if="user?.account_number">
          <div class="text-sm text-zinc-700">
            Account Number
          </div>
          <div class="font-medium text-zinc-900">
            {{ 'xxxxx' + user?.account_number?.slice(-4) }}
          </div>
        </div>
        <div v-if="user?.routing_number">
          <div class="text-sm text-zinc-700">
            Routing Number
          </div>
          <div class="font-medium text-zinc-900">
            {{ 'xxxxx' + user?.routing_number?.slice(-4) }}
          </div>
        </div>
      </div>

      <div v-if="editMode">
        <VForm
          class="mt-8"
          autocomplete="off"
          :validation-schema="validationSchema"
          @submit="handleSubmit"
        >
          <div class="space-y-5">
            <div>
              <label
                for="payout_method"
                class="block text-sm font-medium text-gray-700 mb-2"
              >Payout Method <span class="text-red-500">*</span></label>
              <Field
                id="payout_method"
                v-model="formData.payout_method"
                name="payout_method"
                as="select"
                class="block w-full px-5 py-3 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                placeholder="Select treatment(s)"
              >
                <option value="PayPal">
                  PayPal
                </option>
                <option value="Direct Bank Transfer">
                  Direct Bank Transfer
                </option>
              </Field>
              <ErrorMessage
                name="payout_method"
                class="text-red-500 text-sm"
              />
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.payout_method)"
                class="text-red-500 text-sm"
              >
                {{ inputErrors.payout_method[0] }}
              </p>
            </div>

            <div
              v-if="formData.payout_method === 'PayPal'"
              class="relative flex-auto"
            >
              <label
                for="payout_paypal_email"
                class="block text-sm font-medium text-gray-700 mb-2"
              >PayPal Email Address <span class="text-red-500">*</span></label>
              <Field
                id="payout_paypal_email"
                v-model="formData.payout_paypal_email"
                type="text"
                name="payout_paypal_email"
                class="block w-full px-5 py-3 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                placeholder=" "
                autocomplete="off"
                @keyup="removeKeyFromInputErrors('payout_paypal_email')"
              />
              <ErrorMessage
                name="payout_paypal_email"
                class="text-red-500 text-sm"
              />
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.payout_paypal_email)"
                class="text-red-500 text-sm"
              >
                {{ inputErrors.payout_paypal_email[0] }}
              </p>
            </div>

            <div
              v-if="formData.payout_method === 'Direct Bank Transfer'"
              class="grid grid-cols-1 sm:grid-cols-2 gap-6"
            >
              <div class="relative flex-auto">
                <label
                  for="account_holder_first_name"
                  class="block text-sm font-medium text-gray-700 mb-2"
                >Account Holder First Name <span class="text-red-500">*</span></label>
                <Field
                  id="account_holder_first_name"
                  v-model="formData.account_holder_first_name"
                  type="text"
                  name="account_holder_first_name"
                  class="block w-full px-5 py-3 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                  placeholder=" "
                  autocomplete="off"
                  @keyup="removeKeyFromInputErrors('account_holder_first_name')"
                />
                <ErrorMessage
                  name="account_holder_first_name"
                  class="text-red-500 text-sm"
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.account_holder_first_name)"
                  class="text-red-500 text-sm"
                >
                  {{ inputErrors.account_holder_first_name[0] }}
                </p>
              </div>

              <div class="relative flex-auto">
                <label
                  for="account_holder_last_name"
                  class="block text-sm font-medium text-gray-700 mb-2"
                >Account Holder First Name <span class="text-red-500">*</span></label>
                <Field
                  id="account_holder_last_name"
                  v-model="formData.account_holder_last_name"
                  type="text"
                  name="account_holder_last_name"
                  class="block w-full px-5 py-3 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                  placeholder=" "
                  autocomplete="off"
                  @keyup="removeKeyFromInputErrors('account_holder_last_name')"
                />
                <ErrorMessage
                  name="account_holder_last_name"
                  class="text-red-500 text-sm"
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.account_holder_last_name)"
                  class="text-red-500 text-sm"
                >
                  {{ inputErrors.account_holder_last_name[0] }}
                </p>
              </div>

              <div class="relative flex-auto col-span-1 sm:col-span-2">
                <label
                  for="bank_name"
                  class="block text-sm font-medium text-gray-700 mb-2"
                >Bank Name <span class="text-red-500">*</span></label>
                <Field
                  id="bank_name"
                  v-model="formData.bank_name"
                  type="text"
                  name="bank_name"
                  class="block w-full px-5 py-3 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                  placeholder=" "
                  autocomplete="off"
                  @keyup="removeKeyFromInputErrors('bank_name')"
                />
                <ErrorMessage
                  name="bank_name"
                  class="text-red-500 text-sm"
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.bank_name)"
                  class="text-red-500 text-sm"
                >
                  {{ inputErrors.bank_name[0] }}
                </p>
              </div>

              <div class="relative flex-auto">
                <label
                  for="account_number"
                  class="block text-sm font-medium text-gray-700 mb-2"
                >Account Number <span class="text-red-500">*</span></label>
                <Field
                  id="account_number"
                  v-model="formData.account_number"
                  type="text"
                  name="account_number"
                  class="block w-full px-5 py-3 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                  placeholder=" "
                  autocomplete="off"
                  @keyup="removeKeyFromInputErrors('account_number')"
                />
                <ErrorMessage
                  name="account_number"
                  class="text-red-500 text-sm"
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.account_number)"
                  class="text-red-500 text-sm"
                >
                  {{ inputErrors.account_number[0] }}
                </p>
              </div>

              <div class="relative flex-auto">
                <label
                  for="routing_number"
                  class="block text-sm font-medium text-gray-700 mb-2"
                >Routing Number <span class="text-red-500">*</span></label>
                <Field
                  id="routing_number"
                  v-model="formData.routing_number"
                  type="text"
                  name="routing_number"
                  class="block w-full px-5 py-3 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                  placeholder=" "
                  autocomplete="off"
                  @keyup="removeKeyFromInputErrors('routing_number')"
                />
                <ErrorMessage
                  name="routing_number"
                  class="text-red-500 text-sm"
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.routing_number)"
                  class="text-red-500 text-sm"
                >
                  {{ inputErrors.routing_number[0] }}
                </p>
              </div>
            </div>

            <div class="mt-8 flex gap-3">
              <button
                type="button"
                class="inline-flex w-full items-center justify-center rounded-full border !border-black px-3.5 py-2.5 font-semibold leading-7 text-black hover:bg-gray-200 text-sm"
                :disabled="isLoading"
                @click="discardEdit"
              >
                Discard
              </button>
              <TwButton
                type="submit"
                class="w-full"
                :loading="isLoading"
              >
                Update
              </TwButton>
            </div>
          </div>
        </VForm>
      </div>
    </div>
  </div>
</template>
