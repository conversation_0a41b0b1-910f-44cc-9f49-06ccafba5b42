<script setup>
import ApiService from '@/services/ApiService'
import AlertError from '@/views/user/components/AlertError.vue'
import { computed, onMounted, ref } from 'vue'
import { processErrors } from '@/utils/errorHandler'
import { IconPencil } from '@tabler/icons-vue'
import { toast } from 'vue-sonner'
import { isEmptyObject, isNullOrUndefined } from '@/@core/utils'
import * as yup from 'yup'
import { Form as VForm, Field, ErrorMessage } from 'vee-validate'
import { vMaska } from 'maska'
import { useAuthStore } from '@/store/auth'
import { storeToRefs } from 'pinia'
import { states } from '@/data/states'

const props = defineProps({
  loading: {
    type: Boolean,
    default: true,
  },
})

const emit = defineEmits(['updateUserData'])

const authStore = useAuthStore()
const { userData } = storeToRefs(authStore)
const skeletonLoading = computed(() => props.loading)
const isLoading = ref(false)
const serverErrors = ref([])
const inputErrors = ref({})
const scrollToEle = ref(null)
const user = ref({})
const editMode = ref(false)
const countries = [{ label: 'USA', value: 'USA' }]

const formData = ref({
  address_1: '',
  address_2: '',
  city: '',
  state: '',
  zipcode: '',
  country: '',
})

const validationSchema = yup.object().shape({
  address_1: yup.string().required('Street address is required'),
  city: yup.string().required('City is required'),
  state: yup.string().required('State / Territory is required'),
  zipcode: yup.string().required('Zipcode is required'),
  country: yup.string().required('Country is required'),
})

const statesList = computed(() => states.filter(item => item.type === 'state'))
const territoriesList = computed(() => states.filter(item => item.type === 'territory'))

watch(userData, () => {
  user.value = userData.value

  formData.value = {
    address_1: userData.value?.address_1,
    address_2: userData.value?.address_2,
    city: userData.value?.city,
    state: userData.value?.state,
    zipcode: userData.value?.zipcode,
    country: userData.value?.country,
  }
})

async function handleSubmit(values) {
  try {
    isLoading.value = true
    inputErrors.value = {}
    serverErrors.value = []

    const { data } = await ApiService.post('/affiliate/update-address', values)

    if (data.status === 200) {
      toast.success(data.message)
      discardEdit()
      emit('updateUserData')
    } else {
      if (data.errors) {
        inputErrors.value = data.errors
      }
    }
  } catch (error) {
    if (error.response.errors) {
      inputErrors.value = error.response.errors
    }
    serverErrors.value = processErrors(error)
    console.error(error)
  } finally {
    isLoading.value = false
  }
}

function discardEdit() {
  editMode.value = false

  formData.value = {
    address_1: user.value?.address_1,
    address_2: user.value?.address_2,
    city: user.value?.city,
    state: user.value?.state,
    zipcode: user.value?.zipcode,
    country: user.value?.country,
  }

  inputErrors.value = {}
  serverErrors.value = []
}
</script>

<template>
  <div class="bg-white py-5 px-6 rounded-[20px]">
    <div class="flex justify-between mb-3">
      <div class="text-xl font-semibold">
        Address
      </div>
      <div>
        <button
          v-if="!editMode"
          class="inline-flex gap-x-1 text-sm font-medium border !border-zinc-800 px-4 py-1.5 rounded-full hover:bg-gray-100 transition-all duration-300 disabled:cursor-not-allowed"
          :disabled="skeletonLoading"
          @click="editMode = true"
        >
          <IconPencil
            class="h-4 w-[14px]"
            stroke-width="2"
          />
          <span>Edit</span>
        </button>
      </div>
    </div>

    <div
      ref="scrollToEle"
      class="h-0"
    ></div>

    <AlertError
      v-if="isEmptyObject(inputErrors) && serverErrors.length > 0"
      title="Error!"
      :errors="serverErrors"
    />

    <div
      v-if="skeletonLoading"
      class="space-y-3"
    >
      <div class="space-y-1">
        <div class="h-4 w-20 bg-gray-300 rounded animate-pulse"></div>
        <div class="h-5 w-25 bg-gray-300 rounded animate-pulse"></div>
      </div>
      <div class="space-y-1">
        <div class="h-4 w-20 bg-gray-300 rounded animate-pulse"></div>
        <div class="h-5 w-25 bg-gray-300 rounded animate-pulse"></div>
      </div>
      <div class="space-y-1">
        <div class="h-4 w-20 bg-gray-300 rounded animate-pulse"></div>
        <div class="h-5 w-25 bg-gray-300 rounded animate-pulse"></div>
      </div>
      <div class="space-y-1">
        <div class="h-4 w-20 bg-gray-300 rounded animate-pulse"></div>
        <div class="h-5 w-25 bg-gray-300 rounded animate-pulse"></div>
      </div>
    </div>

    <div v-else>
      <div
        v-if="!editMode"
        class="space-y-5"
      >
        <address
          v-if="user?.address_1"
          class="grid not-italic text-gray-700 mt-1.5"
        >
          <span v-if="user?.address_2">{{ user?.address_1 }}, {{ user?.address_2 }}</span>
          <span v-else>{{ user?.address_1 }}</span>
          <span>{{ user?.city }}</span>
          <span>{{ user?.state }}-{{ user?.zipcode }}</span>
          <span>{{ user?.country }}</span>
        </address>
        <span v-else> - </span>
      </div>

      <div v-if="editMode">
        <VForm
          class="mt-8"
          autocomplete="off"
          :validation-schema="validationSchema"
          @submit="handleSubmit"
        >
          <div class="space-y-5">
            <!-- 👉 Street address line 1 -->
            <div>
              <div class="relative">
                <Field
                  id="address_1"
                  v-model="formData.address_1"
                  type="text"
                  name="address_1"
                  class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                  placeholder=" "
                  autocomplete="off"
                  required
                />
                <label
                  for="address_1"
                  class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4 cursor-text"
                >Street Address</label>
              </div>
              <ErrorMessage
                name="address_1"
                class="text-red-500 text-sm ms-5"
              />
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.address_1)"
                class="text-red-500 text-sm ms-5"
              >
                {{ inputErrors.address_1[0] }}
              </p>
            </div>

            <!-- 👉 Street address line 2 -->
            <div>
              <div class="relative">
                <Field
                  id="address_2"
                  v-model="formData.address_2"
                  type="text"
                  name="address_2"
                  class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                  placeholder=" "
                  autocomplete="off"
                />
                <label
                  for="address_2"
                  class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4 cursor-text"
                >Apt / Suite (optional)</label>
              </div>
              <ErrorMessage
                name="address_2"
                class="text-red-500 text-sm ms-5"
              />
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.address_2)"
                class="text-red-500 text-sm ms-5"
              >
                {{ inputErrors.address_2[0] }}
              </p>
            </div>

            <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <!-- 👉 City -->
              <div>
                <div class="relative">
                  <Field
                    id="city"
                    v-model="formData.city"
                    type="text"
                    name="city"
                    class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                    placeholder=" "
                    autocomplete="off"
                    required
                  />
                  <label
                    for="city"
                    class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4 cursor-text"
                  >City</label>
                </div>
                <ErrorMessage
                  name="city"
                  class="text-red-500 text-sm ms-5"
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.city)"
                  class="text-red-500 text-sm ms-5"
                >
                  {{ inputErrors.city[0] }}
                </p>
              </div>

              <!-- 👉 State / Territory -->
              <div class="relative">
                <Field
                  id="state"
                  v-model="formData.state"
                  as="select"
                  name="state"
                  class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                  required
                >
                  <option
                    value=""
                    disabled
                    selected
                  >
                    Select
                  </option>
                  <optgroup label="States">
                    <option
                      v-for="state in statesList"
                      :key="state.code"
                      :value="state.code"
                    >
                      {{ state.name }}
                    </option>
                  </optgroup>
                  <optgroup label="Territories">
                    <option
                      v-for="territory in territoriesList"
                      :key="territory.code"
                      :value="territory.code"
                    >
                      {{ territory.name }}
                    </option>
                  </optgroup>
                </Field>
                <label
                  for="state"
                  class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-focus:scale-75 peer-focus:-translate-y-4"
                >State / Territory</label>
                <ErrorMessage
                  name="state"
                  class="text-red-500 text-sm ms-5"
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.state)"
                  class="text-red-500 text-sm ms-5"
                >
                  {{ inputErrors.state[0] }}
                </p>
              </div>
            </div>

            <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <!-- 👉 Zipcode -->
              <div>
                <div class="relative">
                  <Field
                    id="zipcode"
                    v-model="formData.zipcode"
                    v-maska
                    type="text"
                    name="zipcode"
                    class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                    placeholder=" "
                    autocomplete="off"
                    data-maska="#####-####"
                    required
                  />
                  <label
                    for="zipcode"
                    class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4 cursor-text"
                  >Zipcode</label>
                </div>
                <ErrorMessage
                  name="zipcode"
                  class="text-red-500 text-sm ms-5"
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.zipcode)"
                  class="text-red-500 text-sm ms-5"
                >
                  {{ inputErrors.zipcode[0] }}
                </p>
              </div>

              <!-- 👉 Country -->
              <div class="relative">
                <Field
                  id="country"
                  v-model="formData.country"
                  as="select"
                  name="country"
                  class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                  required
                >
                  <option
                    value=""
                    disabled
                    selected
                  >
                    Select
                  </option>
                  <option
                    v-for="item in countries"
                    :key="item.value"
                    :value="item.value"
                  >
                    {{ item.label }}
                  </option>
                </Field>
                <label
                  for="country"
                  class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-focus:scale-75 peer-focus:-translate-y-4"
                >Country</label>
                <ErrorMessage
                  name="country"
                  class="text-red-500 text-sm ms-5"
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.country)"
                  class="text-red-500 text-sm ms-5"
                >
                  {{ inputErrors.country[0] }}
                </p>
              </div>
            </div>

            <div class="mt-8 flex gap-3">
              <button
                type="button"
                class="inline-flex w-full items-center justify-center rounded-full border !border-black px-3.5 py-2.5 font-semibold leading-7 text-black hover:bg-gray-200 text-sm"
                :disabled="isLoading"
                @click="discardEdit"
              >
                Discard
              </button>
              <TwButton
                type="submit"
                class="w-full"
                :loading="isLoading"
              >
                Update
              </TwButton>
            </div>
          </div>
        </VForm>
      </div>
    </div>
  </div>
</template>
