<script setup>
import ApiService from '@/services/ApiService'
import { useAuthStore } from '@/store/auth'
import { processErrors } from '@/utils/errorHandler'
import { IconClipboardText } from '@tabler/icons-vue'
import { storeToRefs } from 'pinia'
import { onMounted } from 'vue'
import { toast } from 'vue-sonner'
import copy from 'copy-to-clipboard'
import { formatCurrency } from '@/utils/helpers'

const authStore = useAuthStore()
const { userData } = storeToRefs(authStore)
const isLoading = ref(false)
const summary = ref({})
const campaigns = ref([])
const currentCommissionLevel  = ref(null)

onMounted(async () => {
  await getAnalyticsData()
})

async function getAnalyticsData() {
  try {
    isLoading.value = true

    const { data } = await ApiService.get('/affiliate/dashboard')

    if (data.status === 200) {
      summary.value = data.summary
      campaigns.value = data.campaigns
      currentCommissionLevel.value = data.current_commission_level
    } else {
      toast.error(processErrors(data)[0])
    }
  } catch (error) {
    console.error(error)
    toast.error(processErrors(error)[0])
  } finally {
    isLoading.value = false
  }
}

async function copyLinkToClipboard(link) {
  try {
    copy(link)
    toast.success('Link copied to clipboard')
  } catch (error) {
    console.error(error)
    toast.error('Failed to copy link to clipboard')
  }
}
</script>

<template>
  <div>
    <h1 class="text-3xl sm:text-4xl md:text-5xl font-bold text-black mb-2 sm:mb-8 text-center">
      Welcome, {{ userData?.first_name }}!
    </h1>

    <!-- skelton loading -->
    <div
      v-if="isLoading"
      class="animate-pulse"
    >
      <div class="grid grid-cols-1 sm:grid-cols-3 sm:gap-4">
        <div class="w-full bg-white p-4 rounded-2xl mt-6 grid gap-2">
          <div class="w-1/4 h-4 bg-gray-200 rounded"></div>
          <div class="w-1/2 h-6 bg-gray-200 rounded"></div>
        </div>
        <div class="w-full bg-white p-4 rounded-2xl mt-6 grid gap-2">
          <div class="w-1/4 h-4 bg-gray-200 rounded"></div>
          <div class="w-1/2 h-6 bg-gray-200 rounded"></div>
        </div>
        <div class="w-full bg-white p-4 rounded-2xl mt-6 grid gap-2">
          <div class="w-1/4 h-4 bg-gray-200 rounded"></div>
          <div class="w-1/2 h-6 bg-gray-200 rounded"></div>
        </div>
      </div>

      <div class="w-full bg-white p-4 rounded-2xl mt-6">
        <div class="text-lg font-bold text-gray-800">
          <div class="w-1/3 h-6 bg-gray-200 rounded"></div>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 mt-4">
          <div class="w-full bg-gray-100 shadow-sm p-4 rounded-2xl grid gap-2">
            <div class="w-1/3 h-4 bg-gray-200 rounded"></div>
            <div class="w-1/2 h-6 bg-gray-200 rounded"></div>
          </div>
          <div class="w-full bg-gray-100 shadow-sm p-4 rounded-2xl grid gap-2">
            <div class="w-1/3 h-4 bg-gray-200 rounded"></div>
            <div class="w-1/2 h-6 bg-gray-200 rounded"></div>
          </div>
          <div class="w-full bg-gray-100 shadow-sm p-4 rounded-2xl grid gap-2">
            <div class="w-1/3 h-4 bg-gray-200 rounded"></div>
            <div class="w-1/2 h-6 bg-gray-200 rounded"></div>
          </div>
        </div>
        <div class="w-full mt-6">
          <div class="flex flex-col sm:flex-row sm:items-center gap-3 bg-gray-50 border !border-gray-200 !rounded-xl w-full px-2.5 py-2 font-medium">
            <div class="w-full h-8 bg-gray-200 rounded text-gray-500 text-sm break-words pb-3 sm:!pb-0"></div>
            <div class="w-20 h-8 bg-gray-200 rounded text-white border-gray-200 border sm:mt-0"></div>
          </div>
        </div>
      </div>
    </div>

    <div v-else>
      <!-- summary data -->
      <div class="grid grid-cols-1 sm:grid-cols-3 sm:gap-4">
        <div class="w-full bg-blue-50 p-4 rounded-2xl mt-6 grid shadow-sm">
          <span class="text-sm text-blue-600 font-medium">Total Sales</span>
          <span class="text-3xl font-bold text-blue-800">{{ summary.total_sales ?? '-' }}</span>
        </div>
        <div class="w-full bg-green-50 p-4 rounded-2xl mt-6 grid shadow-sm">
          <span class="text-sm text-green-600 font-medium">Total Earnings</span>
          <span class="text-3xl font-bold text-green-800">{{ summary.total_earnings ? formatCurrency(summary.total_earnings) : '-' }}</span>
        </div>
        <div class="w-full bg-yellow-50 p-4 rounded-2xl mt-6 grid shadow-sm">
          <span class="text-sm text-yellow-600 font-medium">Commission Rate</span>
          <span
            v-if="currentCommissionLevel"
            class="text-3xl font-bold text-yellow-800"
          >
            {{
              currentCommissionLevel.commission_type === 'percentage'
                ? currentCommissionLevel.commission_value + '%'
                : formatCurrency(currentCommissionLevel.commission_value)
            }} <span class="text-base">/sale <span class="font-medium">(Level {{ currentCommissionLevel.level }})</span></span>
          </span>
          <span
            v-else
            class="text-3xl font-bold text-yellow-800"
          > - </span>
        </div>
      </div>

      <!-- campaign data -->
      <div
        v-for="campaign in campaigns"
        :key="campaign.category"
        class="w-full bg-white p-4 rounded-2xl mt-6"
      >
        <div class="text-lg font-bold text-gray-800">
          {{
            campaign.category === 'HL'
              ? 'Hair Treatment'
              : campaign.category === 'WL'
                ? 'Weight Loss'
                : campaign.category
          }} Campaign
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 mt-4">
          <div class="w-full bg-gray-100 shadow-sm p-4 rounded-2xl grid gap-2">
            <span class="text-sm uppercase font-medium text-gray-600">Total Clicks</span>
            <span class="font-semibold text-2xl text-gray-800">{{ campaign.total_clicks ?? '-' }}</span>
          </div>
          <div class="w-full bg-gray-100 shadow-sm p-4 rounded-2xl grid gap-2">
            <span class="text-sm uppercase font-medium text-gray-600">Total Sales</span>
            <span class="font-semibold text-2xl text-gray-800">{{ campaign.total_sales ?? '-' }}</span>
          </div>
          <div class="w-full bg-gray-100 shadow-sm p-4 rounded-2xl grid gap-2">
            <span class="text-sm uppercase font-medium text-gray-600">Total Earnings</span>
            <span class="font-semibold text-2xl text-gray-800">{{ campaign.total_earnings ? formatCurrency(campaign.total_earnings) : '-' }}</span>
          </div>
        </div>
        <div
          v-if="campaign.links"
          class="w-full mt-6"
        >
          <h5 class="text-base font-medium text-gray-800 mb-1.5">
            Affiliate Link
          </h5>
          <div class="flex flex-col sm:flex-row sm:items-center bg-gray-50 border !border-gray-200 !rounded-xl w-full px-2.5 py-2 font-medium">
            <div class="w-full bg-gray-50 text-gray-500 text-sm break-words pb-3 sm:!pb-0">
              {{ campaign.links }}
            </div>
            <button
              class="text-white hover:bg-gray-800 rounded-lg py-2 px-2.5 inline-flex items-center justify-center bg-black border-gray-200 border"
              @click="copyLinkToClipboard(campaign.links)"
            >
              <span
                id="default-message"
                class="inline-flex items-center"
              >
                <IconClipboardText
                  class="w-4 h-4 me-1"
                  stroke-width="2"
                />
                <span class="text-xs font-semibold">Copy</span>
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
