<script setup>
import { ref, onMounted } from 'vue'
import { IconArrowDown, IconCreativeCommonsNc, IconInfoCircleFilled, IconTrendingUp } from '@tabler/icons-vue'
import { useAuthStore } from '@/store/auth'
import { storeToRefs } from 'pinia'
import ApiService from '@/services/ApiService'
import { toast } from 'vue-sonner'
import { processErrors } from '@/utils/errorHandler'
import { isEmpty } from '@/@core/utils'
import { formatCurrency } from '@/utils/helpers'
import ModalRequestPayout from '@/views/affiliate/components/ModalRequestPayout.vue'

const authStore = useAuthStore()
const { userData } = storeToRefs(authStore)
const skeletonLoading = ref(false)
const pageLoading = ref(false)

const overview = ref({
  total_earnings: 0,
  available_balance: 0,
  minimum_withdraw_amount: 0,
})

const transactions = ref([])
const currentPage = ref(1)
const totalPage = ref(0)
const totalRecords = ref(0)
const transactionsPerPage = ref(10)
const transactionTypeFilter = ref('')
const modalRequestPayoutRef = ref(null)

const TRANSACTION_TYPE = {
  CREDIT: 'credit',
  DEBIT: 'debit',
}

const TRANSACTION_STATUS = {
  PENDING: 0,
  APPROVED: 1,
  REJECTED: 2,
}

onMounted(async () => {
  skeletonLoading.value = true
  await getTransactions()
  skeletonLoading.value = false
})

async function getTransactions() {
  try {
    pageLoading.value = true

    const { data } = await ApiService.post('/affiliate/get-transaction-history', {
      page: currentPage.value,
      perPage: transactionsPerPage.value,
      transaction_type: transactionTypeFilter.value,
    })

    if (data.status === 200) {
      overview.value = data.overview

      if (currentPage.value === 1) {
        transactions.value = data.transactionHistories.records
      } else {
        transactions.value = [
          ...transactions.value, // Prepend existing pages
          ...data.transactionHistories.records, // Append new pages
        ]
      }
      totalPage.value = data.transactionHistories.totalPage
      totalRecords.value = data.transactionHistories.totalRecords
    } else {
      toast.error(processErrors(data)[0])
    }
  } catch (error) {
    console.error(error)
    toast.error(processErrors(error)[0])
  } finally {
    pageLoading.value = false
  }
}

async function handlePagination() {
  if (!skeletonLoading.value && !pageLoading.value && currentPage.value < totalPage.value) {
    currentPage.value++
    await getTransactions()
  }
}

watch(transactionTypeFilter, () => {
  currentPage.value = 1
  getTransactions()
})

function formatDescription(transaction) {
  if (transaction.transaction_type === TRANSACTION_TYPE.CREDIT) {
    if (transaction.category === 'ED') {
      return 'Commission earned from ED'
    } else if(transaction.category === 'HL') {
      return 'Commission earned from Hair Loss'
    } else if (transaction.category === 'ReversalPayoutRequest') {
      return 'Payout Request Rejected - Credited'
    } else {
      return 'Commission earned'
    }
  } else if (transaction.transaction_type === TRANSACTION_TYPE.DEBIT) {
    return 'Payout Requested'
  }
}

function resolveStatus(status) {
  if (status === TRANSACTION_STATUS.PENDING) {
    return { label: 'Pending', classes: 'bg-amber-200 text-amber-800' }
  } else if (status === TRANSACTION_STATUS.APPROVED) {
    return { label: 'Approved', classes: 'bg-green-200 text-green-800' }
  } else if (status === TRANSACTION_STATUS.REJECTED) {
    return { label: 'Rejected', classes: 'bg-red-300 text-red-900' }
  }
}
</script>

<template>
  <div>
    <h1 class="text-3xl sm:text-4xl md:text-5xl font-bold text-black mb-8 text-center">
      Welcome, {{ userData?.first_name }}!
    </h1>

    <div class="max-w-4xl mx-auto">
      <!-- Affiliate Overview skeleton -->
      <div
        v-if="skeletonLoading"
        class="bg-white p-6 rounded-2xl mb-6 animate-pulse"
      >
        <div class="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="bg-gray-50 p-4 !rounded-xl">
            <div class="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
            <div class="h-10 bg-gray-200 rounded w-2/3"></div>
          </div>
          <div class="bg-gray-50 p-4 !rounded-xl">
            <div class="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
            <div class="h-10 bg-gray-200 rounded w-2/3"></div>
          </div>
        </div>
        <div class="flex justify-end mt-5">
          <div class="h-10 bg-gray-200 rounded min-w-40"></div>
        </div>
      </div>

      <!-- Affiliate Overview -->
      <div
        v-else
        class="bg-white p-6 rounded-2xl mb-6"
      >
        <h2 class="text-xl font-bold text-gray-800 mb-4">
          Overview
        </h2>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="bg-blue-50 p-4 !rounded-xl">
            <p class="text-sm text-blue-600 font-medium">
              Total Earnings
            </p>
            <p class="text-3xl font-bold text-blue-800">
              {{ formatCurrency(overview.total_earnings) }}
            </p>
          </div>
          <div class="bg-green-50 p-4 !rounded-xl">
            <p class="text-sm text-green-600 font-medium">
              Available Balance
            </p>
            <p class="text-3xl font-bold text-green-800">
              {{ formatCurrency(overview.available_balance) }}
            </p>
          </div>
        </div>

        <div class="flex justify-end">
          <TwButton
            v-if="overview.available_balance >= overview.minimum_withdraw_amount"
            class="min-w-40 mt-5"
            variant="success"
            @click="modalRequestPayoutRef.open()"
          >
            <span>Request Payout</span>
          </TwButton>
          <TwButton
            v-else
            v-tooltip="`You will be able to request a payout once your balance reaches ${formatCurrency(overview.minimum_withdraw_amount)}`"
            class="min-w-40 mt-5"
            variant="success"
            disabled
          >
            <IconInfoCircleFilled class="w-[18px] h-[18px] me-2" />
            <span>Request Payout</span>
          </TwButton>
        </div>
      </div>

      <!-- Transactions List -->
      <div class="bg-white p-6 rounded-2xl">
        <div class="flex flex-wrap justify-between items-center mb-4">
          <h3 class="text-xl font-semibold text-gray-800">
            Transactions
          </h3>
          <div
            v-if="skeletonLoading"
            class="h-8 bg-gray-200 rounded w-48"
          ></div>
          <select
            v-else
            v-model="transactionTypeFilter"
            class="form-select rounded-md text-sm border !border-gray-300 focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
          >
            <option value="">
              All Transactions
            </option>
            <option value="credit">
              Earned Only
            </option>
            <option value="debit">
              Withdrawn Only
            </option>
          </select>
        </div>

        <div v-if="!skeletonLoading && isEmpty(transactions)">
          <div class="grid place-items-center min-h-28">
            <div class="flex flex-col justify-center items-center">
              <IconCreativeCommonsNc
                class="h-14 text-zinc-600"
                stroke-width="1.5"
              />
              <span class="text-zinc-600 text-sm font-medium mt-2">
                You do not have any transactions.
              </span>
            </div>
          </div>
        </div>

        <ul
          v-if="!skeletonLoading && !pageLoading"
          class="space-y-4"
        >
          <!-- Change with ID when available -->
          <li
            v-for="(transaction, index) in transactions"
            :key="index"
            class="flex justify-between items-center p-4 bg-gray-50 rounded-lg"
          >
            <div class="flex items-center">
              <div
                class="w-10 h-10 rounded-full flex items-center justify-center me-3"
                :class="[
                  transaction.transaction_type === TRANSACTION_TYPE.CREDIT ? 'bg-green-100 text-green-600' : 'bg-blue-100 text-blue-600'
                ]"
              >
                <IconTrendingUp
                  v-if="transaction.transaction_type === TRANSACTION_TYPE.CREDIT"
                  class="h-5 w-5"
                  stroke-width="2"
                />
                <IconArrowDown
                  v-else
                  class="h-5 w-5"
                  stroke-width="2"
                />
              </div>
              <div>
                <p class="font-medium text-gray-800">
                  {{ formatDescription(transaction) }}
                </p>
                <p class="text-xs text-gray-600">
                  <span v-if="transaction.reference_id">#{{ transaction.reference_id }} &#8226;</span> {{ transaction.transaction_at }}
                </p>
                <div
                  v-if="transaction.transaction_type === TRANSACTION_TYPE.DEBIT"
                  class="mt-1"
                >
                  <span
                    :class="`text-xs font-medium px-3 py-1 rounded-full ${
                      resolveStatus(transaction.status).classes}`"
                  >
                    {{ resolveStatus(transaction.status).label }}
                  </span>
                </div>
              </div>
            </div>
            <div
              class="font-semibold text-end"
              :class="[
                transaction.transaction_type === TRANSACTION_TYPE.CREDIT ? 'text-green-600' : 'text-blue-600'
              ]"
            >
              <span>
                {{ transaction.transaction_type === TRANSACTION_TYPE.CREDIT ? '+' : '-' }}{{ formatCurrency(transaction.amount) }}
              </span>
              <span class="block text-[13px] text-gray-500 font-medium">
                {{ formatCurrency(transaction.balance) }}
              </span>
            </div>
          </li>
        </ul>

        <ul
          v-if="skeletonLoading"
          class="space-y-4 mt-4"
        >
          <li
            v-for="_ in 5"
            :key="_"
            class="flex justify-between items-center p-4 animate-pulse bg-gray-100 rounded-lg"
          >
            <div class="flex items-center w-full">
              <div class="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center me-3"></div>
              <div class="w-full">
                <div class="h-4 bg-gray-200 rounded w-1/4"></div>
                <div class="mt-1 h-4 bg-gray-200 rounded w-2/4"></div>
              </div>
            </div>
            <div class="h-4 bg-gray-200 rounded w-1/2"></div>
          </li>
        </ul>

        <ul
          v-if="pageLoading"
          class="space-y-4 mt-4"
        >
          <li
            v-for="_ in 5"
            :key="_"
            class="flex justify-between items-center p-4 animate-pulse bg-gray-100 rounded-lg"
          >
            <div class="flex items-center w-full">
              <div class="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center me-3"></div>
              <div class="w-full">
                <div class="h-4 bg-gray-200 rounded w-1/4"></div>
                <div class="mt-1 h-4 bg-gray-200 rounded w-2/4"></div>
              </div>
            </div>
            <div class="h-4 bg-gray-200 rounded w-1/2"></div>
          </li>
        </ul>

        <div
          v-if="currentPage < totalPage"
          class="text-center"
        >
          <TwButton
            class="text-xs !py-2"
            @click="handlePagination"
          >
            See More
          </TwButton>
        </div>
      </div>
    </div>

    <!-- Modal Affiliate Request Payout -->
    <ModalRequestPayout
      ref="modalRequestPayoutRef"
      :max-amount="overview.available_balance ?? 0"
      @reload="() => {
        currentPage = 1
        getTransactions()
      }"
    />
  </div>
</template>
