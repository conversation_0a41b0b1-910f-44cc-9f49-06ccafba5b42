<script setup>
import ApiService from '@/services/ApiService'
import { useAuthStore } from '@/store/auth'
import { processErrors } from '@/utils/errorHandler'
import { storeToRefs } from 'pinia'
import { onMounted } from 'vue'
import { toast } from 'vue-sonner'
import ProfileDetailsCard from '../components/ProfileDetailsCard.vue'
import ProfileAddressCard from '../components/ProfileAddressCard.vue'
import ProfilePayoutMethodsCard from '../components/ProfilePayoutMethodsCard.vue'
import ProfilePasswordCard from '../components/ProfilePasswordCard.vue'

const authStore = useAuthStore()
const { userData } = storeToRefs(authStore)
const isLoading = ref(true)

onMounted(async () => {
  await getProfile()
})

async function getProfile() {
  try {
    isLoading.value = true

    const { data } = await ApiService.get('/affiliate/profile')

    if (data.status === 200) {
      authStore.updateUserData(data.userDetail)
    } else {
      toast.error(processErrors(data)[0])
    }
  } catch (error) {
    console.error(error)
    toast.error(processErrors(error)[0])
  } finally {
    isLoading.value = false
  }
}
</script>

<template>
  <div>
    <h1 class="text-3xl sm:text-4xl md:text-5xl font-bold text-black mb-10 text-center">
      Welcome, {{ userData?.first_name }}!
    </h1>

    <div class="space-y-5">
      <ProfileDetailsCard
        :loading="isLoading"
        @update-user-data="getProfile"
      />
      <ProfileAddressCard
        :loading="isLoading"
        @update-user-data="getProfile"
      />
      <ProfilePayoutMethodsCard
        :loading="isLoading"
        @update-user-data="getProfile"
      />
      <ProfilePasswordCard />
    </div>
  </div>
</template>
