<script setup>
import Header from '@/views/user/components/Header.vue'
import { onMounted, onUnmounted } from 'vue'
import ConfettiExplosion from 'vue-confetti-explosion'

const frontendUrl = computed(() => import.meta.env.VITE_MARKETING_SITE_URL)

onMounted(() => {
  if (!sessionStorage.getItem('affiliateUserSignupSuccess')) {
    location.href = frontendUrl.value
  }
  sessionStorage.removeItem('affiliateUserId')
  sessionStorage.removeItem('affiliateUserDetailsSessionData')
  sessionStorage.removeItem('affiliateAddressDetailsSessionData')
  sessionStorage.removeItem('affiliateAdditionalSessionData')
})

onUnmounted(() => {
  sessionStorage.removeItem('affiliateUserSignupSuccess')
})
</script>

<template>
  <div>
    <Header />

    <div class="grid place-items-center">
      <div class="w-full sm:max-w-lg grid place-items-center h-[calc(100vh-15rem)]">
        <div class="px-4 py-12 sm:px-20 flex flex-col items-center">
          <ConfettiExplosion />
          <img
            class="w-28 mb-8"
            src="@/assets/user/images/confetti.png"
            alt="Confetti"
          />
          <div class="mb-6 text-center">
            <h1 class="text-2xl sm:text-3xl text-gray-800 font-semibold mb-4">
              Thank you for your interest!
            </h1>
            <p class="text-base sm:text-lg text-gray-600">
              The team will be reviewing your application and will get back to you in 48 hours or less. In the meantime feel free to explore our treatment options.
            </p>
          </div>
          <a
            :href="frontendUrl"
            class="btn-primary px-8"
          >
            Explore Treatments
          </a>
        </div>
      </div>
    </div>
  </div>
</template>
