<script setup>
import { onMounted, ref } from 'vue'
import { isEmpty, isEmptyObject, isNullOrUndefined } from '@/@core/utils'
import AlertError from '@/views/user/components/AlertError.vue'
import router from '@/router'
import { useSessionStorage } from '@vueuse/core'
import { ErrorMessage, Field, Form as VForm } from 'vee-validate'
import * as yup from 'yup'
import ApiService from '@/services/ApiService'
import { processErrors } from '@/utils/errorHandler'
import MultiSelect from 'primevue/multiselect'

const userId = useSessionStorage('affiliateUserId', undefined)
const sessionData = useSessionStorage('affiliateAdditionalSessionData', {})
const isLoading = ref(false)
const inputErrors = ref({})
const serverErrors = ref([])
const affiliateAgreementUrl = computed(() => import.meta.env.VITE_AFFILIATE_AGREEMENT_URL)

const formData = ref({
  advertising_channel_methods: [],
  advertising_channel_method_urls: '',
  targeted_treatments_to_promote: '',
  avg_visitors_per_month: '',
  is_affiliate_agreement_accepted: 0,
  is_ppc_impression_cookie_restricted: 0,
})

const advertisingChannels = [
  'Blog/Site',
  'YouTube',
  'Social Media',
  'Marketing Agency',
  'B2B',
  'Other',
]

const treatments = [
  { label: 'Erectile Dysfunction (ED)', value: 'ED' },
  { label: 'Hair Treatment', value: 'HL' },
  { label: 'Weight Loss', value: 'WL' },
]

const validationSchema = yup.object().shape({
  advertising_channel_methods: yup.array()
    .min(1, 'Please select at least one channel.')
    .required('Please select at least one channel.'),
  targeted_treatments_to_promote: yup.array()
    .min(1, 'Please select at least one treatment.')
    .required('Please select at least one channel.'),
  is_affiliate_agreement_accepted: yup.string().required('Please accept the affiliate program agreement.'),
  is_ppc_impression_cookie_restricted: yup.string().required('Please accept the agreement.'),
})

onMounted(async () => {
  if (isEmpty(userId.value)) {
    router.push({ name: 'affiliate-signup' })
  } else {
    if (!isEmptyObject(sessionData.value)) {
      formData.value = { ...sessionData.value }
    }
  }
})

async function handleFormSubmit() {
  try {
    isLoading.value = true
    inputErrors.value = {}
    serverErrors.value = []

    const postData = {
      id: userId.value,
      ...formData.value,
    }

    const { data } = await ApiService.post('/affiliate/update-user-additional-info', postData)

    if (data.status === 200) {
      sessionData.value = { ...formData.value }
      router.push({ name: 'affiliate-signup-payout-methods' })
    } else {
      isLoading.value = false
      if (data.errors) {
        inputErrors.value = data.errors
      } else {
        serverErrors.value = processErrors(data)
      }
    }
  } catch (error) {
    isLoading.value = false
    serverErrors.value = processErrors(error)
    console.error(error)
  }
}
</script>

<template>
  <VForm
    autocomplete="off"
    class="pb-10"
    :validation-schema="validationSchema"
    @submit="handleFormSubmit"
  >
    <AlertError
      v-if="isEmptyObject(inputErrors) && !isEmpty(serverErrors)"
      title="Error!"
      :errors="serverErrors"
    />
    <div class="space-y-6">
      <div>
        <label class="block text-sm font-semibold text-gray-600 mb-2">
          What is your main channel to promote White Label Rx? <span class="text-red-500">*</span>
        </label>
        <MultiSelect
          v-model="formData.advertising_channel_methods"
          name="advertising_channel_methods"
          :options="advertisingChannels"
          placeholder="Select Channel(s)"
          display="chip"
          class="block w-full text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black"
          :show-toggle-all="false"
        />
        <Field
          id="advertising_channel_methods"
          v-model="formData.advertising_channel_methods"
          type="hidden"
          name="advertising_channel_methods"
          class="hidden"
        />
        <ErrorMessage
          name="advertising_channel_methods"
          class="text-red-500 text-sm"
        />
        <p
          v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.advertising_channel_methods)"
          class="text-red-500 text-sm"
        >
          {{ inputErrors.advertising_channel_methods[0] }}
        </p>
      </div>

      <div class="relative">
        <label
          for="advertising_channel_method_urls"
          class="block text-sm font-semibold text-gray-600 mb-2"
        >Please provide the URLs where you are planning to promote White Label Rx. (List all channels)</label>
        <Field
          id="advertising_channel_method_urls"
          v-model="formData.advertising_channel_method_urls"
          as="textarea"
          name="advertising_channel_method_urls"
          rows="3"
          class="block w-full px-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
          placeholder=" "
        ></Field>
        <ErrorMessage
          name="advertising_channel_method_urls"
          class="text-red-500 text-sm"
        />
        <p
          v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.advertising_channel_method_urls)"
          class="text-red-500 text-sm"
        >
          {{ inputErrors.advertising_channel_method_urls[0] }}
        </p>
      </div>

      <div>
        <label
          for="targeted_treatments_to_promote"
          class="block text-sm font-medium text-gray-700 mb-2"
        >Which treatment(s) are you going to promote? <span class="text-red-500">*</span></label>

        <MultiSelect
          v-model="formData.targeted_treatments_to_promote"
          name="targeted_treatments_to_promote"
          :options="treatments"
          option-label="label"
          option-value="value"
          placeholder="Select treatment(s)"
          display="chip"
          class="block w-full text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black"
          :show-toggle-all="false"
        />
        <Field
          id="targeted_treatments_to_promote"
          v-model="formData.targeted_treatments_to_promote"
          type="hidden"
          name="targeted_treatments_to_promote"
          class="hidden"
        />
        <ErrorMessage
          name="targeted_treatments_to_promote"
          class="text-red-500 text-sm"
        />
        <p
          v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.targeted_treatments_to_promote)"
          class="text-red-500 text-sm"
        >
          {{ inputErrors.targeted_treatments_to_promote[0] }}
        </p>
      </div>

      <div class="relative">
        <label
          for="avg_visitors_per_month"
          class="block text-sm font-semibold text-gray-600 mb-2"
        >How many visitors/views does your website/channel receive on average per month?</label>
        <Field
          id="avg_visitors_per_month"
          v-model="formData.avg_visitors_per_month"
          name="avg_visitors_per_month"
          as="textarea"
          rows="3"
          class="block w-full px-5 py-3 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
        />
        <ErrorMessage
          name="avg_visitors_per_month"
          class="text-red-500 text-sm"
        />
        <p
          v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.avg_visitors_per_month)"
          class="text-red-500 text-sm"
        >
          {{ inputErrors.avg_visitors_per_month[0] }}
        </p>
      </div>

      <div class="space-y-4">
        <div>
          <div class="flex items-start">
            <div class="flex items-center h-5">
              <Field
                id="is_affiliate_agreement_accepted"
                v-model="formData.is_affiliate_agreement_accepted"
                name="is_affiliate_agreement_accepted"
                type="checkbox"
                value="1"
                class="focus:ring-black h-5 w-5 text-black border !border-gray-300 rounded"
              />
            </div>
            <div class="ms-3 text-sm">
              <label
                for="is_affiliate_agreement_accepted"
                class="font-medium text-gray-700"
              >I read the Affiliate Program Agreement: <a
                :href="affiliateAgreementUrl"
                class="text-black underline hover:no-underline font-medium"
                target="_blank"
                rel="noopener noreferrer"
              >{{ affiliateAgreementUrl }}</a></label>
            </div>
          </div>
          <ErrorMessage
            name="is_affiliate_agreement_accepted"
            class="text-red-500 text-sm ms-8"
          />
          <p
            v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.is_affiliate_agreement_accepted)"
            class="text-red-500 text-sm ms-8"
          >
            {{ inputErrors.is_affiliate_agreement_accepted[0] }}
          </p>
        </div>
        <div>
          <div class="flex items-start">
            <div class="flex items-center h-5">
              <Field
                id="is_ppc_impression_cookie_restricted"
                v-model="formData.is_ppc_impression_cookie_restricted"
                name="is_ppc_impression_cookie_restricted"
                type="checkbox"
                value="1"
                class="focus:ring-black h-5 w-5 text-black border !border-gray-300 rounded"
              />
            </div>
            <div class="ms-3 text-sm">
              <label
                for="is_ppc_impression_cookie_restricted"
                class="font-medium text-gray-700"
              >I understand that White Label Rx does NOT allow pay per click/impression advertising, cookie insertions, impersonating White Label Rx, and remains the right to suspend my affiliate account/commissions if I use such methods.</label>
            </div>
          </div>
        </div>
        <ErrorMessage
          name="is_ppc_impression_cookie_restricted"
          class="text-red-500 text-sm ms-8"
        />
        <p
          v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.is_ppc_impression_cookie_restricted)"
          class="text-red-500 text-sm ms-8"
        >
          {{ inputErrors.is_ppc_impression_cookie_restricted[0] }}
        </p>
      </div>
    </div>

    <div class="mt-8 flex justify-between">
      <RouterLink
        :to="{ name: 'affiliate-signup-address-details' }"
        type="button"
        class="btn-secondary w-40"
      >
        Previous
      </RouterLink>
      <div></div> <!-- Spacer for alignment -->
      <TwButton
        :loading="isLoading"
        type="submit"
        class="w-40"
      >
        Next
      </TwButton>
    </div>
  </VForm>
</template>
