<script setup>
import { onMounted, ref } from 'vue'
import { isEmpty, isEmptyObject, isNullOrUndefined } from '@/@core/utils'
import { removeKeyFromObject } from '@/utils/helpers'
import AlertError from '@/views/user/components/AlertError.vue'
import router from '@/router'
import { ErrorMessage, Field, Form as VForm } from 'vee-validate'
import * as yup from 'yup'
import ApiService from '@/services/ApiService'
import { processErrors } from '@/utils/errorHandler'
import { useSessionStorage } from '@vueuse/core'

const userId = useSessionStorage('affiliateUserId', undefined)
const step1SessionData = useSessionStorage('affiliateUserDetailsSessionData', {})
const isLoading = ref(false)
const inputErrors = ref({})
const serverErrors = ref([])

const formData = reactive({
  payout_method: '', // PayPal, Direct Bank Transfer

  // required only if payout_method is PayPal
  payout_paypal_email: '',

  // required only if payout_method is Direct Bank Transfer
  account_holder_first_name: '',
  account_holder_last_name: '',
  bank_name: '',
  account_number: '',
  routing_number: '',
})

const validationSchema = yup.object().shape({
  payout_method: yup.string().required('Please select a payout method.'),

  // required only if payout_method is PayPal
  payout_paypal_email: yup.string().nullable().when('payout_method', {
    is: 'PayPal',
    then: schema => schema
      .required('Please enter your PayPal email address.')
      .email('Please enter a valid email address.'),
    otherwise: schema => schema.nullable(),
  }),

  // required only if payout_method is Direct Bank Transfer
  account_holder_first_name: yup.string().when('payout_method', {
    is: 'Direct Bank Transfer',
    then: schema => schema.required('Please enter your first name.'),
    otherwise: schema => schema.nullable(),
  }),
  account_holder_last_name: yup.string().when('payout_method', {
    is: 'Direct Bank Transfer',
    then: schema => schema.required('Please enter your last name.'),
    otherwise: schema => schema.nullable(),
  }),
  bank_name: yup.string().when('payout_method', {
    is: 'Direct Bank Transfer',
    then: schema => schema.required('Please enter the name of your bank.'),
    otherwise: schema => schema.nullable(),
  }),
  account_number: yup.string().when('payout_method', {
    is: 'Direct Bank Transfer',
    then: schema => schema.required('Please enter your account number.'),
    otherwise: schema => schema.nullable(),
  }),
  routing_number: yup.string().when('payout_method', {
    is: 'Direct Bank Transfer',
    then: schema => schema.required('Please enter your routing number.'),
    otherwise: schema => schema.nullable(),
  }),
})

onMounted(async () => {
  if (isEmpty(userId.value)) {
    router.push({ name: 'affiliate-signup' })
  }
  if (!isEmptyObject(step1SessionData.value)) {
    formData.account_holder_first_name = step1SessionData.value.first_name
    formData.account_holder_last_name = step1SessionData.value.last_name
  }
})

async function handleFormSubmit() {
  try {
    isLoading.value = true
    inputErrors.value = {}
    serverErrors.value = []

    const postData = {
      id: userId.value,
      payout_method: formData.payout_method,
    }

    if (formData.payout_method === 'PayPal') {
      postData['payout_paypal_email'] = formData.payout_paypal_email
    } else if (formData.payout_method === 'Direct Bank Transfer') {
      postData['account_holder_first_name'] = formData.account_holder_first_name
      postData['account_holder_last_name'] = formData.account_holder_last_name
      postData['bank_name'] = formData.bank_name
      postData['account_number'] = formData.account_number
      postData['routing_number'] = formData.routing_number
    }

    const { data } = await ApiService.post('/affiliate/update-user-payout-method', postData)

    if (data.status === 200) {
      sessionStorage.setItem('affiliateUserSignupSuccess', true)
      router.push({ name: 'affiliate-signup-success' })
    } else {
      isLoading.value = false
      if (data.errors) {
        inputErrors.value = data.errors
      } else {
        serverErrors.value = processErrors(data)
      }
    }
  } catch (error) {
    isLoading.value = false
    serverErrors.value = processErrors(error)
    console.error(error)
  }
}

function removeKeyFromInputErrors(key) {
  inputErrors.value = removeKeyFromObject(inputErrors.value, key)
}
</script>

<template>
  <VForm
    autocomplete="off"
    class="pb-10"
    :validation-schema="validationSchema"
    @submit="handleFormSubmit"
  >
    <AlertError
      v-if="isEmptyObject(inputErrors) && !isEmpty(serverErrors)"
      title="Error!"
      :errors="serverErrors"
    />
    <div class="grid grid-cols-1 gap-6">
      <div>
        <label
          for="payout_method"
          class="block text-sm font-medium text-gray-700 mb-2"
        >Payout Method <span class="text-red-500">*</span></label>
        <Field
          id="payout_method"
          v-model="formData.payout_method"
          name="payout_method"
          as="select"
          class="block w-full px-5 py-3 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
          placeholder="Select treatment(s)"
        >
          <option value="PayPal">
            PayPal
          </option>
          <option value="Direct Bank Transfer">
            Direct Bank Transfer
          </option>
        </Field>
        <ErrorMessage
          name="payout_method"
          class="text-red-500 text-sm"
        />
        <p
          v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.payout_method)"
          class="text-red-500 text-sm"
        >
          {{ inputErrors.payout_method[0] }}
        </p>
      </div>

      <div
        v-if="formData.payout_method === 'PayPal'"
        class="relative flex-auto"
      >
        <label
          for="payout_paypal_email"
          class="block text-sm font-medium text-gray-700 mb-2"
        >PayPal Email Address <span class="text-red-500">*</span></label>
        <Field
          id="payout_paypal_email"
          v-model="formData.payout_paypal_email"
          type="text"
          name="payout_paypal_email"
          class="block w-full px-5 py-3 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
          placeholder=" "
          autocomplete="off"
          @keyup="removeKeyFromInputErrors('payout_paypal_email')"
        />
        <ErrorMessage
          name="payout_paypal_email"
          class="text-red-500 text-sm"
        />
        <p
          v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.payout_paypal_email)"
          class="text-red-500 text-sm"
        >
          {{ inputErrors.payout_paypal_email[0] }}
        </p>
      </div>

      <div
        v-if="formData.payout_method === 'Direct Bank Transfer'"
        class="grid grid-cols-1 sm:grid-cols-2 gap-6"
      >
        <div class="relative flex-auto">
          <label
            for="account_holder_first_name"
            class="block text-sm font-medium text-gray-700 mb-2"
          >Account Holder First Name <span class="text-red-500">*</span></label>
          <Field
            id="account_holder_first_name"
            v-model="formData.account_holder_first_name"
            type="text"
            name="account_holder_first_name"
            class="block w-full px-5 py-3 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
            placeholder=" "
            autocomplete="off"
            @keyup="removeKeyFromInputErrors('account_holder_first_name')"
          />
          <ErrorMessage
            name="account_holder_first_name"
            class="text-red-500 text-sm"
          />
          <p
            v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.account_holder_first_name)"
            class="text-red-500 text-sm"
          >
            {{ inputErrors.account_holder_first_name[0] }}
          </p>
        </div>

        <div class="relative flex-auto">
          <label
            for="account_holder_last_name"
            class="block text-sm font-medium text-gray-700 mb-2"
          >Account Holder First Name <span class="text-red-500">*</span></label>
          <Field
            id="account_holder_last_name"
            v-model="formData.account_holder_last_name"
            type="text"
            name="account_holder_last_name"
            class="block w-full px-5 py-3 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
            placeholder=" "
            autocomplete="off"
            @keyup="removeKeyFromInputErrors('account_holder_last_name')"
          />
          <ErrorMessage
            name="account_holder_last_name"
            class="text-red-500 text-sm"
          />
          <p
            v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.account_holder_last_name)"
            class="text-red-500 text-sm"
          >
            {{ inputErrors.account_holder_last_name[0] }}
          </p>
        </div>

        <div class="relative flex-auto col-span-1 sm:col-span-2">
          <label
            for="bank_name"
            class="block text-sm font-medium text-gray-700 mb-2"
          >Bank Name <span class="text-red-500">*</span></label>
          <Field
            id="bank_name"
            v-model="formData.bank_name"
            type="text"
            name="bank_name"
            class="block w-full px-5 py-3 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
            placeholder=" "
            autocomplete="off"
            @keyup="removeKeyFromInputErrors('bank_name')"
          />
          <ErrorMessage
            name="bank_name"
            class="text-red-500 text-sm"
          />
          <p
            v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.bank_name)"
            class="text-red-500 text-sm"
          >
            {{ inputErrors.bank_name[0] }}
          </p>
        </div>

        <div class="relative flex-auto">
          <label
            for="account_number"
            class="block text-sm font-medium text-gray-700 mb-2"
          >Account Number <span class="text-red-500">*</span></label>
          <Field
            id="account_number"
            v-model="formData.account_number"
            type="text"
            name="account_number"
            class="block w-full px-5 py-3 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
            placeholder=" "
            autocomplete="off"
            @keyup="removeKeyFromInputErrors('account_number')"
          />
          <ErrorMessage
            name="account_number"
            class="text-red-500 text-sm"
          />
          <p
            v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.account_number)"
            class="text-red-500 text-sm"
          >
            {{ inputErrors.account_number[0] }}
          </p>
        </div>

        <div class="relative flex-auto">
          <label
            for="routing_number"
            class="block text-sm font-medium text-gray-700 mb-2"
          >Routing Number <span class="text-red-500">*</span></label>
          <Field
            id="routing_number"
            v-model="formData.routing_number"
            type="text"
            name="routing_number"
            class="block w-full px-5 py-3 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
            placeholder=" "
            autocomplete="off"
            @keyup="removeKeyFromInputErrors('routing_number')"
          />
          <ErrorMessage
            name="routing_number"
            class="text-red-500 text-sm"
          />
          <p
            v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.routing_number)"
            class="text-red-500 text-sm"
          >
            {{ inputErrors.routing_number[0] }}
          </p>
        </div>
      </div>
    </div>

    <div class="mt-8 flex justify-between">
      <RouterLink
        :to="{ name: 'affiliate-signup-additional-questions' }"
        type="button"
        class="btn-secondary w-40"
      >
        Previous
      </RouterLink>
      <div></div> <!-- Spacer for alignment -->
      <TwButton
        :loading="isLoading"
        type="submit"
        class="w-40"
      >
        Submit
      </TwButton>
    </div>
  </VForm>
</template>
