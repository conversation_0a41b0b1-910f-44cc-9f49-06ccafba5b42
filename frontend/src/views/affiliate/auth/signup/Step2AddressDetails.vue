<script setup>
import { computed, onMounted, ref } from 'vue'
import { isEmpty, isEmptyObject, isNullOrUndefined } from '@/@core/utils'
import AlertError from '@/views/user/components/AlertError.vue'
import router from '@/router'
import { useSessionStorage } from '@vueuse/core'
import { ErrorMessage, Field, Form as VForm } from 'vee-validate'
import * as yup from 'yup'
import { vMaska } from 'maska'
import ApiService from '@/services/ApiService'
import { processErrors } from '@/utils/errorHandler'
import AutoComplete from 'primevue/autocomplete'
import useMapsApi from '@/composables/useMapsApi'
import { states } from '@/data/states'

const { searchPlaces, autoCompleteOptions } = useMapsApi()

const userId = useSessionStorage('affiliateUserId', undefined)
const sessionData = useSessionStorage('affiliateAddressDetailsSessionData', {})
const isLoading = ref(false)
const inputErrors = ref({})
const serverErrors = ref([])
const countries = [{ label: 'USA', value: 'USA' }]

const formData = ref({
  address_1: '',
  address_2: '',
  city: '',
  state: '',
  zipcode: '',
  country: 'USA',
})

const validationSchema = yup.object().shape({
  address_1: yup.string().required('Street address is required'),
  city: yup.string().required('City is required'),
  state: yup.string().required('State / Territory is required'),
  zipcode: yup.string().required('Zipcode is required'),
  country: yup.string().required('Country is required'),
})

const statesList = computed(() => states.filter(item => item.type === 'state'))
const territoriesList = computed(() => states.filter(item => item.type === 'territory'))

onMounted(async () => {
  if (isEmpty(userId.value)) {
    router.push({ name: 'affiliate-signup' })
  } else {
    if (!isEmptyObject(sessionData.value)) {
      formData.value = { ...sessionData.value }
    }
  }
})

const handleAddressSearch = () => {
  if (!isEmpty(formData.value.address_1)) {
    searchPlaces(formData.value.address_1)
  }
}

const handleAddressItemSelection = $event => {
  const item = $event.value

  formData.value.address_1 = item.streetAddress
  formData.value.city = item.city

  for (const state of states) {
    if (state.code === item.state) {
      formData.value.state = item.state
    }
  }

  formData.value.zipcode = item.postalCode
  formData.value.country = item.country
}

async function handleFormSubmit() {
  try {
    isLoading.value = true
    inputErrors.value = {}
    serverErrors.value = []

    const postData = {
      id: userId.value,
      ...formData.value,
    }

    const { data } = await ApiService.post('/affiliate/update-user-address', postData)

    if (data.status === 200) {
      sessionData.value = { ...formData.value }
      router.push({ name: 'affiliate-signup-additional-questions' })
    } else {
      isLoading.value = false
      if (data.errors) {
        inputErrors.value = data.errors
      } else {
        serverErrors.value = processErrors(data)
      }
    }
  } catch (error) {
    isLoading.value = false
    serverErrors.value = processErrors(error)
    console.error(error)
  }
}
</script>

<template>
  <VForm
    autocomplete="off"
    :validation-schema="validationSchema"
    class="pb-10"
    @submit="handleFormSubmit"
  >
    <AlertError
      v-if="isEmptyObject(inputErrors) && !isEmpty(serverErrors)"
      title="Error!"
      :errors="serverErrors"
    />
    <div class="space-y-5">
      <!-- 👉 Street address -->
      <div>
        <div class="relative">
          <AutoComplete
            id="address_1"
            v-model="formData.address_1"
            name="address_1"
            option-label="streetAddress"
            placeholder="Street Address"
            class="w-full"
            input-class="block w-full px-5 py-3.5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:!outline-none focus:!ring-1 focus:!ring-black peer"
            :suggestions="autoCompleteOptions"
            :delay="500"
            @complete="handleAddressSearch"
            @item-select="handleAddressItemSelection"
          >
            <template #option="slotProps">
              <div class="">
                <p class="mb-0 text-sm font-medium">
                  {{ slotProps.option.displayName }}
                </p>
                <small>{{ slotProps.option.formattedAddress }}</small>
              </div>
            </template>
          </AutoComplete>
          <Field
            v-model="formData.address_1"
            type="hidden"
            class="hidden h-0 w-0"
            name="address_1"
            required
          />
        </div>
        <ErrorMessage
          name="address_1"
          class="text-red-500 text-sm ms-5"
        />
        <p
          v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.address_1)"
          class="text-red-500 text-sm ms-5"
        >
          {{ inputErrors.address_1[0] }}
        </p>
      </div>

      <!-- 👉 Street address line 2 -->
      <div>
        <div class="relative">
          <Field
            id="address_2"
            v-model="formData.address_2"
            type="text"
            name="address_2"
            class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
            placeholder=" "
            autocomplete="off"
            required
          />
          <label
            for="address_2"
            class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4 cursor-text"
          >Apt / Suite (optional)</label>
        </div>
        <ErrorMessage
          name="address_2"
          class="text-red-500 text-sm ms-5"
        />
        <p
          v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.address_2)"
          class="text-red-500 text-sm ms-5"
        >
          {{ inputErrors.address_2[0] }}
        </p>
      </div>

      <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
        <!-- 👉 City -->
        <div>
          <div class="relative">
            <Field
              id="city"
              v-model="formData.city"
              type="text"
              name="city"
              class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
              placeholder=" "
              autocomplete="off"
              required
            />
            <label
              for="city"
              class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4 cursor-text"
            >City</label>
          </div>
          <ErrorMessage
            name="city"
            class="text-red-500 text-sm ms-5"
          />
          <p
            v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.city)"
            class="text-red-500 text-sm ms-5"
          >
            {{ inputErrors.city[0] }}
          </p>
        </div>

        <!-- 👉 State / Territory -->
        <div class="relative">
          <Field
            id="state"
            v-model="formData.state"
            as="select"
            name="state"
            class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
            required
          >
            <option
              value=""
              disabled
              selected
            >
              Select
            </option>
            <optgroup label="States">
              <option
                v-for="state in statesList"
                :key="state.code"
                :value="state.code"
              >
                {{ state.name }}
              </option>
            </optgroup>
            <optgroup label="Territories">
              <option
                v-for="territory in territoriesList"
                :key="territory.code"
                :value="territory.code"
              >
                {{ territory.name }}
              </option>
            </optgroup>
          </Field>
          <label
            for="state"
            class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-focus:scale-75 peer-focus:-translate-y-4"
          >State / Territory</label>
          <ErrorMessage
            name="state"
            class="text-red-500 text-sm ms-5"
          />
          <p
            v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.state)"
            class="text-red-500 text-sm ms-5"
          >
            {{ inputErrors.state[0] }}
          </p>
        </div>
      </div>

      <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
        <!-- 👉 Zipcode -->
        <div>
          <div class="relative">
            <Field
              id="zipcode"
              v-model="formData.zipcode"
              v-maska
              type="text"
              name="zipcode"
              class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
              placeholder=" "
              autocomplete="off"
              data-maska="#####-####"
              required
            />
            <label
              for="zipcode"
              class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4 cursor-text"
            >Zipcode</label>
          </div>
          <ErrorMessage
            name="zipcode"
            class="text-red-500 text-sm ms-5"
          />
          <p
            v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.zipcode)"
            class="text-red-500 text-sm ms-5"
          >
            {{ inputErrors.zipcode[0] }}
          </p>
        </div>

        <!-- 👉 Country -->
        <div class="relative">
          <Field
            id="country"
            v-model="formData.country"
            as="select"
            name="country"
            class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
            required
          >
            <option
              value=""
              disabled
              selected
            >
              Select
            </option>
            <option
              v-for="item in countries"
              :key="item.value"
              :value="item.value"
            >
              {{ item.label }}
            </option>
          </Field>
          <label
            for="country"
            class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-focus:scale-75 peer-focus:-translate-y-4"
          >Country</label>
          <ErrorMessage
            name="country"
            class="text-red-500 text-sm ms-5"
          />
          <p
            v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.country)"
            class="text-red-500 text-sm ms-5"
          >
            {{ inputErrors.country[0] }}
          </p>
        </div>
      </div>
    </div>

    <div class="mt-8 flex justify-between">
      <RouterLink
        :to="{ name: 'affiliate-signup-user-details' }"
        type="button"
        class="btn-secondary w-40"
      >
        Previous
      </RouterLink>
      <div></div> <!-- Spacer for alignment -->
      <TwButton
        :loading="isLoading"
        type="submit"
        class="w-40"
      >
        Next
      </TwButton>
    </div>
  </VForm>
</template>

<style lang="scss">
.p-inputtext {
  border: none !important;
  border-color: transparent;
  font-family: "Konnect", system-ui, -apple-system, BlinkMacSystemFont,
    "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue",
    sans-serif;
    font-size: 14px !important;
    font-weight: 500;

  &:enabled:hover {
    border-color: transparent;
  }
  &:enabled:focus {
    outline: none;
    outline-offset: 0;
    box-shadow: none;
    border-color: transparent;
  }
}

.p-autocomplete-panel {
  max-width: 400px;
}
.p-autocomplete-panel .p-autocomplete-items .p-autocomplete-item {
  white-space: pre-line;
}
.p-autocomplete-panel {
  z-index: 2102 !important;
}
</style>
