<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import Header from '@/views/user/components/Header.vue'

const steps = [
  { title: 'User Details', route: '/affiliate/signup/user-details' },
  { title: 'Address Details', route: '/affiliate/signup/address-details' },
  { title: 'Additional Questions', route: '/affiliate/signup/additional-questions' },
  { title: 'Payout Methods', route: '/affiliate/signup/payout-methods' },
]

const route = useRoute()

const currentStepIndex = computed(() => {
  return steps.findIndex(step => step.route === route.path)
})
</script>

<template>
  <div>
    <Header />

    <div class="flex items-center justify-center px-4 sm:px-6 lg:px-8">
      <div class="max-w-3xl w-full space-y-10 p-8">
        <div class="text-center">
          <h2 class="text-3xl font-extrabold text-gray-900">
            Sign Up for Affiliate Program
          </h2>
          <p
            v-if="currentStepIndex === 0"
            class="mt-2 text-base text-gray-600"
          >
            Already have an account? <RouterLink
              :to="{ name: 'affiliate-login' }"
              class="text-black underline font-medium"
            >
              Sign in
            </RouterLink>
          </p>
        </div>

        <!-- Stepper -->
        <div class="flex justify-between items-center mb-8">
          <template
            v-for="(step, index) in steps"
            :key="index"
          >
            <div class="flex flex-col justify-center items-center">
              <div
                class="rounded-full h-10 w-10 flex items-center justify-center"
                :class="[
                  currentStepIndex > index
                    ? 'bg-green-500 text-white'
                    : currentStepIndex === index
                      ? 'bg-zinc-700 text-white'
                      : 'bg-gray-300 text-gray-600'
                ]"
              >
                {{ index + 1 }}
              </div>
              <div
                class="mt-2 text-sm font-medium hidden sm:block"
                :class="{'text-zinc-700 font-semibold': currentStepIndex === index}"
              >
                {{ step.title }}
              </div>
            </div>
            <div
              v-if="index < steps.length - 1"
              class="flex-1 border-t-2 !border-gray-300 mx-2"
            ></div>
          </template>
        </div>

        <!-- Router view for step components -->
        <router-view></router-view>
      </div>
    </div>
  </div>
</template>
