<script setup>
import { onMounted, ref } from 'vue'
import { isEmpty, isEmptyObject, isNullOrUndefined } from '@/@core/utils'
import { preventSpacesFromStart, removeKeyFromObject } from '@/utils/helpers'
import AlertError from '@/views/user/components/AlertError.vue'
import router from '@/router'
import { useSessionStorage } from '@vueuse/core'
import { ErrorMessage, Field, Form as VForm } from 'vee-validate'
import * as yup from 'yup'
import { vMaska } from 'maska'
import { IconEye, IconEyeOff } from '@tabler/icons-vue'
import useCaptcha from '@/composables/useCaptcha'
import ApiService from '@/services/ApiService'
import { processErrors } from '@/utils/errorHandler'

const { getRecaptchaToken } = useCaptcha()

const userId = useSessionStorage('affiliateUserId', undefined)
const sessionData = useSessionStorage('affiliateUserDetailsSessionData', {})
const isLoading = ref(false)
const inputErrors = ref({})
const serverErrors = ref([])
const isPasswordVisible = ref(false)
const isConfirmPasswordVisible = ref(false)

const formData = ref({
  'first_name': '',
  'last_name': '',
  'email': '',
  'phone_number': '',
  'password': '',
  'confirm_password': '',
  'website': '',
  'company_name': '',
})

onMounted(() => {
  if (!isEmptyObject(sessionData.value)) {
    formData.value = { ...sessionData.value }
  }
})

const validationSchema = yup.object().shape({
  first_name: yup.string().required('First name is required'),
  last_name: yup.string().required('Last name is required'),
  phone_number: yup.string().required('Phone Number is required'),
  email: yup.string()
    .required('Email address is required')
    .matches(/^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,4}$/, 'Invalid email address'),
  password: yup.string()
    .required('Password is required')
    .min(8, 'Password must be at least 8 characters')
    .matches(/(?=.*[A-Z0-9!@#$%^&*])/, 'Include at least one uppercase letter or number or symbol'),
  confirm_password: yup.string().required('Confirm Password is required').oneOf([yup.ref('password'), null], 'Passwords must match'),
})

function removeKeyFromInputErrors(key) {
  inputErrors.value = removeKeyFromObject(inputErrors.value, key)
}

async function handleFormSubmit(values) {
  try {
    isLoading.value = true
    inputErrors.value = {}
    serverErrors.value = []

    const captchaToken = await getRecaptchaToken('affiliate_signup')

    const postData = {
      ...values,
      phone_number: values.phone_number.replace(/\D/g, ''),
      'g-recaptcha-response': captchaToken,
    }

    if (userId.value) {
      postData['id'] = userId.value
    }

    const { data } = await ApiService.post('/affiliate/user-account-details', postData)

    if (data.status === 200) {
      userId.value = data.id
      sessionData.value = { ...formData.value }
      router.push({ name: 'affiliate-signup-address-details' })
    } else {
      isLoading.value = false
      if (data.errors) {
        inputErrors.value = data.errors
      } else {
        serverErrors.value = processErrors(data)
      }
    }
  } catch (error) {
    isLoading.value = false
    serverErrors.value = processErrors(error)
    console.error(error)
  }
}
</script>

<template>
  <VForm
    class="pb-10"
    :validation-schema="validationSchema"
    autocomplete="off"
    @submit="handleFormSubmit"
  >
    <AlertError
      v-if="isEmptyObject(inputErrors) && !isEmpty(serverErrors)"
      title="Error!"
      :errors="serverErrors"
    />

    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
      <div class="relative flex-auto">
        <Field
          id="first_name"
          v-model="formData.first_name"
          type="text"
          name="first_name"
          class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
          placeholder=" "
          autocomplete="off"
          autofocus
          @keydown="preventSpacesFromStart($event)"
          @keyup="removeKeyFromInputErrors('first_name')"
        />
        <label
          for="first_name"
          class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4 cursor-text"
        >First Name <span class="text-red-500">*</span></label>
        <ErrorMessage
          name="first_name"
          class="text-red-500 text-sm ms-5"
        />
        <p
          v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.first_name)"
          class="text-red-500 text-sm ms-5"
        >
          {{ inputErrors.first_name[0] }}
        </p>
      </div>

      <div class="relative flex-auto">
        <Field
          id="last_name"
          v-model="formData.last_name"
          type="text"
          name="last_name"
          class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
          placeholder=" "
          autocomplete="off"
          @keydown="preventSpacesFromStart($event)"
          @keyup="removeKeyFromInputErrors('last_name')"
        />
        <label
          for="last_name"
          class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4 cursor-text"
        >Last Name <span class="text-red-500">*</span></label>
        <ErrorMessage
          name="last_name"
          class="text-red-500 text-sm ms-5"
        />
        <p
          v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.last_name)"
          class="text-red-500 text-sm ms-5"
        >
          {{ inputErrors.last_name[0] }}
        </p>
      </div>

      <div>
        <div class="relative">
          <Field
            id="phone_number"
            v-model="formData.phone_number"
            v-maska
            type="text"
            name="phone_number"
            class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
            placeholder=" "
            autocomplete="off"
            data-maska="(###) ###-####"
            required
            @keyup="removeKeyFromInputErrors('phone_number')"
          />
          <label
            for="phone_number"
            class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4 cursor-text"
          >Phone Number <span class="text-red-500">*</span></label>
        </div>
        <ErrorMessage
          name="phone_number"
          class="text-red-500 text-sm ms-5"
        />
        <p
          v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.phone_number)"
          class="text-red-500 text-sm ms-5"
        >
          {{ inputErrors.phone_number[0] }}
        </p>
      </div>

      <div class="relative mb-4">
        <Field
          id="email"
          v-model="formData.email"
          type="email"
          name="email"
          class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
          placeholder=" "
          autocomplete="off"
          validate-on-input
          autofocus
          required
          @keydown="preventSpacesFromStart($event)"
          @keyup="removeKeyFromInputErrors('email')"
        />
        <label
          for="email"
          class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4 cursor-text"
        >Email address <span class="text-red-500">*</span></label>
        <ErrorMessage
          name="email"
          class="text-red-500 text-sm ms-5 inline-block"
        />
        <p
          v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.email)"
          class="text-red-500 text-sm ms-5"
        >
          {{ inputErrors.email[0] }}
        </p>
      </div>

      <div class="relative">
        <Field
          id="company_name"
          v-model="formData.company_name"
          name="company_name"
          type="text"
          class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
          placeholder=" "
          @keydown="preventSpacesFromStart($event)"
          @keyup="removeKeyFromInputErrors('company_name')"
        />
        <label
          for="company_name"
          class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4 cursor-text"
        >Company Name</label>
        <ErrorMessage
          name="company_name"
          class="text-red-500 text-sm ms-5 inline-block"
        />
        <p
          v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.company_name)"
          class="text-red-500 text-sm ms-5"
        >
          {{ inputErrors.company_name[0] }}
        </p>
      </div>

      <div class="relative">
        <Field
          id="website"
          v-model="formData.website"
          name="website"
          type="text"
          class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
          placeholder=" "
          @keydown="preventSpacesFromStart($event)"
          @keyup="removeKeyFromInputErrors('website')"
        />
        <label
          for="website"
          class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4 cursor-text"
        >Website</label>
        <ErrorMessage
          name="website"
          class="text-red-500 text-sm ms-5 inline-block"
        />
        <p
          v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.website)"
          class="text-red-500 text-sm ms-5"
        >
          {{ inputErrors.website[0] }}
        </p>
      </div>

      <div>
        <div class="relative">
          <Field
            v-slot="{ field }"
            name="password"
          >
            <input
              v-bind="field"
              id="password"
              v-model="formData.password"
              :type="isPasswordVisible ? 'text' : 'password'"
              class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
              placeholder=" "
              autocomplete="new-password"
              required
              @keyup="removeKeyFromInputErrors('password')"
              @keydown="preventSpacesFromStart($event)"
            >
          </Field>
          <label
            for="password"
            class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4"
          >Password <span class="text-red-500">*</span></label>
          <div class="absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
            <IconEye
              v-if="!isPasswordVisible"
              class="h-5 w-5"
              stroke-width="2"
              @click="isPasswordVisible = !isPasswordVisible"
            />
            <IconEyeOff
              v-else
              class="h-5 w-5"
              stroke-width="2"
              @click="isPasswordVisible = !isPasswordVisible"
            />
          </div>
        </div>
        <ErrorMessage
          name="password"
          class="text-red-500 text-sm ms-5 inline-block"
        />
        <p
          v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.password)"
          class="text-red-500 text-sm ms-5"
        >
          {{ inputErrors.password[0] }}
        </p>
      </div>

      <div>
        <div class="relative">
          <Field
            v-slot="{ field }"
            name="confirm_password"
          >
            <input
              v-bind="field"
              id="confirm_password"
              v-model="formData.confirm_password"
              :type="isConfirmPasswordVisible ? 'text' : 'password'"
              class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
              placeholder=" "
              autocomplete="new-password"
              required
              @keyup="removeKeyFromInputErrors('confirm_password')"
              @keydown="preventSpacesFromStart($event)"
            >
          </Field>
          <label
            for="confirm_password"
            class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4"
          >Confirm Password <span class="text-red-500">*</span></label>
          <div class="absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
            <IconEye
              v-if="!isConfirmPasswordVisible"
              class="h-5 w-5"
              stroke-width="2"
              @click="isConfirmPasswordVisible = !isConfirmPasswordVisible"
            />
            <IconEyeOff
              v-else
              class="h-5 w-5"
              stroke-width="2"
              @click="isConfirmPasswordVisible = !isConfirmPasswordVisible"
            />
          </div>
        </div>
        <ErrorMessage
          name="confirm_password"
          class="text-red-500 text-sm ms-5 inline-block"
        />
        <p
          v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.confirm_password)"
          class="text-red-500 text-sm ms-5"
        >
          {{ inputErrors.confirm_password[0] }}
        </p>
      </div>
    </div>

    <div class="mt-10 flex justify-between">
      <div></div> <!-- Spacer for alignment -->
      <TwButton
        type="submit"
        class="w-40"
        :loading="isLoading"
      >
        Next
      </TwButton>
    </div>
  </VForm>
</template>
