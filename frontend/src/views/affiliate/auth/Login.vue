<script setup>
import { isEmptyObject, isNullOrUndefined } from '@/@core/utils'
import { useAppAbility } from '@/plugins/casl/useAppAbility'
import { alertWarning } from '@/plugins/sweetalert2'
import router from '@/router'
import ApiService from '@/services/ApiService'
import { useAuthStore } from '@/store/auth'
import { processErrors } from '@/utils/errorHandler'
import AlertError from '@/views/user/components/AlertError.vue'
import { IconArrowLeft, IconBolt, IconEye, IconEyeOff, IconMail } from '@tabler/icons-vue'
import { ErrorMessage, Field, Form as VForm } from 'vee-validate'
import { computed, onMounted } from 'vue'
import { RouterLink, useRoute } from 'vue-router'
import * as yup from 'yup'
import Header from '@/views/user/components/Header.vue'
import useCaptcha from '@/composables/useCaptcha'

const { getRecaptchaToken } = useCaptcha()
const authStore = useAuthStore()

const isLoading = ref(false)
const inputErrors = ref({})
const serverErrors = ref([])
const isPasswordVisible = ref(false)
const password = ref('')
const passwordInputRef = ref(null)
const frontendUrl = computed(() => import.meta.env.VITE_MARKETING_SITE_URL)

onMounted(() => {
  showResetPasswordLinkExpiredPopup()
})

const validationSchema = yup.object().shape({
  email: yup.string().required('Email address is required').email('Invalid email address'),
  password: yup.string().required('Password is required'),
})

const handleSubmit = async values => {
  try {
    isLoading.value = true
    inputErrors.value = {}
    serverErrors.value = []

    const captchaToken = await getRecaptchaToken('login')

    const postData = {
      ...values,
      'g-recaptcha-response': captchaToken,
    }

    const { data } = await ApiService.post('/affiliate/login', postData)

    if (data.status === 200) {
      authStore.setAuth(data)
      router.replace({ name: 'affiliate-dashboard' })
    } else {
      isLoading.value = false
      if (data.errors) {
        inputErrors.value = data.errors
      } else {
        serverErrors.value = processErrors(data)
      }
    }
  } catch (error) {
    console.error(error)
    isLoading.value = false
    serverErrors.value = processErrors(error)
  }
}

const showResetPasswordLinkExpiredPopup = () => {
  if (sessionStorage.getItem('resetLinkExpired') === '1') {
    alertWarning.fire({
      title: 'Link Expired!',
      html: 'We apologize, but it appears that the link to reset your password has expired. Please request a new link to reset your password.',
      confirmButtonColor: '#000',
    })
    sessionStorage.removeItem('resetLinkExpired')
  }
}
</script>

<template>
  <div>
    <Header show-back-button />

    <div class="flex items-center justify-center py-14 sm:px-16">
      <div class="px-4 w-full sm:max-w-lg">
        <h2 class="text-center text-2xl md:text-4xl font-bold leading-tight text-black">
          Affiliate Login!
        </h2>
        <p class="mt-2 text-center text-base text-gray-600">
          Not a member? <RouterLink
            :to="{ name: 'affiliate-signup' }"
            class="text-black underline font-medium"
          >
            Sign up
          </RouterLink>
        </p>

        <AlertError
          v-if="isEmptyObject(inputErrors) && serverErrors.length > 0"
          title="Error!"
          :errors="serverErrors"
        />

        <VForm
          class="mt-8"
          :validation-schema="validationSchema"
          @submit="handleSubmit"
        >
          <div class="space-y-5">
            <div class="mb-4">
              <div class="relative">
                <Field
                  v-slot="{ field }"
                  name="email"
                >
                  <input
                    id="email"
                    v-bind="field"
                    type="email"
                    name="email"
                    class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                    placeholder=" "
                    autofocus
                    required
                  />
                </Field>
                <label
                  for="email"
                  class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4 cursor-text"
                >Email</label>
                <div class="absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                  <IconMail
                    class="h-5 w-5"
                    color="#fff"
                    fill="#000"
                  />
                </div>
              </div>
              <ErrorMessage
                name="email"
                class="text-red-500 text-sm ms-5"
              />
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.email)"
                class="text-red-500 text-sm ms-5"
              >
                {{ inputErrors.email[0] }}
              </p>
            </div>

            <div class="mb-4">
              <div class="relative">
                <Field
                  v-slot="{ field }"
                  name="password"
                >
                  <input
                    v-bind="field"
                    id="password"
                    ref="passwordInputRef"
                    v-model="password"
                    :type="isPasswordVisible ? 'text' : 'password'"
                    class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                    placeholder=" "
                    required
                  />
                </Field>
                <label
                  for="password"
                  class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4"
                >Password</label>
                <div class="absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                  <IconEye
                    v-if="!isPasswordVisible"
                    class="h-5 w-5"
                    stroke-width="2"
                    @click="isPasswordVisible = !isPasswordVisible"
                  />
                  <IconEyeOff
                    v-else
                    class="h-5 w-5"
                    stroke-width="2"
                    @click="isPasswordVisible = !isPasswordVisible"
                  />
                </div>
              </div>
              <ErrorMessage
                name="password"
                class="text-red-500 text-sm ms-5"
              />
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.password)"
                class="text-red-500 text-sm ms-5"
              >
                {{ inputErrors.password[0] }}
              </p>
            </div>

            <div class="mt-8">
              <TwButton
                type="submit"
                class="w-full uppercase"
                :loading="isLoading"
              >
                <span>Login</span>
                <IconBolt
                  class="h-4 w-4 ms-1"
                  stroke-width="1"
                  fill="currentColor"
                />
              </TwButton>
            </div>
          </div>
        </VForm>

        <div class="mt-8 text-center">
          <RouterLink
            :to="{ name: 'affiliate-forgot-password' }"
            class="text-base underline font-semibold hover:no-underline transition-all"
          >
            Forgot password?
          </RouterLink>
        </div>

        <div class="flex justify-center mt-12">
          <a
            class="text-sm font-semibold border-b-2 border-solid !border-black hover:text-gray-600 hover:!border-gray-600"
            :href="frontendUrl"
          >
            <span class="flex items-center">
              <IconArrowLeft
                stroke-width="2"
                class="h-4 w-4 me-1"
              /> Back to Home
            </span>
          </a>
        </div>
      </div>
    </div>
  </div>
</template>
