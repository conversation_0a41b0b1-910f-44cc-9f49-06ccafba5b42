<script setup>
import Header from '@/views/user/components/Header.vue'
import { Form as VForm, Field, ErrorMessage } from 'vee-validate'
import ApiService from '@/services/ApiService'
import router from '@/router'
import * as yup from 'yup'
import { processErrors } from '@/utils/errorHandler'
import { isEmptyObject, isNullOrUndefined } from '@/@core/utils'
import AlertError from '@/views/user/components/AlertError.vue'
import useCaptcha from '@/composables/useCaptcha'
import { IconArrowLeft, IconMail } from '@tabler/icons-vue'
import { useRoute } from 'vue-router'
import { computed } from 'vue'
import { toast } from 'vue-sonner'

const { getRecaptchaToken } = useCaptcha()
const route = useRoute()

const isLoading = ref(false)
const serverErrors = ref([])
const inputErrors = ref({})

const isEmailSent = computed(() => route.query.email_sent || false)
const emailAddress = computed(() => route.query.email_address || '')

const validationSchema = yup.object().shape({
  email: yup.string().required('Email address is required').email('Invalid email address'),
})

const handleSubmit = async values => {
  try {
    isLoading.value = true
    serverErrors.value = []
    inputErrors.value = {}

    const captchaToken = await getRecaptchaToken('forgotPassword')

    const postData = {
      email: values.email,
      'g-recaptcha-response': captchaToken,
    }

    let { data } = await ApiService.post('/affiliate/forgot-password', postData)

    if (data.status === 200) {
      toast.success(data.message)
      router.push({ name: 'affiliate-forgot-password', query: { email_address: values.email, email_sent: true } })
    } else {
      isLoading.value = false
      if (data.errors) {
        inputErrors.value = data.errors
      }
      serverErrors.value = processErrors(data)
    }
  } catch (error) {
    isLoading.value = false
    if (error.response.errors) {
      inputErrors.value = error.response.errors
    }
    serverErrors.value = processErrors(error)
    console.log(error)
  }
}
</script>

<template>
  <div>
    <Header />

    <div
      v-if="!isEmailSent"
      class="flex items-center justify-center py-14 sm:px-16"
    >
      <div class="px-4 w-full sm:max-w-lg">
        <h2 class="text-center text-2xl md:text-5xl font-bold leading-tight text-black mb-3">
          Recover Password
        </h2>
        <p class="mt-2 text-base font-medium text-center text-gray-600">
          Regain Access to Your Affiliate Account Quickly and Securely
        </p>

        <AlertError
          v-if="isEmptyObject(inputErrors) && serverErrors.length > 0"
          :errors="serverErrors"
        />

        <VForm
          class="mt-8"
          autocomplete="off"
          :validation-schema="validationSchema"
          @submit="handleSubmit"
        >
          <div class="space-y-5">
            <div class="mb-4">
              <div class="relative">
                <Field
                  id="email"
                  name="email"
                  type="email"
                  class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                  placeholder=" "
                  autocomplete="off"
                  required
                />
                <label
                  for="email"
                  class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4 cursor-text"
                >Email address</label>
                <div class="absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                  <IconMail
                    class="h-5 w-5"
                    color="#fff"
                    fill="#000"
                  />
                </div>
              </div>
              <ErrorMessage
                name="email"
                class="text-red-500 text-sm ms-5"
              />
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.email)"
                class="text-red-500 text-sm ms-5"
              >
                {{ inputErrors.email[0] }}
              </p>
            </div>

            <div class="mt-8">
              <button
                type="submit"
                class="inline-flex w-full items-center justify-center rounded-full bg-black px-3.5 py-2.5 font-semibold leading-7 text-white hover:bg-black/80 text-sm disabled:bg-gray-800 uppercase"
                :disabled="isLoading"
              >
                <span
                  v-if="isLoading"
                  role="status"
                  class="me-2"
                >
                  <svg
                    aria-hidden="true"
                    class="w-6 h-6 text-gray-200 animate-spin fill-yellow-300"
                    viewBox="0 0 100 101"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                      fill="currentColor"
                    />
                    <path
                      d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                      fill="currentFill"
                    />
                  </svg>
                  <span class="sr-only">Loading...</span>
                </span>
                <span
                  v-if="!isLoading"
                  class="flex items-center"
                >
                  Reset Password
                </span>
              </button>
            </div>
          </div>
        </VForm>

        <div class="flex justify-center mt-12">
          <RouterLink
            class="text-sm font-semibold border-b-2 border-solid !border-black hover:text-gray-600 hover:!border-gray-600"
            :to="{name: 'affiliate-login'}"
          >
            <span class="flex items-center">
              <IconArrowLeft
                stroke-width="2"
                class="h-4 w-4 me-1"
              /> Back to Login
            </span>
          </RouterLink>
        </div>
      </div>
    </div>

    <div
      v-if="isEmailSent"
      class="flex items-center justify-center py-14 sm:px-16"
    >
      <div class="px-4 w-full sm:max-w-lg">
        <h2 class="text-center text-2xl md:text-5xl font-bold leading-tight text-black mb-5">
          Check your Inbox
        </h2>

        <p class="mt-2 text-base font-medium text-center text-gray-600">
          If an account is registered to <strong>{{ emailAddress }} </strong> you will receive an email with a link to reset your password.
        </p>

        <p class="text-base font-medium text-center text-gray-600 mt-4 mb-4">
          If you don't receive an email, check your spam / junk folder and confirm that you're using the same email address that was used to create your account.
        </p>

        <div
          class="flex items-center p-4 mb-4 text-sm text-blue-800 rounded-lg bg-blue-50"
          role="alert"
        >
          <svg
            class="flex-shrink-0 inline w-4 h-4 me-3"
            aria-hidden="true"
            xmlns="http://www.w3.org/2000/svg"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z" />
          </svg>
          <span class="sr-only">Info</span>
          <div class="font-semibold">
            If you requested multiple links, only the most recent link will be active.
          </div>
        </div>

        <div class="flex justify-center mt-12">
          <RouterLink
            class="text-sm font-semibold border-b-2 border-solid !border-black hover:text-gray-600 hover:!border-gray-600"
            :to="{name: 'affiliate-login'}"
          >
            <span class="flex items-center">
              <IconArrowLeft
                stroke-width="2"
                class="h-4 w-4 me-1"
              /> Back to Login
            </span>
          </RouterLink>
        </div>
      </div>
    </div>
  </div>
</template>
