<script setup>
import { isEmpty, isEmptyObject, isNullOrUndefined, parseFloatOrZero } from '@/@core/utils'
import { requiredValidator } from '@/@core/utils/validators'
import { useOtcProductStore } from '@/store/admin/otcProductStore'
import { processErrors } from '@/utils/errorHandler'
import { onMounted, ref } from 'vue'
import { storeToRefs } from 'pinia'
import { formatCurrency, isNumber, removeKeyFromObject } from '@/utils/helpers'
import { useRoute, useRouter } from 'vue-router'
import { useObjectUrl } from '@vueuse/core'
import { useGlobalData } from '@/store/global'

const route = useRoute()
const router = useRouter()

const { showSnackbar } = useGlobalData()
const otcProductStore = useOtcProductStore()
const { categories } = storeToRefs(otcProductStore)

const currentStep = ref(1)
const formRef = ref(null)
const formDescriptionRef = ref(null)
const formPriceRef = ref(null)
const isLoading = ref(false)
const serverErrors = ref([])
const inputErrors = ref({})
const previewImage = ref(null)
const productId = ref(null)

const formData = ref({
  drug_name: null,
  drug_form: null,
  packaging: null,
  strength: null,
  category_id: null,
  ndc: null,
  stock_availability: true,
  max_qty_per_order: '5',
  image: null,
})

const productImage = ref({ file: null, url: '' })
const inputProductImageRef = ref(null)
const isProductImgUploading = ref(false)
const isEmptyProductImg = ref(false)

const descriptionData = ref({
  id: '',
  summary: '',
  description: '',
})

const priceData = ref({
  id: '',
  plan_type: ['one_time'],
  price: '',
  selling_price: '',
  discount_type: 'none',
  discount_value: '',
})

onMounted(async () => {
  await otcProductStore.fetchCategories()
})

const categoryOptions = computed(() => {
  return categories.value
})

const onFileChange = $event => {
  const { files } = $event.target
  if (!files || files.length === 0) return
  const file = files[0]

  if (file.type.slice(0, 6) !== 'image/') {
    showSnackbar('Only image files are allowed', 'error')

    return false
  }
  if (file.size > 3 * 1024 * 1024) {
    showSnackbar('File size should be less than 3MB', 'error')

    return false
  }

  removeKeyFromInputErrors('product_image')
  isEmptyProductImg.value = false

  productImage.value = { file, url: useObjectUrl(file).value ?? '' }

  formData.value.image = file
}

function removeKeyFromInputErrors(key) {
  inputErrors.value = removeKeyFromObject(inputErrors.value, key)
}

const handleSubmit = () => {
  formRef.value?.validate().then(async ({ valid }) => {
    if (valid) {
      try {
        isLoading.value = true
        serverErrors.value = []
        inputErrors.value = {}

        const formDataToSend = new FormData()

        Object.keys(formData.value).forEach(key => {
          if (key === 'image' && formData.value[key]) {
            formDataToSend.append('image', formData.value[key], formData.value[key].name)
          } else if (key === 'stock_availability') {
            formDataToSend.append(key, formData.value[key] ? 1 : 0)
          } else if (formData.value[key] !== null && formData.value[key] !== undefined) {
            formDataToSend.append(key, formData.value[key])
          }
        })

        if (productId.value) {
          formDataToSend.append('id', productId.value)

          const result = await otcProductStore.editProduct(formDataToSend)

          if (result) {
            productId.value = productId.value
            descriptionData.value.id = productId.value
            priceData.value.id = productId.value
            currentStep.value = 2
          }
        } else {
          const newProductId = await otcProductStore.addProduct(formDataToSend)

          if (newProductId) {
            productId.value = newProductId
            descriptionData.value.id = newProductId
            priceData.value.id = newProductId
            currentStep.value = 2

            // router.push({ name: 'admin-otc-products-add', query: { product_id: newProductId } })
          }
        }
      } catch (error) {
        console.error(error)
        if (error.response?.data?.errors) {
          inputErrors.value = error.response.data.errors
        } else {
          serverErrors.value = processErrors(error)
        }
      } finally {
        isLoading.value = false
      }
    }
  })
}

const handleDescriptionSubmit = () => {
  formDescriptionRef.value?.validate().then(async ({ valid }) => {
    if (valid) {
      try {
        isLoading.value = true
        serverErrors.value = []
        inputErrors.value = {}

        const result = await otcProductStore.updateProductDescription(descriptionData.value)

        if (result) {
          currentStep.value = 3
        }
      } catch (error) {
        console.error(error)
        if (error.response?.data?.errors) {
          inputErrors.value = error.response.data.errors
        } else {
          serverErrors.value = processErrors(error)
        }
      } finally {
        isLoading.value = false
      }
    }
  })
}

const handlePriceSubmit = () => {
  formPriceRef.value?.validate().then(async ({ valid }) => {
    if (valid) {
      try {
        isLoading.value = true
        serverErrors.value = []
        inputErrors.value = {}

        const result = await otcProductStore.updateProductPrice({
          ...priceData.value,
          discount_type: priceData.value.discount_type === 'none' ? null : priceData.value.discount_type,
          discount_value: priceData.value.discount_type === 'none' ? null : priceData.value.discount_value,
        })

        if (result) {
          currentStep.value = 4
        }
      } catch (error) {
        console.error(error)
        if (error.response?.data?.errors) {
          inputErrors.value = error.response.data.errors
        } else {
          serverErrors.value = processErrors(error)
        }
      } finally {
        isLoading.value = false
      }
    }
  })
}

const resetForm = () => {
  formData.value = {
    drug_name: '',
    drug_form: '',
    packaging: '',
    strength: '',
    category_id: '',
    ndc: '',
    stock_availability: true,
    max_qty_per_order: '10',
    image: null,
  }

  descriptionData.value = {
    id: '',
    summary: '',
    description: '',
  }

  priceData.value = {
    id: '',
    plan_type: ['one_time'],
    price: '',
    selling_price: '',
    discount_type: 'fixed',
    discount_value: '',
  }

  previewImage.value = null
  productId.value = null
  currentStep.value = 1
  serverErrors.value = []
  inputErrors.value = {}
}

const netRevenue = computed(() => {
  let sellingPrice = parseFloatOrZero(priceData.value.selling_price)
  if (priceData.value.discount_type === 'fixed') {
    sellingPrice -= parseFloatOrZero(priceData.value.discount_value)
  } else if (priceData.value.discount_type === 'percentage') {
    sellingPrice -= (sellingPrice * parseFloatOrZero(priceData.value.discount_value) / 100)
  }

  return sellingPrice - parseFloatOrZero(priceData.value.price)
})

const maxDiscountValidator = computed(() => {
  if (isEmpty(priceData.value.discount_value)) return true

  if (priceData.value.discount_type === 'fixed') {
    return (parseFloatOrZero(priceData.value.discount_value) < parseFloatOrZero(priceData.value.selling_price)) || 'Discount must be less than selling price'
  } else if (priceData.value.discount_type === 'percentage') {
    return (parseFloatOrZero(priceData.value.discount_value) < 100) || 'Discount must be less than 100%'
  } else {
    return true
  }
})

const getDiscountHint = computed(() => {
  if (priceData.value.discount_type === 'percentage') {
    const totalAmount = parseFloatOrZero(priceData.value.selling_price)
    const discountPercentage = parseFloatOrZero(priceData.value.discount_value)
    const discountAmount = (totalAmount * discountPercentage) / 100

    return `${formatCurrency(discountAmount.toFixed(2))} off`
  }

  return undefined
})
</script>

<template>
  <section>
    <VCard v-if="currentStep === 1">
      <VCardText>
        <h5 class="text-h5 mb-6">
          Product Information
        </h5>

        <VAlert
          v-if="!isEmpty(serverErrors)"
          type="error"
          variant="tonal"
          title="Error!"
          class="mb-6"
        >
          <ul class="mb-0">
            <li
              v-for="error in serverErrors"
              :key="error"
              class="mb-0"
            >
              {{ error }}
            </li>
          </ul>
        </VAlert>

        <VForm
          ref="formRef"
          @submit.prevent="handleSubmit"
        >
          <VRow>
            <!-- 👉 Drug Name -->
            <VCol
              cols="12"
              md="6"
            >
              <AppTextField
                v-model="formData.drug_name"
                label="Product Name"
                :error-messages="inputErrors.drug_name"
                placeholder="Enter product name"
                :rules="[requiredValidator]"
              />
            </VCol>

            <!-- 👉 Drug Form -->
            <VCol
              cols="12"
              md="6"
            >
              <AppTextField
                v-model="formData.drug_form"
                label="Product Form"
                :error-messages="inputErrors.drug_form"
                placeholder="e.g. Tablet, Capsule, Liquid"
                :rules="[requiredValidator]"
              />
            </VCol>

            <!-- 👉 Packaging -->
            <VCol
              cols="12"
              md="6"
            >
              <AppTextField
                v-model="formData.packaging"
                label="Packaging"
                :error-messages="inputErrors.packaging"
                placeholder="e.g. 1 Bottle, 30 Tablets"
                :rules="[requiredValidator]"
              />
            </VCol>

            <!-- 👉 Strength -->
            <VCol
              cols="12"
              md="6"
            >
              <AppTextField
                v-model="formData.strength"
                label="Strength"
                :error-messages="inputErrors.strength"
                placeholder="e.g. 500mg, 10mg/ml"
              />
            </VCol>

            <!-- 👉 Category -->
            <VCol
              cols="12"
              md="6"
            >
              <AppSelect
                v-model="formData.category_id"
                label="Category"
                :items="categoryOptions"
                item-title="name"
                item-value="id"
                :error-messages="inputErrors.category_id"
                placeholder="Select category"
                :rules="[requiredValidator]"
              />
            </VCol>

            <!-- 👉 NDC -->
            <VCol
              cols="12"
              md="6"
            >
              <AppTextField
                v-model="formData.ndc"
                label="NDC / Product Identifier (For pharmacy)"
                :error-messages="inputErrors.ndc"
                placeholder="Enter NDC / Product Identifier"
              />
            </VCol>

            <!-- 👉 Stock Availability -->
            <VCol
              cols="12"
              md="6"
            >
              <label class="d-block mb-2">Stock availability</label>
              <VSwitch
                v-model="formData.stock_availability"
                :label="formData.stock_availability ? 'In stock' : 'Out of stock'"
                :error-messages="inputErrors.stock_availability"
                hide-details
                style="width: fit-content;"
              />
            </VCol>

            <!-- 👉 Max Quantity Per Order -->
            <VCol
              cols="12"
              md="6"
            >
              <AppTextField
                v-model="formData.max_qty_per_order"
                label="Max Quantity Per Order"
                :error-messages="inputErrors.max_qty_per_order"
                placeholder="Enter maximum quantity per order"
                :rules="[requiredValidator]"
                @keydown="isNumber"
              />
            </VCol>

            <!-- 👉 Product Image -->
            <VCol
              cols="12"
              md="6"
            >
              <VCardText class="d-flex pa-0">
                <VAvatar
                  rounded
                  size="80"
                  class="me-6"
                  color="primary"
                  variant="tonal"
                >
                  <VImg
                    v-if="productImage.url"
                    :src="productImage.url"
                  />
                  <VIcon
                    v-else
                    size="80"
                    icon="tabler-box"
                  />
                </VAvatar>
                <div class="d-flex flex-column justify-center gap-4">
                  <div class="d-flex flex-wrap gap-2">
                    <VBtn
                      color="primary"
                      variant="tonal"
                      :loading="isProductImgUploading"
                      @click="inputProductImageRef.click()"
                    >
                      <VIcon
                        icon="tabler-cloud-upload"
                        class="d-sm-none"
                      />
                      <span class="d-none d-sm-block">Select Image</span>
                    </VBtn>

                    <input
                      ref="inputProductImageRef"
                      type="file"
                      name="file"
                      accept="image/*"
                      hidden
                      @input="onFileChange($event)"
                    />
                  </div>
                  <p class="text-body-2 mb-0">
                    Allowed only image files with size less than 2MB.
                  </p>
                </div>
              </VCardText>
              <p
                v-if="isEmptyProductImg"
                class="text-sm text-error mb-0 ms-3 mt-2"
              >
                Product image is required
              </p>
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.image)"
                class="text-sm text-error mb-0 ms-3 mt-2"
              >
                {{ inputErrors.image[0] }}
              </p>
            </VCol>
          </VRow>

          <VCardActions class="pa-0 mt-8">
            <VSpacer />
            <VBtn
              :to="{ name: 'admin-otc-products' }"
              variant="tonal"
              color="secondary"
              style="min-width: 150px"
            >
              Cancel
            </VBtn>
            <VBtn
              type="submit"
              variant="elevated"
              color="primary"
              :loading="isLoading"
              style="min-width: 150px"
            >
              Continue
            </VBtn>
          </VCardActions>
        </VForm>
      </VCardText>
    </VCard>

    <VCard v-else-if="currentStep === 2">
      <VCardText>
        <h5 class="text-h5 mb-6">
          Step 2: Product Description
        </h5>

        <VAlert
          v-if="!isEmpty(serverErrors)"
          type="error"
          variant="tonal"
          title="Error!"
          class="mb-6"
        >
          <ul class="mb-0">
            <li
              v-for="error in serverErrors"
              :key="error"
              class="mb-0"
            >
              {{ error }}
            </li>
          </ul>
        </VAlert>

        <VForm
          ref="formDescriptionRef"
          @submit.prevent="handleDescriptionSubmit"
        >
          <VRow>
            <!-- 👉 Summary -->
            <VCol cols="12">
              <AppTextarea
                v-model="descriptionData.summary"
                label="Summary"
                :error-messages="inputErrors.summary"
                placeholder="Enter a brief summary of the product"
                :rules="[requiredValidator]"
                rows="3"
              />
            </VCol>

            <!-- 👉 Description -->
            <VCol cols="12">
              <VLabel class="mb-2">
                Description
              </VLabel>
              <TiptapEditor
                v-model="descriptionData.description"
                class="border rounded basic-editor"
                editor-height="300px"
              />
              <div
                v-if="inputErrors.description"
                class="text-error text-caption mt-1"
              >
                {{ inputErrors.description }}
              </div>
            </VCol>
          </VRow>

          <VCardActions class="pa-0 mt-4">
            <VBtn
              variant="tonal"
              color="secondary"
              style="min-width: 150px"
              @click="currentStep = 1"
            >
              Back
            </VBtn>
            <VSpacer />
            <VBtn
              type="submit"
              variant="elevated"
              color="primary"
              :loading="isLoading"
              style="min-width: 150px"
            >
              Continue
            </VBtn>
          </VCardActions>
        </VForm>
      </VCardText>
    </VCard>

    <VCard v-else-if="currentStep === 3">
      <VCardText>
        <h5 class="text-h5 mb-6">
          Product Pricing
        </h5>

        <VAlert
          v-if="!isEmpty(serverErrors)"
          type="error"
          variant="tonal"
          title="Error!"
          class="mb-6"
        >
          <ul class="mb-0">
            <li
              v-for="error in serverErrors"
              :key="error"
              class="mb-0"
            >
              {{ error }}
            </li>
          </ul>
        </VAlert>

        <VForm
          ref="formPriceRef"
          @submit.prevent="handlePriceSubmit"
        >
          <VRow>
            <!-- 👉 Plan Type -->
            <VCol cols="12">
              <VLabel>Plan Type</VLabel>
              <VCheckbox
                v-model="priceData.plan_type"
                label="One-time purchase"
                value="one_time"
                :error-messages="inputErrors.plan_type"
                hide-details
              />
              <VCheckbox
                v-model="priceData.plan_type"
                label="Subscription"
                value="subscription"
                :error-messages="inputErrors.plan_type"
                hide-details
              />
              <div
                v-if="inputErrors.plan_type"
                class="text-error text-caption mt-1"
              >
                {{ inputErrors.plan_type }}
              </div>
            </VCol>

            <!-- 👉 Base Price -->
            <VCol
              cols="12"
              md="6"
            >
              <AppTextField
                v-model="priceData.price"
                label="Base Price"
                prefix="$"
                :error-messages="inputErrors.price"
                placeholder="Enter base price"
                :rules="[requiredValidator]"
                @keypress="isNumber($event)"
              />
            </VCol>

            <!-- 👉 Selling Price -->
            <VCol
              cols="12"
              md="6"
            >
              <AppTextField
                v-model="priceData.selling_price"
                label="Selling Price"
                prefix="$"
                :error-messages="inputErrors.selling_price"
                placeholder="Enter selling price"
                @keypress="isNumber($event)"
              />
            </VCol>

            <!-- 👉 Discount Type -->
            <VCol cols="12">
              <VRadioGroup
                v-model="priceData.discount_type"
                label="Discount Type"
                :rules="[requiredValidator]"
                required
                class="d-flex"
                :error-messages="inputErrors.discount_type"
              >
                <VRadio
                  label="None"
                  value="none"
                  class="ms-2"
                />
                <VRadio
                  label="Fixed Amount"
                  value="fixed"
                  class="ms-2"
                />
                <VRadio
                  label="Percentage (of the total amount)"
                  value="percentage"
                  class="ms-2"
                />
              </VRadioGroup>
            </VCol>

            <!-- 👉 Discount Value -->
            <VCol
              v-if="priceData.discount_type !== 'none'"
              cols="12"
              md="6"
            >
              <AppTextField
                v-model="priceData.discount_value"
                label="Discount Value"
                :rules="[maxDiscountValidator]"
                :maxlength="priceData.discount_type === 'fixed' ? 10 : 2"
                :prefix="priceData.discount_type === 'fixed' ? '$' : ''"
                :suffix="priceData.discount_type === 'percentage' ? '%' : ''"
                :hint="getDiscountHint"
                persistent-hint
                placeholder="Enter discount value"
                :error-messages="inputErrors.discount_value"
                @keypress="isNumber($event)"
              />
            </VCol>

            <!-- 👉 Net Revenue -->
            <VCol
              cols="12"
              class="text-body-2"
            >
              <VAlert
                color="secondary"
                variant="tonal"
                class="alert-fix"
              >
                Net Revenue: <span
                  class="text-base font-weight-bold text-high-emphasis"
                  :class="{ 'text-error': netRevenue <= 0 }"
                >{{ formatCurrency(netRevenue) }}</span>
              </VAlert>
            </VCol>
          </VRow>

          <VCardActions class="pa-0 mt-4">
            <VBtn
              variant="tonal"
              color="secondary"
              style="min-width: 150px"
              @click="currentStep = 2"
            >
              Back
            </VBtn>
            <VSpacer />
            <VBtn
              type="submit"
              variant="elevated"
              color="primary"
              style="min-width: 150px"
              :loading="isLoading"
            >
              Save Product
            </VBtn>
          </VCardActions>
        </VForm>
      </VCardText>
    </VCard>

    <VCard v-else-if="currentStep === 4">
      <VCardText class="text-center pa-10">
        <VIcon
          icon="tabler-circle-check"
          size="80"
          color="success"
          class="mb-6"
        />
        <h4 class="text-h4 mb-4">
          Product Added Successfully!
        </h4>
        <p class="mb-6">
          Your OTC product has been added to the system.
        </p>
        <div class="d-flex justify-center gap-4">
          <VBtn
            :to="{ name: 'admin-otc-products' }"
            variant="outlined"
          >
            Back to Products
          </VBtn>
          <VBtn
            :to="{ name: 'admin-otc-products-add' }"
            @click="resetForm"
          >
            Add Another Product
          </VBtn>
        </div>
      </VCardText>
    </VCard>
  </section>
</template>
