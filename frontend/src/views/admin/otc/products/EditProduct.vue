<script setup>
import { isEmpty, parseFloatOrZero } from '@/@core/utils'
import { requiredValidator } from '@/@core/utils/validators'
import { useOtcProductStore } from '@/store/admin/otcProductStore'
import { processErrors } from '@/utils/errorHandler'
import { onMounted, ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import { storeToRefs } from 'pinia'
import { formatCurrency, isNumber } from '@/utils/helpers'

const route = useRoute()
const otcProductStore = useOtcProductStore()
const { categories } = storeToRefs(otcProductStore)

const activeTab = ref('product-info')
const formProductInfoRef = ref(null)
const formDescriptionRef = ref(null)
const formPriceRef = ref(null)
const isLoading = ref(false)
const skeletonLoading = ref(true)
const serverErrors = ref([])
const inputErrors = ref({})
const productId = ref(route.params.product_id)

const productData = ref({
  id: '',
  drug_name: '',
  drug_form: '',
  packaging: '',
  strength: '',
  category_id: '',
  ndc: '',
  stock_availability: true,
  max_qty_per_order: '10',
})

const inputProductImageRef = ref(null)

const productImage = ref({
  url: '',
  file: null,
})

const descriptionData = ref({
  id: '',
  summary: '',
  description: '',
})

const priceData = ref({
  id: '',
  plan_type: [],
  price: '',
  selling_price: '',
  discount_type: 'none',
  discount_value: '',
})

const categoryOptions = computed(() => {
  return categories.value
})

onMounted(async () => {
  await Promise.all([
    otcProductStore.fetchCategories(),
    fetchProductDetails(),
  ])
})

const fetchProductDetails = async () => {
  try {
    skeletonLoading.value = true

    const product = await otcProductStore.fetchProductDetails(productId.value)

    if (product) {
      productData.value = {
        id: product.id,
        drug_name: product.drug_name || '',
        drug_form: product.drug_form || '',
        packaging: product.packaging || '',
        strength: product.strength || '',
        category_id: product.category_id || '',
        ndc: product.ndc || '',
        stock_availability: product.stock_availability === 1,
        max_qty_per_order: product.max_qty_per_order?.toString() || '10',
      }

      productImage.value.url = product.image || ''

      descriptionData.value = {
        id: product.id,
        summary: product.summary || '',
        description: product.description || '',
      }

      priceData.value = {
        id: product.id,
        plan_type: product.plan_type ? (Array.isArray(product.plan_type) ? product.plan_type : [product.plan_type]) : ['one_time'],
        price: product.price?.toString() || '',
        selling_price: product.selling_price?.toString() || '',
        discount_type: product.discount_type || 'none',
        discount_value: product.discount_value?.toString() || '',
      }

      if (product.main_category_name && !product.category_name) {
        product.category_name = product.main_category_name
      }
    }
  } catch (error) {
    console.error(error)
    serverErrors.value = processErrors(error)
  } finally {
    skeletonLoading.value = false
  }
}

const onFileChange = event => {
  const file = event.target.files[0]
  if (file) {
    productImage.value.file = file
    productImage.value.url = URL.createObjectURL(file)
    uploadProductImage()
  }
}

const uploadProductImage = async () => {
  try {
    isLoading.value = true
    serverErrors.value = []
    inputErrors.value = {}

    const formData = new FormData()

    formData.append('id', productId.value)
    formData.append('image', productImage.value.file)

    const result = await otcProductStore.updateProductImage(formData)

    if (result.success) {
      productImage.value.url = result.url
    }
  } catch (error) {
    console.error(error)
    if (error.response?.data?.errors) {
      inputErrors.value = error.response.data.errors
    } else {
      serverErrors.value = processErrors(error)
    }
  } finally {
    isLoading.value = false
  }
}

const handleProductInfoSubmit = () => {
  formProductInfoRef.value?.validate().then(async ({ valid }) => {
    if (valid) {
      try {
        isLoading.value = true
        serverErrors.value = []
        inputErrors.value = {}

        const formData = new FormData()

        Object.keys(productData.value).forEach(key => {
          if (key === 'stock_availability') {
            formData.append(key, productData.value[key] ? '1' : '0')
          } else if (productData.value[key] !== null && productData.value[key] !== undefined) {
            formData.append(key, productData.value[key])
          }
        })

        formData.append('id', productId.value)

        const result = await otcProductStore.editProduct(formData)

        if (result) {
          await fetchProductDetails()
        }
      } catch (error) {
        console.error(error)
        if (error.response?.data?.errors) {
          inputErrors.value = error.response.data.errors
        } else {
          serverErrors.value = processErrors(error)
        }
      } finally {
        isLoading.value = false
      }
    }
  })
}

const handleDescriptionSubmit = () => {
  formDescriptionRef.value?.validate().then(async ({ valid }) => {
    if (valid) {
      try {
        isLoading.value = true
        serverErrors.value = []
        inputErrors.value = {}

        const result = await otcProductStore.updateProductDescription(descriptionData.value)

        if (result) {
          await fetchProductDetails()
        }
      } catch (error) {
        console.error(error)
        if (error.response?.data?.errors) {
          inputErrors.value = error.response.data.errors
        } else {
          serverErrors.value = processErrors(error)
        }
      } finally {
        isLoading.value = false
      }
    }
  })
}

const handlePriceSubmit = () => {
  formPriceRef.value?.validate().then(async ({ valid }) => {
    if (valid) {
      try {
        isLoading.value = true
        serverErrors.value = []
        inputErrors.value = {}

        const result = await otcProductStore.updateProductPrice({
          ...priceData.value,
          discount_type: priceData.value.discount_type === 'none' ? null : priceData.value.discount_type,
          discount_value: priceData.value.discount_type === 'none' ? null : priceData.value.discount_value,
        })

        if (result) {
          await fetchProductDetails()
        }
      } catch (error) {
        console.error(error)
        if (error.response?.data?.errors) {
          inputErrors.value = error.response.data.errors
        } else {
          serverErrors.value = processErrors(error)
        }
      } finally {
        isLoading.value = false
      }
    }
  })
}

const netRevenue = computed(() => {
  let sellingPrice = parseFloatOrZero(priceData.value.selling_price)
  if (priceData.value.discount_type === 'fixed') {
    sellingPrice -= parseFloatOrZero(priceData.value.discount_value)
  } else if (priceData.value.discount_type === 'percentage') {
    sellingPrice -= (sellingPrice * parseFloatOrZero(priceData.value.discount_value) / 100)
  }

  return sellingPrice - parseFloatOrZero(priceData.value.price)
})

const maxDiscountValidator = computed(() => {
  if (isEmpty(priceData.value.discount_value)) return true

  if (priceData.value.discount_type === 'fixed') {
    return (parseFloatOrZero(priceData.value.discount_value) < parseFloatOrZero(priceData.value.selling_price)) || 'Discount must be less than selling price'
  } else if (priceData.value.discount_type === 'percentage') {
    return (parseFloatOrZero(priceData.value.discount_value) < 100) || 'Discount must be less than 100%'
  } else {
    return true
  }
})

const getDiscountHint = computed(() => {
  if (priceData.value.discount_type === 'percentage') {
    const totalAmount = parseFloatOrZero(priceData.value.selling_price)
    const discountPercentage = parseFloatOrZero(priceData.value.discount_value)
    const discountAmount = (totalAmount * discountPercentage) / 100

    return `${formatCurrency(discountAmount.toFixed(2))} off`
  }

  return undefined
})
</script>


<template>
  <section>
    <VCard>
      <VCardText>
        <VAlert
          v-if="!isEmpty(serverErrors)"
          type="error"
          variant="tonal"
          title="Error!"
          class="mb-6"
        >
          <ul class="mb-0">
            <li
              v-for="error in serverErrors"
              :key="error"
              class="mb-0"
            >
              {{ error }}
            </li>
          </ul>
        </VAlert>

        <div class="d-flex justify-space-between align-center mb-6">
          <div>
            <h5 class="text-h5 mb-1">
              Edit Product
            </h5>
            <p class="text-medium-emphasis mb-0">
              Update product details, description, and pricing
            </p>
          </div>
          <VBtn
            :to="{ name: 'admin-otc-products' }"
            variant="outlined"
            color="secondary"
          >
            Back to Products
          </VBtn>
        </div>

        <VTabs
          v-model="activeTab"
          class="mb-3"
        >
          <VTab value="product-info">
            <VIcon
              icon="mdi-information"
              class="me-2"
            />
            Basic Information
          </VTab>
          <VTab value="description">
            <VIcon
              icon="mdi-text-box"
              class="me-2"
            />
            Description
          </VTab>
          <VTab value="pricing">
            <VIcon
              icon="mdi-currency-usd"
              class="me-2"
            />
            Pricing
          </VTab>
        </VTabs>

        <VWindow
          v-model="activeTab"
          class="pb-3"
        >
          <!-- Product Information Tab -->
          <VWindowItem value="product-info">
            <VForm
              ref="formProductInfoRef"
              @submit.prevent="handleProductInfoSubmit"
            >
              <VRow class="mt-5">
                <!-- 👉 Product Image -->
                <VCol cols="12">
                  <VLabel class="mb-2">
                    Product Image
                  </VLabel>
                  <div class="d-flex align-center gap-4">
                    <div class="me-0 me-md-5">
                      <VAvatar
                        variant="tonal"
                        color="primary"
                        size="150"
                        class="rounded-lg"
                      >
                        <img
                          v-if="productImage.url"
                          :src="productImage.url"
                          alt="Product Image"
                          style="max-height: 150px; max-width: 150px;"
                          class="rounded"
                        />
                        <div v-else>
                          <VIcon
                            icon="tabler-photo"
                            size="50"
                          />
                        </div>
                      </VAvatar>
                      <div class="mt-3">
                        <VBtn
                          size="small"
                          variant="tonal"
                          block
                          :loading="isLoading"
                          @click="inputProductImageRef.click()"
                        >
                          <VIcon
                            icon="tabler-cloud-upload"
                            start
                          />
                          Update
                        </VBtn>
                        <input
                          ref="inputProductImageRef"
                          type="file"
                          name="file"
                          accept="image/*"
                          hidden
                          @input="onFileChange($event)"
                        />
                      </div>
                    </div>
                  </div>
                </VCol>

                <!-- 👉 Drug Name -->
                <VCol
                  cols="12"
                  md="6"
                >
                  <AppTextField
                    v-model="productData.drug_name"
                    label="Product Name"
                    :error-messages="inputErrors.drug_name"
                    placeholder="Enter product name"
                    :rules="[requiredValidator]"
                    :loading="skeletonLoading"
                  />
                </VCol>

                <!-- 👉 Drug Form -->
                <VCol
                  cols="12"
                  md="6"
                >
                  <AppTextField
                    v-model="productData.drug_form"
                    label="Product Form"
                    :error-messages="inputErrors.drug_form"
                    placeholder="e.g. Tablet, Capsule, Liquid"
                    :rules="[requiredValidator]"
                    :loading="skeletonLoading"
                  />
                </VCol>

                <!-- 👉 Packaging -->
                <VCol
                  cols="12"
                  md="6"
                >
                  <AppTextField
                    v-model="productData.packaging"
                    label="Packaging"
                    :error-messages="inputErrors.packaging"
                    placeholder="e.g. Bottle, Box, Strip"
                    :rules="[requiredValidator]"
                    :loading="skeletonLoading"
                  />
                </VCol>

                <!-- 👉 Strength -->
                <VCol
                  cols="12"
                  md="6"
                >
                  <AppTextField
                    v-model="productData.strength"
                    label="Strength"
                    :error-messages="inputErrors.strength"
                    placeholder="e.g. 500mg, 10mg/ml"
                    :loading="skeletonLoading"
                  />
                </VCol>

                <!-- 👉 Category -->
                <VCol
                  cols="12"
                  md="6"
                >
                  <AppSelect
                    v-model="productData.category_id"
                    label="Category"
                    :items="categoryOptions"
                    item-title="name"
                    item-value="id"
                    :error-messages="inputErrors.category_id"
                    placeholder="Select category"
                    :rules="[requiredValidator]"
                    :loading="skeletonLoading"
                  />
                </VCol>

                <!-- 👉 NDC -->
                <VCol
                  cols="12"
                  md="6"
                >
                  <AppTextField
                    v-model="productData.ndc"
                    label="NDC / Product Identifier (For pharmacy)"
                    :error-messages="inputErrors.ndc"
                    placeholder="Enter NDC / Product Identifier"
                    :loading="skeletonLoading"
                  />
                </VCol>

                <!-- 👉 Stock Availability -->
                <VCol
                  cols="12"
                  md="6"
                >
                  <label class="d-block mb-2">Stock availability</label>
                  <VSwitch
                    v-model="productData.stock_availability"
                    :label="productData.stock_availability ? 'In stock' : 'Out of stock'"
                    :error-messages="inputErrors.stock_availability"
                    hide-details
                    :loading="skeletonLoading"
                    style="width: fit-content;"
                  />
                </VCol>

                <!-- 👉 Max Quantity Per Order -->
                <VCol
                  cols="12"
                  md="6"
                >
                  <AppTextField
                    v-model="productData.max_qty_per_order"
                    label="Max Quantity Per Order"
                    :error-messages="inputErrors.max_qty_per_order"
                    placeholder="Enter maximum quantity"
                    :rules="[requiredValidator]"
                    :loading="skeletonLoading"
                    @keypress="isNumber($event)"
                  />
                </VCol>
              </VRow>

              <VCardActions class="pa-0 mt-8">
                <VSpacer />
                <VBtn
                  :to="{ name: 'admin-otc-products' }"
                  variant="tonal"
                  color="secondary"
                  style="min-width: 150px"
                >
                  Cancel
                </VBtn>
                <VBtn
                  type="submit"
                  :loading="isLoading"
                  variant="elevated"
                  style="min-width: 150px"
                  color="primary"
                >
                  Update
                </VBtn>
              </VCardActions>
            </VForm>
          </VWindowItem>

          <!-- Description Tab -->
          <VWindowItem value="description">
            <VForm
              ref="formDescriptionRef"
              @submit.prevent="handleDescriptionSubmit"
            >
              <VRow class="mt-5">
                <!-- 👉 Summary -->
                <VCol cols="12">
                  <AppTextarea
                    v-model="descriptionData.summary"
                    label="Summary"
                    :error-messages="inputErrors.summary"
                    placeholder="Enter a brief summary of the product"
                    :rules="[requiredValidator]"
                    :loading="skeletonLoading"
                    rows="3"
                  />
                </VCol>

                <!-- 👉 Description -->
                <VCol cols="12">
                  <VLabel class="mb-2">
                    Description
                  </VLabel>
                  <TiptapEditor
                    v-model="descriptionData.description"
                    class="border rounded basic-editor"
                    editor-height="300px"
                    :disabled="skeletonLoading"
                  />
                  <div
                    v-if="inputErrors.description"
                    class="text-error text-caption mt-1"
                  >
                    {{ inputErrors.description }}
                  </div>
                </VCol>
              </VRow>

              <VCardActions class="pa-0 mt-8">
                <VSpacer />
                <VBtn
                  :to="{ name: 'admin-otc-products' }"
                  variant="tonal"
                  color="secondary"
                  style="min-width: 150px"
                >
                  Cancel
                </VBtn>
                <VBtn
                  type="submit"
                  :loading="isLoading"
                  color="primary"
                  style="min-width: 150px"
                  variant="elevated"
                >
                  Update
                </VBtn>
              </VCardActions>
            </VForm>
          </VWindowItem>

          <!-- Pricing Tab -->
          <VWindowItem value="pricing">
            <VForm
              ref="formPriceRef"
              @submit.prevent="handlePriceSubmit"
            >
              <VRow class="mt-5">
                <!-- 👉 Plan Type -->
                <VCol cols="12">
                  <VLabel>Plan Type</VLabel>
                  <VCheckbox
                    v-model="priceData.plan_type"
                    label="One-time purchase"
                    value="one_time"
                    :error-messages="inputErrors.plan_type"
                    hide-details
                    :disabled="skeletonLoading"
                  />
                  <VCheckbox
                    v-model="priceData.plan_type"
                    label="Subscription"
                    value="subscription"
                    :error-messages="inputErrors.plan_type"
                    hide-details
                    :disabled="skeletonLoading"
                  />
                  <div
                    v-if="inputErrors.plan_type"
                    class="text-error text-caption mt-1"
                  >
                    {{ inputErrors.plan_type }}
                  </div>
                </VCol>

                <!-- 👉 Price -->
                <VCol
                  cols="12"
                  md="6"
                >
                  <AppTextField
                    v-model="priceData.price"
                    label="Base Price"
                    prefix="$"
                    :error-messages="inputErrors.price"
                    placeholder="Enter base price"
                    :rules="[requiredValidator]"
                    :loading="skeletonLoading"
                    @keypress="isNumber($event)"
                  />
                </VCol>

                <!-- 👉 Selling Price -->
                <VCol
                  cols="12"
                  md="6"
                >
                  <AppTextField
                    v-model="priceData.selling_price"
                    label="Selling Price"
                    prefix="$"
                    :error-messages="inputErrors.selling_price"
                    placeholder="Enter selling price"
                    :rules="[requiredValidator]"
                    :loading="skeletonLoading"
                    @keypress="isNumber($event)"
                  />
                </VCol>

                <!-- 👉 Discount Type -->
                <VCol cols="12">
                  <VRadioGroup
                    v-model="priceData.discount_type"
                    label="Discount Type"
                    :rules="[requiredValidator]"
                    required
                    class="d-flex"
                    :loading="skeletonLoading"
                    :error-messages="inputErrors.discount_type"
                  >
                    <VRadio
                      label="None"
                      value="none"
                      class="ms-2"
                    />
                    <VRadio
                      label="Fixed Amount"
                      value="fixed"
                      class="ms-2"
                    />
                    <VRadio
                      label="Percentage (of the total amount)"
                      value="percentage"
                      class="ms-2"
                    />
                  </VRadioGroup>
                </VCol>

                <!-- 👉 Discount Value -->
                <VCol
                  v-if="priceData.discount_type !== 'none'"
                  cols="12"
                  md="6"
                >
                  <AppTextField
                    v-model="priceData.discount_value"
                    label="Discount Value"
                    :rules="[maxDiscountValidator]"
                    :maxlength="priceData.discount_type === 'fixed' ? 10 : 2"
                    :prefix="priceData.discount_type === 'fixed' ? '$' : ''"
                    :suffix="priceData.discount_type === 'percentage' ? '%' : ''"
                    :hint="getDiscountHint"
                    persistent-hint
                    placeholder="Enter discount value"
                    :loading="skeletonLoading"
                    :error-messages="inputErrors.discount_value"
                    @keypress="isNumber($event)"
                  />
                </VCol>

                <!-- 👉 Net Revenue -->
                <VCol
                  cols="12"
                  class="text-body-2"
                >
                  <VAlert
                    color="secondary"
                    variant="tonal"
                    class="alert-fix"
                  >
                    Net Revenue: <span
                      class="text-base font-weight-bold text-high-emphasis"
                      :class="{ 'text-error': netRevenue <= 0 }"
                    >{{ formatCurrency(netRevenue) }}</span>
                  </VAlert>
                </VCol>
              </VRow>

              <VCardActions class="pa-0 mt-8">
                <VBtn
                  :to="{ name: 'admin-otc-products' }"
                  variant="tonal"
                  color="secondary"
                  style="min-width: 150px"
                >
                  Cancel
                </VBtn>
                <VSpacer />
                <VBtn
                  type="submit"
                  :loading="isLoading"
                  color="primary"
                  style="min-width: 150px"
                  variant="elevated"
                >
                  Update
                </VBtn>
              </VCardActions>
            </VForm>
          </VWindowItem>
        </VWindow>
      </VCardText>
    </VCard>
  </section>
</template>
