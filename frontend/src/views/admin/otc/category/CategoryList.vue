<script setup>
import { isEmpty } from '@/@core/utils'
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { processErrors } from '@/utils/errorHandler'
import { calculateStartIndex } from '@/utils/helpers'
import { refDebounced } from '@vueuse/core'
import { computed, onMounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import CategoryAddDrawer from './CategoryAddDrawer.vue'
import CategoryEditDrawer from './CategoryEditDrawer.vue'
import { useCategoryStore } from '@/store/admin/categoryStore'

const categoryStore = useCategoryStore()
const globalData = useGlobalData()
const { showSnackbar } = globalData
const route = useRoute()
const router = useRouter()
const searchQuery = ref(route.query.search ?? null)
const debouncedSearchQuery = refDebounced(searchQuery, 300)
const selectedStatus = ref(!isNaN(parseInt(route.query.status)) ? parseInt(route.query.status) : null)
const rowPerPage = ref(route.query.per_page ?? 10)
const currentPage = ref(!isNaN(parseInt(route.query.page)) ? parseInt(route.query.page) : 1)
const totalPage = ref(1)
const totalItems = ref(0)
const items = ref([])
const sortByColumnName = ref(route.query.sort_by ?? 'created_at')
const sortDirection = ref(route.query.sort_dir ?? 'desc')
const skeletonLoading = ref(true)
const itemId = ref('')
const isDeleteDialogVisible = ref(false)
const deleteItemLoading = ref(false)
const isChangeAccountStatusDialogVisible = ref(false)
const changeStatusLoading = ref(false)
const isAddCategoryDrawerOpen = ref(false)
const isEditCategoryDrawerOpen = ref(false)
const selectedCategoryId = ref(null)

onMounted(async () => {
  await Promise.all([
    fetchItems(),
    categoryStore.fetchParentCategories(),
  ])
})

function updateRoute() {
  router.replace({
    query: {
      page: currentPage.value,
      per_page: rowPerPage.value,
      search: searchQuery.value,
      sort_by: sortByColumnName.value,
      sort_dir: sortDirection.value,
      status: selectedStatus.value,
    },
  })
}

watch([debouncedSearchQuery, rowPerPage, selectedStatus], () => {
  currentPage.value = 1
  fetchItems()
})

watch([currentPage, sortByColumnName, sortDirection], () => {
  fetchItems()
})

async function fetchItems() {
  skeletonLoading.value = true

  try {
    const { data } = await ApiService.post('/admin/category/list', {
      searchQuery: searchQuery.value,
      perPage: rowPerPage.value,
      page: currentPage.value,
      sortByColumnName: sortByColumnName.value,
      isSortDirDesc: sortDirection.value,
      is_active: selectedStatus.value,
    })

    if (data.status === 200) {
      const pagedData = data.categories

      if (isEmpty(pagedData.records) && currentPage.value !== 1) {
        currentPage.value = currentPage.value - 1

        return
      }

      let startIndex = calculateStartIndex(pagedData.current_page, pagedData.per_page)
      if (currentPage.value > 1) {
        startIndex = (currentPage.value - 1) * rowPerPage.value + 1
      }

      items.value = pagedData.records.map((d, index) => ({
        ...d,
        sno: index + startIndex,
      }))
      totalPage.value = pagedData.totalPage == 0 ? 1 : pagedData.totalPage
      totalItems.value = pagedData.totalRecords
      updateRoute()
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    console.error(error)
    if (error.response.data.message) {
      showSnackbar(error.response.data.message, 'error')
    } else if (error.response.message) {
      showSnackbar(error.response.message, 'error')
    }
  } finally {
    skeletonLoading.value = false
  }
}

function sortColumn(columnName) {
  if (sortByColumnName.value !== '' && sortByColumnName.value === columnName && sortDirection.value === 'asc') {
    sortDirection.value = 'desc'
  } else {
    sortDirection.value = 'asc'
  }

  sortByColumnName.value = columnName
}

function resolveItemStatus(stat) {
  if (stat === 1) {
    return { color: 'success', status: true, label: 'Active' }
  } else {
    return { color: 'secondary', status: false, label: 'Inactive' }
  }
}

function onAccountStatusChange(id) {
  isChangeAccountStatusDialogVisible.value = true
  itemId.value = id
}

async function handleStatusChangeConfirmation(isConfirmed) {
  if (isConfirmed) {
    await changeItemStatus()
  }
}

async function changeItemStatus() {
  changeStatusLoading.value = true

  try {
    const { data } = await ApiService.get(`/admin/category/update/status/${itemId.value}`)

    if (data.status === 200) {
      await fetchItems()
      itemId.value = ''
      showSnackbar(data.message)
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    console.error(error)
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    changeStatusLoading.value = false
    isChangeAccountStatusDialogVisible.value = false
  }
}

function onDelete(id) {
  isDeleteDialogVisible.value = true
  itemId.value = id
}

async function handleDeleteConfirmation(isConfirmed) {
  if (isConfirmed) {
    await deleteItem()
  }
}

async function deleteItem() {
  deleteItemLoading.value = true

  try {
    const { data } = await ApiService.delete(`/admin/category/delete/${itemId.value}`)

    if (data.status) {
      await fetchItems()
      itemId.value = ''
      showSnackbar(data.message)
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    console.error(error)
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    deleteItemLoading.value = false
    isDeleteDialogVisible.value = false
  }
}

const paginationData = computed(() => {
  const currentPageValue = currentPage.value <= 0 ? 1 : currentPage.value

  const firstIndex = items.value.length
    ? (currentPageValue - 1) * rowPerPage.value + 1
    : 0

  const lastIndex = items.value.length + (currentPageValue - 1) * rowPerPage.value

  return `Showing ${firstIndex} to ${lastIndex} of ${totalItems.value} entries`
})
</script>

<template>
  <section>
    <VRow class="match-height">
      <VCol cols="12">
        <VCard title="Filter">
          <!-- 👉 Filters -->
          <VCardText>
            <VRow>
              <VCol
                cols="12"
                sm="4"
              >
                <AppSelect
                  v-model="selectedStatus"
                  label="Select Status"
                  :items="[{ title: 'Active', value: 1 }, { title: 'Inactive', value: 0 }]"
                  clearable
                  clear-icon="tabler-x"
                  placeholder="Select"
                />
              </VCol>
            </VRow>
          </VCardText>

          <VDivider />

          <VCardText class="d-flex flex-wrap py-4 gap-4">
            <div
              class="me-3"
              style="width: 80px"
            >
              <VSelect
                v-model="rowPerPage"
                density="compact"
                variant="outlined"
                :items="[10, 20, 30, 50]"
              />
            </div>

            <VSpacer />

            <div class="d-flex align-center flex-wrap gap-4">
              <!-- 👉 Search  -->
              <div style="width: 23rem">
                <VTextField
                  v-model="searchQuery"
                  placeholder="Search"
                  density="compact"
                  clearable
                  clear-icon="tabler-x"
                />
              </div>
              <!-- 👉 Add button -->
              <VBtn
                variant="tonal"
                color="primary"
                @click="isAddCategoryDrawerOpen = true"
              >
                Add New Category
              </VBtn>
            </div>
          </VCardText>

          <VDivider />

          <VTable class="text-no-wrap">
            <!-- 👉 table head -->
            <thead>
              <tr>
                <th scope="col">
                  #
                </th>
                <th scope="col">
                  Category
                </th>
                <th scope="col">
                  Slug
                </th>
                <th scope="col">
                  Parent Category
                </th>
                <th scope="col">
                  STATUS
                </th>
                <th
                  scope="col"
                  class="cursor-pointer"
                  @click="sortColumn('created_at')"
                >
                  <div class="w-125px">
                    CREATED AT
                    <span class="d-grid float-right">
                      <VIcon
                        size="10"
                        icon="tabler-chevron-up"
                      />
                      <VIcon
                        size="10"
                        icon="tabler-chevron-down"
                      />
                    </span>
                  </div>
                </th>
                <th
                  scope="col"
                  class="cursor-pointer"
                  @click="sortColumn('updated_at')"
                >
                  <div class="w-125px">
                    UPDATED AT
                    <span class="d-grid float-right">
                      <VIcon
                        size="10"
                        icon="tabler-chevron-up"
                      />
                      <VIcon
                        size="10"
                        icon="tabler-chevron-down"
                      />
                    </span>
                  </div>
                </th>
                <th scope="col">
                  ACTIONS
                </th>
              </tr>
            </thead>

            <!-- 👉 table body -->
            <tbody v-if="skeletonLoading">
              <tr
                v-for="n in 10"
                :key="n"
              >
                <td
                  v-for="i in 8"
                  :key="i"
                >
                  <Skeleton
                    width="8rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
              </tr>
            </tbody>
            <tbody v-else>
              <tr
                v-for="item in items"
                :key="item.id"
                style="height: 3.75rem"
              >
                <!-- 👉 NO -->
                <td>
                  {{ item.sno }}
                </td>

                <!-- 👉 Category -->
                <td class="title">
                  <div>
                    {{ item.name }}
                  </div>
                </td>

                <!-- 👉 Slug -->
                <td>
                  {{ item.slug }}
                </td>

                <!-- 👉 Parent Category -->
                <td>
                  {{ item.parent_category_name ?? '-' }}
                </td>

                <!-- 👉 Status -->
                <td>
                  <VSwitch
                    v-model="resolveItemStatus(item.is_active).status"
                    :label="resolveItemStatus(item.is_active).label"
                    inset
                    @change="onAccountStatusChange(item.id)"
                  />
                </td>

                <!-- 👉 Created At -->
                <td>
                  <div
                    v-if="item.created_date && item.created_time"
                    class="d-flex flex-column"
                  >
                    <span>{{ item.created_date }}</span>
                    <span>{{ item.created_time }}</span>
                  </div>
                  <div v-else>
                    -
                  </div>
                </td>

                <!-- 👉 Updated At -->
                <td>
                  <div
                    v-if="item.updated_date !== item.created_date || item.updated_time !== item.created_time"
                    class="d-flex flex-column"
                  >
                    <span>{{ item.updated_date }}</span>
                    <span>{{ item.updated_time }}</span>
                  </div>
                  <div v-else>
                    -
                  </div>
                </td>

                <!-- 👉 Actions -->
                <td
                  class="text-center"
                  style="width: 5rem"
                >
                  <VBtn
                    v-tooltip.top="`Edit category`"
                    icon
                    size="x-small"
                    color="default"
                    variant="text"
                    @click="() => {
                      selectedCategoryId = item.id
                      isEditCategoryDrawerOpen = true
                    }"
                  >
                    <VIcon
                      size="22"
                      icon="tabler-edit"
                    />
                  </VBtn>
                  <VBtn
                    icon
                    size="x-small"
                    color="default"
                    variant="text"
                    @click="onDelete(item.id)"
                  >
                    <VIcon
                      size="22"
                      icon="tabler-trash"
                    />
                  </VBtn>
                </td>
              </tr>
            </tbody>

            <!-- 👉 table footer  -->
            <tfoot v-show="!skeletonLoading && isEmpty(items)">
              <tr>
                <td
                  colspan="10"
                  class="text-center"
                >
                  No data available
                </td>
              </tr>
            </tfoot>
          </VTable>

          <VDivider />

          <VCardText class="d-flex align-center flex-wrap justify-space-between gap-4 py-3 px-5">
            <span class="text-sm text-disabled">
              {{ paginationData }}
            </span>
            <VPagination
              v-model="currentPage"
              size="small"
              :total-visible="5"
              :length="totalPage"
            />
          </VCardText>
        </VCard>
      </VCol>
    </VRow>

    <!-- 👉 Change Status confirm dialog -->
    <ConfirmDialog
      v-model:isDialogVisible="isChangeAccountStatusDialogVisible"
      confirmation-question="Are you sure you want to toggle this category status?"
      :loading="changeStatusLoading"
      @confirm="handleStatusChangeConfirmation"
    />

    <!-- 👉 Delete confirm dialog -->
    <ConfirmDialog
      v-model:isDialogVisible="isDeleteDialogVisible"
      confirmation-question="Are you sure you want to delete this category?"
      :loading="deleteItemLoading"
      @confirm="handleDeleteConfirmation"
    />

    <!-- 👉 Add category drawer -->
    <CategoryAddDrawer
      v-model:isDrawerOpen="isAddCategoryDrawerOpen"
      @added="() => {
        fetchItems()
        categoryStore.fetchParentCategories()
      }"
    />

    <!-- 👉 Edit category drawer -->
    <CategoryEditDrawer
      v-model:isDrawerOpen="isEditCategoryDrawerOpen"
      :category-id="selectedCategoryId"
      @updated="() => {
        fetchItems()
        categoryStore.fetchParentCategories()
      }"
    />
  </section>
</template>

<style lang="scss">
.v-table__wrapper td.title {
  min-width: 200px;
  h6 {
    a {
      white-space: pre-line;
    }
  }
}
</style>
