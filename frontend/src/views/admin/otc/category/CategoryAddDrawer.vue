<script setup>
import { requiredValidator } from '@validators'
import { useGlobalData } from '@/store/global'
import { scrollToTop } from '@/utils/helpers'
import ApiService from '@/services/ApiService'
import { processErrors } from '@/utils/errorHandler'
import { PerfectScrollbar } from 'vue3-perfect-scrollbar'
import { useCategoryStore } from '@/store/admin/categoryStore'
import { storeToRefs } from 'pinia'
import { watch } from 'vue'

const props = defineProps({
  isDrawerOpen: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits(['update:isDrawerOpen', 'added'])
const categoryStore = useCategoryStore()
const { parentCategories } = storeToRefs(categoryStore)
const globalData = useGlobalData()
const { showSnackbar } = globalData

const loading = ref(false)
const serverErrors = ref([])
const refForm = ref()

const formData = ref({
  name: undefined,
  parent_id: null,
})

watch(props, () => {
  refForm.value?.reset()
  refForm.value?.resetValidation()
  serverErrors.value = []
})

const resetForm = () => {
  emit('update:isDrawerOpen', false)
  refForm.value?.reset()
  refForm.value?.resetValidation()
}

const onFormSubmit = () => {
  refForm.value?.validate().then(async ({ valid }) => {
    if (valid) {
      serverErrors.value = []
      loading.value = true

      const postData = { ...formData.value }

      try {
        const { data } = await ApiService.post('/admin/category/add', postData)

        if (data.status === 200) {
          showSnackbar(data.message)
          emit('added')
          resetForm()
        } else {
          serverErrors.value = processErrors(data)
        }
      } catch (error) {
        console.error(error)
        serverErrors.value = processErrors(error)
      } finally {
        loading.value = false
      }
    } else {
      scrollToTop()
    }
  })
}

const handleDrawerModelValueUpdate = val => {
  emit('update:isDrawerOpen', val)
}
</script>

<template>
  <VNavigationDrawer
    :model-value="props.isDrawerOpen"
    temporary
    location="end"
    width="400"
    class="category-navigation-drawer scrollable-content"
    @update:model-value="handleDrawerModelValueUpdate"
  >
    <!-- 👉 Header -->
    <AppDrawerHeaderSection
      title="Add Category"
      @cancel="$emit('update:isDrawerOpen', false)"
    />

    <VDivider />

    <PerfectScrollbar :options="{ wheelPropagation: false }">
      <VForm
        ref="refForm"
        @submit.prevent="onFormSubmit"
      >
        <VCard flat>
          <VCardText>
            <VAlert
              v-if="serverErrors.length > 0"
              type="error"
              variant="tonal"
              title="Validation failed!"
              closable
              class="mb-8"
            >
              <ul>
                <li
                  v-for="error in serverErrors"
                  :key="error"
                >
                  {{ error }}
                </li>
              </ul>
            </VAlert>

            <VRow>
              <VCol cols="12">
                <VAlert
                  class="mb-2"
                  variant="tonal"
                >
                  <h4>Category Creation Guide:</h4>
                  <ul class="list-disc ps-5">
                    <li>
                      To add a main/parent category: Leave the <span class="font-weight-bold">Parent Category</span> dropdown empty.
                    </li>
                    <li>
                      To add a subcategory: Select a <span class="font-weight-bold">Parent Category</span> from the dropdown below.
                    </li>
                  </ul>
                </VAlert>
              </VCol>

              <VCol cols="12">
                <VTextField
                  v-model="formData.name"
                  label="Name"
                  :rules="[requiredValidator]"
                />
              </VCol>

              <VCol cols="12">
                <VSelect
                  v-model="formData.parent_id"
                  label="Parent Category"
                  :items="parentCategories"
                  item-title="name"
                  item-value="id"
                  clearable
                  placeholder="Select parent category (optional)"
                />
              </VCol>
            </VRow>
            <VRow class="mt-3">
              <VCol cols="12">
                <VBtn
                  type="submit"
                  class="me-3"
                  :loading="loading"
                  @click="refForm?.validate()"
                >
                  Submit
                </VBtn>
                <VBtn
                  variant="tonal"
                  color="secondary"
                  :disabled="loading"
                  @click="resetForm"
                >
                  Cancel
                </VBtn>
              </VCol>
            </VRow>
          </VCardText>
        </VCard>
      </VForm>
    </PerfectScrollbar>
  </VNavigationDrawer>
</template>
