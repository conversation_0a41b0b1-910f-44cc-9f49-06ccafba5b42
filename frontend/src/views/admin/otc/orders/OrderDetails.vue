<script setup>
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { processErrors } from '@/utils/errorHandler'
import { computed, onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import { formatCurrency, formattedPhoneNumber, resolveInitials } from '@/utils/helpers'
import { isEmpty } from '@/@core/utils'

const route = useRoute()
const { showSnackbar } = useGlobalData()
const orderId = computed(() => route.params.orderId)
const skeletonLoading = ref(true)
const order = ref({})
const isUpdatingStatus = ref(false)
const activeMenuProductId = ref(null)

const statusOptions = ref([
  { title: 'Processing', value: 0 },
  { title: 'In-transit', value: 3 },
  { title: 'Delivered', value: 1 },
])

async function fetchOrderDetails() {
  try {
    skeletonLoading.value = true

    const { data } = await ApiService.get(`/admin/otc-orders-details/${orderId.value}`)

    if (data.status === 200) {
      order.value = data.orderDetails
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    console.error(error)
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    skeletonLoading.value = false
  }
}

async function updateProductStatus(productId, newStatus) {
  try {
    isUpdatingStatus.value = true
    activeMenuProductId.value = productId

    const { data } = await ApiService.post('/admin/update-otc-product-status', {
      otc_order_product_id: productId,
      status: newStatus,
    })

    if (data.status === 200) {
      showSnackbar(data.message || 'Product status updated successfully')
      await fetchOrderDetails()
    } else {
      showSnackbar(data.message || 'Failed to update product status', 'error')
    }
  } catch (error) {
    console.error(error)
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    isUpdatingStatus.value = false
    activeMenuProductId.value = null
  }
}

onMounted(async () => {
  await fetchOrderDetails()
})
</script>

<template>
  <div v-if="skeletonLoading">
    <div
      class="d-flex justify-center align-center"
      style="height: calc(100vh - 14rem)"
    >
      <VProgressCircular
        indeterminate
        size="48"
      />
    </div>
  </div>
  <div v-else-if="!isEmpty(order)">
    <div class="d-flex justify-space-between align-center flex-wrap gap-y-4 mb-6">
      <div>
        <div class="d-flex gap-2 align-center flex-wrap">
          <h4 class="text-h4">
            Order #{{ order.order_reference_no }}
          </h4>
        </div>
      </div>
    </div>

    <VRow>
      <VCol
        cols="12"
        md="8"
      >
        <!-- 👉 Order Details -->
        <VCard class="mb-6">
          <VCardTitle class="pt-6 px-6">
            Order Details
          </VCardTitle>

          <VCardText>
            <div class="mb-6 text-sm">
              <div class="d-flex justify-space-between border-b py-3 px-1">
                <div>Order Number:</div>
                <div>{{ order.order_reference_no }}</div>
              </div>
              <div class="d-flex justify-space-between border-b py-3 px-1">
                <div>Created At:</div>
                <div>
                  {{ order.created_date }}
                </div>
              </div>
              <div class="d-flex justify-space-between border-b py-3 px-1">
                <div>Updated At:</div>
                <div>
                  {{ order.updated_date }}
                </div>
              </div>
            </div>

            <VTable>
              <thead>
                <tr>
                  <th>
                    Product
                  </th>
                  <th>
                    Quantity
                  </th>
                  <th class="text-right">
                    Price
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr
                  v-for="product in order.order_products"
                  :key="product.id"
                >
                  <td>
                    <div
                      class="d-flex align-center py-2 gap-3"
                      style="min-width: 300px;"
                    >
                      <VAvatar
                        v-if="product.product_details?.product_image"
                        variant="tonal"
                        size="50"
                        class="me-3"
                        :image="product.product_details.product_image"
                        rounded
                      />
                      <VAvatar
                        v-else
                        variant="tonal"
                        size="50"
                        class="me-3"
                        rounded
                      >
                        {{ product.product_details?.product_name ? resolveInitials(product.product_details.product_name) : 'G' }}
                      </VAvatar>
                      <div>
                        <h6
                          class="text-base"
                          style="font-weight: 500;"
                        >
                          {{ product.product_details?.product_name }}
                        </h6>
                        <div class="d-flex flex-column mt-1">
                          <div class="d-flex align-center gap-2">
                            <VChip
                              label
                              :text="product.status_text ?? 'Unknown'"
                              :color="product.status_color ?? 'default'"
                            />
                            <div class="ms-2">
                              <VBtn
                                variant="text"
                                size="small"
                                color="primary"
                                class="ps-1 pe-0"
                                :disabled="isUpdatingStatus"
                                :loading="isUpdatingStatus && activeMenuProductId === product.id"
                              >
                                Update Status
                                <VIcon
                                  size="16"
                                  icon="mdi-chevron-down"
                                />

                                <VMenu
                                  activator="parent"
                                  location="bottom"
                                >
                                  <VList
                                    density="compact"
                                    min-width="180"
                                  >
                                    <VListItem
                                      v-for="option in statusOptions"
                                      :key="option.value"
                                      :value="option.value"
                                      :active="product.status === option.value"
                                      @click="updateProductStatus(product.id, option.value)"
                                    >
                                      <VListItemTitle>{{ option.title }}</VListItemTitle>
                                    </VListItem>
                                  </VList>
                                </VMenu>
                              </VBtn>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td>
                    {{ product.purchased_qty }}
                  </td>
                  <td class="text-right">
                    <div class="d-flex justify-end">
                      <VChip
                        v-if="product.promo_code_name"
                        class="me-3 font-weight-bold"
                        color="success"
                        size="x-small"
                      >
                        {{ product.promo_code_name }}&nbsp;<span v-if="product.promo_code_type === 'percentage'">({{ product.promo_code_value }}%)</span>
                      </VChip>
                      <span
                        v-if="product.total_product_amount !== product.grand_total_amount"
                        class="text-decoration-line-through text-sm me-2"
                      >{{ formatCurrency(product.total_product_amount) }}</span>
                      <span class="font-weight-medium text-high-emphasis">{{ formatCurrency(product.grand_total_amount) }}</span>
                    </div>
                  </td>
                </tr>
              </tbody>
            </VTable>

            <VDivider />

            <div class="d-flex align-end flex-column mt-6 px-4">
              <table class="text-high-emphasis">
                <tbody>
                  <tr class="text-base">
                    <td width="300px">
                      Subtotal:
                    </td>
                    <td class="text-right">
                      {{ formatCurrency(order.sub_total) }}
                    </td>
                  </tr>
                  <tr class="text-base">
                    <td>Shipping & handling cost: </td>
                    <td class="text-right">
                      <span
                        v-if="order.shipping_cost === 0"
                        class="text-success"
                      >FREE</span>
                      <span v-else>{{ formatCurrency(order.shipping_cost) }}</span>
                    </td>
                  </tr>
                  <tr class="text-base">
                    <td class="text-high-emphasis font-weight-medium">
                      Total:
                    </td>
                    <td class="font-weight-medium text-right">
                      {{ formatCurrency(order.grand_total) }}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </VCardText>
        </VCard>
      </VCol>

      <VCol
        cols="12"
        md="4"
      >
        <!-- 👉 Customer Details  -->
        <VCard
          class="mb-6"
          title="Customer Details"
        >
          <VCardText class="d-flex flex-column gap-y-6">
            <div class="d-flex align-center">
              <VAvatar
                class="me-3"
                rounded="lg"
                size="60"
                variant="tonal"
              >
                {{ order.user_details?.full_name ? resolveInitials(order.user_details.full_name) : 'U' }}
              </VAvatar>
              <div>
                <h5 class="text-h5">
                  {{ order.user_details?.full_name }}
                </h5>
                <p class="text-body-2 mb-0">
                  {{ order.user_details?.email }}
                </p>
                <p class="text-body-2 mb-0">
                  {{ formattedPhoneNumber(order.user_details?.phone_number) }}
                </p>
              </div>
            </div>
          </VCardText>
        </VCard>

        <!-- 👉 Payment Details -->
        <VCard
          v-if="!isEmpty(order.payment_history)"
          title="Payment Details"
          class="mb-6"
        >
          <VCardText>
            <div>
              <div class="text-xs text-high-emphasis font-weight-medium text-uppercase mb-2">
                Payment Method
              </div>
              <div>
                <div class="text-sm text-uppercase font-weight-medium">
                  {{ order.payment_history?.card_brand_type }}
                </div>
                <div class="text-base">
                  xxxx xxxx xxxx {{ order.payment_history?.card_number }}
                </div>
              </div>
            </div>

            <div
              v-if="order.payment_transaction_id"
              class="mt-4"
            >
              <div class="text-xs text-high-emphasis font-weight-medium text-uppercase mb-2">
                Transaction ID
              </div>
              <div>
                <div class="text-sm">
                  {{ order.payment_transaction_id }}
                </div>
              </div>
            </div>

            <a
              v-if="order && order.invoice_url"
              :href="order.invoice_url"
              class="v-btn text-link cursor-pointer mt-4"
              target="_blank"
              rel="noopener noreferrer"
            >View Invoice</a>
          </VCardText>
        </VCard>

        <!-- 👉 Shipping Details -->
        <VCard
          class="mb-6"
          title="Shipping Address"
        >
          <VCardText>
            <div class="text-medium-emphasis">
              {{ order.shipping_details?.address_line_1 }} <br>
              <span v-if="order.shipping_details?.address_line_2">
                {{ order.shipping_details.address_line_2 }} <br>
              </span>
              {{ order.shipping_details?.city }}, {{ order.shipping_details?.state }}-{{ order.shipping_details?.zipcode }} <br>
              {{ order.shipping_details?.country }}
            </div>
          </VCardText>
        </VCard>
      </VCol>
    </VRow>
  </div>
</template>
