<script setup>
import { isEmpty } from '@/@core/utils'
import router from '@/router'
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { resolveVisitStatus } from '@/utils/admin'
import { processErrors } from '@/utils/errorHandler'
import { formattedPhoneNumber } from '@/utils/helpers'
import { capitalize, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'

const { showSnackbar } = useGlobalData()
const route = useRoute()
const logId = computed(() => route.params.logId)
const skeletonLoading = ref(true)
const order = ref({})
const isVisitEventDetailsDialogVisible = ref(false)
const selectedBelugaEventId = ref('')

onMounted(async () => {
  await fetchVisitLogDetails()
})

const fetchVisitLogDetails = async () => {
  try {
    skeletonLoading.value = true

    const { data } = await ApiService.get(`/admin/manage-visit-order-details/${logId.value}`)

    if (data.status === 200) {
      order.value = data.WhitelblrxVisitOrders
    } else {
      showSnackbar(data.message, 'error')
      router.push({ name: 'admin-visit-logs' })
    }
  } catch (error) {
    console.error(error)
    showSnackbar(processErrors(error)[0], 'error')
    router.push({ name: 'admin-visit-logs' })
  } finally {
    skeletonLoading.value = false
  }
}

const subscriptionDetailsLink = computed(() => {
  const visitType = order.value.visit_details?.visit_type?.toLowerCase()

  const subscriptionDetailsRouteMap = {
    'ed': 'admin-ed-manage-subscription',
    'hairloss': 'admin-hl-manage-subscription',
    'weightloss': 'admin-wl-manage-subscription',
    'weightlossfollowup': 'admin-wl-manage-subscription',
  }

  return {
    name: subscriptionDetailsRouteMap[visitType],
    params: { subscriptionId: order.value.visit_details?.subscription_id },
  }
})
</script>

<template>
  <div v-if="skeletonLoading">
    <div
      class="d-flex justify-center align-center"
      style="height: calc(100vh - 14rem)"
    >
      <VProgressCircular
        indeterminate
        size="48"
      />
    </div>
  </div>
  <div
    v-else
    class="order-details"
  >
    <div class="d-flex justify-space-between align-center flex-wrap gap-y-4 mb-6">
      <div>
        <div
          v-if="order.visit_details?.subscription_reference_id"
          class="d-flex gap-2 align-center flex-wrap"
        >
          <h4 class="text-h4">
            Subscription ID: {{ order.visit_details?.subscription_reference_id }}
          </h4>
        </div>
      </div>

      <div class="d-flex flex-wrap gap-3">
        <VBtn
          color="primary"
          variant="tonal"
          :to="{
            name: 'admin-visit-medical-qa',
            params: { logId },
          }"
        >
          View Medical QA
        </VBtn>
      </div>
    </div>

    <VRow class="match-height">
      <VCol
        cols="12"
        md="6"
      >
        <!-- Visit Details -->
        <VCard
          title="Visit Details"
          class="h-100"
        >
          <VCardText>
            <VTable class="text-no-wrap order-table">
              <tr v-if="order.visit_details?.subscription_reference_id && order.visit_details?.subscription_id">
                <td>Subscription ID</td>
                <td class="text-right">
                  <RouterLink
                    v-if="subscriptionDetailsLink"
                    :to="subscriptionDetailsLink"
                    class="text-link"
                  >
                    {{ order.visit_details?.subscription_reference_id ?? "-" }}
                  </RouterLink>
                  <span v-else>{{ order.visit_details?.subscription_reference_id ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>Master ID</td>
                <td class="text-right">
                  <span>{{ order.visit_details?.master_id ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>Visit Type</td>
                <td class="text-right">
                  <span>{{ order.visit_details?.visit_type ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>Visit Mode</td>
                <td class="text-right">
                  <span>{{ order.visit_details?.visit_mode ? capitalize(order.visit_details?.visit_mode) : "-" }}</span>
                </td>
              </tr>
              <template v-if="order.visit_details?.visit_mode === 'synchronous'">
                <tr>
                  <td>Synchronous Visit Status</td>
                  <td class="text-right">
                    <VChip
                      v-if="order.visit_details?.synchronous_visit_status_color && order.visit_details?.synchronous_visit_status_text"
                      label
                      :color="order.visit_details?.synchronous_visit_status_color"
                      size="small"
                      class="text-capitalize"
                    >
                      {{ order.visit_details?.synchronous_visit_status_text }}
                    </VChip>
                    <span v-else>-</span>
                  </td>
                </tr>
                <tr v-if="order.visit_details?.appointmentScheduledLink">
                  <td>Appointment Schedule Link</td>
                  <td class="text-right">
                    {{ order.visit_details?.appointmentScheduledLink }}
                  </td>
                </tr>
                <tr v-if="order.visit_details?.appointmentDocName">
                  <td>Appointment with Provider</td>
                  <td class="text-right">
                    {{ order.visit_details?.appointmentDocName }}
                  </td>
                </tr>
                <tr v-if="order.visit_details?.appointmentDateTime">
                  <td>Appointment Date & Time</td>
                  <td class="text-right">
                    {{ order.visit_details?.appointmentDateTime }}
                  </td>
                </tr>
                <tr v-if="order.visit_details?.appointmentUrl">
                  <td>Appointment URL</td>
                  <td class="text-right">
                    {{ order.visit_details?.appointmentUrl }}
                  </td>
                </tr>
              </template>
              <tr>
                <td>Visit Status</td>
                <td class="text-right">
                  <VChip
                    label
                    :color="resolveVisitStatus(order.visit_details?.status).color"
                    size="small"
                    class="text-capitalize"
                  >
                    {{ resolveVisitStatus(order.visit_details?.status).label }}
                  </VChip>
                </td>
              </tr>
              <tr>
                <td>Pharmacy</td>
                <td class="text-right">
                  <span>{{ order.visit_details?.pharmacy_name ?? "-" }}</span>
                </td>
              </tr>
              <tr v-if="order.visit_details?.beluga_visit_outcome">
                <td>Visit Outcome</td>
                <td class="text-right">
                  <span>{{ order.visit_details?.beluga_visit_outcome ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>Visit created at</td>
                <td class="text-right">
                  <span>{{ order.visit_details?.visit_created_at ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>Last updated at</td>
                <td class="text-right">
                  <span>{{ order.visit_details?.visit_updated_at ?? "-" }}</span>
                </td>
              </tr>
            </VTable>
          </VCardText>
        </VCard>
      </VCol>

      <VCol
        cols="12"
        md="6"
      >
        <!-- Customer Details -->
        <VCard
          title="Customer Details"
          class="h-100"
        >
          <VCardText>
            <VTable class="text-no-wrap order-table">
              <tr>
                <td>First Name</td>
                <td class="text-right">
                  <span>{{ order.customer_details?.first_name ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>Last Name</td>
                <td class="text-right">
                  <span>{{ order.customer_details?.last_name ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>Sex</td>
                <td class="text-right">
                  <span>{{ order.customer_details?.sex ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>DOB</td>
                <td class="text-right">
                  <span>{{ order.customer_details?.dob ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>Email</td>
                <td class="text-right">
                  <span>{{ order.customer_details?.email ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>Phone</td>
                <td class="text-right">
                  <span>{{ !isEmpty(order.customer_details?.phone_number) ? formattedPhoneNumber(order.customer_details?.phone_number) : "-" }}</span>
                </td>
              </tr>
            </VTable>
          </VCardText>
        </VCard>
      </VCol>
      <VCol
        cols="12"
        md="6"
      >
        <!-- Medical Conditions -->
        <VCard
          title="Medical Conditions"
          class="h-100"
        >
          <VCardText>
            <div class="mb-3">
              <div class="text-body-1 text-medium-emphasis">
                Self Reported Medications
              </div>
              <div class="text-body-2 text-high-emphasis">
                {{ order.medical_condition_details?.self_reported_medications ?? "-" }}
              </div>
            </div>

            <div class="mb-3">
              <div class="text-body-1 text-medium-emphasis">
                Allergies
              </div>

              <div class="text-body-2 text-high-emphasis">
                {{ order.medical_condition_details?.allergies ?? "-" }}
              </div>
            </div>

            <div class="mb-3">
              <div class="text-body-1 text-medium-emphasis">
                Medical History
              </div>
              <div class="text-body-2 text-high-emphasis">
                {{ order.medical_condition_details?.medical_history ?? "-" }}
              </div>
            </div>
          </VCardText>
        </VCard>
      </VCol>
      <VCol
        cols="12"
        md="6"
      >
        <VCard
          :title="
            order.visit_details?.is_medicine_pickup_at_local_pharmacy === 1
              ? 'Address'
              : 'Shipping Address'
          "
          class="h-100"
        >
          <VCardText>
            <VTable class="text-no-wrap order-table">
              <tr>
                <td>Address</td>
                <td class="text-right">
                  <span>{{ order.shipping_details?.address ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>City</td>
                <td class="text-right">
                  <span>{{ order.shipping_details?.city ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>State / Territory</td>
                <td class="text-right">
                  <span>{{ order.shipping_details?.state ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>Zipcode</td>
                <td class="text-right">
                  <span>{{ order.shipping_details?.zip_code ?? "-" }}</span>
                </td>
              </tr>
            </VTable>
          </VCardText>
        </VCard>
      </VCol>

      <VCol
        v-if="order.visit_details?.is_medicine_pickup_at_local_pharmacy === 1 && !isEmpty(order.local_pharmacy_details)"
        cols="12"
      >
        <VCard title="Prescription Pickup Location">
          <VCardText>
            <div>
              <div
                v-if="order.local_pharmacy_details?.PharmacyName"
                class="text-high-emphasis font-weight-bold mb-1"
              >
                {{ order.local_pharmacy_details?.PharmacyName }}
              </div>
              <div v-if="order.local_pharmacy_details?.Address1">
                {{ order.local_pharmacy_details?.Address1 }}
                <span v-if="order.local_pharmacy_details?.Address2">
                  , {{ order.local_pharmacy_details?.Address2 }}
                </span>
              </div>
              <div v-if="order.local_pharmacy_details?.City">
                {{ order.local_pharmacy_details?.City }}
              </div>
              <div>
                <span v-if="order.local_pharmacy_details?.State">{{ order.local_pharmacy_details?.State }}</span>
                <span v-if="order.local_pharmacy_details?.ZipCode">-{{ order.local_pharmacy_details?.ZipCode }} </span>
              </div>
              <div class="mt-2">
                <div v-if="order.local_pharmacy_details?.PharmacyId">
                  Pharmacy ID: {{ order.local_pharmacy_details?.PharmacyId }}
                </div>
                <div v-if="order.local_pharmacy_details?.PrimaryPhone">
                  Phone: {{ order.local_pharmacy_details?.PrimaryPhone }}
                </div>
                <div v-if="order.local_pharmacy_details?.PrimaryFax">
                  Fax: {{ order.local_pharmacy_details?.PrimaryFax }}
                </div>
              </div>
            </div>

            <!-- Locate Pharmacy -->
            <div
              v-if="order.local_pharmacy_details?.PharmacyDirection"
              class="mt-5"
            >
              <a
                :href="order.local_pharmacy_details?.PharmacyDirection"
                class="dt-link"
                target="_blank"
                rel="noreferrer noopener"
              >
                <VIcon
                  icon="tabler-map-pin"
                  size="18"
                  start
                /> Locate Pharmacy
              </a>
            </div>
          </VCardText>
        </VCard>
      </VCol>

      <VCol cols="12">
        <VCard title="Medicine Data Sent to Provider">
          <VCardText>
            <VTable class="text-no-wrap order-table product-table">
              <thead>
                <tr class="text-start fw-bolder text-uppercase">
                  <th>Name</th>
                  <th>Strength</th>
                  <th>Quantity</th>
                  <th>Refills</th>
                  <th>Med ID</th>
                  <th>Prescription Status</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>
                    {{
                      !isEmpty(order?.medicine_data_sent_to_provider?.name)
                        ? order?.medicine_data_sent_to_provider?.name
                        : "-"
                    }}
                  </td>
                  <td>
                    {{
                      !isEmpty(order?.medicine_data_sent_to_provider?.strength)
                        ? order?.medicine_data_sent_to_provider?.strength
                        : "-"
                    }}
                  </td>
                  <td>
                    {{
                      !isEmpty(order?.medicine_data_sent_to_provider?.quantity)
                        ? order?.medicine_data_sent_to_provider?.quantity
                        : "-"
                    }}
                  </td>
                  <td>
                    {{
                      !isEmpty(order?.medicine_data_sent_to_provider?.refills)
                        ? order?.medicine_data_sent_to_provider?.refills
                        : "-"
                    }}
                  </td>
                  <td>
                    {{
                      !isEmpty(order?.medicine_data_sent_to_provider?.medId)
                        ? order?.medicine_data_sent_to_provider?.medId
                        : "-"
                    }}
                  </td>
                  <td>
                    <VChip
                      v-if="!isEmpty(order?.medicine_data_sent_to_provider?.statusText)"
                      label
                      :color="order?.medicine_data_sent_to_provider?.statusColor"
                      size="small"
                      class="text-capitalize"
                    >
                      {{ order?.medicine_data_sent_to_provider?.statusText }}
                    </VChip>
                    <span v-else>-</span>
                  </td>
                </tr>
              </tbody>
            </VTable>
          </VCardText>
        </VCard>
      </VCol>

      <VCol
        v-if="!isEmpty(order.medicine_data_received_from_beluga)"
        cols="12"
        md="8"
      >
        <VCard title="Medicine Data Received from Provider">
          <VCardText>
            <VTable class="text-no-wrap order-table product-table">
              <thead>
                <tr class="text-start fw-bolder text-uppercase">
                  <th>Name</th>
                  <th>Strength</th>
                  <th>Quantity</th>
                  <th>Refills</th>
                  <th>Med ID</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>
                    {{
                      !isEmpty(order?.medicine_data_received_from_beluga?.name)
                        ? order?.medicine_data_received_from_beluga?.name
                        : "-"
                    }}
                  </td>
                  <td>
                    {{
                      !isEmpty(order?.medicine_data_received_from_beluga?.strength)
                        ? order?.medicine_data_received_from_beluga?.strength
                        : "-"
                    }}
                  </td>
                  <td>
                    {{
                      !isEmpty(order?.medicine_data_received_from_beluga?.quantity)
                        ? order?.medicine_data_received_from_beluga?.quantity
                        : "-"
                    }}
                  </td>
                  <td>
                    {{
                      !isEmpty(order?.medicine_data_received_from_beluga?.refills)
                        ? order?.medicine_data_received_from_beluga?.refills
                        : "-"
                    }}
                  </td>
                  <td>
                    {{
                      !isEmpty(order?.medicine_data_received_from_beluga?.medId)
                        ? order?.medicine_data_received_from_beluga?.medId
                        : "-"
                    }}
                  </td>
                </tr>
              </tbody>
            </VTable>
          </VCardText>
        </VCard>
      </VCol>

      <VCol
        v-if="!isEmpty(order.prescriber_details)"
        cols="12"
        md="4"
      >
        <VCard
          title="Prescriber Details"
          class="h-100"
        >
          <VCardText>
            <VTable class="text-no-wrap order-table">
              <tr>
                <td>First Name</td>
                <td class="text-right">
                  <span>
                    {{ !isEmpty(order.prescriber_details?.first_name) ? order.prescriber_details?.first_name : "-" }}
                  </span>
                </td>
              </tr>
              <tr>
                <td>Last Name</td>
                <td class="text-right">
                  <span>
                    {{ !isEmpty(order.prescriber_details?.last_name) ? order.prescriber_details?.last_name : "-" }}
                  </span>
                </td>
              </tr>
              <tr>
                <td>NPI</td>
                <td class="text-right">
                  <span>
                    {{ !isEmpty(order.prescriber_details?.npi) ? order.prescriber_details?.npi : "-" }}
                  </span>
                </td>
              </tr>
              <tr>
                <td>Address</td>
                <td class="text-right">
                  <span>{{ order.prescriber_details?.address ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>City</td>
                <td class="text-right">
                  <span>{{ order.prescriber_details?.city ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>State / Territory</td>
                <td class="text-right">
                  <span>{{ order.prescriber_details?.state ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>Zipcode</td>
                <td class="text-right">
                  <span>{{ order.prescriber_details?.zip ?? "-" }}</span>
                </td>
              </tr>
            </VTable>
          </VCardText>
        </VCard>
      </VCol>

      <VCol cols="12">
        <VCard title="Events Log">
          <VCardText v-if="!isEmpty(order.provider_event_logs)">
            <VTable class="text-no-wrap order-table product-table">
              <thead>
                <tr class="text-start fw-bolder text-uppercase">
                  <th>Status Code</th>
                  <th>Sender</th>
                  <th>Receiver</th>
                  <th>Event Name</th>
                  <th>Date</th>
                  <th class="text-end">
                    ACTION
                  </th>
                </tr>
              </thead>
              <tbody>
                <!-- status code -->
                <tr
                  v-for="(item, index) in order.provider_event_logs"
                  :key="index"
                >
                  <td>
                    <VChip
                      v-if="item.status_code && item.status_code === 200"
                      label
                      color="success"
                      size="small"
                      class="text-capitalize"
                    >
                      {{ item.status_code }}
                    </VChip>
                    <VChip
                      v-else-if="item.status_code && item.status_code !== 200"
                      label
                      color="error"
                      size="small"
                      class="text-capitalize"
                    >
                      {{ item.status_code }}
                    </VChip>
                    <span v-else>-</span>
                  </td>
                  <!-- sender -->
                  <td>{{ item.request_sent_from ?? "-" }}</td>
                  <!-- receiver -->
                  <td>{{ item.request_sent_to ?? "-" }}</td>
                  <!-- event name -->
                  <td>
                    <VChip
                      v-if="item.event_type && item.status_code"
                      label
                      :color="item.status_code === 200 ? 'success' : 'error'"
                      size="small"
                      class="text-capitalize"
                    >
                      {{ item.event_type }}
                    </VChip>
                    <VChip
                      v-else-if="item.event_type && !item.status_code"
                      label
                      color="primary"
                      size="small"
                      class="text-capitalize"
                    >
                      {{ item.event_type }}
                    </VChip>
                    <span v-else>-</span>
                  </td>
                  <!-- date -->
                  <td>{{ item.created_date_time ?? "-" }}</td>
                  <!-- action -->
                  <td class="text-end">
                    <VBtn
                      variant="text"
                      color="default"
                      size="small"
                      @click="() => {
                        selectedBelugaEventId = item.id
                        isVisitEventDetailsDialogVisible = true
                      }"
                    >
                      <VIcon
                        size="22"
                        icon="tabler-eye"
                      />
                    </VBtn>
                  </td>
                </tr>
              </tbody>
            </VTable>
          </VCardText>
          <VCardText v-else>
            No Events logged
          </VCardText>
        </VCard>
      </VCol>
    </VRow>

    <!-- 👉 Visit Event Details Dialog -->
    <VisitEventDetailsDialog
      v-model:isDialogVisible="isVisitEventDetailsDialogVisible"
      :event-id="selectedBelugaEventId"
    />
  </div>
</template>


<style lang="scss">
.order-details {
  .text-size-14 {
    font-size: 14px;
  }
  .order-table {
    font-size: 14px;
    tr {
      border-bottom: thin solid rgba(var(--v-border-color), var(--v-border-opacity));

      &:last-child {
        border-bottom-width: 0;
      }

      td {
        padding-top: 0.5rem !important;
        padding-bottom: 0.5rem !important;

        &:first-child {
          padding-right: 0.5rem;
        }

        &:last-child {
          padding-left: 0.5rem;
          border-bottom: 0;
        }

        span {
          font-weight: 600;
        }
      }
    }
  }
  .product-table {
    font-size: 14px;

    .product {
      position: relative;
      display: flex;
      align-items: center;

      .product-image {
        max-width: 100px;
        width: 100%;
      }

      .product-details {
        margin: 0 0 0 20px;
        display: grid;

        span {
          &:not(:last-child) {
            margin-bottom: 8px;
          }
        }
      }
    }
  }
}
</style>
