<script setup>
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { processErrors } from '@/utils/errorHandler'
import { computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import router from '@/router'
import Image from 'primevue/image'
import { isEmpty } from '@/@core/utils'

const route = useRoute()
const { showSnackbar } = useGlobalData()

const logId = computed(() => route.params.logId)
const skeletonLoading = ref(true)
const documents = ref([])
const questionAnswers = ref([])

onMounted(async () => {
  await fetchMedicalQA()
})

async function fetchMedicalQA() {
  try {
    skeletonLoading.value = true

    const { data } = await ApiService.get(`/admin/get-order-question-answers/${logId.value}`)

    if (data.status === 200) {
      questionAnswers.value = data.questionAnswers ?? []
      documents.value = data.beluga_documents ?? []
    } else {
      showSnackbar(data.message, 'error')
      router.go(-1)
    }
  } catch (error) {
    console.error(error)
    showSnackbar(processErrors(error)[0], 'error')
    router.go(-1)
  } finally {
    skeletonLoading.value = false
  }
}
</script>

<template>
  <VRow>
    <VCol cols="12">
      <VCard title="Medical Question Answers">
        <VCardText>
          <VRow>
            <VCol
              v-if="skeletonLoading"
              cols="12"
              md="4"
            >
              <Skeleton
                height="200px"
                width="300px"
              ></Skeleton>
            </VCol>
            <VCol
              v-for="(document, idx) in documents"
              v-else-if="!isEmpty(documents)"
              :key="idx"
              cols="12"
              md="4"
            >
              <h5 class="mb-2">
                {{ document.title }}
              </h5>
              <Image
                :src="document.image"
                :height="200"
                preview
              />
            </VCol>
          </VRow>

          <div
            v-if="skeletonLoading"
            class="mt-5"
          >
            <Skeleton
              height="2rem"
              class="mb-3"
            ></Skeleton>
            <Skeleton
              height="2rem"
              class="mb-6"
            ></Skeleton>

            <Skeleton
              height="2rem"
              class="mb-3"
            ></Skeleton>
            <Skeleton
              height="2rem"
              class="mb-6"
            ></Skeleton>
          </div>

          <VList
            v-else
            lines="two"
            class="subtitle-fix"
          >
            <VListItem
              v-for="(item, index) in questionAnswers"
              :key="index"
            >
              <template #title>
                <VListItemTitle class="text-medium-emphasis mb-1">
                  Que. {{ index + 1 }}: {{ item.question }}
                </VListItemTitle>
              </template>
              <template #subtitle>
                <VListItemSubtitle class="text-body-1">
                  Ans: <span class="font-weight-bold text-high-emphasis">{{ item.answer }}</span>
                </VListItemSubtitle>
              </template>

              <VDivider
                v-if="index !== questionAnswers.length - 1"
                class="border-opacity-25 mt-4"
              />
            </VListItem>
          </VList>
        </VCardText>
      </VCard>
    </VCol>
  </VRow>
</template>

<style lang="scss">
.subtitle-fix {
  .v-list-item-subtitle {
    opacity: unset !important;
  }
  .v-list-item-title {
    overflow: initial;
    white-space: initial;
    text-overflow: initial;
  }
}
</style>
