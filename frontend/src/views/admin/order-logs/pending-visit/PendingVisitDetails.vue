<script setup>
import ApiService from '@/services/ApiService'
import { processErrors } from '@/utils/errorHandler'
import { onMounted, ref } from 'vue'
import { isEmpty } from '@/@core/utils'
import 'prismjs'
import 'prismjs/themes/prism.css'
import Prism from 'vue-prism-component'
import { useRoute } from 'vue-router'
import { capitalizeFirstChar } from '@/utils/helpers'
import { toast } from 'vue-sonner'

const route = useRoute()
const skeletonLoading = ref(true)
const category = ref(null)
const qaDetails = ref([])
const productDetails = ref({})
const shippingDetails = ref({})
const isGovtIdUploaded = ref(false)
const isSelfieUploaded = ref(false)
const isFullBodyUploaded = ref(false)
const isAllQuestionsAnswered = ref(false)
const visitUrl = ref(null)

const isProductSelectionCompleted = computed(() => {
  if (!isEmpty(productDetails.value)) {
    if (category.value?.toUpperCase() === 'ED') {
      if (productDetails.value?.frequency === 'as_needed') {
        return !isEmpty(productDetails.value?.product_name)
          && !isEmpty(productDetails.value?.strength)
          && !isEmpty(productDetails.value?.qty)
      } else {
        return !isEmpty(productDetails.value?.product_name)
          && !isEmpty(productDetails.value?.strength)
      }
    } else if (category.value?.toUpperCase() === 'HL') {
      return !isEmpty(productDetails.value?.product_name)
          && !isEmpty(productDetails.value?.strength)
    } else if (category.value?.toUpperCase() === 'WL') {
      return !isEmpty(productDetails.value?.product_name)
    }
  }

  return false
})

onMounted(async () => {
  await fetchDetails()
})

const fetchDetails = async () => {
  try {
    skeletonLoading.value = true

    const { data } = await ApiService.get(`/admin/get-pending-visit-qa-details/${route.params.id}`)

    if (data.status === 200) {
      category.value = data.category
      qaDetails.value = data.qa_details
      productDetails.value = data.product_details
      shippingDetails.value = data.shipping_details
      isGovtIdUploaded.value = Boolean(data.is_gov_id_uploaded)
      isSelfieUploaded.value = Boolean(data.is_selfie_uploaded)
      isFullBodyUploaded.value = Boolean(data.is_body_uploaded)
      isAllQuestionsAnswered.value = Boolean(data.is_all_question_answers_completed)
      visitUrl.value = data.visit_url
    } else {
      showSnackbar(data.message, 'error')
      emit('update:isDialogVisible', false)
    }
  } catch (error) {
    console.error(error)
    showSnackbar(processErrors(error)[0], 'error')
    emit('update:isDialogVisible', false)
  } finally {
    skeletonLoading.value = false
  }
}

function copyUrl() {
  navigator.clipboard.writeText(visitUrl.value)
  toast.success('URL copied to clipboard')
}
</script>

<template>
  <div>
    <VRow v-if="skeletonLoading">
      <VCol cols="12">
        <div
          class="d-flex justify-center align-center"
          style="height: calc(100vh - 14rem)"
        >
          <VProgressCircular
            indeterminate
            size="48"
          />
        </div>
      </VCol>
    </VRow>
    <VCard
      v-if="!skeletonLoading && visitUrl"
      title="Pending Visit URL"
      class="mb-6"
    >
      <VCardText>
        <div class="d-flex align-center gap-3">
          <AppTextField
            v-model="visitUrl"
            readonly
          />
          <VBtn
            variant="tonal"
            color="primary"
            @click="copyUrl"
          >
            <VIcon
              icon="tabler-copy"
              start
            /> Copy
          </VBtn>
        </div>
      </VCardText>
    </VCard>
    <VCard
      v-if="!skeletonLoading"
      variant="text"
      title="Pending Visit Progress"
    >
      <VCardText>
        <VTimeline
          align="start"
          justify="center"
          truncate-line="both"
          density="compact"
          class="v-timeline-icon-only"
        >
          <!-- SECTION Timeline Item: Question/Answers -->
          <VTimelineItem>
            <template #icon>
              <VIcon
                size="20"
                icon="tabler-report"
                color="primary"
              />
            </template>

            <VCard>
              <VCardTitle class="d-flex justify-space-between px-5 pt-5 pb-4">
                <h5 class="text-h5">
                  Treatment Questions
                </h5>
                <VChip
                  label
                  :color="isAllQuestionsAnswered ? 'success' : 'warning'"
                  :text="isAllQuestionsAnswered ? 'Completed' : 'Pending'"
                />
              </VCardTitle>
              <VCardText>
                <div
                  v-if="!isEmpty(qaDetails)"
                  style=""
                >
                  <prism
                    language="javascript"
                    class="rounded-sm prism-component-fix"
                  >
                    {{ qaDetails }}
                  </prism>
                </div>
                <div
                  v-else
                  class="text-center"
                >
                  <span class="text-disabled">No Data Available</span>
                </div>
              </VCardText>
            </VCard>
          </VTimelineItem>
          <!-- !SECTION -->

          <!-- SECTION Timeline Item: Product Details -->
          <VTimelineItem>
            <template #icon>
              <VIcon
                size="20"
                icon="tabler-shopping-cart"
                color="success"
              />
            </template>

            <VCard>
              <VCardTitle class="d-flex justify-space-between px-5 pt-5 pb-4">
                <h5 class="text-h5">
                  Product Details
                </h5>
                <VChip
                  label
                  :color="isProductSelectionCompleted ? 'success' : 'warning'"
                  :text="isProductSelectionCompleted ? 'Completed' : 'Pending'"
                />
              </VCardTitle>

              <VCardText v-if="productDetails">
                <div class="d-flex align-start flex-sm-row flex-column mb-3 gap-y-2">
                  <div>
                    <VImg
                      height="62"
                      width="62"
                      :src="productDetails.product_image"
                      class="rounded me-4"
                    />
                  </div>
                  <div>
                    <h6 class="text-base">
                      <span v-if="!isEmpty(productDetails.product_type)">
                        {{ capitalizeFirstChar(productDetails.product_type) }}&nbsp;
                      </span>
                      {{ productDetails.product_name }}
                    </h6>
                    <div class="mt-2 d-flex flex-wrap gap-2">
                      <VChip
                        v-if="productDetails.frequency"
                        label
                        size="small"
                        :text="productDetails.frequency?.replaceAll('_', ' ')?.toUpperCase()"
                      />
                      <VChip
                        v-if="productDetails.strength && productDetails.strength_unit"
                        label
                        size="small"
                        :text="`${productDetails.strength} ${productDetails.strength_unit}`"
                      />
                      <VChip
                        v-if="productDetails.qty"
                        label
                        size="small"
                        :text="`${productDetails.qty} units`"
                      />
                    </div>
                  </div>
                </div>
              </VCardText>
            </VCard>
          </VTimelineItem>
          <!-- !SECTION -->

          <!-- SECTION Timeline Item: Shipping Details -->
          <VTimelineItem>
            <template #icon>
              <VIcon
                size="20"
                icon="tabler-truck-delivery"
                color="warning"
              />
            </template>

            <VCard>
              <VCardTitle class="d-flex justify-space-between px-5 pt-5 pb-4">
                <h5 class="text-h5">
                  Shipping Details
                </h5>
                <VChip
                  label
                  :color="!isEmpty(shippingDetails) ? 'success' : 'warning'"
                  :text="!isEmpty(shippingDetails) ? 'Completed' : 'Pending'"
                />
              </VCardTitle>

              <VCardText v-if="shippingDetails">
                <div class="d-flex align-start flex-sm-row flex-column mb-3 gap-y-2">
                  <div>
                    <div
                      v-if="shippingDetails.title"
                      class="text-base mb-3 d-flex flex-column"
                    >
                      <span>Shipping Method:</span>
                      <strong>{{ shippingDetails.title === 'local_pickup' ? 'Local Pharmacy Pickup' : shippingDetails.title }}</strong>
                    </div>

                    <div v-if="shippingDetails.user_address_details">
                      <div>{{ shippingDetails.title === 'local_pickup' ? 'User' : 'Shipping' }} Address:</div>
                      <div class="text-medium-emphasis">
                        {{ shippingDetails.user_address_details?.address_line_1 }} <br>
                        <span v-if="shippingDetails.user_address_details?.address_line_2">
                          {{ shippingDetails.user_address_details?.address_line_2 }} <br>
                        </span>
                        {{ shippingDetails.user_address_details?.city }}, {{ shippingDetails.user_address_details?.state }}-{{ shippingDetails.user_address_details?.zipcode }} <br>
                        {{ shippingDetails.user_address_details?.country }}
                      </div>
                    </div>

                    <!-- Local Pharmacy Address -->
                    <div
                      v-if="shippingDetails.title === 'local_pickup' && shippingDetails.pharmacy_details"
                      class="mt-2 d-flex flex-wrap gap-2"
                    >
                      <div>
                        <div
                          v-if="shippingDetails.pharmacy_details?.StoreName"
                          class="text-high-emphasis font-weight-bold mb-1"
                        >
                          {{ shippingDetails.pharmacy_details?.StoreName }}
                        </div>
                        <div v-if="shippingDetails.pharmacy_details?.Address1">
                          {{ shippingDetails.pharmacy_details?.Address1 }}
                          <span v-if="shippingDetails.pharmacy_details?.Address2">
                            , {{ shippingDetails.pharmacy_details?.Address2 }}
                          </span>
                        </div>
                        <div v-if="shippingDetails.pharmacy_details?.City">
                          {{ shippingDetails.pharmacy_details?.City }}
                        </div>
                        <div>
                          <span v-if="shippingDetails.pharmacy_details?.State">{{ shippingDetails.pharmacy_details?.State }}</span>
                          <span v-if="shippingDetails.pharmacy_details?.ZipCode">-{{ shippingDetails.pharmacy_details?.ZipCode }} </span>
                        </div>
                        <div class="mt-2">
                          <div v-if="shippingDetails.pharmacy_details?.PharmacyId">
                            Pharmacy ID: {{ shippingDetails.pharmacy_details?.PharmacyId }}
                          </div>
                          <div v-if="shippingDetails.pharmacy_details?.PrimaryPhone">
                            Phone: {{ shippingDetails.pharmacy_details?.PrimaryPhone }}
                          </div>
                          <div v-if="shippingDetails.pharmacy_details?.PrimaryFax">
                            Fax: {{ shippingDetails.pharmacy_details?.PrimaryFax }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </VCardText>
            </VCard>
          </VTimelineItem>
          <!-- !SECTION -->

          <!-- SECTION Timeline Item: Govt Id -->
          <VTimelineItem>
            <template #icon>
              <VIcon
                size="20"
                icon="tabler-id"
                color="info"
              />
            </template>

            <VCard>
              <VCardTitle class="d-flex justify-space-between px-5 pt-5 pb-4">
                <h5 class="text-h5">
                  Government Id
                </h5>
                <VChip
                  label
                  :color="isGovtIdUploaded ? 'success' : 'warning'"
                  :text="isGovtIdUploaded ? 'Completed' : 'Pending'"
                />
              </VCardTitle>
            </VCard>
          </VTimelineItem>
          <!-- !SECTION -->

          <!-- SECTION Timeline Item: Selfie -->
          <VTimelineItem v-if="category?.toUpperCase() === 'ED' || category?.toUpperCase() === 'HL'">
            <template #icon>
              <VIcon
                size="20"
                icon="tabler-camera-selfie"
                color="info"
              />
            </template>

            <VCard>
              <VCardTitle class="d-flex justify-space-between px-5 pt-5 pb-4">
                <h5 class="text-h5">
                  User Selfie
                </h5>
                <VChip
                  label
                  :color="isSelfieUploaded ? 'success' : 'warning'"
                  :text="isSelfieUploaded ? 'Completed' : 'Pending'"
                />
              </VCardTitle>
            </VCard>
          </VTimelineItem>
          <!-- !SECTION -->

          <!-- SECTION Timeline Item: Full Body -->
          <VTimelineItem v-if="category?.toUpperCase() === 'WL'">
            <template #icon>
              <VIcon
                size="20"
                icon="tabler-body-scan"
                color="info"
              />
            </template>

            <VCard>
              <VCardTitle class="d-flex justify-space-between px-5 pt-5 pb-4">
                <h5 class="text-h5">
                  Full Body Image
                </h5>
                <VChip
                  label
                  :color="isFullBodyUploaded ? 'success' : 'warning'"
                  :text="isFullBodyUploaded ? 'Completed' : 'Pending'"
                />
              </VCardTitle>
            </VCard>
          </VTimelineItem>
          <!-- !SECTION -->

          <!-- SECTION Timeline Item: Checkout -->
          <VTimelineItem>
            <template #icon>
              <VIcon
                size="20"
                icon="tabler-credit-card"
                color="info"
              />
            </template>

            <VCard>
              <VCardTitle class="d-flex justify-space-between px-5 pt-5 pb-4">
                <h5 class="text-h5">
                  Checkout
                </h5>
                <VChip
                  label
                  color="warning"
                  text="Pending"
                />
              </VCardTitle>
            </VCard>
          </VTimelineItem>
          <!-- !SECTION -->
        </VTimeline>
      </VCardText>
    </VCard>
  </div>
</template>

<style lang="scss">
.prism-component-fix {
  max-height: 400px;
  white-space: pre-wrap;
  word-wrap: break-word;

  code {
    white-space: pre-wrap;
  }
}
</style>
