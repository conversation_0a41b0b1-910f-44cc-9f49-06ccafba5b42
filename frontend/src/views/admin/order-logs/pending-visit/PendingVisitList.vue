<script setup>
import { isEmpty } from '@/@core/utils'
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { processErrors } from '@/utils/errorHandler'
import { calculateStartIndex, formattedPhoneNumber, resolveInitials } from '@/utils/helpers'
import { refDebounced } from '@vueuse/core'
import { computed, onMounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const globalData = useGlobalData()
const { showSnackbar } = globalData
const route = useRoute()
const router = useRouter()
const searchQuery = ref(route.query.search ?? null)
const debouncedSearchQuery = refDebounced(searchQuery, 300)
const selectedCategory = ref(route.query.category ? JSON.parse(route.query.category) : [])
const dateRange = ref(route.query.from_date && route.query.to_date ? route.query.from_date + ' to ' + route.query.to_date : null)
const fromDate = ref(null)
const toDate = ref(null)
const filterCurrentStage = ref()
const rowPerPage = ref(route.query.per_page ?? 10)
const currentPage = ref(!isNaN(parseInt(route.query.page)) ? parseInt(route.query.page) : 1)
const totalPage = ref(1)
const totalItems = ref(0)
const items = ref([])
const sortByColumnName = ref(route.query.sort_by ?? 'created_at')
const sortDirection = ref(route.query.sort_dir ?? 'desc')
const skeletonLoading = ref(true)
const itemId = ref('')
const isDeleteDialogVisible = ref(false)
const deleteItemLoading = ref(false)

onMounted(async () => {
  fetchItems()
})

const updateRoute = () => {
  router.replace({
    query: {
      page: currentPage.value,
      per_page: rowPerPage.value,
      category: JSON.stringify(selectedCategory.value),
      search: searchQuery.value,
      sort_by: sortByColumnName.value,
      sort_dir: sortDirection.value,
      from_date: fromDate.value,
      to_date: toDate.value,
      current_stage: filterCurrentStage.value,
    },
  })
}

watch([debouncedSearchQuery, selectedCategory, rowPerPage, fromDate, toDate, filterCurrentStage], () => {
  currentPage.value = 1
  fetchItems()
})

watch([currentPage, sortByColumnName, sortDirection], () => {
  fetchItems()
})

const fetchItems = async () => {
  skeletonLoading.value = true

  try {
    const { data } = await ApiService.post('/admin/get-pending-visits', {
      searchQuery: searchQuery.value,
      perPage: rowPerPage.value,
      page: currentPage.value,
      sortByColumnName: sortByColumnName.value,
      isSortDirDesc: sortDirection.value,
      from_date: fromDate.value,
      to_date: toDate.value,
      category: selectedCategory.value,
      current_stage: filterCurrentStage.value,
    })

    if (data.status === 200) {
      const pagedData = data.visits

      if (isEmpty(pagedData.records) && currentPage.value !== 1) {
        currentPage.value = currentPage.value - 1

        return
      }

      let startIndex = calculateStartIndex(pagedData.current_page, pagedData.per_page)
      if (currentPage.value > 1) {
        startIndex = (currentPage.value - 1) * rowPerPage.value + 1
      }

      items.value = pagedData.records.map((d, index) => ({
        ...d,
        sno: index + startIndex,
      }))
      totalPage.value = pagedData.totalPage == 0 ? 1 : pagedData.totalPage
      totalItems.value = pagedData.totalRecords
      updateRoute()
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    console.error(error)
    if (error.response.data.message) {
      showSnackbar(error.response.data.message, 'error')
    } else if (error.response.message) {
      showSnackbar(error.response.message, 'error')
    }
  } finally {
    skeletonLoading.value = false
  }
}

const sortColumn = columnName => {
  if (sortByColumnName.value !== '' && sortByColumnName.value === columnName && sortDirection.value === 'asc') {
    sortDirection.value = 'desc'
  } else {
    sortDirection.value = 'asc'
  }

  sortByColumnName.value = columnName
}

const categoryFilterOptions = [
  { title: 'ED', value: 'ED' },
  { title: 'HL', value: 'HL' },
  { title: 'WL', value: 'WL' },
]

const currentStageFilterOptions = [
  { title: 'Questions', value: 'qa' },
  { title: 'Product', value: 'product' },
  { title: 'Shipping', value: 'shipping' },
  { title: 'Documents', value: 'document' },
  { title: 'Checkout', value: 'pre_checkout' },
]

const resolveCurrentStage = stage => {
  const stages = {
    qa: 'Questions',
    product: 'Product',
    shipping: 'Shipping',
    document: 'Documents',
    pre_checkout: 'Checkout',
  }

  return stages[stage] ?? '-'
}

const onDelete = id => {
  isDeleteDialogVisible.value = true
  itemId.value = id
}

async function handleDeleteConfirmation(isConfirmed) {
  if (isConfirmed) {
    await deleteItem()
  }
}

async function deleteItem() {
  deleteItemLoading.value = true

  try {
    const { data } = await ApiService.delete(`/admin/delete-pending-visit/${itemId.value}`)

    if (data.status) {
      await fetchItems()
      itemId.value = ''
      showSnackbar(data.message)
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    console.error(error)
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    deleteItemLoading.value = false
    isDeleteDialogVisible.value = false
  }
}

const paginationData = computed(() => {
  const currentPageValue = currentPage.value <= 0 ? 1 : currentPage.value

  const firstIndex = items.value.length
    ? (currentPageValue - 1) * rowPerPage.value + 1
    : 0

  const lastIndex = items.value.length + (currentPageValue - 1) * rowPerPage.value

  return `Showing ${firstIndex} to ${lastIndex} of ${totalItems.value} entries`
})
</script>

<template>
  <section>
    <VRow class="match-height">
      <VCol cols="12">
        <VCard title="Filter">
          <!-- 👉 Filters -->
          <VCardText>
            <VRow>
              <VCol
                cols="12"
                sm="4"
              >
                <AppSelect
                  v-model="filterCurrentStage"
                  label="Current Stage"
                  :items="currentStageFilterOptions"
                  clearable
                  clear-icon="tabler-x"
                  placeholder="Select"
                />
              </VCol>
              <VCol
                cols="12"
                sm="4"
              >
                <AppSelect
                  v-model="selectedCategory"
                  label="Category"
                  :items="categoryFilterOptions"
                  clearable
                  multiple
                  chips
                  clear-icon="tabler-x"
                  placeholder="Select"
                />
              </VCol>
              <VCol
                cols="12"
                sm="4"
              >
                <AppDateTimePicker
                  v-model="dateRange"
                  label="Created Date"
                  placeholder="Select Date Range"
                  variant="outlined"
                  :config="{
                    mode: 'range',
                    dateFormat: 'm/d/Y',
                    onClose: function (selectedDates, dateStr, instance) {
                      if (selectedDates.length === 1) {
                        instance.setDate([selectedDates[0], selectedDates[0]], true)
                      }
                    },
                    maxDate: new Date(),
                    minDate: (new Date()).setFullYear((new Date()).getFullYear() - 120),
                  }"
                  clearable
                  clear-icon="tabler-x"
                />
              </VCol>
            </VRow>
          </VCardText>

          <VDivider />

          <VCardText class="d-flex flex-wrap py-4 gap-4">
            <div
              class="me-3"
              style="width: 80px"
            >
              <VSelect
                v-model="rowPerPage"
                density="compact"
                variant="outlined"
                :items="[10, 20, 30, 50]"
              />
            </div>

            <VSpacer />

            <div class="d-flex align-center flex-wrap gap-4">
              <!-- 👉 Search  -->
              <div style="width: 23rem">
                <VTextField
                  v-model="searchQuery"
                  placeholder="Search"
                  density="compact"
                  clearable
                  clear-icon="tabler-x"
                />
              </div>
            </div>
          </VCardText>

          <VDivider />

          <VTable class="text-no-wrap">
            <!-- 👉 table head -->
            <thead>
              <tr>
                <th scope="col">
                  #
                </th>
                <th scope="col">
                  USER
                </th>
                <th scope="col">
                  CATEGORY
                </th>
                <th scope="col">
                  Current Stage
                </th>
                <th
                  scope="col"
                  class="cursor-pointer"
                  @click="sortColumn('created_at')"
                >
                  <div class="w-125px">
                    CREATED AT
                    <span class="d-grid float-right">
                      <VIcon
                        size="10"
                        icon="tabler-chevron-up"
                      />
                      <VIcon
                        size="10"
                        icon="tabler-chevron-down"
                      />
                    </span>
                  </div>
                </th>
                <th
                  scope="col"
                  class="cursor-pointer"
                  @click="sortColumn('updated_at')"
                >
                  <div class="w-125px">
                    UPDATED AT
                    <span class="d-grid float-right">
                      <VIcon
                        size="10"
                        icon="tabler-chevron-up"
                      />
                      <VIcon
                        size="10"
                        icon="tabler-chevron-down"
                      />
                    </span>
                  </div>
                </th>
                <th scope="col">
                  ACTIONS
                </th>
              </tr>
            </thead>

            <!-- 👉 table body -->
            <tbody v-if="skeletonLoading">
              <tr
                v-for="n in 10"
                :key="n"
              >
                <td
                  v-for="i in 7"
                  :key="i"
                >
                  <Skeleton
                    width="8rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
              </tr>
            </tbody>
            <tbody v-else>
              <tr
                v-for="item in items"
                :key="item.id"
                style="height: 3.75rem"
              >
                <!-- 👉 NO -->
                <td>
                  {{ item.sno }}
                </td>

                <!-- 👉 User -->
                <td class="title">
                  <div class="d-flex align-center py-1">
                    <VAvatar
                      v-if="item.selfie"
                      variant="tonal"
                      size="40"
                      class="me-3"
                      :image="item.selfie"
                      rounded
                    />
                    <VAvatar
                      v-else
                      variant="tonal"
                      size="40"
                      class="me-3"
                      rounded
                    >
                      {{ resolveInitials(`${item.first_name} ${item.last_name}`) }}
                    </VAvatar>
                    <div>
                      <h6 class="text-base">
                        {{ `${item.first_name} ${item.last_name}` }}
                      </h6>
                      <div class="d-flex flex-column">
                        <span class="text-body-2">{{ item.email }}</span>
                        <span class="text-body-2">{{ formattedPhoneNumber(item.phone_number) }}</span>
                      </div>
                    </div>
                  </div>
                </td>

                <!-- 👉 Categories -->
                <td>
                  {{ item.category }}
                </td>

                <!-- 👉 Current Stage -->
                <td>
                  {{ resolveCurrentStage(item.current_stage) }}
                </td>

                <!-- 👉 Created At -->
                <td>
                  <div
                    v-if="item.created_date && item.created_time"
                    class="d-flex flex-column"
                  >
                    <span>{{ item.created_date }}</span>
                    <span>{{ item.created_time }}</span>
                  </div>
                  <div v-else>
                    -
                  </div>
                </td>

                <!-- 👉 Updated At -->
                <td>
                  <div
                    v-if="item.updated_date !== item.created_date || item.updated_time !== item.created_time"
                    class="d-flex flex-column"
                  >
                    <span>{{ item.updated_date }}</span>
                    <span>{{ item.updated_time }}</span>
                  </div>
                  <div v-else>
                    -
                  </div>
                </td>

                <!-- 👉 Actions -->
                <td
                  class="text-center"
                  style="width: 5rem"
                >
                  <VBtn
                    v-tooltip.top="'View Details'"
                    variant="text"
                    color="default"
                    size="x-small"
                    :to="{
                      name: 'admin-pending-visit-details',
                      params: { id: item.id },
                      query: { ...route.query },
                    }"
                    icon
                  >
                    <VIcon
                      size="22"
                      icon="tabler-eye"
                    />
                  </VBtn>
                  <VBtn
                    icon
                    size="x-small"
                    color="default"
                    variant="text"
                    @click="onDelete(item.id)"
                  >
                    <VIcon
                      size="22"
                      icon="tabler-trash"
                    />
                  </VBtn>
                </td>
              </tr>
            </tbody>

            <!-- 👉 table footer  -->
            <tfoot v-show="!skeletonLoading && isEmpty(items)">
              <tr>
                <td
                  colspan="10"
                  class="text-center"
                >
                  No data available
                </td>
              </tr>
            </tfoot>
          </VTable>

          <VDivider />

          <VCardText class="d-flex align-center flex-wrap justify-space-between gap-4 py-3 px-5">
            <span class="text-sm text-disabled">
              {{ paginationData }}
            </span>
            <VPagination
              v-model="currentPage"
              size="small"
              :total-visible="5"
              :length="totalPage"
            />
          </VCardText>
        </VCard>
      </VCol>
    </VRow>

    <!-- 👉 Delete confirm dialog -->
    <ConfirmDialog
      v-model:isDialogVisible="isDeleteDialogVisible"
      confirmation-question="Are you sure you want to delete this pending visit?"
      :loading="deleteItemLoading"
      @confirm="handleDeleteConfirmation"
    />
  </section>
</template>

<style lang="scss">
.v-table__wrapper td.title {
  min-width: 200px;
  h6 {
    a {
      white-space: pre-line;
    }
  }
}
</style>
