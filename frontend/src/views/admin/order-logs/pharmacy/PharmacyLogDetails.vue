<script setup>
import { isEmpty } from '@/@core/utils'
import router from '@/router'
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { getShippingActivityColor } from '@/utils/admin'
import { processErrors } from '@/utils/errorHandler'
import { formattedPhoneNumber, resolveInitials } from '@/utils/helpers'
import { computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'

const { showSnackbar } = useGlobalData()
const route = useRoute()
const logId = computed(() => route.params.logId)
const skeletonLoading = ref(true)
const order = ref({})
const isUpdateShippingDrawerVisible = ref(false)
const isUpdateOrderNoteDrawerVisible = ref(false)
const isPharmacyEventDetailsDialogVisible = ref(false)
const selectedGogoEventId = ref('')
const isEditShippingStatusDialogVisible = ref(false)
const isEditAffiliateNumberDialogVisible = ref(false)
const fetchFromGogoLoading = ref(false)

onMounted(async () => {
  await fetchLogDetails()
})

async function fetchLogDetails() {
  try {
    skeletonLoading.value = true

    const { data } = await ApiService.get(`/admin/manage-pharmacy-order-logs/${logId.value}`)

    if (data.status === 200) {
      order.value = data.pharmacyDetails
    } else {
      showSnackbar(data.message, 'error')
      router.push({ name: 'admin-pharmacy-logs' })
    }
  } catch (error) {
    console.error(error)
    showSnackbar(processErrors(error)[0], 'error')
    router.push({ name: 'admin-pharmacy-logs' })
  } finally {
    skeletonLoading.value = false
  }
}

const orderDetailsLink = computed(() => {
  const visitType = String(order.value?.order_details?.category_name).toLowerCase()

  return {
    name: `admin-${visitType}-order-details`,
    params: { orderId: order.value?.order_details?.refill_id },
  }
})

async function fetchFromGogo() {
  try {
    fetchFromGogoLoading.value = true

    const { data } = await ApiService.get(`/admin/fetch-pharmacy-affiliate-order-details/${order.value?.order_details?.refill_id}`)

    if (data.status === 200) {
      fetchLogDetails()
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    console.error(error)
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    fetchFromGogoLoading.value = false
  }
}
</script>

<template>
  <div v-if="skeletonLoading">
    <div
      class="d-flex justify-center align-center"
      style="height: calc(100vh - 14rem)"
    >
      <VProgressCircular
        indeterminate
        size="48"
      />
    </div>
  </div>
  <div
    v-else
    class="order-details"
  >
    <div
      v-if="Boolean(order.is_show_retrieve_order_details_btn)"
      class="d-flex justify-end align-center flex-wrap gap-y-4 mb-6"
    >
      <VBtn
        variant="tonal"
        :loading="fetchFromGogoLoading"
        @click="fetchFromGogo"
      >
        Fetch from Pharmacy
      </VBtn>
    </div>

    <VRow class="match-height">
      <VCol
        cols="12"
        md="6"
      >
        <VCard
          title="Order Details"
          class="h-100"
        >
          <VCardText>
            <VTable class="text-no-wrap order-table">
              <tr>
                <td>Order No</td>
                <td class="text-right">
                  <RouterLink
                    v-if="order.order_details?.refill_id && orderDetailsLink"
                    class="text-link"
                    :to="orderDetailsLink"
                    style="font-weight: 600;"
                  >
                    {{ order.order_details?.order_no }}
                  </RouterLink>
                  <span v-else> {{ order.order_details?.order_no ?? "-" }} </span>
                </td>
              </tr>
              <tr>
                <td>Affiliate Order No</td>
                <td class="text-right">
                  <div>{{ order.order_details?.pharmacy_order_no ?? "-" }}</div>
                  <button
                    v-if="Boolean(order.is_show_update_affiliate_order_btn)"
                    class="text-link mt-1"
                    @click="isEditAffiliateNumberDialogVisible = true"
                  >
                    Update Affiliate Order No
                  </button>
                </td>
              </tr>
              <tr>
                <td>Order Status</td>
                <td class="text-right">
                  <div class="status">
                    <VChip
                      label
                      :color="order.order_details?.status_text_color"
                      size="small"
                      class="text-capitalize order-status"
                    >
                      {{ order.order_details?.status_text }}
                    </VChip>
                  </div>
                  <button
                    class="text-link mt-1"
                    @click="isEditShippingStatusDialogVisible = true"
                  >
                    Change Status
                  </button>
                </td>
              </tr>
              <tr>
                <td>Order Type</td>
                <td class="text-right">
                  <span v-if="!isEmpty(order.order_details?.order_type) && !isEmpty(order.order_details?.refill_number)">
                    {{
                      order.order_details?.refill_number === 0
                        ? order.order_details?.order_type
                        : `${order.order_details?.order_type} ${order.order_details?.refill_number}`
                    }}
                  </span>
                  <span v-else> - </span>
                </td>
              </tr>
              <tr>
                <td>Visit Type</td>
                <td class="text-right">
                  <span>{{ order.order_details?.category_name ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>Order created at</td>
                <td class="text-right">
                  <span>{{ order.order_details?.created_date ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>Last updated at</td>
                <td class="text-right">
                  <span>{{ order.order_details?.updated_date ?? "-" }}</span>
                </td>
              </tr>
            </VTable>
          </VCardText>
        </VCard>
      </VCol>

      <VCol
        cols="12"
        md="6"
      >
        <VCard
          title="Customer Details"
          class="h-100"
        >
          <VCardText>
            <VTable class="text-no-wrap order-table">
              <tr>
                <td>Affiliate Customer Number</td>
                <td class="text-right">
                  <span>{{ order.customer_details?.affiliate_contact_no ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>First Name</td>
                <td class="text-right">
                  <span>{{ order.customer_details?.first_name ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>Last Name</td>
                <td class="text-right">
                  <span>{{ order.customer_details?.last_name ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>Gender</td>
                <td class="text-right">
                  <span>{{ order.customer_details?.gender ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>DOB</td>
                <td class="text-right">
                  <span>{{ order.customer_details?.dob ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>Email</td>
                <td class="text-right">
                  <span>{{ order.customer_details?.email ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>Phone</td>
                <td class="text-right">
                  <span>{{ formattedPhoneNumber(order.customer_details?.mobile_no) ?? "-" }}</span>
                </td>
              </tr>
            </VTable>
          </VCardText>
        </VCard>
      </VCol>

      <VCol
        cols="12"
        md="6"
      >
        <VCard
          title="Medical Conditions"
          class="h-100"
        >
          <VCardText>
            <div class="mb-3">
              <div class="text-body-1 text-medium-emphasis">
                Self Reported Medications
              </div>
              <Skeleton
                v-if="skeletonLoading"
                height="1rem"
              ></Skeleton>
              <div
                v-else
                class="text-body-2 text-high-emphasis"
              >
                {{
                  !isEmpty(order.medical_details?.current_medication) ? order.medical_details?.current_medication : "-"
                }}
              </div>
            </div>

            <div class="mb-3">
              <div class="text-body-1 text-medium-emphasis">
                Allergies
              </div>
              <Skeleton
                v-if="skeletonLoading"
                height="1rem"
              ></Skeleton>
              <div
                v-else
                class="text-body-2 text-high-emphasis"
              >
                {{ !isEmpty(order.medical_details?.allergies) ? order.medical_details?.allergies : "-" }}
              </div>
            </div>

            <div class="mb-3">
              <div class="text-body-1 text-medium-emphasis">
                Medical History
              </div>
              <Skeleton
                v-if="skeletonLoading"
                height="1rem"
              ></Skeleton>
              <div
                v-else
                class="text-body-2 text-high-emphasis"
              >
                {{
                  !isEmpty(order.medical_details?.medical_condition) ? order.medical_details?.medical_condition : "-"
                }}
              </div>
            </div>
          </VCardText>
        </VCard>
      </VCol>

      <VCol
        cols="12"
        md="6"
      >
        <VCard class="h-100">
          <VCardItem>
            <VCardTitle> Shipping Address </VCardTitle>
            <template #append>
              <VBtn
                v-if="order.order_details?.is_update_pharmacy_shipping_address === 1"
                variant="tonal"
                size="small"
                @click="isUpdateShippingDrawerVisible = true"
              >
                Update
              </VBtn>
            </template>
          </VCardItem>
          <VCardText>
            <VTable class="text-no-wrap order-table">
              <tr>
                <td>Address</td>
                <td class="text-right">
                  <span class="d-inline-block max-w-250px text-wrap word-break-all">
                    {{ order.shipping_address?.address ?? "-" }}
                  </span>
                </td>
              </tr>
              <tr>
                <td>City</td>
                <td class="text-right">
                  <span>{{ order.shipping_address?.city ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>State / Territory</td>
                <td class="text-right">
                  <span>{{ order.shipping_address?.state ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>Zipcode</td>
                <td class="text-right">
                  <span>{{ order.shipping_address?.zip_code ?? "-" }}</span>
                </td>
              </tr>
            </VTable>
          </VCardText>
        </VCard>
      </VCol>

      <VCol
        cols="12"
        class="expansion-fix"
      >
        <VCard title="Drugs">
          <VCardText
            v-if="order.order_details?.information_received_by_pharmacy === 0 && !isEmpty(order.drug_details)"
            class="pb-0"
          >
            <div class="d-flex align-center py-1">
              <VAvatar
                v-if="order.drug_details.product_img"
                variant="tonal"
                size="40"
                class="me-3"
                :image="order.drug_details.product_img"
                rounded
              />
              <VAvatar
                v-else
                variant="tonal"
                size="40"
                class="me-3"
                rounded
              >
                {{ resolveInitials(order.drug_details.product_name) }}
              </VAvatar>
              <div>
                <h6 class="text-base">
                  {{ order.drug_details.product_name }}
                </h6>
                <div
                  v-if="order.drug_details?.drug_name"
                  class="text-body-2"
                >
                  {{ order.drug_details.drug_name }} <span v-if="order.drug_details?.provider_qty">({{ order.drug_details.provider_qty }})</span>
                </div>
                <div class="d-flex flex-column">
                  <span
                    v-if="order.order_details?.category_name?.toUpperCase() === 'WL'"
                    class="d-block text-body-2"
                  >
                    {{ order.drug_details?.order_type }} ({{ order.drug_details.strength }} {{ order.drug_details.strength_unit }})
                  </span>
                  <span
                    v-else
                    class="d-block text-body-2"
                  >
                    <span class="d-block">
                      {{ order.drug_details.strength }} {{ order.drug_details.strength_unit }} x {{ order.drug_details.qty * order.drug_details.subscription_interval }} units
                    </span>
                    <span class="d-block">
                      ({{ order.drug_details.subscription_interval * 30 }}-day supply)
                    </span>
                  </span>
                </div>
              </div>
            </div>
          </VCardText>
          <VCardText>
            <VExpansionPanels multiple>
              <VExpansionPanel
                v-for="(item, index) in order.drugs"
                :key="index"
                class="border-md border-dashed"
              >
                <VExpansionPanelTitle> {{ item.full_medicine_name }} </VExpansionPanelTitle>
                <VExpansionPanelText>
                  <VRow class="py-3">
                    <VCol
                      cols="12"
                      md="6"
                      class="table-card border-sm border-dashed"
                    >
                      <h6 class="text-h6">
                        Drug
                      </h6>
                      <VTable class="text-no-wrap order-table">
                        <tr>
                          <td>Medicine Name</td>
                          <td class="text-right">
                            <Skeleton
                              v-if="skeletonLoading"
                              height="1rem"
                            ></Skeleton>
                            <span
                              v-else
                              class="d-inline-block max-w-250px text-wrap word-break-all"
                            >
                              {{ item.medicine_name ?? "-" }}
                            </span>
                          </td>
                        </tr>
                        <tr>
                          <td>NDC</td>
                          <td class="text-right">
                            <Skeleton
                              v-if="skeletonLoading"
                              height="1rem"
                            ></Skeleton>
                            <span v-else>{{ item.ndc ?? "-" }}</span>
                          </td>
                        </tr>
                        <tr>
                          <td>Dose</td>
                          <td class="text-right">
                            <Skeleton
                              v-if="skeletonLoading"
                              height="1rem"
                            ></Skeleton>
                            <span
                              v-else
                              class="d-inline-block max-w-250px text-wrap word-break-all"
                            >
                              {{ item.dose ?? "-" }}
                            </span>
                          </td>
                        </tr>
                        <tr>
                          <td>Form</td>
                          <td class="text-right">
                            <Skeleton
                              v-if="skeletonLoading"
                              height="1rem"
                            ></Skeleton>
                            <span
                              v-else
                              class="d-inline-block max-w-250px text-wrap word-break-all"
                            >
                              {{ item.form ?? "-" }}
                            </span>
                          </td>
                        </tr>
                        <tr>
                          <td>Packaging</td>
                          <td class="text-right">
                            <Skeleton
                              v-if="skeletonLoading"
                              height="1rem"
                            ></Skeleton>
                            <span
                              v-else
                              class="d-inline-block max-w-250px text-wrap word-break-all"
                            >
                              {{ item.packaging ?? "-" }}
                            </span>
                          </td>
                        </tr>
                        <tr>
                          <td>Quantity</td>
                          <td class="text-right">
                            <Skeleton
                              v-if="skeletonLoading"
                              height="1rem"
                            ></Skeleton>
                            <span
                              v-else
                              class="d-inline-block max-w-250px text-wrap word-break-all"
                            >
                              {{ item.qty ?? "-" }}
                            </span>
                          </td>
                        </tr>
                        <tr>
                          <td>Refill Script Number</td>
                          <td class="text-right">
                            <Skeleton
                              v-if="skeletonLoading"
                              height="1rem"
                            ></Skeleton>
                            <span
                              v-else
                              class="d-inline-block max-w-250px text-wrap word-break-all"
                            >
                              {{ item.refill_script_number ?? "-" }}
                            </span>
                          </td>
                        </tr>
                      </VTable>
                    </VCol>
                    <VCol
                      cols="12"
                      md="6"
                      class="table-card border-sm border-dashed"
                    >
                      <h6 class="text-h6">
                        Prescription
                      </h6>
                      <VTable class="text-no-wrap order-table">
                        <tr>
                          <td>Written Date</td>
                          <td class="text-right">
                            <Skeleton
                              v-if="skeletonLoading"
                              height="1rem"
                            ></Skeleton>
                            <span v-else>{{ item.prescription?.written_date ?? "-" }}</span>
                          </td>
                        </tr>
                        <tr>
                          <td>Written Quantity</td>
                          <td class="text-right">
                            <Skeleton
                              v-if="skeletonLoading"
                              height="1rem"
                            ></Skeleton>
                            <span v-else>{{ item.prescription?.written_qty ?? "-" }}</span>
                          </td>
                        </tr>
                        <tr>
                          <td>Script No</td>
                          <td class="text-right">
                            <Skeleton
                              v-if="skeletonLoading"
                              height="1rem"
                            ></Skeleton>
                            <span v-else>{{ item.prescription?.script_no ?? "-" }}</span>
                          </td>
                        </tr>
                        <tr>
                          <td
                            colspan="2"
                            class="px-0"
                          >
                            <p class="text-body-1">
                              Dispense History
                            </p>
                            <VTable
                              title="Dispense History"
                              class="text-no-wrap order-table"
                            >
                              <thead>
                                <tr>
                                  <th>Dispense Quantity</th>
                                  <th class="text-right">
                                    Dispense Date
                                  </th>
                                </tr>
                              </thead>

                              <tbody v-if="!isEmpty(item.prescription?.dispense_history)">
                                <tr
                                  v-for="(his, key) in item.prescription?.dispense_history"
                                  :key="key"
                                >
                                  <td class="text-body-2">
                                    <Skeleton
                                      v-if="skeletonLoading"
                                      height="1rem"
                                    ></Skeleton>
                                    <span v-else>{{ his.dispense_qty }}</span>
                                  </td>
                                  <td class="text-right text-body-2">
                                    <Skeleton
                                      v-if="skeletonLoading"
                                      height="1rem"
                                    ></Skeleton>
                                    <span v-else>{{ his.dispense_date }}</span>
                                  </td>
                                </tr>
                              </tbody>

                              <tbody v-else>
                                <tr class="text-center">
                                  <td colspan="2">
                                    No dispense history
                                  </td>
                                </tr>
                              </tbody>
                            </VTable>
                          </td>
                        </tr>
                      </VTable>
                    </VCol>
                    <VCol
                      cols="12"
                      md="6"
                      class="table-card border-sm border-dashed"
                    >
                      <h6 class="text-h6">
                        Prescriber
                      </h6>
                      <VTable class="text-no-wrap order-table">
                        <tr>
                          <td>First Name</td>
                          <td class="text-right">
                            <Skeleton
                              v-if="skeletonLoading"
                              height="1rem"
                            ></Skeleton>
                            <span v-else>{{ item.prescriber?.first_name ?? "-" }}</span>
                          </td>
                        </tr>
                        <tr>
                          <td>Last Name</td>
                          <td class="text-right">
                            <Skeleton
                              v-if="skeletonLoading"
                              height="1rem"
                            ></Skeleton>
                            <span v-else>{{ item.prescriber?.last_name ?? "-" }}</span>
                          </td>
                        </tr>
                        <tr>
                          <td>Address</td>
                          <td class="text-right">
                            <Skeleton
                              v-if="skeletonLoading"
                              height="1rem"
                            ></Skeleton>
                            <span
                              v-else-if="item.prescriber?.address"
                              class="d-inline-block max-w-250px text-wrap word-break-all"
                            >
                              {{ item.prescriber?.address?.Line1 }},
                              <span v-if="item.prescriber?.address?.Line2">
                                {{ item.prescriber?.address?.Line2 }}
                              </span>
                              {{ item.prescriber?.address?.City }}, {{ item.prescriber?.address?.State }} -
                              {{ item.prescriber?.address?.Zip }}
                            </span>
                            <span v-else>-</span>
                          </td>
                        </tr>
                        <tr>
                          <td>FAX No</td>
                          <td class="text-right">
                            <Skeleton
                              v-if="skeletonLoading"
                              height="1rem"
                            ></Skeleton>
                            <span v-else>{{ item.prescriber?.fax_number ?? "-" }}</span>
                          </td>
                        </tr>
                        <tr>
                          <td>NPI</td>
                          <td class="text-right">
                            <Skeleton
                              v-if="skeletonLoading"
                              height="1rem"
                            ></Skeleton>
                            <span v-else>{{ item.prescriber?.npi ?? "-" }}</span>
                          </td>
                        </tr>
                      </VTable>
                    </VCol>
                    <VCol
                      cols="12"
                      md="6"
                      class="table-card border-sm border-dashed p-3"
                    >
                      <h6 class="text-h6 mb-3">
                        Order Item Status
                      </h6>
                      <div class="overflow-y-auto">
                        <VTimeline
                          v-if="!isEmpty(item.order_item_status)"
                          side="end"
                          align="start"
                          line-inset="8"
                          truncate-line="both"
                          density="compact"
                        >
                          <VTimelineItem
                            v-for="(statusItem, i) in item.order_item_status"
                            :key="i"
                            dot-color="primary"
                            size="x-small"
                          >
                            <!-- 👉 Header -->
                            <div class="d-flex justify-space-between align-center gap-2 flex-wrap">
                              <span class="app-timeline-title">
                                <VChip
                                  label
                                  :color="statusItem.status_name?.toLowerCase()?.includes('cancelled') ? 'error' : 'primary'"
                                  size="small"
                                  class="text-capitalize mb-1"
                                >{{
                                  statusItem.status_name
                                }}</VChip>
                              </span>
                              <span class="app-timeline-meta text-body-2">{{ statusItem.status_set_date }}</span>
                            </div>
                            <!-- 👉 Content -->
                            <div class="app-timeline-text mb-1 text-subtitle-2">
                              <span>{{ statusItem.status_set_by }}</span>
                            </div>
                          </VTimelineItem>
                        </VTimeline>
                        <span v-else>No data available</span>
                      </div>
                    </VCol>
                  </VRow>
                </VExpansionPanelText>
              </VExpansionPanel>
            </VExpansionPanels>
          </VCardText>
        </VCard>
      </VCol>

      <VCol
        cols="12"
        md="6"
      >
        <VCard
          title="Shipment Details"
          class="h-100"
        >
          <VCardText>
            <VTable class="text-no-wrap order-table">
              <tr>
                <td>Tracking URL</td>
                <td class="text-right">
                  <a
                    v-if="!isEmpty(order.shipment_details?.tracking_url)"
                    :href="order.shipment_details?.tracking_url"
                    target="_blank"
                    rel="noopener noreferrer"
                    class="text-wrap word-break-all font-weight-bold text-primary text-link-admin"
                  >
                    Track Order
                  </a>
                  <span v-else>-</span>
                </td>
              </tr>
              <tr>
                <td>Shipment Method</td>
                <td class="text-right">
                  <span>{{ order.shipment_details?.shipment_method ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>Tracking Number</td>
                <td class="text-right">
                  <span>{{ order.shipment_details?.tracking_number ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>Delivery Provider</td>
                <td class="text-right">
                  <span>{{ order.shipment_details?.delivery_provider ?? "-" }}</span>
                </td>
              </tr>
            </VTable>
          </VCardText>
        </VCard>
      </VCol>

      <VCol
        cols="12"
        md="6"
      >
        <VCard title="Order Status History">
          <VCardText>
            <VTimeline
              v-if="!isEmpty(order.order_status_history)"
              side="end"
              align="start"
              line-inset="8"
              truncate-line="both"
              density="compact"
            >
              <VTimelineItem
                v-for="(item, index) in order.order_status_history"
                :key="index"
                dot-color="primary"
                size="x-small"
              >
                <!-- 👉 Header -->
                <div class="d-flex justify-space-between align-center gap-2 flex-wrap">
                  <span class="app-timeline-title">
                    <VChip
                      label
                      :color="item.status === 'Cancelled' ? 'error' : 'primary'"
                      size="small"
                      class="text-capitalize mb-1"
                    >{{ item.status }}</VChip>
                  </span>
                  <span class="app-timeline-meta text-body-2">{{ item.status_set_date }}</span>
                </div>

                <!-- 👉 Content -->
                <div class="app-timeline-text mb-1 text-subtitle-2">
                  <span>{{ item.status_set_by }}</span>
                </div>
              </VTimelineItem>
            </VTimeline>
            <span v-else>No data available</span>
          </VCardText>
        </VCard>
      </VCol>

      <VCol
        cols="12"
        md="6"
      >
        <VCard title="Order Shipment Details">
          <VCardText
            class="overflow-y-auto"
            style="max-height: 400px"
          >
            <VTimeline
              v-if="!isEmpty(order.order_ship_details)"
              side="end"
              align="start"
              line-inset="8"
              truncate-line="both"
              density="compact"
            >
              <VTimelineItem
                v-for="(item, index) in order.order_ship_details"
                :key="index"
                dot-color="primary"
                size="x-small"
              >
                <!-- 👉 Header -->
                <div class="d-flex justify-space-between align-center gap-2 flex-wrap">
                  <span class="app-timeline-title">
                    <VChip
                      label
                      :color="getShippingActivityColor(item.EventStatus)"
                      size="small"
                      class="text-capitalize mb-1"
                    >
                      {{ item.EventStatusText }}
                    </VChip>
                  </span>
                  <span class="app-timeline-meta text-body-2">{{ item.EventDate }}</span>
                </div>

                <!-- 👉 Content -->
                <div
                  v-if="!isEmpty(item.EventDescription)"
                  class="app-timeline-text mb-1 text-subtitle-2"
                >
                  <span>{{ item.EventDescription }}</span>
                </div>

                <p
                  v-if="!isEmpty(item.EventLocation)"
                  class="app-timeline-meta mb-2 text-caption font-italic text-secondary"
                >
                  {{ item.EventLocation }}
                </p>
                <p
                  v-if="!isEmpty(item.ActionByUserName)"
                  class="app-timeline-meta mb-2 text-caption font-italic text-secondary"
                >
                  Action By: {{ item.ActionByUserName }}
                  <span v-if="!isEmpty(item.ActionByUserRole)">
                    {{ '(' + item.ActionByUserRole + ')' }}
                  </span>
                </p>
              </VTimelineItem>
            </VTimeline>
            <span v-else>No data available</span>
          </VCardText>
        </VCard>
      </VCol>

      <VCol
        cols="12"
        md="6"
      >
        <VCard>
          <VCardItem>
            <VCardTitle> Order notes </VCardTitle>
            <template #append>
              <VBtn
                v-if="order.order_details?.is_update_pharmacy_note === 1"
                variant="tonal"
                size="small"
                @click="isUpdateOrderNoteDrawerVisible = true"
              >
                Add
              </VBtn>
            </template>
          </VCardItem>
          <VCardText
            class="overflow-y-auto"
            style="max-height: 400px"
          >
            <div v-if="!isEmpty(order.order_notes)">
              <div
                v-for="(note, index) in order.order_notes"
                :key="index"
              >
                <div class="note-item mb-3">
                  <div class="text-body-1 mb-2">
                    {{ note.note }}
                  </div>
                  <div class="d-flex flex-column text-xs text-secondary text-caption">
                    <span>Added by: {{ note.created_by }}</span>
                    <span>Date added: {{ note.created_date }}</span>
                  </div>
                </div>
                <VDivider
                  v-if="index !== order.order_notes.length - 1"
                  thickness="2"
                  class="mb-2"
                />
              </div>
            </div>
            <span v-else>No order notes</span>
          </VCardText>
        </VCard>
      </VCol>

      <VCol cols="12">
        <VCard title="Events Log">
          <VCardText>
            <VTable class="text-no-wrap order-table product-table">
              <thead>
                <tr class="text-start fw-bolder text-uppercase">
                  <th>Order #</th>
                  <th>Sender</th>
                  <th>Receiver</th>
                  <th>Status Code</th>
                  <th>Created At</th>
                  <th>Updated At</th>
                  <th class="text-end">
                    Action
                  </th>
                </tr>
              </thead>
              <tbody>
                <!-- status code -->
                <tr
                  v-for="(item, index) in order.event_logs"
                  :key="index"
                >
                  <!-- order # -->
                  <td>{{ item.order_no ?? "-" }}</td>
                  <!-- sender -->
                  <td>{{ item.request_sent_from ?? "-" }}</td>
                  <!-- receiver -->
                  <td>{{ item.request_sent_to ?? "-" }}</td>
                  <!-- status code -->
                  <td>
                    <VChip
                      v-if="item.event_status_code && item.event_status_code === '200'"
                      label
                      color="success"
                      size="small"
                      class="text-capitalize"
                    >
                      {{ item.event_status_code }}
                    </VChip>
                    <VChip
                      v-else-if="item.event_status_code && item.event_status_code !== '200'"
                      label
                      color="error"
                      size="small"
                      class="text-capitalize"
                    >
                      {{ item.event_status_code }}
                    </VChip>
                    <span v-else>-</span>
                  </td>
                  <!-- created date -->
                  <td>
                    {{ item.created_date }} <br />
                    {{ item.created_time }}
                  </td>
                  <!-- updated date -->
                  <td>
                    {{ item.updated_date }} <br />
                    {{ item.updated_time }}
                  </td>
                  <!-- action -->
                  <td class="text-end">
                    <VBtn
                      variant="text"
                      color="default"
                      size="small"
                      @click="() => {
                        selectedGogoEventId = item.id
                        isPharmacyEventDetailsDialogVisible = true
                      }"
                    >
                      <VIcon
                        size="22"
                        icon="tabler-eye"
                      />
                    </VBtn>
                  </td>
                </tr>
                <tr v-if="isEmpty(order.event_logs)">
                  <td>
                    No Events
                  </td>
                </tr>
              </tbody>
            </VTable>
          </VCardText>
        </VCard>
      </VCol>
    </VRow>

    <PharmacyUpdateShippingAddressDialog
      v-model:isDrawerOpen="isUpdateShippingDrawerVisible"
      :refill-id="order.order_details?.refill_id"
      :shipping-address="order.shipping_address"
      @updated="fetchLogDetails"
    />

    <PharmacyUpdateOrderNoteDialog
      v-model:isDrawerOpen="isUpdateOrderNoteDrawerVisible"
      :refill-id="order.order_details?.refill_id"
      @updated="fetchLogDetails"
    />

    <!-- 👉 Event Details Dialog -->
    <PharmacyEventDetailsDialog
      v-model:isDialogVisible="isPharmacyEventDetailsDialogVisible"
      :event-id="selectedGogoEventId"
    />

    <!-- 👉 Edit Affiliate Order no -->
    <EditAffiliateOrderNoDrawer
      v-model:isDrawerOpen="isEditAffiliateNumberDialogVisible"
      :refill-id="order.order_details?.refill_id"
      @updated="fetchLogDetails"
    />

    <!-- 👉 Edit Shipping Status Dialog -->
    <PharmacyEditOrderStatusDrawer
      v-model:isDrawerOpen="isEditShippingStatusDialogVisible"
      :refill-id="order.order_details?.refill_id"
      :form-data="{
        trackingNo: order.shipment_details?.tracking_number ?? '',
        trackingUrl: order.shipment_details?.tracking_url ?? '',
        deliveryProvider: order.shipment_details?.delivery_provider ?? '',
      }"
      @updated="fetchLogDetails"
    />
  </div>
</template>

<style lang="scss">
.order-details {
  .text-size-14 {
    font-size: 14px;
  }
  .order-table {
    font-size: 14px;
    tr {
      border-bottom: thin solid rgba(var(--v-border-color), var(--v-border-opacity));

      &:last-child {
        border-bottom-width: 0;
      }

      td {
        padding-top: 0.5rem !important;
        padding-bottom: 0.5rem !important;

        &:first-child {
          padding-right: 0.5rem;
        }

        &:last-child {
          padding-left: 0.5rem;
          border-bottom: 0;
        }

        span {
          font-weight: 600;
        }
      }
    }
  }
  .product-table {
    font-size: 14px;

    .product {
      position: relative;
      display: flex;
      align-items: center;

      .product-image {
        max-width: 100px;
        width: 100%;
      }

      .product-details {
        margin: 0 0 0 20px;
        display: grid;

        span {
          &:not(:last-child) {
            margin-bottom: 8px;
          }
        }
      }
    }
  }
}
</style>
