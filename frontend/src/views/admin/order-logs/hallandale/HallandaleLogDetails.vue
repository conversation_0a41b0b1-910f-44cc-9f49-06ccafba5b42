<script setup>
import { isEmpty } from '@/@core/utils'
import router from '@/router'
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { getShippingActivityColor } from '@/utils/admin'
import { processErrors } from '@/utils/errorHandler'
import { formattedPhoneNumber } from '@/utils/helpers'
import { computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'

const { showSnackbar } = useGlobalData()
const route = useRoute()
const logId = computed(() => route.params.logId)
const skeletonLoading = ref(true)
const order = ref({})
const isHallandaleEventDetailsDialogVisible = ref(false)
const selectedHallandaleEventId = ref('')
const isEditShippingStatusDialogVisible = ref(false)
const isEditOrderIdDrawerVisible = ref(false)

// const fetchFromHallandaleLoading = ref(false)

onMounted(async () => {
  await fetchLogDetails()
})

async function fetchLogDetails() {
  try {
    skeletonLoading.value = true

    const { data } = await ApiService.get(`/admin/manage-hallandale-order-details/${logId.value}`)

    if (data.status === 200) {
      order.value = data.orderDetails
    } else {
      showSnackbar(data.message, 'error')
      router.push({ name: 'admin-hallandale-logs' })
    }
  } catch (error) {
    console.error(error)
    showSnackbar(processErrors(error)[0], 'error')
    router.push({ name: 'admin-hallandale-logs' })
  } finally {
    skeletonLoading.value = false
  }
}

const orderDetailsLink = computed(() => {
  const visitType = String(order.value.order_details?.category_name).toLowerCase()

  return {
    name: `admin-${visitType}-order-details`,
    params: { orderId: order.value.order_details?.refill_id },
  }
})

// async function fetchFromHallandale() {
//   try {
//     fetchFromHallandaleLoading.value = true

//     const { data } = await ApiService.get(`/admin/fetch-hallandale-affiliate-order-details/${order.value?.order_details?.refill_id}`)

//     if (data.status === 200) {
//       fetchLogDetails()
//     } else {
//       showSnackbar(data.message, 'error')
//     }
//   } catch (error) {
//     console.error(error)
//     showSnackbar(processErrors(error)[0], 'error')
//   } finally {
//     fetchFromHallandaleLoading.value = false
//   }
// }
</script>

<template>
  <div v-if="skeletonLoading">
    <div
      class="d-flex justify-center align-center"
      style="height: calc(100vh - 14rem)"
    >
      <VProgressCircular
        indeterminate
        size="48"
      />
    </div>
  </div>
  <div
    v-else
    class="order-details"
  >
    <!--
      <div
      v-if="Boolean(order.is_show_retrieve_order_details_btn)"
      class="d-flex justify-end align-center flex-wrap gap-y-4 mb-6"
      >
      <VBtn
      variant="tonal"
      :loading="fetchFromHallandaleLoading"
      @click="fetchFromHallandale"
      >
      Fetch from Hallandale
      </VBtn>
      </div>
    -->

    <VRow class="match-height">
      <VCol
        cols="12"
        md="6"
      >
        <VCard
          title="Order Details"
          class="h-100"
        >
          <VCardText>
            <VTable class="text-no-wrap order-table">
              <tr>
                <td>Order No</td>
                <td class="text-right">
                  <RouterLink
                    v-if="order?.order_details?.refill_id && orderDetailsLink"
                    class="text-link"
                    :to="orderDetailsLink"
                    style="font-weight: 600;"
                  >
                    {{ order?.order_details?.order_no }}
                  </RouterLink>
                  <span v-else> {{ order?.order_details?.order_no ?? "-" }} </span>
                </td>
              </tr>
              <tr>
                <td>Hallandale Order ID</td>
                <td class="text-right">
                  <div>{{ order?.order_details?.order_id ?? "-" }}</div>
                  <button
                    class="text-link mt-1"
                    @click="isEditOrderIdDrawerVisible = true"
                  >
                    Update Order ID
                  </button>
                </td>
              </tr>
              <tr>
                <td>Order Status</td>
                <td class="text-right">
                  <div class="status">
                    <VChip
                      label
                      :color="order?.order_details?.status_text_color"
                      size="small"
                      class="text-capitalize order-status"
                    >
                      {{ order?.order_details?.status_text }}
                    </VChip>
                  </div>
                  <button
                    class="text-link mt-1"
                    @click="isEditShippingStatusDialogVisible = true"
                  >
                    Change Status
                  </button>
                </td>
              </tr>
              <tr>
                <td>Order Type</td>
                <td class="text-right">
                  <span v-if="!isEmpty(order?.order_details?.order_type)">
                    {{ order?.order_details?.order_type }}
                  </span>
                  <span v-else> - </span>
                </td>
              </tr>
              <tr>
                <td>Visit Type</td>
                <td class="text-right">
                  <span>{{ order?.order_details?.category_name ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>Order created at</td>
                <td class="text-right">
                  <span>{{ order?.order_details?.created_date ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>Last updated at</td>
                <td class="text-right">
                  <span>{{ order?.order_details?.updated_date ?? "-" }}</span>
                </td>
              </tr>
            </VTable>
          </VCardText>
        </VCard>
      </VCol>

      <VCol
        cols="12"
        md="6"
      >
        <VCard
          title="Customer Details"
          class="h-100"
        >
          <VCardText>
            <VTable class="text-no-wrap order-table">
              <tr>
                <td>First Name</td>
                <td class="text-right">
                  <span>{{ order?.customer_details?.first_name ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>Last Name</td>
                <td class="text-right">
                  <span>{{ order?.customer_details?.last_name ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>Gender</td>
                <td class="text-right">
                  <span>{{ order?.customer_details?.gender ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>DOB</td>
                <td class="text-right">
                  <span>{{ order?.customer_details?.dob ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>Email</td>
                <td class="text-right">
                  <span>{{ order?.customer_details?.email ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>Phone</td>
                <td class="text-right">
                  <span>{{ formattedPhoneNumber(order?.customer_details?.mobile_no) ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>Address</td>
                <td class="text-right">
                  <span>
                    {{ order?.customer_details?.address_line_1 ?? "-" }}
                    <span
                      v-if="order?.customer_details?.address_line_2"
                      class="d-block"
                    >
                      {{ order?.customer_details?.address_line_2 }}
                    </span>
                  </span>
                </td>
              </tr>
              <tr>
                <td>City</td>
                <td class="text-right">
                  <span>{{ order?.customer_details?.city ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>State</td>
                <td class="text-right">
                  <span>{{ order?.customer_details?.state ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>Zipcode</td>
                <td class="text-right">
                  <span>{{ order?.customer_details?.zipcode ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>Country</td>
                <td class="text-right">
                  <span>{{ order?.customer_details?.country ?? "-" }}</span>
                </td>
              </tr>
            </VTable>
          </VCardText>
        </VCard>
      </VCol>

      <VCol
        cols="12"
        md="6"
      >
        <VCard
          title="Shipping Details"
          class="h-100"
        >
          <VCardText>
            <VTable class="text-no-wrap order-table">
              <tr>
                <td>Receipt Type</td>
                <td class="text-right">
                  <span>{{ order?.shipping_details?.receipt_type ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>First Name</td>
                <td class="text-right">
                  <span>{{ order?.shipping_details?.first_name ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>Last Name</td>
                <td class="text-right">
                  <span>{{ order?.shipping_details?.last_name ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>Email</td>
                <td class="text-right">
                  <span>{{ order?.shipping_details?.email ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>Phone</td>
                <td class="text-right">
                  <span>{{ formattedPhoneNumber(order?.shipping_details?.mobile_no) ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>Address</td>
                <td class="text-right">
                  <span>
                    {{ order?.shipping_details?.address_line_1 ?? "-" }}
                    <span
                      v-if="order?.shipping_details?.address_line_2"
                      class="d-block"
                    >
                      {{ order?.shipping_details?.address_line_2 }}
                    </span>
                  </span>
                </td>
              </tr>
              <tr>
                <td>City</td>
                <td class="text-right">
                  <span>{{ order?.shipping_details?.city ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>State</td>
                <td class="text-right">
                  <span>{{ order?.shipping_details?.state ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>Zipcode</td>
                <td class="text-right">
                  <span>{{ order?.shipping_details?.zipcode ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>Country</td>
                <td class="text-right">
                  <span>{{ order?.shipping_details?.country ?? "-" }}</span>
                </td>
              </tr>
            </VTable>
          </VCardText>
        </VCard>
      </VCol>

      <VCol
        cols="12"
        md="6"
      >
        <VCard
          title="Prescriber"
          class="h-100"
        >
          <VCardText>
            <VTable class="text-no-wrap order-table">
              <tr>
                <td>First Name</td>
                <td class="text-right">
                  <span>{{ order?.prescriber?.first_name ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>Last Name</td>
                <td class="text-right">
                  <span>{{ order?.prescriber?.last_name ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>Address</td>
                <td class="text-right">
                  <span>{{ order?.prescriber?.address ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>City</td>
                <td class="text-right">
                  <span>{{ order?.prescriber?.city ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>State</td>
                <td class="text-right">
                  <span>{{ order?.prescriber?.state ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>Zipcode</td>
                <td class="text-right">
                  <span>{{ order?.prescriber?.zipcode ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>NPI</td>
                <td class="text-right">
                  <span>{{ order?.prescriber?.npi ?? "-" }}</span>
                </td>
              </tr>
              <tr v-if="!isEmpty(order?.prescriber?.receipt_type)">
                <td>Receipt Type</td>
                <td class="text-right">
                  <span>{{ order?.prescriber?.receipt_type ?? "-" }}</span>
                </td>
              </tr>
            </VTable>
          </VCardText>
        </VCard>
      </VCol>

      <VCol
        cols="12"
        class="expansion-fix"
      >
        <VCard title="Drugs">
          <VCardText>
            <VExpansionPanels multiple>
              <VExpansionPanel
                v-for="(item, index) in order?.drugs"
                :key="index"
                class="border-md border-dashed"
              >
                <VExpansionPanelTitle> {{ item.drug_name }} </VExpansionPanelTitle>
                <VExpansionPanelText>
                  <VRow>
                    <VCol cols="12">
                      <VTable class="text-no-wrap order-table">
                        <tr>
                          <td>Product ID</td>
                          <td class="text-right">
                            <Skeleton
                              v-if="skeletonLoading"
                              height="1rem"
                            ></Skeleton>
                            <span v-else>{{ item.product_id ?? "-" }}</span>
                          </td>
                        </tr>
                        <tr>
                          <td>Drug Name</td>
                          <td class="text-right">
                            <Skeleton
                              v-if="skeletonLoading"
                              height="1rem"
                            ></Skeleton>
                            <span
                              v-else
                              class="d-inline-block max-w-250px text-wrap word-break-all"
                            >
                              {{ item.drug_name ?? "-" }}
                            </span>
                          </td>
                        </tr>
                        <tr>
                          <td>Drug Strength</td>
                          <td class="text-right">
                            <Skeleton
                              v-if="skeletonLoading"
                              height="1rem"
                            ></Skeleton>
                            <span
                              v-else
                              class="d-inline-block max-w-250px text-wrap word-break-all"
                            >
                              {{ item.drug_strength ?? "-" }}
                            </span>
                          </td>
                        </tr>
                        <tr>
                          <td>Drug Form</td>
                          <td class="text-right">
                            <Skeleton
                              v-if="skeletonLoading"
                              height="1rem"
                            ></Skeleton>
                            <span
                              v-else
                              class="d-inline-block max-w-250px text-wrap word-break-all"
                            >
                              {{ item.drug_form ?? "-" }}
                            </span>
                          </td>
                        </tr>
                        <tr>
                          <td>RX Type</td>
                          <td class="text-right">
                            <Skeleton
                              v-if="skeletonLoading"
                              height="1rem"
                            ></Skeleton>
                            <span
                              v-else
                              class="d-inline-block max-w-250px text-wrap word-break-all"
                            >
                              {{ item.rx_type ?? "-" }}
                            </span>
                          </td>
                        </tr>
                        <tr>
                          <td>Quantity</td>
                          <td class="text-right">
                            <Skeleton
                              v-if="skeletonLoading"
                              height="1rem"
                            ></Skeleton>
                            <span
                              v-else
                              class="d-inline-block max-w-250px text-wrap word-break-all"
                            >
                              {{ item.qty ?? "-" }}
                            </span>
                          </td>
                        </tr>
                        <tr>
                          <td>Directions</td>
                          <td class="text-right">
                            <Skeleton
                              v-if="skeletonLoading"
                              height="1rem"
                            ></Skeleton>
                            <span
                              v-else
                              class="d-inline-block max-w-250px text-wrap word-break-all"
                            >
                              {{ item.directions ?? "-" }}
                            </span>
                          </td>
                        </tr>
                      </VTable>
                    </VCol>
                  </VRow>
                </VExpansionPanelText>
              </VExpansionPanel>
            </VExpansionPanels>
          </VCardText>
        </VCard>
      </VCol>

      <VCol
        cols="12"
        md="6"
      >
        <VCard
          title="Shipment Information"
          class="h-100"
        >
          <VCardText>
            <VTable class="text-no-wrap order-table">
              <tr>
                <td>Tracking URL</td>
                <td class="text-right">
                  <a
                    v-if="!isEmpty(order.shipment_information?.tracking_url)"
                    :href="order.shipment_information?.tracking_url"
                    target="_blank"
                    rel="noopener noreferrer"
                    class="text-wrap word-break-all font-weight-bold text-primary text-link-admin"
                  >
                    Track Order
                  </a>
                  <span v-else>-</span>
                </td>
              </tr>
              <tr>
                <td>Tracking Number</td>
                <td class="text-right">
                  <span>{{ order.shipment_information?.tracking_number ?? "-" }}</span>
                </td>
              </tr>
              <tr>
                <td>Delivery Provider</td>
                <td class="text-right">
                  <span>{{ order.shipment_information?.delivery_provider ?? "-" }}</span>
                </td>
              </tr>
            </VTable>
          </VCardText>
        </VCard>
      </VCol>

      <VCol
        cols="12"
        md="6"
      >
        <VCard title="Shipping Activity">
          <VCardText
            class="overflow-y-auto"
            style="max-height: 400px"
          >
            <VTimeline
              v-if="!isEmpty(order.shipping_activities)"
              side="end"
              align="start"
              line-inset="8"
              truncate-line="both"
              density="compact"
            >
              <VTimelineItem
                v-for="(item, index) in order.shipping_activities"
                :key="index"
                dot-color="primary"
                size="x-small"
              >
                <!-- 👉 Header -->
                <div class="d-flex justify-space-between align-center gap-2 flex-wrap">
                  <span class="app-timeline-title">
                    <VChip
                      label
                      :color="getShippingActivityColor(item.EventStatus)"
                      size="small"
                      class="text-capitalize mb-1"
                    >
                      {{ item.EventStatusText }}
                    </VChip>
                  </span>
                  <span class="app-timeline-meta text-body-2">{{ item.EventDate }}</span>
                </div>

                <!-- 👉 Content -->
                <div
                  v-if="!isEmpty(item.EventDescription)"
                  class="app-timeline-text mb-1 text-subtitle-2"
                >
                  <span>{{ item.EventDescription }}</span>
                </div>

                <p
                  v-if="!isEmpty(item.ActionByUserName)"
                  class="app-timeline-meta mb-2 text-caption font-italic text-secondary"
                >
                  Action By: {{ item.ActionByUserName }}
                  <span v-if="!isEmpty(item.ActionByUserRole)">
                    {{ '(' + item.ActionByUserRole + ')' }}
                  </span>
                </p>
              </VTimelineItem>
            </VTimeline>
            <span v-else>No data available</span>
          </VCardText>
        </VCard>
      </VCol>
    </VRow>

    <!-- 👉 Event Details Dialog -->
    <HallandaleEventDetailsDialog
      v-model:isDialogVisible="isHallandaleEventDetailsDialogVisible"
      :event-id="selectedHallandaleEventId"
    />

    <!-- 👉 Edit Affiliate Order no -->
    <EditHallandaleOrderIdDrawer
      v-model:isDrawerOpen="isEditOrderIdDrawerVisible"
      :refill-id="order?.order_details?.refill_id"
      @updated="fetchLogDetails"
    />

    <!-- 👉 Edit Shipping Status Dialog -->
    <EditHallandaleOrderStatusDrawer
      v-model:isDrawerOpen="isEditShippingStatusDialogVisible"
      :refill-id="order?.order_details?.refill_id"
      :form-data="{
        trackingNo: order.shipment_information?.tracking_number ?? '',
        trackingUrl: order.shipment_information?.tracking_url ?? '',
        deliveryProvider: order.shipment_information?.delivery_provider ?? '',
      }"
      @updated="fetchLogDetails"
    />
  </div>
</template>

<style lang="scss">
.order-details {
  .text-size-14 {
    font-size: 14px;
  }
  .order-table {
    font-size: 14px;
    tr {
      border-bottom: thin solid rgba(var(--v-border-color), var(--v-border-opacity));

      &:last-child {
        border-bottom-width: 0;
      }

      td {
        padding-top: 0.5rem !important;
        padding-bottom: 0.5rem !important;

        &:first-child {
          padding-right: 0.5rem;
        }

        &:last-child {
          padding-left: 0.5rem;
          border-bottom: 0;
        }

        span {
          font-weight: 600;
        }
      }
    }
  }
  .product-table {
    font-size: 14px;

    .product {
      position: relative;
      display: flex;
      align-items: center;

      .product-image {
        max-width: 100px;
        width: 100%;
      }

      .product-details {
        margin: 0 0 0 20px;
        display: grid;

        span {
          &:not(:last-child) {
            margin-bottom: 8px;
          }
        }
      }
    }
  }
}
</style>
