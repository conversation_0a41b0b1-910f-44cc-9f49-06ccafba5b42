<script setup>
import { isEmpty } from '@/@core/utils'
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { processErrors } from '@/utils/errorHandler'
import { calculateStartIndex, formatCurrency, formattedPhoneNumber, resolveInitials } from '@/utils/helpers'
import { refDebounced } from '@vueuse/core'
import { computed, onMounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const globalData = useGlobalData()
const { showSnackbar } = globalData
const route = useRoute()
const router = useRouter()

const searchQuery = ref(route.query.search ?? null)
const debouncedSearchQuery = refDebounced(searchQuery, 300)
const selectedStatus = ref(!isNaN(parseInt(route.query.status)) ? parseInt(route.query.status) : null)

const dateRange = ref(
  route.query.from_date && route.query.to_date ? route.query.from_date + ' to ' + route.query.to_date : null,
)

const fromDate = ref(null)
const toDate = ref(null)
const rowPerPage = ref(10)
const currentPage = ref(!isNaN(parseInt(route.query.page)) ? parseInt(route.query.page) : 1)
const totalPage = ref(1)
const totalItems = ref(0)
const items = ref([])
const sortByColumnName = ref(route.query.sort_by ?? 'created_at')
const sortDirection = ref(route.query.sort_dir ?? 'desc')
const skeletonLoading = ref(true)
const categories = ref([])
const filterCategories = ref([])

const isRefundRequestDialogVisible = ref(false)
const selectedRefundRequestId = ref('')
const isRefundApproveDialogVisible = ref(false)
const isRefundDeclineDialogVisible = ref(false)
const refundActionLoading = ref(false)

onMounted(() => {
  fetchItems()
})

const updateRoute = () => {
  router.replace({
    query: {
      page: currentPage.value,
      per_page: rowPerPage.value,
      status: selectedStatus.value,
      search: searchQuery.value,
      sort_by: sortByColumnName.value,
      sort_dir: sortDirection.value,
      from_date: fromDate.value,
      to_date: toDate.value,
    },
  })
}

watch(dateRange, () => {
  if (isEmpty(dateRange.value)) {
    fromDate.value = null
    toDate.value = null

    return
  }

  const dateArr = dateRange.value.split(' to ')

  if (dateArr.length === 1) {
    fromDate.value = dateArr[0] || null
    toDate.value = dateArr[0] || null
  } else if (dateArr.length === 2) {
    fromDate.value = dateArr[0] || null
    toDate.value = dateArr[1] || null
  } else {
    fromDate.value = null
    toDate.value = null
  }
})

watch([debouncedSearchQuery, selectedStatus, fromDate, toDate, rowPerPage, filterCategories], () => {
  currentPage.value = 1
  fetchItems()
})

watch([currentPage, sortByColumnName, sortDirection], () => {
  fetchItems()
})

const fetchItems = async () => {
  skeletonLoading.value = true

  try {
    const { data } = await ApiService.post('/admin/refund-request-list', {
      searchQuery: searchQuery.value,
      perPage: rowPerPage.value,
      page: currentPage.value,
      sortByColumnName: sortByColumnName.value,
      isSortDirDesc: sortDirection.value,
      from_date: fromDate.value,
      to_date: toDate.value,
      status: selectedStatus.value,
      category_names: filterCategories.value,
    })

    if (data.status === 200) {
      categories.value = data.categories

      const pagedData = data.orders

      if (isEmpty(pagedData.records) && currentPage.value !== 1) {
        currentPage.value = currentPage.value - 1

        return
      }

      let startIndex = calculateStartIndex(pagedData.current_page, pagedData.per_page)
      if (currentPage.value > 1) {
        startIndex = (currentPage.value - 1) * rowPerPage.value + 1
      }

      items.value = pagedData.records.map((d, index) => ({
        ...d,
        sno: index + startIndex,
      }))
      totalPage.value = pagedData.totalPage == 0 ? 1 : pagedData.totalPage
      totalItems.value = pagedData.totalRecords
      updateRoute()
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    console.error(error)
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    skeletonLoading.value = false
  }
}

const sortColumn = columnName => {
  if (sortByColumnName.value !== '' && sortByColumnName.value === columnName && sortDirection.value === 'asc') {
    sortDirection.value = 'desc'
  } else {
    sortDirection.value = 'asc'
  }

  sortByColumnName.value = columnName
}

// 👉 search filters
const status = [
  {
    title: 'Pending',
    value: 0,
  },
  {
    title: 'Success',
    value: 1,
  },
  {
    title: 'Canceled',
    value: 2,
  },
  {
    title: 'Failed',
    value: 3,
  },
]

async function handleRefundApproveConfirmation(isConfirmed) {
  if (isConfirmed) {
    await approveDeclineRefund({
      refund_id: selectedRefundRequestId.value,
      refund_action_type: 'approve',
    })
  }
}

async function handleRefundDeclineConfirmation(cancelReason) {
  if (cancelReason) {
    await approveDeclineRefund({
      refund_id: selectedRefundRequestId.value,
      refund_action_type: 'decline',
      cancel_reason: cancelReason,
    })
  }
}

async function approveDeclineRefund(requestBody) {
  try {
    refundActionLoading.value = true

    const { data } = await ApiService.post('/admin/refund-action', requestBody)

    if (data.status === 200) {
      selectedRefundRequestId.value = ''
      showSnackbar(data.message)
      fetchItems()
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    isRefundApproveDialogVisible.value = false
    isRefundDeclineDialogVisible.value = false
    refundActionLoading.value = false
  }
}

// 👉 Computing pagination data
const paginationData = computed(() => {
  const currentPageValue = currentPage.value <= 0 ? 1 : currentPage.value

  const firstIndex = items.value.length
    ? (currentPageValue - 1) * rowPerPage.value + 1
    : 0

  const lastIndex = items.value.length + (currentPageValue - 1) * rowPerPage.value

  return `Showing ${firstIndex} to ${lastIndex} of ${totalItems.value} entries`
})
</script>

<template>
  <section>
    <VRow class="match-height">
      <VCol cols="12">
        <VCard title="Filter">
          <!-- 👉 Filters -->
          <VCardText>
            <VRow>
              <VCol
                cols="12"
                sm="4"
              >
                <AppSelect
                  v-model="selectedStatus"
                  label="Refund Status"
                  :items="status"
                  clearable
                  clear-icon="tabler-x"
                  placeholder="Select"
                />
              </VCol>
              <VCol
                cols="12"
                sm="4"
              >
                <AppSelect
                  v-model="filterCategories"
                  label="Category"
                  :items="categories"
                  clearable
                  multiple
                  clear-icon="tabler-x"
                  placeholder="Select Category"
                />
              </VCol>
              <VCol
                cols="12"
                sm="4"
              >
                <AppDateTimePicker
                  v-model="dateRange"
                  label="Created Date"
                  placeholder="Select Date Range"
                  variant="outlined"
                  :config="{
                    mode: 'range',
                    dateFormat: 'm/d/Y',
                    onClose: function (selectedDates, dateStr, instance) {
                      if (selectedDates.length === 1) {
                        instance.setDate([selectedDates[0], selectedDates[0]], true)
                      }
                    },
                    maxDate: new Date(),
                    minDate: (new Date()).setFullYear((new Date()).getFullYear() - 120),
                  }"
                  clearable
                  clear-icon="tabler-x"
                />
              </VCol>
            </VRow>
          </VCardText>

          <VDivider />

          <VCardText class="d-flex flex-wrap py-4 gap-4">
            <div
              class="me-3"
              style="width: 80px"
            >
              <VSelect
                v-model="rowPerPage"
                density="compact"
                variant="outlined"
                :items="[10, 20, 30, 50]"
              />
            </div>

            <VSpacer />

            <div class="d-flex align-center flex-wrap gap-4">
              <!-- 👉 Search  -->
              <div style="width: 23rem">
                <VTextField
                  v-model="searchQuery"
                  placeholder="Search"
                  density="compact"
                  clearable
                  clear-icon="tabler-x"
                />
              </div>
            </div>
          </VCardText>

          <VDivider />

          <VTable class="text-no-wrap">
            <!-- 👉 table head -->
            <thead>
              <tr>
                <th scope="col">
                  #
                </th>
                <th scope="col">
                  ORDER #
                </th>
                <th scope="col">
                  CATEGORY
                </th>
                <th scope="col">
                  PRODUCT
                </th>
                <th scope="col">
                  USER
                </th>
                <th scope="col">
                  AMOUNT
                </th>
                <th scope="col">
                  REFUND STATUS
                </th>
                <th
                  scope="col"
                  class="cursor-pointer"
                  @click="sortColumn('created_at')"
                >
                  <div class="w-125px">
                    CREATED AT
                    <span class="d-grid float-right">
                      <VIcon
                        size="10"
                        icon="tabler-chevron-up"
                      />
                      <VIcon
                        size="10"
                        icon="tabler-chevron-down"
                      />
                    </span>
                  </div>
                </th>
                <th
                  scope="col"
                  class="cursor-pointer"
                  @click="sortColumn('updated_at')"
                >
                  <div class="w-125px">
                    UPDATED AT
                    <span class="d-grid float-right">
                      <VIcon
                        size="10"
                        icon="tabler-chevron-up"
                      />
                      <VIcon
                        size="10"
                        icon="tabler-chevron-down"
                      />
                    </span>
                  </div>
                </th>
                <th scope="col">
                  ACTIONS
                </th>
              </tr>
            </thead>

            <!-- 👉 table body -->
            <tbody v-if="skeletonLoading">
              <tr
                v-for="n in 10"
                :key="n"
              >
                <td>
                  <Skeleton
                    width="2rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
                <td>
                  <Skeleton
                    width="12rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
                <td>
                  <Skeleton
                    width="5rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
                <td>
                  <Skeleton
                    width="5rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
                <td>
                  <Skeleton
                    width="8rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
                <td>
                  <Skeleton
                    width="8rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
                <td>
                  <Skeleton
                    width="8rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
                <td>
                  <Skeleton
                    width="8rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
                <td>
                  <Skeleton
                    width="8rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
                <td>
                  <Skeleton
                    width="8rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
              </tr>
            </tbody>
            <tbody v-else>
              <tr
                v-for="item in items"
                :key="item.id"
                style="height: 3.75rem"
              >
                <!-- 👉 NO -->
                <td>
                  {{ item.sno }}
                </td>

                <!-- 👉 Order # -->
                <td>
                  <RouterLink
                    :to="{
                      name: `admin-${item.category_name.toLowerCase()}-order-details`,
                      params: { orderId: item.subscription_refill_id },
                      query: { ...route.query },
                    }"
                    class="dt-link"
                  >
                    {{ item.order_no }}
                  </RouterLink>
                </td>

                <!-- 👉 Category -->
                <td>
                  {{ item.category_name }}
                </td>

                <!-- 👉 Product -->
                <td class="title">
                  <div class="d-flex align-center py-1">
                    <VAvatar
                      v-if="item.product_img"
                      variant="tonal"
                      size="40"
                      class="me-3"
                      :image="item.product_img"
                      rounded
                    />
                    <VAvatar
                      v-else
                      variant="tonal"
                      size="40"
                      class="me-3"
                      rounded
                    >
                      {{ resolveInitials(item.product_name) }}
                    </VAvatar>
                    <div>
                      <h6 class="text-base">
                        {{ item.product_name }}
                      </h6>
                      <div
                        v-if="item.category_name?.toLowerCase() === 'wl'"
                        class="d-flex flex-column"
                      >
                        <span class="d-block text-body-2">
                          {{ item.strength }} {{ item.strength_unit }} / weekly
                        </span>
                      </div>
                      <div
                        v-else
                        class="d-flex flex-column"
                      >
                        <span class="d-block text-body-2">
                          {{ item.strength }} {{ item.strength_unit }} x {{ item.qty * item.subscription_interval }} units
                        </span>
                        <span class="d-block text-body-2">({{ item.subscription_interval * 30 }}-day supply)</span>
                      </div>
                    </div>
                  </div>
                </td>

                <!-- 👉 User -->
                <td class="title">
                  <div>
                    <h6 class="text-base">
                      {{ item.full_name }}
                    </h6>
                    <div class="d-flex flex-column">
                      <span class="text-body-2">{{ item.email }}</span>
                      <span class="text-body-2">{{ formattedPhoneNumber(item.phone_number) }}</span>
                    </div>
                  </div>
                </td>

                <!-- 👉 Refund Status -->
                <td>
                  {{ formatCurrency(item.refund_amount) }}
                </td>

                <!-- 👉 Refund Status -->
                <td>
                  <VChip
                    label
                    :color="item.refund_status_color_code"
                    size="small"
                    class="text-capitalize"
                  >
                    {{ item.refund_status_text }}
                  </VChip>
                </td>

                <!-- 👉 Created at -->
                <td>
                  <div class="d-flex flex-column">
                    <span>{{ item.created_date }}</span>
                    <span>{{ item.created_time }}</span>
                  </div>
                </td>

                <!-- 👉 Updated at -->
                <td>
                  <div
                    v-if="item.updated_date !== item.created_date || item.updated_time !== item.created_time"
                    class="d-flex flex-column"
                  >
                    <span>{{ item.updated_date }}</span>
                    <span>{{ item.updated_time }}</span>
                  </div>
                  <div v-else>
                    -
                  </div>
                </td>

                <!-- 👉 Actions -->
                <td
                  class="text-center"
                  style="width: 5rem"
                >
                  <VMenu>
                    <template #activator="{ props }">
                      <VBtn
                        v-bind="props"
                        variant="tonal"
                        color="secondary"
                        rounded="lg"
                        size="x-small"
                        icon
                      >
                        <VIcon
                          icon="mdi-dots-horizontal"
                          size="22"
                        />
                      </VBtn>
                    </template>

                    <VList>
                      <VListItem
                        @click="() => {
                          selectedRefundRequestId = item.id
                          isRefundRequestDialogVisible = true
                        }"
                      >
                        <VListItemTitle>
                          <div class="font-weight-medium">
                            View
                          </div>
                        </VListItemTitle>
                      </VListItem>

                      <VListItem
                        v-if="[0, 3].includes(item.refund_status)"
                        @click="() => {
                          selectedRefundRequestId = item.id
                          isRefundApproveDialogVisible = true
                        }"
                      >
                        <VListItemTitle>
                          <div class="font-weight-medium">
                            Approve
                          </div>
                        </VListItemTitle>
                      </VListItem>

                      <VListItem
                        v-if="[0, 3].includes(item.refund_status)"
                        @click="() => {
                          selectedRefundRequestId = item.id
                          isRefundDeclineDialogVisible = true
                        }"
                      >
                        <VListItemTitle>
                          <div class="font-weight-medium text-error">
                            Cancel
                          </div>
                        </VListItemTitle>
                      </VListItem>
                    </VList>
                  </VMenu>
                </td>
              </tr>
            </tbody>

            <!-- 👉 table footer  -->
            <tfoot v-show="!skeletonLoading && items.length === 0">
              <tr>
                <td
                  colspan="8"
                  class="text-center"
                >
                  No data available
                </td>
              </tr>
            </tfoot>
          </VTable>

          <VDivider />

          <VCardText class="d-flex align-center flex-wrap justify-space-between gap-4 py-3 px-5">
            <span class="text-sm text-disabled">
              {{ paginationData }}
            </span>
            <VPagination
              v-model="currentPage"
              size="small"
              :total-visible="5"
              :length="totalPage"
            />
          </VCardText>
        </VCard>
      </VCol>
    </VRow>

    <!-- 👉 View Refund Request -->
    <RefundRequestDetailsDialog
      v-model:isDialogVisible="isRefundRequestDialogVisible"
      :refund-request-id="selectedRefundRequestId"
      @approve="(refundId) => {
        selectedRefundRequestId = refundId
        isRefundRequestDialogVisible = false
        isRefundApproveDialogVisible = true
      }"
      @decline="(refundId) => {
        selectedRefundRequestId = refundId
        isRefundRequestDialogVisible = false
        isRefundDeclineDialogVisible = true
      }"
    />

    <!-- 👉 Confirm Refund Approve -->
    <ConfirmDialog
      v-model:isDialogVisible="isRefundApproveDialogVisible"
      confirmation-question="Are you sure you want to approve this refund request?"
      :loading="refundActionLoading"
      @confirm="handleRefundApproveConfirmation"
    />

    <!-- 👉 Refund Decline Dialog -->
    <RefundDeclineDialog
      v-model:isDialogVisible="isRefundDeclineDialogVisible"
      @submit="handleRefundDeclineConfirmation"
    />
  </section>
</template>

<style lang="scss">
.v-table__wrapper td.title {
  min-width: 200px;
  h6 {
    a {
      white-space: pre-line;
    }
  }
}
</style>
