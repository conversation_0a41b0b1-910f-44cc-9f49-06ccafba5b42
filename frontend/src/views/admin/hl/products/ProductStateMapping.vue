<script setup>
import { isEmpty } from '@/@core/utils'
import { requiredValidator } from '@/@core/utils/validators'
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { processErrors } from '@/utils/errorHandler'
import { IconListDetails } from '@tabler/icons-vue'
import Image from 'primevue/image'
import { onMounted, ref } from 'vue'

const { showSnackbar } = useGlobalData()

const VIEW_SECTION = {
  LOADING_VIEW: 'LOADING_VIEW',
  EMPTY_VIEW: 'EMPTY_VIEW',
  RECOMMENDED_VIEW: 'RECOMMENDED_VIEW',
  FORM_VIEW: 'FORM_VIEW',
}

const currentView = ref(VIEW_SECTION.LOADING_VIEW)
const formRef = ref(null)
const mappedProducts = ref([])
const products = ref([])
const stateOptions = ref([])
const selectedProduct = ref(null)
const selectedStates = ref([])
const isLoading = ref(false)
const serverErrors = ref([])
const editId = ref(null)
const deleteId = ref(null)
const isDeleteDialogVisible = ref(false)
const deleteLoading = ref(false)

function onProductChange() {
  selectedStates.value = []
}

const productOptions = computed(() => {
  if (isEmpty(products.value)) return []
  if (!isEmpty(editId.value)) return products.value

  const mappedProductIds = []

  mappedProducts.value?.forEach(p => {
    mappedProductIds.push(p.product_id)
  })

  return products.value.filter(p => (!mappedProductIds.includes(p.id)))
})

onMounted(async () => {
  await Promise.all([getMappedProductList(), getProductList(), fetchStates()])
})

const fetchStates = async () => {
  try {
    const { data } = await ApiService.get('/state-list/HL')

    if (data.status === 200) {
      stateOptions.value = data.stateData
    } else {
      console.error(data)
    }
  } catch (error) {
    console.error(error)
  }
}

async function getMappedProductList() {
  try {
    const { data } = await ApiService.get('/admin/get-product-state-mappings/HL')

    if (data.status === 200) {
      mappedProducts.value = data.stateMappingProducts
      if (!isEmpty(data.stateMappingProducts)) {
        currentView.value = VIEW_SECTION.RECOMMENDED_VIEW
      } else {
        currentView.value = VIEW_SECTION.EMPTY_VIEW
      }
    } else {
      if (data.message) {
        showSnackbar(data.message, 'error')
      } else {
        showSnackbar(processErrors(data)[0], 'error')
      }
    }
  } catch (error) {
    showSnackbar(processErrors(error)[0], 'error')
  }
}

async function getProductList() {
  try {
    const { data } = await ApiService.get('/admin/products/HL')

    if (data.status === 200) {
      products.value = data.products
    } else {
      if (data.message) {
        showSnackbar(data.message, 'error')
      } else {
        showSnackbar(processErrors(data)[0], 'error')
      }
    }
  } catch (error) {
    showSnackbar(processErrors(error)[0], 'error')
  }
}

function onClickFormCancel() {
  if (!isEmpty(mappedProducts.value)) {
    currentView.value = VIEW_SECTION.RECOMMENDED_VIEW
  } else {
    currentView.value = VIEW_SECTION.EMPTY_VIEW
  }

  selectedProduct.value = null
  selectedStates.value = []
  editId.value = null
  serverErrors.value = []
}

async function handleSubmit() {
  formRef.value?.validate().then(async result => {
    if (result.valid) {
      await addOrUpdate()
    }
  })
}

async function addOrUpdate() {
  try {
    isLoading.value = true
    serverErrors.value = null

    let postUrl = '/admin/add-product-state-mapping'

    const postData = {
      'category_name': 'HL',
      'product_id': selectedProduct.value,
      'states': selectedStates.value,
    }

    if (editId.value) {
      postUrl = '/admin/edit-product-state-mapping'
      postData['id'] = editId.value
    }

    const { data } = await ApiService.post(postUrl, postData)

    if (data.status === 200) {
      showSnackbar(data.message)
      onClickFormCancel()
      getMappedProductList()
    } else {
      serverErrors.value = processErrors(data)
    }
  } catch (error) {
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    isLoading.value = false
  }
}

function selectAll() {
  if (selectedStates.value.length === stateOptions.value.length) {
    selectedStates.value = []
  } else {
    selectedStates.value = stateOptions.value.map(state => state.code)
  }
}

function onClickEdit(product) {
  currentView.value = VIEW_SECTION.FORM_VIEW
  editId.value = product.id
  selectedProduct.value = product.product_id
  selectedStates.value = product.allowed_states
}

function onClickDelete(product) {
  deleteId.value = product.id
  isDeleteDialogVisible.value = true
}

async function handleDeleteConfirmation(isConfirmed) {
  if (isConfirmed) {
    await deleteMapping()
  }
}

async function deleteMapping() {
  try {
    deleteLoading.value = true

    const { data } = await ApiService.delete(`/admin/delete-product-state-mappings/${deleteId.value}`)

    if (data.status === 200) {
      showSnackbar(data.message)
      deleteId.value = null
      getMappedProductList()
    } else {
      if (data.message) {
        showSnackbar(data.message, 'error')
      } else {
        showSnackbar(processErrors(data)[0], 'error')
      }
    }
  } catch (error) {
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    deleteLoading.value = false
    isDeleteDialogVisible.value = false
  }
}
</script>

<template>
  <div>
    <!-- 👉 Loading View -->
    <VCard
      v-if="currentView === VIEW_SECTION.LOADING_VIEW"
      class="mb-6"
    >
      <VCardText>
        <div
          class="d-flex flex-column align-center justify-center"
          style="height: 200px;"
        >
          <VProgressCircular
            size="42"
            indeterminate
          />
        </div>
      </VCardText>
    </VCard>

    <!-- 👉 No mappings -->
    <VCard
      v-if="currentView === VIEW_SECTION.EMPTY_VIEW"
      class="mb-6"
    >
      <VCardText>
        <div class="d-flex flex-column align-center gap-2">
          <IconListDetails
            size="80"
            stroke-width="1.5"
          />
          <div class="text-lg">
            No product-state mappings added
          </div>
          <div class="mt-6">
            <VBtn @click="currentView = VIEW_SECTION.FORM_VIEW">
              Add New Mapping
            </VBtn>
          </div>
        </div>
      </VCardText>
    </VCard>

    <!-- 👉 Recommended List -->
    <VCard
      v-if="mappedProducts.length > 0"
      class="mb-6"
    >
      <VCardTitle class="pa-6">
        <div class="text-h5">
          Mapped Products
        </div>
      </VCardTitle>

      <VCardText>
        <div
          v-for="product in mappedProducts"
          :key="product.id"
        >
          <VCard class="pa-3 border-sm border-dashed mb-3">
            <div class="d-flex flex-column flex-md-row gap-4">
              <div>
                <VAvatar
                  variant="tonal"
                  color="primary"
                  size="100"
                  class="me-3 rounded-lg"
                >
                  <Image
                    :src="product.product_image"
                    height="100"
                    width="100"
                    image-class="object-fit-cover"
                    preview
                  />
                </VAvatar>
              </div>
              <div class="w-100">
                <div class="text-xl mb-1">
                  <div class="d-flex gap-4">
                    <div>{{ product.product_name }}</div>
                    <div>
                      <VChip
                        :color="Boolean(product.is_active) ? 'success' : 'default'"
                        size="x-small"
                        label
                      >
                        {{ Boolean(product.is_active) ? 'Active' : 'Inactive' }}
                      </VChip>
                    </div>
                  </div>
                </div>
                <div class="mb-1">
                  <span class="font-weight-bold text-sm text-uppercase">
                    Pharmacy:
                  </span> {{ product.pharmacy_name }}
                </div>
                <div>
                  <div class="font-weight-bold text-sm text-uppercase">
                    Available in states
                  </div>
                  <div>{{ product.allowed_states.join(', ') }}</div>
                </div>
                <div
                  v-if="editId !== product.id"
                  class="mt-4 d-flex justify-space-between"
                >
                  <div class="d-flex gap-4">
                    <VBtn
                      size="small"
                      variant="tonal"
                      @click="onClickEdit(product)"
                    >
                      Edit
                    </VBtn>
                    <VBtn
                      color="error"
                      size="small"
                      variant="tonal"
                      @click="onClickDelete(product)"
                    >
                      Delete
                    </VBtn>
                  </div>
                  <div class="text-sm">
                    <div v-if="product.action_by_user_name && product.action_by_user_role">
                      <span>Action By: </span>
                      <span>{{ product.action_by_user_name }} ({{ product.action_by_user_role }})</span>
                    </div>
                    <div v-if="product.updated_date_time">
                      Updated at: {{ product.updated_date_time }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </VCard>
        </div>

        <div
          v-if="currentView !== VIEW_SECTION.FORM_VIEW"
          class="mt-6"
        >
          <VBtn
            block
            @click="currentView = VIEW_SECTION.FORM_VIEW"
          >
            Add New Mapping
          </VBtn>
        </div>
      </VCardText>
    </VCard>

    <!-- 👉 Recommend new product Form -->
    <VCard
      v-if="currentView === VIEW_SECTION.FORM_VIEW"
      class="mb-6"
    >
      <VCardTitle class="pa-6">
        <div class="text-h5">
          {{ editId ? 'Edit Mapping' : 'Add Mapping' }}
        </div>
      </VCardTitle>

      <VCardText>
        <VAlert
          v-if="!isEmpty(serverErrors)"
          type="error"
          variant="tonal"
          title="Error!"
          class="mb-6"
        >
          <ul class="mb-0">
            <li
              v-for="error in serverErrors"
              :key="error"
              class="mb-0"
            >
              {{ error }}
            </li>
          </ul>
        </VAlert>

        <VForm
          ref="formRef"
          @submit.prevent="handleSubmit"
        >
          <VRow>
            <VCol
              cols="12"
              md="6"
            >
              <VSelect
                v-model="selectedProduct"
                label="Select Product"
                :items="productOptions"
                item-title="product_name"
                item-value="id"
                :rules="[requiredValidator]"
                @update:model-value="onProductChange"
              >
                <template #selection="{ item }">
                  <span>
                    {{ item?.raw?.product_name }} <span v-if="item?.raw?.pharmacy_name">
                      ({{ item?.raw?.pharmacy_name }})
                    </span>
                  </span>
                </template>
                <template #item="{ props, item }">
                  <VListItem
                    v-bind="props"
                    class="py-2"
                    :title="item?.raw?.product_name"
                  >
                    <div class="text-sm">
                      {{ item?.raw?.pharmacy_name }}
                    </div>
                  </VListItem>
                </template>
              </VSelect>
            </VCol>

            <VCol cols="12">
              <div class="mt-4">
                <label class="d-block mb-3">Select the states where this product will be available:</label>
                <VCheckbox
                  label="Select All"
                  class="me-3"
                  :indeterminate="selectedStates.length > 0 && selectedStates.length !== stateOptions.length"
                  :model-value="selectedStates.length === stateOptions.length"
                  @change="selectAll"
                />
                <div class="d-flex flex-wrap">
                  <div
                    v-for="state in stateOptions"
                    :key="state.code"
                    style="min-width: 220px;"
                  >
                    <VCheckbox
                      v-model="selectedStates"
                      :label="`${state.code} ~ ${state.name}`"
                      :value="state.code"
                      class="me-3"
                    />
                  </div>
                </div>
              </div>
            </VCol>

            <VCol cols="12">
              <div class="d-flex justify-end gap-3">
                <VBtn
                  type="button"
                  variant="tonal"
                  color="secondary"
                  :disabled="isLoading"
                  @click="onClickFormCancel"
                >
                  Cancel
                </VBtn>
                <VBtn
                  type="submit"
                  :loading="isLoading"
                >
                  Submit
                </VBtn>
              </div>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>

    <!-- 👉 Delete confirm dialog -->
    <ConfirmDialog
      v-model:isDialogVisible="isDeleteDialogVisible"
      confirmation-question="Are you sure you want to delete this product-state mapping?"
      :loading="deleteLoading"
      @confirm="handleDeleteConfirmation"
    />
  </div>
</template>
