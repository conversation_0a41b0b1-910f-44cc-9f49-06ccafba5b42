<script setup>
import { isEmpty, isEmptyObject, isNullOrUndefined } from '@/@core/utils'
import { requiredValidator } from '@/@core/utils/validators'
import router from '@/router'
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { processErrors } from '@/utils/errorHandler'
import { removeKeyFromObject, scrollToTop } from '@/utils/helpers'
import { useObjectUrl } from '@vueuse/core'
import { ref } from 'vue'
import usePharmacyList from '@/composables/usePharmacyList'
import useProductTypes from '@/composables/useProductTypes'

const { showSnackbar } = useGlobalData()
const { pharmacyList } = usePharmacyList()
const { productTypes, fetchProductTypes, error: productTypesError } = useProductTypes()

const serverErrors = ref([])
const inputErrors = ref({})
const isLoading = ref(false)
const formProductInfoRef = ref()
const inputProductImageRef = ref(null)
const isProductImgUploading = ref(false)
const isEmptyProductImg = ref(false)

const formProductData = ref({
  product_name: '',
  active_ingredient: '',
  product_form: '',
  product_type: null,
  product_description: '',
  product_image: '',
  best_for: '',
  pharmacy_id: null,
})

const productImage = ref({ file: null, url: '' })

const onFileChange = $event => {
  const { files } = $event.target
  if (!files || files.length === 0) return
  const file = files[0]

  if (file.type.slice(0, 6) !== 'image/') {
    showSnackbar('Only image files are allowed', 'error')

    return false
  }
  if (file.size > 3 * 1024 * 1024) {
    showSnackbar('File size should be less than 3MB', 'error')

    return false
  }

  removeKeyFromInputErrors('product_image')
  isEmptyProductImg.value = false

  productImage.value = { file, url: useObjectUrl(file).value ?? '' }

  formProductData.value.product_image = file
}

function removeKeyFromInputErrors(key) {
  inputErrors.value = removeKeyFromObject(inputErrors.value, key)
}

const handleProductDataSubmit = () => {
  formProductInfoRef.value?.validate().then(async result => {
    isEmptyProductImg.value = isEmpty(formProductData.value.product_image)

    if (result.valid && !isEmptyProductImg.value) {
      await addProduct()
    } else {
      scrollToTop()
    }
  })
}

const addProduct = async () => {
  try {
    isLoading.value = true
    serverErrors.value = []
    inputErrors.value = {}

    const formData = createProductFormData()

    const { data } = await ApiService.post('/admin/add-hl-product', formData)

    if (data.status === 200) {
      router.replace({ name: 'admin-hl-products-edit', params: { product_id: data.productData.id } })
    } else {
      isLoading.value = false
      if (data.errors) {
        inputErrors.value = data.errors
      }
      scrollToTop()
    }
  } catch (error) {
    isLoading.value = false
    console.error(error)
    if (error.response.errors) {
      inputErrors.value = error.response.errors
    }
    serverErrors.value = processErrors(error)
    scrollToTop()
  }
}

function createProductFormData() {
  const formData = new FormData()
  const productData = formProductData.value

  formData.append('product_name', productData.product_name)
  formData.append('active_ingredient', productData.active_ingredient)
  formData.append('product_form', productData.product_form)
  if (!isEmpty(productTypes.value)) {
    formData.append('product_type', productData.product_type)
  }
  formData.append(
    'product_description',
    !isEmpty(productData.product_description) ? productData.product_description : '',
  )
  formData.append('best_for', !isEmpty(productData.best_for) ? productData.best_for : '')
  if (productData.product_image) {
    formData.append('product_image', productData.product_image, productData.product_image.name)
  }
  formData.append('pharmacy_id', !isEmpty(productData.pharmacy_id) ? productData.pharmacy_id : '')

  return formData
}

onMounted(async () => {
  await fetchProductTypes('HL')
  if (productTypesError.value) {
    showSnackbar(productTypesError.value, 'error')
  }
})
</script>

<template>
  <div>
    <!-- 👉 Add Product Info -->
    <VCard
      title="Product Info"
      class="mb-5"
    >
      <VForm
        ref="formProductInfoRef"
        @submit.prevent="handleProductDataSubmit"
      >
        <VCardText>
          <VAlert
            v-if="isEmptyObject(inputErrors) && serverErrors.length > 0"
            type="error"
            variant="tonal"
            title="Validation failed!"
            class="mb-6"
          >
            <ul class="mb-0">
              <li
                v-for="error in serverErrors"
                :key="error"
                class="mb-0"
              >
                {{ error }}
              </li>
            </ul>
          </VAlert>

          <VRow>
            <VCol
              cols="12"
              :md="!isEmpty(productTypes) ? 6 : 4"
            >
              <VTextField
                v-model="formProductData.product_name"
                label="Product Name"
                :rules="[requiredValidator]"
                @keyup="removeKeyFromInputErrors('product_name')"
              />
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.product_name)"
                class="text-sm text-error mb-0 ms-3"
              >
                {{ inputErrors.product_name[0] }}
              </p>
            </VCol>

            <VCol
              cols="12"
              :md="!isEmpty(productTypes) ? 6 : 4"
            >
              <VTextField
                v-model="formProductData.active_ingredient"
                label="Active Ingredient"
                :rules="[requiredValidator]"
                @keyup="removeKeyFromInputErrors('active_ingredient')"
              />
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.active_ingredient)"
                class="text-sm text-error mb-0 ms-3"
              >
                {{ inputErrors.active_ingredient[0] }}
              </p>
            </VCol>

            <VCol
              cols="12"
              :md="!isEmpty(productTypes) ? 6 : 4"
            >
              <VTextField
                v-model="formProductData.product_form"
                label="Product Form"
                :rules="[requiredValidator]"
                @keyup="removeKeyFromInputErrors('product_form')"
              />
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.product_form)"
                class="text-sm text-error mb-0 ms-3"
              >
                {{ inputErrors.product_form[0] }}
              </p>
            </VCol>

            <VCol
              v-if="!isEmpty(productTypes)"
              cols="12"
              md="6"
            >
              <VSelect
                v-model="formProductData.product_type"
                label="Product Type"
                :rules="[requiredValidator]"
                :items="productTypes"
              />
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.product_type)"
                class="text-sm text-error mb-0 ms-3"
              >
                {{ inputErrors.product_type[0] }}
              </p>
            </VCol>

            <VCol cols="12">
              <TiptapEditor
                v-model="formProductData.product_description"
                class="border rounded basic-editor"
              />
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.product_description)"
                class="text-sm text-error mb-0 ms-3"
              >
                {{ inputErrors.product_description[0] }}
              </p>
            </VCol>

            <VCol
              cols="12"
              md="6"
            >
              <VSelect
                v-model="formProductData.pharmacy_id"
                label="Select Pharmacy"
                placeholder="Select"
                :items="pharmacyList"
                :rules="[requiredValidator]"
                clearable
              />
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.pharmacy_id)"
                class="text-sm text-error mb-0 ms-3"
              >
                {{ inputErrors.pharmacy_id[0] }}
              </p>
            </VCol>

            <VCol
              cols="12"
              md="6"
            >
              <VTextField
                v-model="formProductData.best_for"
                label="Perfect For"
                hint="Separate with comma for multiple values"
                persistent-hint
              />
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.best_for)"
                class="text-sm text-error mb-0 ms-3"
              >
                {{ inputErrors.best_for[0] }}
              </p>
            </VCol>

            <VCol
              cols="12"
              md="6"
            >
              <VCardText class="d-flex pa-0">
                <VAvatar
                  rounded
                  size="80"
                  class="me-6"
                  color="primary"
                  variant="tonal"
                >
                  <VImg
                    v-if="productImage.url"
                    :src="productImage.url"
                  />
                  <VIcon
                    v-else
                    size="80"
                    icon="tabler-box"
                  />
                </VAvatar>
                <div class="d-flex flex-column justify-center gap-4">
                  <div class="d-flex flex-wrap gap-2">
                    <VBtn
                      color="primary"
                      variant="tonal"
                      :loading="isProductImgUploading"
                      @click="inputProductImageRef.click()"
                    >
                      <VIcon
                        icon="tabler-cloud-upload"
                        class="d-sm-none"
                      />
                      <span class="d-none d-sm-block">Select Image</span>
                    </VBtn>

                    <input
                      ref="inputProductImageRef"
                      type="file"
                      name="file"
                      accept="image/*"
                      hidden
                      @input="onFileChange($event)"
                    />
                  </div>
                  <p class="text-body-2 mb-0">
                    Allowed only image files with size less than 2MB.
                  </p>
                </div>
              </VCardText>
              <p
                v-if="isEmptyProductImg"
                class="text-sm text-error mb-0 ms-3 mt-2"
              >
                Product image is required
              </p>
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.product_image)"
                class="text-sm text-error mb-0 ms-3 mt-2"
              >
                {{ inputErrors.product_image[0] }}
              </p>
            </VCol>
          </VRow>
        </VCardText>
        <VCardActions class="pa-6 pt-0 d-flex justify-end">
          <VBtn
            :to="{ name: 'admin-hl-products' }"
            color="secondary"
            variant="tonal"
            style="min-width: 150px"
          >
            Discard
          </VBtn>
          <VBtn
            type="submit"
            :loading="isLoading"
            variant="elevated"
            style="min-width: 150px"
          >
            Next
          </VBtn>
        </VCardActions>
      </VForm>
    </VCard>
  </div>
</template>

<style lang="scss">
.basic-editor {
  .ProseMirror {
    block-size: 200px;
    outline: none;
    overflow-y: auto;
    padding-inline: 0.5rem;
  }
}
</style>
