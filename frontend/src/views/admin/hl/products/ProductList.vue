<script setup>
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { refDebounced } from '@vueuse/core'
import { onMounted, ref, watch } from 'vue'
import { useRouter } from 'vue-router'

const globalData = useGlobalData()
const { showSnackbar } = globalData
const router = useRouter()

const searchQuery = ref('')
const debouncedSearchQuery = refDebounced(searchQuery, 300)
const selectedStatus = ref()
const dateRange = ref(null)
const fromDate = ref(null)
const toDate = ref(null)
const rowPerPage = ref(10)
const currentPage = ref(1)
const totalPage = ref(1)
const totalItems = ref(0)
const items = ref([])
const sortByColumnName = ref('created_at')
const sortDirection = ref('desc')
const isDialogVisible = ref(false)
const isDeleteDialogVisible = ref(false)
const itemId = ref('')
const itemStatus = ref(false)
const skeletonLoading = ref(true)

onMounted(() => {
  fetchItems()
})

const updateRoute = () => {
  router.replace({
    query: {
      page: currentPage.value,
      status: selectedStatus.value,
      searchQuery: searchQuery.value,
      sort_by: sortByColumnName.value,
      sort_dir: sortDirection.value,

      // from_date: fromDate.value,
      // to_date: toDate.value,
    },
  })
}

watch(dateRange, () => {
  if (isEmpty(dateRange.value)) {
    fromDate.value = null
    toDate.value = null

    return
  }

  const dateArr = dateRange.value.split(' to ')

  if (dateArr.length === 1) {
    fromDate.value = dateArr[0] || null
    toDate.value = dateArr[0] || null
  } else if (dateArr.length === 2) {
    fromDate.value = dateArr[0] || null
    toDate.value = dateArr[1] || null
  } else {
    fromDate.value = null
    toDate.value = null
  }
})

watch([debouncedSearchQuery, selectedStatus, fromDate, toDate, rowPerPage], () => {
  currentPage.value = 1
  fetchItems()
})

watch([currentPage, sortByColumnName, sortDirection], () => {
  fetchItems()
})

const fetchItems = () => {
  skeletonLoading.value = true

  ApiService.post('/admin/list-hl-products', {
    searchQuery: searchQuery.value,
    is_active: selectedStatus.value,
    perPage: rowPerPage.value,
    page: currentPage.value,
    sortByColumnName: sortByColumnName.value,
    isSortDirDesc: sortDirection.value,

    // from_date: fromDate.value,
    // to_date: toDate.value,
  })
    .then(({ data }) => {
      if (data.status === 200) {
        let startIndex = calculateStartIndex(data.productData.current_page, data.productData.per_page)
        if (currentPage.value > 1) {
          startIndex = (currentPage.value - 1) * rowPerPage.value + 1
        }

        items.value = data.productData.records.map((d, index) => ({
          ...d,
          sno: index + startIndex,
        }))
        totalPage.value = data.productData.totalPage == 0 ? 1 : data.productData.totalPage
        totalItems.value = data.productData.totalRecords

        updateRoute()
      } else {
        showSnackbar(data.message, 'error')
      }
    })
    .catch(error => {
      console.error(error)
      if (error.response.data.message) {
        showSnackbar(error.response.data.message, 'error')
      } else if (error.response.message) {
        showSnackbar(error.response.message, 'error')
      }
    })
    .finally(() => {
      skeletonLoading.value = false
    })
}

function calculateStartIndex(pageNo, pageSize) {
  if (pageNo <= 0 || pageSize <= 0) {
    return 1
  }

  return (pageNo - 1) * pageSize + 1
}

const sortColumn = columnName => {
  if (sortByColumnName.value !== '' && sortByColumnName.value === columnName && sortDirection.value === 'asc') {
    sortDirection.value = 'desc'
  } else {
    sortDirection.value = 'asc'
  }

  sortByColumnName.value = columnName
}

// 👉 search filters
const status = [
  {
    title: 'Active',
    value: 1,
  },
  {
    title: 'Inactive',
    value: 0,
  },
]

const resolveItemStatusVariant = stat => {
  if (stat === 1) {
    return { color: 'success', status: 'Active' }
  } else {
    return { color: 'secondary', status: 'Inactive' }
  }
}

const resolveItemStatus = stat => {
  if (stat === 1) {
    return { color: 'success', status: true }
  } else {
    return { color: 'secondary', status: false }
  }
}

const onEditClick = id => {
  itemId.value = id
}

const onAccountStatusChange = (id, status) => {
  isDialogVisible.value = true
  itemId.value = id
  itemStatus.value = status
}

const changeItemStatus = () => {
  globalData.circularProgress = true

  let itemsValue = items.value
  let itemIndex = itemsValue.findIndex(item => item.id === itemId.value)

  if (itemIndex === -1) {
    console.error('Item not found')
    globalData.circularProgress = false

    return
  }

  let newStatus = itemsValue[itemIndex].is_active === 1 ? 0 : 1

  ApiService
    .post('/admin/update-hl-product-status', { id: itemId.value })
    .then(response => {
      if (response.data.status === 200) {
        itemsValue[itemIndex].is_active = newStatus
        items.value = itemsValue
        itemId.value = ''
        itemStatus.value = false
        showSnackbar(response.data.message)

        fetchItems()
      } else {
        showSnackbar(response.data.message, 'error')
      }
    })
    .catch(error => {
      console.error(error)
    })
    .finally(() => {
      globalData.circularProgress = false
      isDialogVisible.value = false
    })
}

const onDeleteDialog = id => {
  isDeleteDialogVisible.value = true
  itemId.value = id
}

const deleteProduct = () => {
  globalData.circularProgress = true

  ApiService
    .delete(`/admin/delete-hl-product/${itemId.value}`)
    .then(response => {
      if (response.data.status === 200) {
        isDeleteDialogVisible.value = false
        itemId.value = ''
        showSnackbar(response.data.message)

        fetchItems()
      } else if (response.data.status === 409) {
        isDeleteDialogVisible.value = false
        if (response.data.message) {
          showSnackbar(response.data.message, 'error')
        }
      } else {
        if (response.data.message) {
          showSnackbar(response.data.message, 'error')
        }
      }
    })
    .catch(error => {
      console.error(error)
    })
    .finally(() => {
      globalData.circularProgress = false
    })
}

// 👉 Computing pagination data
const paginationData = computed(() => {
  const currentPageValue = currentPage.value <= 0 ? 1 : currentPage.value

  const firstIndex = items.value.length
    ? (currentPageValue - 1) * rowPerPage.value + 1
    : 0

  const lastIndex = items.value.length + (currentPageValue - 1) * rowPerPage.value

  return `Showing ${firstIndex} to ${lastIndex} of ${totalItems.value} entries`
})
</script>

<template>
  <section>
    <VRow>
      <VCol cols="12">
        <VCard title="Filter">
          <!-- 👉 Filters -->
          <VCardText>
            <VRow>
              <VCol
                cols="12"
                sm="4"
              >
                <AppSelect
                  v-model="selectedStatus"
                  label="Select Status"
                  :items="status"
                  clearable
                  clear-icon="tabler-x"
                  placeholder="Select"
                />
              </VCol>
              <!--
                <VCol
                cols="12"
                sm="4"
                >
                <AppDateTimePicker
                v-model="dateRange"
                label="Created Date"
                variant="outlined"
                :config="{ mode: 'range', dateFormat: 'm/d/Y' }"
                clearable
                clear-icon="tabler-x"
                />
                </VCol>
              -->
            </VRow>
          </VCardText>

          <VDivider />

          <VCardText class="d-flex flex-wrap py-4 gap-4">
            <div
              class="me-3"
              style="width: 80px"
            >
              <VSelect
                v-model="rowPerPage"
                density="compact"
                variant="outlined"
                :items="[10, 20, 30, 50]"
              />
            </div>

            <VSpacer />

            <div class="d-flex align-center flex-wrap gap-4">
              <!-- 👉 Search  -->
              <div style="width: 23rem">
                <VTextField
                  v-model="searchQuery"
                  placeholder="Search"
                  density="compact"
                  clearable
                  clear-icon="tabler-x"
                />
              </div>
              <!-- 👉 Add button -->

              <VBtn
                variant="tonal"
                color="primary"
                :to="{ name: 'admin-hl-products-add' }"
              >
                Add New Product
              </VBtn>
            </div>
          </VCardText>

          <VDivider />

          <VTable class="text-no-wrap">
            <!-- 👉 table head -->
            <thead>
              <tr>
                <th scope="col">
                  #
                </th>
                <th scope="col">
                  Product
                </th>
                <th scope="col">
                  Pharmacy
                </th>
                <th scope="col">
                  STATUS
                </th>
                <th scope="col">
                  ACTIVE/INACTIVE
                </th>
                <th
                  scope="col"
                  class="cursor-pointer"
                  @click="sortColumn('created_at')"
                >
                  <div class="w-125px">
                    CREATED AT
                    <span class="d-grid float-right">
                      <VIcon
                        size="10"
                        icon="tabler-chevron-up"
                      />
                      <VIcon
                        size="10"
                        icon="tabler-chevron-down"
                      />
                    </span>
                  </div>
                </th>
                <th scope="col">
                  ACTIONS
                </th>
              </tr>
            </thead>

            <!-- 👉 table body -->
            <tbody v-if="skeletonLoading">
              <tr
                v-for="n in 10"
                :key="n"
              >
                <td
                  v-for="s in 7"
                  :key="s"
                >
                  <Skeleton
                    width="8rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
              </tr>
            </tbody>
            <tbody v-else>
              <tr
                v-for="item in items"
                :key="item.id"
                style="height: 3.75rem"
              >
                <!-- 👉 NO -->
                <td>
                  {{ item.sno }}
                </td>

                <!-- 👉 Product -->
                <td class="title">
                  <RouterLink
                    :to="{ name: 'admin-hl-products-edit', params: { product_id: item.id } }"
                    class="d-flex"
                  >
                    <VAvatar
                      variant="tonal"
                      size="40"
                      class="me-3"
                      rounded
                    >
                      <VImg
                        v-if="item.product_image"
                        :src="item.product_image"
                        cover
                      />
                      <VIcon
                        v-else
                        icon="tabler-image"
                        size="40"
                      />
                    </VAvatar>
                    <div>
                      <h6 class="text-base">
                        {{ item.product_name }}
                      </h6>
                      <span class="text-body-2">{{ item.active_ingredient }}</span>
                    </div>
                  </RouterLink>
                </td>

                <!-- 👉 Pharmacy -->
                <td>
                  {{ item.pharmacy_name ?? '-' }}
                </td>

                <!-- 👉 Status -->
                <td>
                  <VChip
                    label
                    :color="resolveItemStatusVariant(item.is_active).color"
                    size="small"
                    class="text-capitalize"
                  >
                    {{ resolveItemStatusVariant(item.is_active).status }}
                  </VChip>
                </td>

                <!-- 👉 Status Change -->
                <td>
                  <VSwitch
                    v-model="resolveItemStatus(item.is_active).status"
                    inset
                    @change="onAccountStatusChange(item.id, resolveItemStatus(item.is_active).status)"
                  />
                </td>

                <!-- 👉 Created At -->
                <td>
                  <div class="d-flex flex-column">
                    <span>{{ item.created_date }}</span>
                    <span>{{ item.created_time }}</span>
                  </div>
                </td>

                <!-- 👉 Actions -->
                <td
                  class="text-center"
                  style="width: 5rem"
                >
                  <VMenu>
                    <template #activator="{ props }">
                      <VBtn
                        v-bind="props"
                        variant="tonal"
                        color="secondary"
                        rounded="lg"
                        size="x-small"
                        icon
                      >
                        <VIcon
                          icon="mdi-dots-horizontal"
                          size="22"
                        />
                      </VBtn>
                    </template>

                    <VList>
                      <VListItem :to="{ name: 'admin-hl-products-edit', params: { product_id: item.id } }">
                        <VListItemTitle>
                          <div class="font-weight-medium">
                            Edit Product
                          </div>
                        </VListItemTitle>
                      </VListItem>

                      <VListItem :to="{ name: 'admin-hl-product-overview', params: { product_id: item.id } }">
                        <VListItemTitle>
                          <div class="font-weight-medium">
                            Edit Overview
                          </div>
                        </VListItemTitle>
                      </VListItem>

                      <VListItem @click="onDeleteDialog(item.id)">
                        <VListItemTitle>
                          <div class="font-weight-medium text-error">
                            Delete Product
                          </div>
                        </VListItemTitle>
                      </VListItem>
                    </VList>
                  </VMenu>
                </td>
              </tr>
            </tbody>

            <!-- 👉 table footer  -->
            <tfoot v-show="!skeletonLoading && items.length === 0">
              <tr>
                <td
                  colspan="8"
                  class="text-center"
                >
                  No data available
                </td>
              </tr>
            </tfoot>
          </VTable>

          <VDivider />

          <VCardText class="d-flex align-center flex-wrap justify-space-between gap-4 py-3 px-5">
            <span class="text-sm text-disabled">
              {{ paginationData }}
            </span>
            <VPagination
              v-model="currentPage"
              size="small"
              :total-visible="5"
              :length="totalPage"
            />
          </VCardText>
        </VCard>
      </VCol>
    </VRow>

    <!-- 👉 Change Status -->
    <VDialog
      v-model="isDialogVisible"
      persistent
      class="v-dialog-sm"
    >
      <!-- Dialog close btn -->
      <DialogCloseBtn @click="isDialogVisible = !isDialogVisible" />

      <!-- Dialog Content -->
      <VCard title="Are you sure?">
        <VCardText> Are you sure you want to change status for this item? </VCardText>

        <VCardText class="d-flex justify-end gap-3 flex-wrap">
          <VBtn
            color="secondary"
            variant="tonal"
            @click="isDialogVisible = false"
          >
            No
          </VBtn>
          <VBtn @click="changeItemStatus">
            Yes
          </VBtn>
        </VCardText>
      </VCard>
    </VDialog>

    <!-- 👉 Delete -->
    <VDialog
      v-model="isDeleteDialogVisible"
      persistent
      class="v-dialog-sm"
    >
      <!-- Dialog close btn -->
      <DialogCloseBtn @click="isDeleteDialogVisible = !isDeleteDialogVisible" />

      <!-- Dialog Content -->
      <VCard title="Are you sure?">
        <VCardText> Are you sure you want to delete this item? </VCardText>

        <VCardText class="d-flex justify-end gap-3 flex-wrap">
          <VBtn
            color="secondary"
            variant="tonal"
            @click="isDeleteDialogVisible = false"
          >
            No
          </VBtn>
          <VBtn @click="deleteProduct">
            Yes
          </VBtn>
        </VCardText>
      </VCard>
    </VDialog>
  </section>
</template>

<style lang="scss">
.v-table__wrapper td.title {
  min-width: 200px;
  h6 {
    a {
      white-space: pre-line;
    }
  }
}
</style>
