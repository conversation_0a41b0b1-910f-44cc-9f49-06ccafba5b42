<script setup>
import { isEmpty } from '@/@core/utils'
import Image from 'primevue/image'

const props = defineProps({
  productData: {
    type: Object,
    required: true,
  },
  loading: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['edit'])

const handleProductEdit = () => {
  emit('edit', true)
}
</script>

<template>
  <VCard
    v-if="loading"
    class="mb-5"
    title="Product Info"
  >
    <VCardText>
      <div class="d-flex flex-column flex-md-row">
        <div class="me-0 me-md-5">
          <Skeleton
            class="me-3"
            height="150px"
            width="150px"
          />
        </div>
        <VTable class="w-100 table-fix">
          <tbody>
            <tr>
              <td class="text-base">
                <Skeleton height="20px" />
              </td>
            </tr>
            <tr>
              <td class="text-base">
                <Skeleton height="20px" />
              </td>
            </tr>
            <tr>
              <td class="text-base">
                <Skeleton height="20px" />
              </td>
            </tr>
            <tr>
              <td class="text-base">
                <Skeleton height="70px" />
              </td>
            </tr>
          </tbody>
        </VTable>
      </div>
    </VCardText>
  </VCard>
  <VCard
    v-else
    class="mb-5"
    title="Product Info"
  >
    <VCardText>
      <div class="d-flex flex-column flex-md-row">
        <div class="me-0 me-md-5">
          <VAvatar
            variant="tonal"
            color="primary"
            size="150"
            class="me-3 rounded-lg"
          >
            <Image
              :src="productData.product_image"
              height="150"
              width="150"
              image-class="object-fit-cover"
              preview
            />
          </VAvatar>
        </div>
        <VTable class="w-100 table-fix">
          <tbody>
            <tr>
              <td class="text-base text-no-wrap">
                Product Name
              </td>
              <td class="text-base">
                {{ !isEmpty(productData.product_name) ? productData.product_name : "-" }}
              </td>
            </tr>
            <tr>
              <td class="text-base text-no-wrap">
                Active Ingredient
              </td>
              <td class="text-base">
                {{ !isEmpty(productData.active_ingredient) ? productData.active_ingredient : "-" }}
              </td>
            </tr>
            <tr>
              <td class="text-base text-no-wrap">
                Product Form
              </td>
              <td class="text-base">
                {{ !isEmpty(productData.product_form) ? productData.product_form : "-" }}
              </td>
            </tr>
            <tr v-if="!isEmpty(productData.product_type)">
              <td class="text-base text-no-wrap">
                Product Type
              </td>
              <td class="text-base">
                {{ !isEmpty(productData.product_type) ? productData.product_type : "-" }}
              </td>
            </tr>
            <tr>
              <td class="text-base text-no-wrap">
                Product Pharmacy
              </td>
              <td class="text-base">
                {{ !isEmpty(productData.pharmacy_name) ? productData.pharmacy_name : "-" }}
              </td>
            </tr>
            <tr>
              <td class="text-base text-no-wrap">
                Product Description
              </td>
              <td class="text-base">
                <span
                  v-if="!isEmpty(productData.product_description)"
                  v-html="productData.product_description"
                />
                <span v-else> - </span>
              </td>
            </tr>
            <tr>
              <td class="text-base text-no-wrap">
                Perfect For
              </td>
              <td class="text-base">
                {{ !isEmpty(productData.best_for) ? productData.best_for : "-" }}
              </td>
            </tr>
          </tbody>
        </VTable>
      </div>
    </VCardText>
    <VCardActions class="d-flex justify-space-between">
      <VBtn
        variant="text"
        @click="handleProductEdit"
      >
        <VIcon icon="tabler-edit" /> Edit
      </VBtn>
      <div
        v-if="productData.updated_date && productData.updated_time"
        class="text-xs text-end"
      >
        <div>
          Last updated: {{ productData.updated_date }} {{ productData.updated_time }}
        </div>
        <div v-if="productData.action_by_user_name">
          By: {{ productData.action_by_user_name }}
        </div>
      </div>
    </VCardActions>
  </VCard>
</template>
