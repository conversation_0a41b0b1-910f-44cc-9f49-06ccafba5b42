<script setup>
import { isEmpty, parseFloatOrZero } from '@/@core/utils'
import { alertWarning } from '@/plugins/sweetalert2'
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { ceilToDecimalPlaces, formatCurrency } from '@/utils/helpers'
import Image from 'primevue/image'
import { ref } from 'vue'
import QtyEditForm from './QtyEditForm.vue'
import StrengthForm from './StrengthForm.vue'

const props = defineProps({
  productData: {
    type: Object,
    required: true,
  },
  strengths: {
    type: Array,
    required: true,
  },
})

const emit = defineEmits(['updateList'])

const globalData = useGlobalData()
const { showSnackbar }  = globalData

const selectedStrengthId = ref('')

const handleDeleteStrength = strengthId => {
  alertWarning.fire({
    title: 'Confirm Deletion',
    text: 'Are you sure you want to delete this strength?',
    icon: 'warning',
    showCancelButton: true,
  }).then(result => {
    if (result.isConfirmed) {
      deleteStrength(strengthId)
    }
  })
}

const deleteStrength = async strengthId => {
  try {
    globalData.circularProgress = true

    const { data } = await ApiService.delete(`/admin/delete-hl-product-strength/${strengthId}`)

    if (data.status === 200) {
      showSnackbar(data.message)
      emit('updateList', true)
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    console.error(error)
  } finally {
    globalData.circularProgress = false
  }
}

const activeQtyEditId = ref('')

const editQty = id => {
  activeQtyEditId.value = id
}

const handleQtyUpdated = newQtyData => {
  activeQtyEditId.value = ''
  emit('updateList', true)
}

const handleDiscardQtyEdit = () => {
  activeQtyEditId.value = ''
}

const deleteQty = id => {
  alertWarning.fire({
    title: 'Confirm Deletion',
    text: 'Are you sure you want to delete this quantity?',
    icon: 'warning',
    showCancelButton: true,
  }).then(async result => {
    if (result.isConfirmed) {
      try {
        globalData.circularProgress = true

        const { data } = await ApiService.delete(`/admin/delete-hl-product-qty/${id}`)

        if (data.status === 200) {
          showSnackbar(data.message)
          emit('updateList', true)
        } else {
          showSnackbar(data.message, 'error')
        }
      } catch (error) {
        console.error(error)
      } finally {
        globalData.circularProgress = false
      }
    }
  })
}

// const isStatusLoading = ref(false)

const resolveItemStatus = status => {
  if (status === 1) {
    return {
      label: 'Active',
      status: true,
      color: 'success',
    }
  } else {
    return {
      label: 'Inactive',
      status: false,
      color: 'secondary',
    }
  }
}

const onStrengthStatusChange = (strength, status) => {
  alertWarning.fire({
    title: 'Confirm Status Change',
    text: `Are you sure you want to ${status ? 'deactivate' : 'activate'} ${strength.strength + ' ' + strength.strength_unit} strength?`,
    icon: 'warning',
    showCancelButton: true,
  }).then(result => {
    if (result.isConfirmed) {
      toggleStrengthStatus(strength.id)
    }
  })
}

const toggleStrengthStatus = async id => {
  try {
    globalData.circularProgress = true

    const { data } = await ApiService.get(`/admin/update-hl-strength-status/${id}`)

    if (data.status === 200) {
      showSnackbar(data.message)
      emit('updateList', true)
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    console.error(error)
  } finally {
    globalData.circularProgress = false
  }
}

const onQtyStatusChange = (qty, status) => {
  alertWarning.fire({
    title: 'Confirm Status Change',
    text: `Are you sure you want to ${status ? 'deactivate' : 'activate'} ${qty.qty + ' ' + props.productData.product_form + ' /month'} quantity?`,
    icon: 'warning',
    showCancelButton: true,
  }).then(result => {
    if (result.isConfirmed) {
      toggleQtyStatus(qty.id)
    }
  })
}

const toggleQtyStatus = async id => {
  try {
    globalData.circularProgress = true

    const { data } = await ApiService.get(`/admin/update-hl-qty-status/${id}`)

    if (data.status === 200) {
      showSnackbar(data.message)
      emit('updateList', true)
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    console.error(error)
  } finally {
    globalData.circularProgress = false
  }
}

const onPlanStatusChange = (plan, status) => {
  alertWarning.fire({
    title: 'Confirm Status Change',
    text: `Are you sure you want to ${status ? 'deactivate' : 'activate'} ${plan.interval} ${plan.interval === 1 ? 'month' : 'months'} plan?`,
    icon: 'warning',
    showCancelButton: true,
  }).then(result => {
    if (result.isConfirmed) {
      togglePlanStatus(plan.id)
    }
  })
}

const togglePlanStatus = async id => {
  try {
    globalData.circularProgress = true

    const { data } = await ApiService.get(`/admin/update-hl-subscription-plan-status/${id}`)

    if (data.status === 200) {
      showSnackbar(data.message)
      emit('updateList', true)
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    console.error(error)
  } finally {
    globalData.circularProgress = false
  }
}

function getDiscountHint(plan) {
  if (plan.plan_interval_discount_type === 'percentage') {
    const totalAmount = parseFloatOrZero(plan.plan_interval_price)
    const discountPercentage = parseFloatOrZero(plan.plan_interval_discount)
    const discountAmount = (totalAmount * discountPercentage) / 100

    return `${formatCurrency(discountAmount.toFixed(2))} off`
  }

  return undefined
}
</script>

<template>
  <VExpansionPanels>
    <VExpansionPanel
      v-for="strength in strengths"
      :key="strength.id"
      class="mb-5"
    >
      <VExpansionPanelTitle class="pa-5">
        <h5 class="text-h5">
          Strength: {{ strength.strength }} {{ strength.strength_unit }}
        </h5>
        <VChip
          v-if="strength.is_recommended === 1"
          color="info"
          variant="tonal"
          size="small"
          class="ms-2"
          label
        >
          Popular
        </VChip>
      </VExpansionPanelTitle>
      <VExpansionPanelText>
        <StrengthForm
          v-if="selectedStrengthId === strength.id"
          form-type="edit"
          :strength-id="strength.id"
          :product-data="productData"
          :strengths="strengths"
          @update-list="emit('updateList', true)"
          @close-form="selectedStrengthId = ''"
        />
        <VCard
          v-else
          variant="flat"
        >
          <VCardText>
            <div class="d-flex flex-column flex-md-row">
              <div class="me-0 me-md-5">
                <VAvatar
                  variant="tonal"
                  color="primary"
                  size="150"
                  class="me-3 rounded-lg"
                >
                  <Image
                    :src="strength.strength_image"
                    height="150"
                    width="150"
                    image-class="object-fit-cover"
                    preview
                  />
                </VAvatar>
              </div>
              <VTable class="w-100 table-fix">
                <tbody>
                  <tr>
                    <td class="text-base text-no-wrap">
                      Strength
                    </td>
                    <td class="text-base">
                      {{ strength.strength }} {{ strength.strength_unit }}
                    </td>
                  </tr>
                  <tr>
                    <td class="text-base text-no-wrap">
                      NDC / Product ID
                    </td>
                    <td class="text-base">
                      {{ strength.ndc }}
                    </td>
                  </tr>
                  <tr>
                    <td class="text-base text-no-wrap">
                      Med ID
                    </td>
                    <td class="text-base">
                      {{ !isEmpty(strength.med_id) ? strength.med_id : '-' }}
                    </td>
                  </tr>
                  <tr>
                    <td class="text-base text-no-wrap">
                      Drug Name
                    </td>
                    <td class="text-base">
                      {{ !isEmpty(strength.drug_name) ? strength.drug_name : '-' }}
                    </td>
                  </tr>
                  <tr>
                    <td class="text-base text-no-wrap">
                      Dose Instruction
                    </td>
                    <td class="text-base">
                      {{ !isEmpty(strength.dose_instruction) ? strength.dose_instruction : '-' }}
                    </td>
                  </tr>
                  <tr>
                    <td class="text-base text-no-wrap">
                      Status
                    </td>
                    <td class="text-base">
                      <VSwitch
                        v-model="resolveItemStatus(strength.is_active).status"
                        :label="resolveItemStatus(strength.is_active).label"
                        inset
                        @change="onStrengthStatusChange(strength, resolveItemStatus(strength.is_active).status)"
                      />
                    </td>
                  </tr>
                </tbody>
              </VTable>
            </div>

            <VCardActions class="pa-0 mt-2 d-flex justify-space-between">
              <div>
                <VBtn
                  variant="text"
                  @click="selectedStrengthId = strength.id"
                >
                  <VIcon
                    icon="tabler-edit"
                    start
                  /> Edit Strength
                </VBtn>
                <VBtn
                  variant="text"
                  color="error"
                  @click="handleDeleteStrength(strength.id)"
                >
                  <VIcon
                    icon="tabler-trash"
                    start
                  /> Delete Strength
                </VBtn>
              </div>

              <div
                v-if="strength.updated_date && strength.updated_time"
                class="text-xs text-end"
              >
                <div>
                  Last updated: {{ strength.updated_date }} {{ strength.updated_time }}
                </div>
                <div v-if="strength.action_by_user_name">
                  By: {{ strength.action_by_user_name }}
                </div>
              </div>
            </VCardActions>

            <VExpansionPanels class="mt-5">
              <VExpansionPanel
                v-for="qty in strength.hl_product_qtys"
                :key="qty.id"
                class="border-md border-dashed mt-5"
              >
                <VExpansionPanelTitle class="pa-5">
                  <h6 class="text-h6">
                    Quantity: {{ qty.qty }} {{ productData.product_form }} / month
                  </h6>
                  <VChip
                    v-if="qty.is_recommended === 1"
                    color="info"
                    variant="tonal"
                    size="small"
                    class="ms-2"
                    label
                  >
                    Popular
                  </VChip>
                </VExpansionPanelTitle>
                <VExpansionPanelText>
                  <VCard
                    v-if="activeQtyEditId === qty.id"
                    variant="flat"
                  >
                    <QtyEditForm
                      :qty="qty"
                      :product-form="productData.product_form"
                      :strength="strength"
                      @qty-updated="handleQtyUpdated"
                      @discard-qty-edit="handleDiscardQtyEdit"
                    />
                  </VCard>
                  <VCard
                    v-else
                    variant="flat"
                  >
                    <VCardText class="pa-1 pt-0">
                      <div>
                        <p class="text-subtitle-1 mb-1 d-flex align-items-center">
                          <span>Status:</span>
                          <span class="ms-2">
                            <VSwitch
                              v-model="resolveItemStatus(qty.is_active).status"
                              :label="resolveItemStatus(qty.is_active).label"
                              inset
                              @change="onQtyStatusChange(qty, resolveItemStatus(qty.is_active).status)"
                            />
                          </span>
                        </p>
                      </div>
                      <div class="mt-2">
                        <p class="text-subtitle-1 mb-1">
                          Subscription Plans
                        </p>
                        <div class="d-flex flex-wrap gap-3">
                          <VCard
                            v-for="plan in qty.hl_product_subscription_plans"
                            :key="plan.id"
                            class="text-base text-wrap flex-grow-1 border-md border-dashed"
                            variant="flat"
                          >
                            <VCardTitle class="d-flex align-items-center gap-2">
                              <h5 class="text-h5">
                                Billed every {{ plan.interval }} month
                              </h5>
                              <VChip
                                v-if="plan.is_recommended === 1"
                                color="info"
                                variant="tonal"
                                size="x-small"
                                label
                              >
                                Popular
                              </VChip>
                            </VCardTitle>
                            <VCardText class="pa-4 pt-0">
                              <span class="d-block">
                                Unit price: <span class="text-high-emphasis font-weight-bold">
                                  {{ formatCurrency(ceilToDecimalPlaces(plan.subscription_plan_price_after_discount / (qty.qty * plan.interval))) }}
                                </span>
                                <span
                                  v-if="plan.plan_interval_discount > 0"
                                  class="text-decoration-line-through ms-1"
                                >
                                  {{ formatCurrency(ceilToDecimalPlaces(plan.plan_interval_price / (qty.qty * plan.interval))) }}
                                </span>
                              </span>
                              <span
                                v-if="plan.product_cost"
                                class="d-block"
                              >
                                Base price: <span class="text-high-emphasis font-weight-bold">
                                  {{ formatCurrency(plan.product_cost) }}
                                </span>
                              </span>
                              <span class="d-block">
                                Selling price: <span class="text-high-emphasis font-weight-bold">
                                  {{ formatCurrency(plan.subscription_plan_price_after_discount) }}
                                </span>
                                <span
                                  v-if="plan.plan_interval_discount > 0"
                                  class="text-decoration-line-through ms-1"
                                >
                                  {{ formatCurrency(ceilToDecimalPlaces(plan.plan_interval_price, 2)) }}
                                </span>
                              </span>
                              <span
                                v-if="plan.plan_interval_discount > 0"
                                class="d-block"
                              >
                                Discount: <span class="text-high-emphasis font-weight-bold">
                                  <span v-if="plan.plan_interval_discount_type === 'percentage'">
                                    {{ plan.plan_interval_discount }}% <span v-if="getDiscountHint(plan)">({{ getDiscountHint(plan) }})</span>
                                  </span>
                                  <span v-else>{{ formatCurrency(plan.plan_interval_discount) }}</span>
                                </span>
                              </span>
                              <span
                                v-if="plan.product_cost"
                                class="d-block"
                              >
                                Net Revenue: <span class="text-high-emphasis font-weight-bold">
                                  {{ formatCurrency(ceilToDecimalPlaces(parseFloatOrZero(plan.subscription_plan_price_after_discount) - parseFloatOrZero(plan.product_cost))) }}
                                </span>
                              </span>

                              <span class="d-flex align-items-center mt-4">
                                <span>Status:</span>
                                <span class="ms-2">
                                  <VSwitch
                                    v-model="resolveItemStatus(plan.is_active).status"
                                    :label="resolveItemStatus(plan.is_active).label"
                                    inset
                                    @change="onPlanStatusChange(plan, resolveItemStatus(plan.is_active).status)"
                                  />
                                </span>
                              </span>

                              <div
                                v-if="plan.updated_date && plan.updated_time"
                                class="text-xs mt-6"
                              >
                                <div>
                                  Last updated: {{ plan.updated_date }} {{ plan.updated_time }}
                                </div>
                                <div v-if="plan.action_by_user_name">
                                  By: {{ plan.action_by_user_name }}
                                </div>
                              </div>
                            </VCardText>
                          </VCard>
                        </div>
                      </div>
                    </VCardText>
                    <VCardActions class="pa-0 pt-1 d-flex justify-space-between">
                      <div>
                        <VBtn
                          variant="text"
                          @click="editQty(qty.id)"
                        >
                          <VIcon icon="tabler-edit" /> Edit
                        </VBtn>
                        <VBtn
                          variant="text"
                          color="error"
                          @click="deleteQty(qty.id)"
                        >
                          <VIcon icon="tabler-trash" /> Delete
                        </VBtn>
                      </div>

                      <div
                        v-if="qty.updated_date && qty.updated_time"
                        class="text-xs text-end"
                      >
                        <div>
                          Last updated: {{ qty.updated_date }} {{ qty.updated_time }}
                        </div>
                        <div v-if="qty.action_by_user_name">
                          By: {{ qty.action_by_user_name }}
                        </div>
                      </div>
                    </VCardActions>
                  </VCard>
                </VExpansionPanelText>
              </VExpansionPanel>
            </VExpansionPanels>
          </VCardText>
        </VCard>
      </VExpansionPanelText>
    </VExpansionPanel>
  </VExpansionPanels>
</template>
