<script setup>
import { isEmpty, isEmptyObject, isNullOrUndefined, parseFloatOrZero } from '@/@core/utils'
import { requiredValidator } from '@/@core/utils/validators'
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { processErrors } from '@/utils/errorHandler'
import { ceilToDecimalPlaces, formatCurrency, isNumber } from '@/utils/helpers'
import { v4 as uuidv4 } from 'uuid'
import { onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'

const props = defineProps({
  qty: {
    type: Object,
    required: true,
  },
  productForm: {
    type: String,
  },
  strength: {
    type: Object,
  },
})

const emit = defineEmits(['qtyUpdated', 'discardQtyEdit'])

const route = useRoute()
const { showSnackbar } = useGlobalData()

const productId = ref(route.params.product_id)
const serverErrors = ref([])
const inputErrors = ref({})
const qtyForm = ref({})
const formQtyRef = ref(null)
const isLoading = ref(false)

const recommendedOptions = [
  {
    title: 'Yes',
    value: 1,
  },
  {
    title: 'No',
    value: 0,
  },
]

const intervals = [
  { value: 1, title: 'Monthly' },
  { value: 2, title: '2 Months' },
  { value: 3, title: '3 Months' },
  { value: 4, title: '4 Months' },
  { value: 5, title: '5 Months' },
  { value: 6, title: '6 Months' },
  { value: 7, title: '7 Months' },
  { value: 8, title: '8 Months' },
  { value: 9, title: '9 Months' },
  { value: 10, title: '10 Months' },
  { value: 11, title: '11 Months' },
  { value: 12, title: 'Yearly' },
]

const selectedIntervals = ref([])

const availableIntervals = computed(() => {
  return intervals.filter(interval => !selectedIntervals.value.includes(interval.value))
})

const updateSelectedIntervals = plan => {
  selectedIntervals.value[plan.plan_id] = plan.interval
}

onMounted(() => {
  initForm()
})

const initForm = () => {
  qtyForm.value = {
    ...props.qty,
    subscription_plans: props.qty.hl_product_subscription_plans.map(plan => {
      let planId = uuidv4()
      plan.plan_id = planId
      selectedIntervals.value[planId] = plan.interval

      return plan
    }),
  }
  delete qtyForm.value.hl_product_subscription_plans
}

const addNewPlan = () => {
  const planId = uuidv4()

  const newPlan = {
    plan_id: planId,
    interval: null,
    is_recommended: 0,
    plan_interval_price: '',
    product_cost: '',
    plan_interval_discount_type: 'fixed',
    plan_interval_discount: '',
  }

  qtyForm.value.subscription_plans.push(newPlan)

  selectedIntervals.value[planId] = null
}

const deletePlan = plan => {
  delete selectedIntervals.value[plan.plan_id]
  qtyForm.value.subscription_plans = qtyForm.value.subscription_plans.filter(p => p.plan_id !== plan.plan_id)
}

const isPlanRecommendDisabled = plan => {
  const plans = qtyForm.value.subscription_plans

  return plans.some(p => {
    return p.plan_id !== plan.plan_id && p.is_recommended === 1
  })
}

const maxDiscountValidator = plan => {
  if (isEmpty(plan.plan_interval_discount)) return true

  if (plan.plan_interval_discount_type === 'fixed') {
    return (parseFloatOrZero(plan.plan_interval_discount) < parseFloatOrZero(plan.plan_interval_price)) || 'Discount must be less than selling price'
  } else if (plan.plan_interval_discount_type === 'percentage') {
    return (parseFloatOrZero(plan.plan_interval_discount) < 100) || 'Discount must be less than 100%'
  } else {
    return true
  }
}

const maxProductCostValidator = plan => {
  if (isEmpty(plan.product_cost) || isEmpty(plan.plan_interval_price)) return true

  return (parseFloatOrZero(plan.product_cost) <= parseFloatOrZero(plan.plan_interval_price)) || 'Base price should not exceed selling price'
}

const calculatedPriceForPlan = plan => {
  const quantity = qtyForm.value

  let intervalPrice = 0
  let intervalPriceAfterDiscount = 0

  if (plan.plan_interval_discount_type === 'fixed') {
    intervalPrice = ceilToDecimalPlaces(parseFloatOrZero(plan.plan_interval_price))
    intervalPriceAfterDiscount = ceilToDecimalPlaces(intervalPrice - parseFloatOrZero(plan.plan_interval_discount))
  } else if (plan.plan_interval_discount_type === 'percentage') {
    intervalPrice = ceilToDecimalPlaces(parseFloatOrZero(plan.plan_interval_price))
    intervalPriceAfterDiscount = ceilToDecimalPlaces(intervalPrice - (intervalPrice * parseFloatOrZero(plan.plan_interval_discount) / 100))
  }

  const monthlyPrice = ceilToDecimalPlaces(intervalPrice / parseFloatOrZero(plan.interval))
  const monthlyPriceAfterDiscount = ceilToDecimalPlaces(intervalPriceAfterDiscount / parseFloatOrZero(plan.interval))

  const unitPrice = monthlyPrice > 0 && quantity.qty > 0 ? ceilToDecimalPlaces(parseFloatOrZero(monthlyPrice) / parseFloatOrZero(quantity.qty)) : 0
  const unitPriceAfterDiscount = monthlyPriceAfterDiscount > 0 && quantity.qty > 0 ? ceilToDecimalPlaces(parseFloatOrZero(monthlyPriceAfterDiscount) / parseFloatOrZero(quantity.qty)) : 0

  const netRevenue = ceilToDecimalPlaces(intervalPriceAfterDiscount - parseFloatOrZero(plan.product_cost))

  return {
    unitPrice: `
      Unit Price: <span class="text-base font-weight-bold text-high-emphasis">${formatCurrency(unitPriceAfterDiscount)}</span> ${plan.plan_interval_discount > 0 ? `(before discount: ${formatCurrency(unitPrice)})` : ''}
    `,
    monthlyPrice: `
      Monthly price: <span class="text-base font-weight-bold text-high-emphasis">${formatCurrency(monthlyPriceAfterDiscount)}</span> ${plan.plan_interval_discount > 0 ? `(before discount: ${formatCurrency(monthlyPrice)})` : ''}
    `,
    intervalPrice: `
      Total billed every ${plan.interval} ${plan.interval === 1 ? 'month' : 'months'}: <span class="text-base font-weight-bold text-high-emphasis">${formatCurrency(intervalPriceAfterDiscount)}</span> ${plan.plan_interval_discount > 0 ? `(before discount: ${formatCurrency(intervalPrice)})` : ''}
    `,
    netRevenue: `Net Revenue: <span class="text-base font-weight-bold text-high-emphasis ${netRevenue <= 0 ? 'text-error': ''}">${formatCurrency(netRevenue)}</span>`,
  }
}

function getDiscountHint(plan) {
  if (plan.plan_interval_discount_type === 'percentage') {
    const totalAmount = parseFloatOrZero(plan.plan_interval_price)
    const discountPercentage = parseFloatOrZero(plan.plan_interval_discount)
    const discountAmount = (totalAmount * discountPercentage) / 100

    return `${formatCurrency(discountAmount.toFixed(2))} off`
  }

  return undefined
}

const handleQtySubmit = () => {
  formQtyRef.value?.validate().then(async valid => {
    if (valid.valid) {
      await updateQty()
    } else {
      // scrollToTop()
    }
  })
}

const updateQty = async () => {
  try {
    isLoading.value = true
    serverErrors.value = []
    inputErrors.value = {}

    const formData = createQtyFormData()

    const { data } = await ApiService.post('/admin/edit-hl-product-strength-qty', formData)

    if (data.status === 200) {
      showSnackbar(data.message)
      emit('qtyUpdated', data.qtyData)
    } else {
      isLoading.value = false
      if (data.errors) {
        inputErrors.value = data.errors
      }
      serverErrors.value = processErrors(data)
    }
  } catch (error) {
    isLoading.value = false
    console.error(error)
    if (error.response.errors) {
      inputErrors.value = error.response.errors
    }
    serverErrors.value = processErrors(error)
  }
}

function createQtyFormData() {
  const formData = new FormData()

  formData.append('hl_product_id', productId.value)
  formData.append('hl_product_strength_id', props.strength.id)

  const qty = qtyForm.value

  Object.keys(qty).forEach(key => {
    if (key === 'subscription_plans') {
      qty[key].forEach((plan, planIndex) => {
        Object.keys(plan).forEach(planKey => {
          formData.append(
            `hl_product_subscription_plans[${planIndex}][${planKey}]`, // key
            !isEmpty(plan[planKey]) ? plan[planKey] : '', // value
          )
        })
      })
    } else {
      formData.append(
        key,
        !isEmpty(qty[key]) ? qty[key] : '',
      )
    }
  })

  return formData
}

const cancelQtyEdit = () => {
  emit('discardQtyEdit', true)
}
</script>

<template>
  <VCard>
    <VForm
      ref="formQtyRef"
      @submit.prevent="handleQtySubmit"
    >
      <VCardText class="pa-3">
        <VAlert
          v-if="isEmptyObject(inputErrors) && serverErrors.length > 0"
          type="error"
          variant="tonal"
          title="Validation failed!"
          class="mb-6"
        >
          <ul class="mb-0">
            <li
              v-for="error in serverErrors"
              :key="error"
              class="mb-0"
            >
              {{ error }}
            </li>
          </ul>
        </VAlert>

        <VRow>
          <VCol
            cols="12"
            md="4"
          >
            <VTextField
              v-model="qtyForm.qty"
              label="Quantity"
              :rules="[requiredValidator]"
              @keypress="isNumber($event)"
            />
            <p
              v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['qty'])"
              class="text-sm text-error mb-0 ms-3"
            >
              {{ inputErrors['qty'][0] }}
            </p>
          </VCol>

          <VCol
            cols="12"
            md="4"
          >
            <VSelect
              v-model="qtyForm.is_recommended"
              label="Is Recommended"
              :items="recommendedOptions"
              :rules="[requiredValidator]"
            />
            <p
              v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['is_recommended'])"
              class="text-sm text-error mb-0 ms-3"
            >
              {{ inputErrors['is_recommended'][0] }}
            </p>
          </VCol>

          <VCol cols="12">
            <div>
              <h5 class="mb-5 ms-1">
                Subscription Plans
              </h5>

              <VRow
                v-for="(plan, planIndex) in qtyForm.subscription_plans"
                :key="plan.plan_id"
                class="border-sm border-dashed rounded-lg mx-1 my-3"
              >
                <VCol
                  cols="12"
                  md="4"
                >
                  <VSelect
                    v-model="plan.interval"
                    label="Plan Interval"
                    :items="availableIntervals"
                    :rules="[requiredValidator]"
                    @update:model-value="updateSelectedIntervals(plan)"
                  />
                  <p
                    v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors[`hl_product_subscription_plans.${planIndex}.interval`])"
                    class="text-sm text-error mb-0 ms-3"
                  >
                    {{ inputErrors[`hl_product_subscription_plans.${planIndex}.interval`][0] }}
                  </p>
                </VCol>

                <VCol
                  cols="12"
                  md="4"
                >
                  <VTextField
                    v-model="plan.product_cost"
                    label="Base Price"
                    :rules="[requiredValidator, maxProductCostValidator(plan)]"
                    prefix="$"
                    @keypress="isNumber($event)"
                  />
                  <p
                    v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors[`hl_product_subscription_plans.${planIndex}.product_cost`])"
                    class="text-sm text-error mb-0 ms-3"
                  >
                    {{ inputErrors[`hl_product_subscription_plans.${planIndex}.product_cost`][0] }}
                  </p>
                </VCol>

                <VCol
                  cols="12"
                  md="4"
                >
                  <VTextField
                    v-model="plan.plan_interval_price"
                    label="Selling Price"
                    :rules="[requiredValidator]"
                    prefix="$"
                    @keypress="isNumber($event)"
                  />
                  <p
                    v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors[`hl_product_subscription_plans.${planIndex}.plan_interval_price`])"
                    class="text-sm text-error mb-0 ms-3"
                  >
                    {{ inputErrors[`hl_product_subscription_plans.${planIndex}.plan_interval_price`][0] }}
                  </p>
                </VCol>

                <VCol cols="12">
                  <VRadioGroup
                    v-model="plan.plan_interval_discount_type"
                    label="Discount Type"
                    :rules="[requiredValidator]"
                    required
                    class="d-flex"
                  >
                    <VRadio
                      label="Fixed Amount"
                      value="fixed"
                      class="ms-2"
                    />
                    <VRadio
                      label="Percentage (of the total amount)"
                      value="percentage"
                      class="ms-2"
                    />
                  </VRadioGroup>
                  <p
                    v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors[`hl_product_subscription_plans.${planIndex}.plan_interval_discount_type`])"
                    class="text-sm text-error mb-0 ms-3"
                  >
                    {{ inputErrors[`hl_product_subscription_plans.${planIndex}.plan_interval_discount_type`][0] }}
                  </p>
                </VCol>

                <VCol
                  cols="12"
                  md="4"
                >
                  <VTextField
                    v-model="plan.plan_interval_discount"
                    label="Discount"
                    :rules="[maxDiscountValidator(plan)]"
                    :maxlength="plan.plan_interval_discount_type === 'fixed' ? 10 : 2"
                    :prefix="plan.plan_interval_discount_type === 'fixed' ? '$' : ''"
                    :suffix="plan.plan_interval_discount_type === 'percentage' ? '%' : ''"
                    :hint="getDiscountHint(plan)"
                    persistent-hint
                    @keypress="isNumber($event)"
                  />
                  <p
                    v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors[`hl_product_subscription_plans.${planIndex}.plan_interval_discount`])"
                    class="text-sm text-error mb-0 ms-3"
                  >
                    {{ inputErrors[`hl_product_subscription_plans.${planIndex}.plan_interval_discount`][0] }}
                  </p>
                </VCol>

                <VCol
                  cols="12"
                  md="4"
                >
                  <VSelect
                    v-model="plan.is_recommended"
                    label="Is Recommended"
                    :items="recommendedOptions"
                    :rules="[requiredValidator]"
                    :disabled="isPlanRecommendDisabled(plan)"
                  />
                  <p
                    v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors[`hl_product_subscription_plans.${planIndex}.is_recommended`])"
                    class="text-sm text-error mb-0 ms-3"
                  >
                    {{ inputErrors[`hl_product_subscription_plans.${planIndex}.is_recommended`][0] }}
                  </p>
                </VCol>

                <VCol
                  cols="12"
                  class="text-body-2"
                >
                  <VAlert
                    color="secondary"
                    variant="tonal"
                    class="alert-fix"
                  >
                    <span
                      class="d-block"
                      v-html="calculatedPriceForPlan(plan).unitPrice"
                    ></span>
                    <span
                      class="d-block"
                      v-html="calculatedPriceForPlan(plan).monthlyPrice"
                    ></span>
                    <span
                      class="d-block"
                      v-html="calculatedPriceForPlan(plan).intervalPrice"
                    ></span>
                    <span
                      class="d-block"
                      v-html="calculatedPriceForPlan(plan).netRevenue"
                    ></span>
                  </VAlert>
                </VCol>

                <VCol v-if="planIndex !== 0">
                  <VBtn
                    color="error"
                    variant="tonal"
                    @click="deletePlan(plan)"
                  >
                    <VIcon
                      icon="tabler-trash"
                      start
                    /> Delete Plan
                  </VBtn>
                </VCol>

                <!--
                  <v-divider
                  v-if="
                  qtyForm.subscription_plans?.length > 1 &&
                  planIndex < qtyForm.subscription_plans?.length - 1
                  "
                  class="my-5 mx-4"
                  ></v-divider>
                -->
              </VRow>

              <VBtn
                v-if="qtyForm.subscription_plans?.length < 12"
                color="primary"
                class="mt-5"
                @click="addNewPlan"
              >
                <VIcon
                  icon="tabler-plus"
                  start
                /> Add Plan
              </VBtn>
            </div>
          </VCol>
        </VRow>
      </VCardText>
      <VCardActions class="pa-4 pt-0 justify-end">
        <VBtn
          type="button"
          variant="tonal"
          color="secondary"
          @click="cancelQtyEdit"
        >
          Discard
        </VBtn>
        <VBtn
          type="submit"
          :loading="isLoading"
          variant="elevated"
        >
          Update
        </VBtn>
      </VCardActions>
    </VForm>
  </VCard>
</template>
