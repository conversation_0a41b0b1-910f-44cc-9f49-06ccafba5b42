<script setup>
import { isEmpty, isEmptyObject, isNullOrUndefined } from '@/@core/utils'
import { requiredValidator } from '@/@core/utils/validators'
import router from '@/router'
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { processErrors } from '@/utils/errorHandler'
import { scrollToTop } from '@/utils/helpers'
import { useObjectUrl } from '@vueuse/core'
import { onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import ProductInfoCard from './components/ProductInfoCard.vue'
import StrengthForm from './components/StrengthForm.vue'
import StrengthsViewCard from './components/StrengthsViewCard.vue'
import usePharmacyList from '@/composables/usePharmacyList'
import useProductTypes from '@/composables/useProductTypes'

const route = useRoute()
const { showSnackbar } = useGlobalData()
const { pharmacyList } = usePharmacyList()
const { productTypes, fetchProductTypes, error: productTypesError } = useProductTypes()

const productId = ref(route.params.product_id)
const serverErrors = ref([])
const inputErrors = ref({})
const skeletonLoading = ref(true)
const formProductInfoRef = ref()
const isProductFormActive = ref(false)
const productLoading = ref(false)
const inputProductImageRef = ref(null)
const isProductImgUploading = ref(false)
const isStrengthAddFormActive = ref(false)

const productData = ref({
  id: '',
  active_ingredient: '',
  product_name: '',
  product_description: '',
  product_form: '',
  product_type: null,
  product_image: '',
  best_for: '',
  pharmacy_id: null,
})

const formProductData = ref({
  product_name: '',
  active_ingredient: '',
  product_form: '',
  product_type: null,
  product_description: '',
  product_image: '',
  best_for: '',
  pharmacy_id: null,
})

const productImage = ref({ file: null, url: '' })
const strengths = ref([])

const onFileChange = $event => {
  const { files } = $event.target
  if (!files || files.length === 0) return
  const file = files[0]

  if (file.type.slice(0, 6) !== 'image/') {
    showSnackbar('Only image files are allowed', 'error')

    return false
  }
  if (file.size > 3 * 1024 * 1024) {
    showSnackbar('File size should be less than 3MB', 'error')

    return false
  }

  productImage.value = { file, url: useObjectUrl(file).value ?? '' }

  formProductData.value.product_image = file

  if (!isEmpty(productId.value)) {
    (async () => {
      const formData = new FormData()

      formData.append('id', productId.value)
      formData.append('product_image', productImage.value.file, productImage.value.file.name)

      await updateProductImage(formData)
    })()
  }

  return true
}

onMounted(async () => {
  await getProduct()
  await fetchProductTypes('HL')
  if (productTypesError.value) {
    showSnackbar(productTypesError.value, 'error')
  }
})

const getProduct = async () => {
  try {
    const { data } = await ApiService.get(`/admin/view-hl-product-detail/${productId.value}`)
    if (data.status === 200) {
      productData.value = data.productData
      productImage.value.url = data.productData.product_image
      strengths.value = data.productData.hl_product_strengths
      skeletonLoading.value = false
    } else {
      router.replace({ name: 'admin-hl-products' })
    }
  } catch (error) {
    console.log(error)
  }
}

const handleProductDataSubmit = () => {
  formProductInfoRef.value?.validate().then(async valid => {
    if (valid.valid) {
      const formData = createProductFormData()

      await updateProductInfo(formData)
    } else {
      scrollToTop()
    }
  })
}

const updateProductInfo = async formData =>  {
  try {
    productLoading.value = true
    inputErrors.value = {}
    serverErrors.value = []

    const { data } = await ApiService.post('/admin/edit-hl-product', formData)

    if (data.status === 200) {
      // productData.value = data.productData
      getProduct()
      isProductFormActive.value = false
      showSnackbar(data.message)
    } else {
      if (data.errors) {
        inputErrors.value = data.errors
      }
      serverErrors.value = processErrors(data)
    }
  } catch (error) {
    if (error.response.errors) {
      inputErrors.value = error.response.errors
    }
    serverErrors.value = processErrors(error)
  } finally {
    productLoading.value = false
  }
}

const updateProductImage = async formData => {
  try {
    isProductImgUploading.value = true
    serverErrors.value = []

    const { data } = await ApiService.post('/admin/update-hl-product-image', formData)

    if (data.status === 200) {
      productData.value.product_image = data.productImage
      showSnackbar(data.message)
    } else {
      serverErrors.value = processErrors(data)
      scrollToTop()
    }
  } catch (error) {
    serverErrors.value = processErrors(error)
    scrollToTop()
  } finally {
    isProductImgUploading.value = false
  }
}

const handleProductEdit = () => {
  isProductFormActive.value = true
  formProductData.value = { ...productData.value }
}

const cancelProductEdit = () => {
  isProductFormActive.value = false
  formProductInfoRef.value?.reset()
}

function createProductFormData() {
  const formData = new FormData()
  const productData = formProductData.value

  formData.append('product_id', productId.value)
  formData.append('product_name', productData.product_name)
  formData.append('active_ingredient', productData.active_ingredient)
  formData.append('product_form', productData.product_form)
  if (!isEmpty(productTypes.value)) {
    formData.append('product_type', productData.product_type)
  }
  formData.append(
    'product_description',
    !isEmpty(productData.product_description) ? productData.product_description : '',
  )
  formData.append('best_for', !isEmpty(productData.best_for) ? productData.best_for : '')
  formData.append('pharmacy_id', !isEmpty(productData.pharmacy_id) ? productData.pharmacy_id : '')

  return formData
}
</script>

<template>
  <div>
    <!-- 👉 Edit Product Info -->
    <VCard
      v-if="isProductFormActive"
      title="Product Info"
      class="mb-5"
    >
      <VForm
        ref="formProductInfoRef"
        @submit.prevent="handleProductDataSubmit"
      >
        <VCardText>
          <VAlert
            v-if="isEmptyObject(inputErrors) && serverErrors.length > 0"
            type="error"
            variant="tonal"
            title="Validation failed!"
            class="mb-6"
          >
            <ul class="mb-0">
              <li
                v-for="error in serverErrors"
                :key="error"
                class="mb-0"
              >
                {{ error }}
              </li>
            </ul>
          </VAlert>

          <VRow>
            <VCol
              cols="12"
              :md="!isEmpty(productTypes) ? 6 : 4"
            >
              <VTextField
                v-model="formProductData.product_name"
                label="Product Name"
                :rules="[requiredValidator]"
              />
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.product_name)"
                class="text-sm text-error mb-0 ms-3"
              >
                {{ inputErrors.product_name[0] }}
              </p>
            </VCol>

            <VCol
              cols="12"
              :md="!isEmpty(productTypes) ? 6 : 4"
            >
              <VTextField
                v-model="formProductData.active_ingredient"
                label="Active Ingredient"
                :rules="[requiredValidator]"
              />
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.active_ingredient)"
                class="text-sm text-error mb-0 ms-3"
              >
                {{ inputErrors.active_ingredient[0] }}
              </p>
            </VCol>

            <VCol
              cols="12"
              :md="!isEmpty(productTypes) ? 6 : 4"
            >
              <VTextField
                v-model="formProductData.product_form"
                label="Product Form"
                :rules="[requiredValidator]"
              />
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.product_form)"
                class="text-sm text-error mb-0 ms-3"
              >
                {{ inputErrors.product_form[0] }}
              </p>
            </VCol>

            <VCol
              v-if="!isEmpty(productTypes)"
              cols="12"
              md="6"
            >
              <VSelect
                v-model="formProductData.product_type"
                label="Product Type"
                :rules="[requiredValidator]"
                :items="productTypes"
              />
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.product_type)"
                class="text-sm text-error mb-0 ms-3"
              >
                {{ inputErrors.product_type[0] }}
              </p>
            </VCol>

            <VCol cols="12">
              <TiptapEditor
                v-model="formProductData.product_description"
                class="border rounded basic-editor"
              />
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.product_description)"
                class="text-sm text-error mb-0 ms-3"
              >
                {{ inputErrors.product_description[0] }}
              </p>
            </VCol>

            <VCol
              cols="12"
              md="6"
            >
              <VSelect
                v-model="formProductData.pharmacy_id"
                label="Select Pharmacy"
                placeholder="Select"
                :items="pharmacyList"
                :rules="[requiredValidator]"
                clearable
              />
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.pharmacy_id)"
                class="text-sm text-error mb-0 ms-3"
              >
                {{ inputErrors.pharmacy_id[0] }}
              </p>
            </VCol>

            <VCol
              cols="12"
              md="6"
            >
              <VTextField
                v-model="formProductData.best_for"
                label="Perfect For"
                hint="Separate with comma for multiple values"
                persistent-hint
              />
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.best_for)"
                class="text-sm text-error mb-0 ms-3"
              >
                {{ inputErrors.best_for[0] }}
              </p>
            </VCol>

            <VCol
              cols="12"
              md="6"
            >
              <VCard
                class="mb-6"
                variant="flat"
              >
                <VCardText class="d-flex pa-0">
                  <VAvatar
                    rounded
                    size="80"
                    class="me-6"
                    color="primary"
                    variant="tonal"
                  >
                    <VImg
                      v-if="productImage.url"
                      :src="productImage.url"
                    />
                    <VIcon
                      v-else
                      size="80"
                      icon="tabler-box"
                    />
                  </VAvatar>
                  <div class="d-flex flex-column justify-center gap-4">
                    <div class="d-flex flex-wrap gap-2">
                      <VBtn
                        color="primary"
                        variant="tonal"
                        :loading="isProductImgUploading"
                        @click="inputProductImageRef.click()"
                      >
                        <VIcon
                          icon="tabler-cloud-upload"
                          class="d-sm-none"
                        />
                        <span class="d-none d-sm-block">Select Image</span>
                      </VBtn>

                      <input
                        ref="inputProductImageRef"
                        type="file"
                        name="file"
                        accept="image/*"
                        hidden
                        @input="onFileChange($event)"
                      />
                    </div>
                    <p class="text-body-2 mb-0">
                      Allowed only image files with size less than 2MB.
                    </p>
                  </div>
                </VCardText>
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.product_image)"
                  class="text-sm text-error mb-0 ms-3 mt-2"
                >
                  {{ inputErrors.product_image[0] }}
                </p>
              </VCard>
            </VCol>
          </VRow>
        </VCardText>
        <VCardActions class="pa-6 pt-0 justify-end">
          <VBtn
            v-if="!isEmpty(productId)"
            type="button"
            variant="tonal"
            color="secondary"
            @click="cancelProductEdit"
          >
            Discard
          </VBtn>
          <VBtn
            type="submit"
            :loading="productLoading"
            variant="elevated"
          >
            Update
          </VBtn>
        </VCardActions>
      </VForm>
    </VCard>

    <!-- 👉 View Product Info -->
    <ProductInfoCard
      v-if="!isProductFormActive"
      :loading="skeletonLoading"
      :product-data="productData"
      @edit="handleProductEdit"
    />

    <div
      v-for="i in 2"
      :key="i"
    >
      <Skeleton
        v-if="skeletonLoading"
        width="100%"
        height="4rem"
        class="mb-5"
      ></Skeleton>
    </div>

    <!-- 👉 Strength -->
    <StrengthsViewCard
      v-if="!skeletonLoading"
      :product-data="productData"
      :strengths="strengths"
      @update-list="getProduct"
    />

    <!-- 👉 Add Strength Form -->
    <StrengthForm
      v-if="isStrengthAddFormActive"
      form-type="add"
      :product-data="productData"
      :strengths="strengths"
      @update-list="getProduct"
      @close-form="isStrengthAddFormActive = false"
    />

    <VBtn
      v-if="!skeletonLoading && !isStrengthAddFormActive && !isEmpty(productId)"
      color="primary"
      block
      @click="isStrengthAddFormActive = true"
    >
      <VIcon
        icon="tabler-plus"
        start
      /> Add New Strength
    </VBtn>

    <div
      v-if="!skeletonLoading"
      class="mt-8 d-flex flex-column flex-md-row gap-3"
    >
      <VBtn
        v-if="!isStrengthAddFormActive && !isEmpty(productId)"
        color="primary"
        variant="outlined"
        class="flex-grow-1"
        @click="() => {
          router.push({ name: 'admin-hl-products-add' })
        }"
      >
        Add New Product
      </VBtn>
      <VBtn
        v-if="!isStrengthAddFormActive && !isEmpty(productId)"
        color="primary"
        variant="outlined"
        class="flex-grow-1"
        :to="{ name: 'admin-hl-products' }"
      >
        View All Products
      </VBtn>
    </div>
  </div>
</template>

<style lang="scss">
@use "@core/scss/template/pages/page-auth.scss";

.illustration-image {
  block-size: 550px;
  inline-size: 248px;
}

.bg-image {
  inset-block-end: 0;
}

.app-stepper .v-slide-group__content {
  justify-content: flex-start;
}

.qty-wrapper {
  border: 1px dashed #cecece8f;
  border-radius: 5px;
  margin: 0 !important;
  margin-bottom: 1rem !important;
}

.table-fix {
  tr {
    td {
      height: 0 !important;
      padding-top: 8px !important;
      padding-bottom: 8px !important;
      vertical-align: top;
      &:first-child {
        width: 250px !important;
      }
    }
  }
}
</style>

<style lang="scss" scoped>
.alert-fix {
  color: rgb(var(--v-theme-dark)) !important;
}
</style>

<style lang="scss">
.basic-editor {
  .ProseMirror {
    block-size: 200px;
    outline: none;
    overflow-y: auto;
    padding-inline: 0.5rem;
  }
}
</style>
