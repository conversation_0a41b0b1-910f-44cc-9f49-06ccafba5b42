<script setup>
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { processErrors } from '@/utils/errorHandler'
import {
  getShippingActivityColor,
  resolvePaymentStatusColor,
  resolveTransactionTypeColor,
  resolveRefundStatusColor,
} from '@/utils/admin'
import { computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { formatCurrency, formattedPhoneNumber, resolveInitials } from '@/utils/helpers'
import { isEmpty } from '@/@core/utils'
import { useUpdatePaymentMethod } from '@/store/updatePaymentMethod'

const route = useRoute()
const { showSnackbar } = useGlobalData()
const { setSubscriptionRefillId } = useUpdatePaymentMethod()
const orderId = computed(() => route.params.orderId)
const skeletonLoading = ref(true)
const order = ref({})
const isEditShippingStatusDialogVisible = ref(false)
const isPaymentHistoryDialogVisible = ref(false)
const selectedPaymentHistoryId = ref(null)
const cancelLoading = ref(false)
const isCancelDialogVisible = ref(false)

const selectedRefundId = ref(null)
const refundActionLoading = ref(false)
const isRefundApproveDialogVisible = ref(false)
const isRefundDeclineDialogVisible = ref(false)
const isEditRefundAmountDialogVisible = ref(false)
const orderActivitiesRef = ref(null)
const isChangePaymentMethodDrawerOpen = ref(false)
const paymentMethods = ref([])

const isOrderCancelDialogVisible = ref(false)

onMounted(async () => {
  await fetchOrderDetails()
})

async function fetchOrderDetails() {
  try {
    skeletonLoading.value = true

    const { data } = await ApiService.get(`/admin/hl-manage-order-details/${orderId.value}`)

    if (data.status === 200) {
      order.value = data.orderDetails

      if (!isEmpty(data.orderDetails.user_payment_methods)) {
        paymentMethods.value = data.orderDetails.user_payment_methods
      }
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    console.error(error)
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    skeletonLoading.value = false
  }
}

async function handleCancelConfirmation(isConfirmed) {
  if (isConfirmed) {
    await cancelPharmacyOrder()
  }
}

async function cancelPharmacyOrder() {
  try {
    cancelLoading.value = true

    const { data } = await ApiService.get(`/admin/cancel-hl-pharmacy-order/${orderId.value}`)

    if (data.status === 200) {
      await fetchOrderDetails()
      showSnackbar(data.message)
      isCancelDialogVisible.value = false
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    cancelLoading.value = false
  }
}

async function handleRefundApproveConfirmation(isConfirmed) {
  if (isConfirmed) {
    await approveDeclineRefund({
      refund_id: selectedRefundId.value,
      refund_action_type: 'approve',
    })
  }
}

async function handleRefundDeclineConfirmation(cancelReason) {
  if (cancelReason) {
    await approveDeclineRefund({
      refund_id: selectedRefundId.value,
      refund_action_type: 'decline',
      cancel_reason: cancelReason,
    })
  }
}

async function approveDeclineRefund(requestBody) {
  try {
    refundActionLoading.value = true

    const { data } = await ApiService.post('/admin/refund-action', requestBody)

    if (data.status === 200) {
      selectedRefundId.value = null
      showSnackbar(data.message)
      fetchOrderDetails()
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    isRefundApproveDialogVisible.value = false
    isRefundDeclineDialogVisible.value = false
    refundActionLoading.value = false
  }
}

const isRetryPaymentConfirmDialogVisible = ref(false)
const retryPaymentLoading = ref(false)

async function handleRetryCaptureAuthorizePayment(isConfirmed) {
  if (isConfirmed) {
    await retryCaptureAuthorizePayment()
  }
}

async function retryCaptureAuthorizePayment() {
  try {
    retryPaymentLoading.value = true

    const { data } = await ApiService.get(
      `/admin/retry-capture-authorize-transaction/${order.value.is_retry_payment_summary[1]}`,
    )

    if (data.status === 200) {
      await fetchOrderDetails()
      showSnackbar(data.message)
      isRetryPaymentConfirmDialogVisible.value = false
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    retryPaymentLoading.value = false
  }
}
</script>

<template>
  <div v-if="skeletonLoading">
    <div
      class="d-flex justify-center align-center"
      style="height: calc(100vh - 14rem)"
    >
      <VProgressCircular
        indeterminate
        size="48"
      />
    </div>
  </div>
  <div v-else-if="!isEmpty(order)">
    <div class="d-flex justify-space-between align-center flex-wrap gap-y-4 mb-6">
      <div>
        <div class="d-flex gap-2 align-center flex-wrap">
          <h4 class="text-h4">
            Order #{{ order.order_no }}
          </h4>
        </div>
      </div>

      <div class="d-flex flex-wrap gap-3">
        <VBtn
          v-if="order.is_pharmacy_order_cancelable === 1"
          color="error"
          variant="tonal"
          @click="isCancelDialogVisible = true"
        >
          Cancel Pharmacy Order
        </VBtn>

        <VBtn
          v-if="order.is_medicine_pickup_at_local_pharmacy !== 1 && order.subscription_id"
          color="primary"
          variant="tonal"
          :to="{
            name: 'admin-hl-manage-subscription',
            params: { subscriptionId: order.subscription_id },
          }"
        >
          View Subscription
        </VBtn>

        <!-- Cancel Order Button -->
        <VBtn
          v-if="
            (order.is_medicine_pickup_at_local_pharmacy === 1
              && (order.status === 0 || order.status === 1 || order.status === 4))
              || order.is_enable_cancel_order_btn === 1
          "
          color="error"
          variant="tonal"
          @click="isOrderCancelDialogVisible = true"
        >
          Cancel Order
        </VBtn>
      </div>
    </div>

    <VRow>
      <VCol
        cols="12"
        md="8"
      >
        <!-- 👉 Order Details -->
        <VCard class="mb-6">
          <VCardTitle class="pt-6 px-6">
            Order Details
          </VCardTitle>

          <VCardText>
            <div class="mb-6 text-sm">
              <div class="d-flex justify-space-between border-b py-3 px-1">
                <div>Order Number:</div>
                <div>{{ order.order_no }}</div>
              </div>
              <div class="d-flex justify-space-between border-b py-3 px-1">
                <div>Order Status:</div>
                <div class="d-flex flex-column align-end">
                  <VChip
                    variant="tonal"
                    :color="order.order_color"
                    label
                  >
                    {{ order.order_status }}
                  </VChip>
                  <!--
                    <button
                    v-if="order.update_order_shipment_status === 1"
                    class="text-link mt-1"
                    @click="isEditShippingStatusDialogVisible = true"
                    >
                    Change Status
                    </button>
                  -->
                </div>
              </div>
              <div
                v-if="!isEmpty(order.cancel_reason)"
                class="d-flex justify-space-between border-b py-3 px-1"
              >
                <VAlert
                  color="error"
                  variant="tonal"
                  border-color="error"
                >
                  <VAlertTitle class="mb-1">
                    <span class="text-sm font-weight-bold text-high-emphasis">Cancelation Reason</span>
                  </VAlertTitle>
                  <span class="text-body-1">
                    {{ order.cancel_reason }}
                  </span>
                </VAlert>
              </div>
              <div class="d-flex justify-space-between border-b py-3 px-1">
                <div>Order Type:</div>
                <div>{{ order.refill_number === 0 ? order.refill_type : `${order.refill_type} ${order.refill_number}` }}</div>
              </div>
              <div class="d-flex justify-space-between border-b py-3 px-1">
                <div>Pharmacy:</div>
                <div>{{ order.pharmacy_name ?? '-' }}</div>
              </div>
              <div class="d-flex justify-space-between border-b py-3 px-1">
                <div>Created At:</div>
                <div>
                  {{ order.created_date_time }}
                </div>
              </div>
              <div class="d-flex justify-space-between border-b py-3 px-1">
                <div>Updated At:</div>
                <div>
                  {{ order.updated_date_time }}
                </div>
              </div>
              <div
                v-if="order.canceled_at"
                class="d-flex justify-space-between border-b py-3 px-1"
              >
                <div>Canceled At:</div>
                <div class="text-end">
                  <div>{{ order.canceled_at }}</div>
                  <div
                    v-if="order.action_by_user_name"
                    class="text-xs"
                  >
                    <span class="text-high-emphasis">Action by: </span>
                    <span v-if="order.action_by_user_name">{{ order.action_by_user_name + " " }}</span>
                    <span v-if="order.action_by_user_role">({{ order.action_by_user_role }})</span>
                  </div>
                </div>
              </div>
              <div
                v-if="order.is_medicine_pickup_at_local_pharmacy === 1 && order.master_id"
                class="d-flex justify-space-between py-3 border-b px-1"
              >
                <div>Master ID:</div>
                <div>
                  {{ order.master_id }}
                </div>
              </div>
            </div>

            <VTable>
              <thead>
                <tr>
                  <th>
                    Product
                  </th>
                  <th>
                    Quantity
                  </th>
                  <th class="text-right">
                    Price
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>
                    <div class="d-flex align-center py-1 gap-3">
                      <VAvatar
                        v-if="order.product_img"
                        variant="tonal"
                        size="50"
                        class="me-3"
                        :image="order.product_img"
                        rounded
                      />
                      <VAvatar
                        v-else
                        variant="tonal"
                        size="50"
                        class="me-3"
                        rounded
                      >
                        {{ order.product_name ? resolveInitials(order.product_name) : 'G' }}
                      </VAvatar>
                      <div>
                        <h6 class="text-base">
                          {{ order.product_name }}
                        </h6>
                        <div class="d-flex flex-column">
                          <span class="d-block text-body-2">
                            {{ order.strength }} {{ order.strength_unit }} x {{ order.qty * order.subscription_interval }} units
                          </span>
                          <span
                            v-if="order.is_medicine_pickup_at_local_pharmacy !== 1"
                            class="d-block text-body-2"
                          >({{ order.subscription_interval * 30 }}-day supply)</span>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td>
                    {{ order.subscription_interval * order.qty }} {{ order.product_form + `${order.qty > 1 ? 's' : ''}` }}
                  </td>
                  <td class="text-right">
                    {{ formatCurrency(order.orderSummary?.medicine_amount) }}
                  </td>
                </tr>
              </tbody>
            </VTable>

            <VDivider />

            <div class="d-flex align-end flex-column mt-6 px-4">
              <table class="text-high-emphasis">
                <tbody>
                  <tr
                    v-if="order.orderSummary?.first_time_order_discount_amount"
                    class="text-base"
                  >
                    <td width="300px">
                      First Order Discount:
                    </td>
                    <td class="text-right">
                      -{{ formatCurrency(order.orderSummary?.first_time_order_discount_amount) }}
                    </td>
                  </tr>
                  <tr
                    v-if="order.orderSummary?.promo_code_name"
                    class="text-base"
                  >
                    <td width="300px">
                      <div>Promotion applied:</div>
                      <div class="text-high-emphasis font-weight-bold text-xs">
                        {{ order.orderSummary.promo_code_name }} <span v-if="order.orderSummary?.promo_code_type === 'percentage'">({{ order.orderSummary.promo_code_value }}%)</span>
                      </div>
                    </td>
                    <td class="text-right">
                      -{{ formatCurrency(order.orderSummary?.promo_code_discount_amount) }}
                    </td>
                  </tr>
                  <tr
                    v-if="order.orderSummary?.sub_total"
                    class="text-base"
                  >
                    <td width="300px">
                      Subtotal:
                    </td>
                    <td class="text-right">
                      {{ formatCurrency(order.orderSummary?.sub_total) }}
                    </td>
                  </tr>
                  <tr
                    v-if="order.is_medicine_pickup_at_local_pharmacy !== 1"
                    class="text-base"
                  >
                    <td>Shipping & handling cost: </td>
                    <td class="text-right">
                      <span
                        v-if="order.orderSummary?.shipping_cost === 0"
                        class="text-success"
                      >FREE</span>
                      <span v-else>{{ formatCurrency(order.orderSummary?.shipping_cost) }}</span>
                    </td>
                  </tr>
                  <tr
                    v-if="!isEmpty(order.orderSummary?.provider_consultation_fee)"
                    class="text-base"
                  >
                    <td>Physician & processing fee: </td>
                    <td class="text-right">
                      <span
                        v-if="order.orderSummary?.provider_consultation_fee === 0"
                        class="text-success"
                      >FREE</span>
                      <span v-else>{{ formatCurrency(order.orderSummary?.provider_consultation_fee) }}</span>
                    </td>
                  </tr>
                  <tr
                    v-if="order.orderSummary?.convenience_fee"
                    class="text-base"
                  >
                    <td>Convenience fee: </td>
                    <td class="text-right">
                      <span>{{ formatCurrency(order.orderSummary?.convenience_fee) }}</span>
                    </td>
                  </tr>
                  <tr class="text-base">
                    <td class="text-high-emphasis font-weight-medium">
                      Total:
                    </td>
                    <td class="font-weight-medium text-right">
                      {{ formatCurrency(order.orderSummary?.total_amount) }}
                    </td>
                  </tr>
                  <tr v-if="order.revenue && order.revenue !== 0">
                    <td>
                      <div class="mt-4">
                        Revenue:
                      </div>
                    </td>
                    <td class="font-weight-medium text-right">
                      <div class="mt-4">
                        <span
                          v-if="order.revenue > 0"
                          class="text-success"
                        >{{ formatCurrency(order.revenue) }}</span>
                        <span
                          v-if="order.revenue < 0"
                          class="text-error"
                        >{{ formatCurrency(order.revenue) }}</span>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </VCardText>
        </VCard>

        <!-- 👉 Order Activities -->
        <HLOrderActivities
          ref="orderActivitiesRef"
          class="mb-6"
          :order-id="orderId"
          @update-payment-method="isChangePaymentMethodDrawerOpen = true"
        />

        <!-- 👉 Shipping Activity -->
        <VCard
          v-if="order.is_medicine_pickup_at_local_pharmacy === 0"
          title="Shipping Activity"
          class="mb-6"
        >
          <template #subtitle>
            <span
              v-if="!isEmpty(order.tracking_no)"
              class="text-medium-emphasis"
            >
              Tracking number: {{ order.tracking_no }}
            </span>
          </template>
          <VCardText>
            <VTimeline
              v-if="!isEmpty(order.shipping_activities)"
              truncate-line="both"
              align="start"
              side="end"
              line-color="primary"
              density="compact"
              class="v-timeline-density-compact"
            >
              <VTimelineItem
                v-for="(item, index) in order.shipping_activities"
                :key="index"
                dot-color="primary"
                size="x-small"
              >
                <div class="d-flex justify-space-between align-center">
                  <div class="app-timeline-title">
                    <VChip
                      :color="getShippingActivityColor(item.EventStatus)"
                      label
                      class="text-capitalize mb-1"
                    >
                      {{ item.EventStatusText }}
                    </VChip>
                    <div v-if="item.EventDescription">
                      {{ item.EventDescription }}
                    </div>
                  </div>
                  <div class="app-timeline-meta text-right">
                    <div v-if="item.EventDate">
                      {{ item.EventDate }}
                    </div>
                    <div v-if="item.action_by_user_name">
                      <span class="text-medium-emphasis">Updated by: </span>
                      <span v-if="item.action_by_user_name">{{ item.action_by_user_name + ' ' }}</span>
                      <span v-if="item.action_by_user_role">({{ item.action_by_user_role }})</span>
                    </div>
                  </div>
                </div>
                <p
                  v-if="item.EventLocation"
                  class="app-timeline-text mb-0"
                >
                  {{ item.EventLocation }}
                </p>
              </VTimelineItem>
            </VTimeline>

            <div
              v-else
              class="d-flex flex-column align-center justify-center"
            >
              <VIcon
                size="x-large"
                icon="mdi-truck"
              />
              <span class="text-h6 ms-2 mt-1">Not shipped</span>
            </div>
          </VCardText>
        </VCard>

        <!-- 👉 Payment History -->
        <VCard
          title="Payment History"
          class="mb-6"
        >
          <VCardText>
            <div
              v-if="isEmpty(order.payment_histories)"
              class="d-flex flex-column align-center"
            >
              <VIcon
                icon="tabler-package-off"
                size="48"
              />
              <p class="text-body-1 mt-4">
                No Payment History
              </p>
            </div>
            <VTable
              v-else
              class="text-no-wrap text-sm"
              fixed-header
            >
              <thead>
                <tr>
                  <th class="text-uppercase text-body-2">
                    Amount
                  </th>
                  <th class="text-uppercase text-body-2">
                    Transaction Type
                  </th>
                  <th class="text-uppercase text-body-2">
                    Status
                  </th>
                  <th class="text-uppercase text-body-2">
                    Transaction ID
                  </th>
                  <th class="text-uppercase text-body-2">
                    Payment Method
                  </th>
                  <th class="text-uppercase text-body-2">
                    Created At
                  </th>
                  <th class="text-uppercase text-body-2">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr
                  v-for="item in order.payment_histories"
                  :key="item.id"
                >
                  <td>
                    <strong>{{ formatCurrency(item.amount) }}</strong>
                  </td>
                  <td>
                    <VChip
                      size="x-small"
                      label
                      :color="resolveTransactionTypeColor(item.transaction_type)"
                    >
                      {{ item.transaction_type_text }}
                    </VChip>
                  </td>
                  <td>
                    <VChip
                      size="x-small"
                      label
                      :color="resolvePaymentStatusColor(item.status)"
                    >
                      {{ item.payment_status_text }}
                    </VChip>
                  </td>
                  <td>
                    {{ item.transaction_id ?? '-' }}
                  </td>
                  <td>
                    <div>{{ item.card_brand_type }}</div>
                    <div>xxxx xxxx xxxx {{ item.card_number }}</div>
                  </td>
                  <td>
                    <span class="d-block">{{ item.created_date }}</span>
                    <span class="d-block">{{ item.created_time }}</span>
                  </td>
                  <td class="text-right">
                    <VTooltip text="View Details">
                      <template #activator="{ props }">
                        <VBtn
                          v-bind="props"
                          icon
                          color="default"
                          variant="text"
                          @click="() => {
                            selectedPaymentHistoryId = item.id
                            isPaymentHistoryDialogVisible = true
                          }"
                        >
                          <VIcon
                            icon="tabler-eye"
                            class="cursor-pointer"
                          />
                        </VBtn>
                      </template>
                    </VTooltip>
                  </td>
                </tr>
              </tbody>
            </VTable>
          </VCardText>
        </VCard>
      </VCol>

      <VCol
        cols="12"
        md="4"
      >
        <!-- 👉 Customer Details  -->
        <VCard
          class="mb-6"
          title="Customer Details"
        >
          <VCardText class="d-flex flex-column gap-y-6">
            <div class="d-flex align-center">
              <VAvatar
                :image="order.selfie"
                class="me-3"
                rounded="lg"
                size="60"
              />
              <div>
                <h5 class="text-h5">
                  {{ order.full_name }}
                </h5>
                <p class="text-body-2 mb-0">
                  {{ order.email }}
                </p>
                <p class="text-body-2 mb-0">
                  {{ formattedPhoneNumber(order.phone_number) }}
                </p>
              </div>
            </div>
          </VCardText>
        </VCard>

        <!-- 👉 Payment Details -->
        <VCard
          v-if="!isEmpty(order.payment_details)"
          title="Payment Details"
          class="mb-6"
        >
          <VCardText>
            <div>
              <div class="text-xs text-high-emphasis font-weight-medium text-uppercase mb-2">
                Payment Method
              </div>
              <div>
                <div class="text-sm text-uppercase font-weight-medium">
                  {{ order.payment_details?.card_brand_type }}
                </div>
                <div class="text-base">
                  xxxx xxxx xxxx {{ order.payment_details?.card_number }}
                </div>
              </div>
            </div>

            <div
              v-if="order.paypal_transaction_id"
              class="mt-4"
            >
              <div class="text-xs text-high-emphasis font-weight-medium text-uppercase mb-2">
                Transaction ID
              </div>
              <div>
                <div class="text-sm">
                  {{ order.paypal_transaction_id }}
                </div>
              </div>
            </div>

            <a
              v-if="order && order.invoice_url"
              :href="order.invoice_url"
              class="v-btn text-link cursor-pointer mt-4"
              target="_blank"
              rel="noopener noreferrer"
            >View Invoice</a>

            <VBtn
              v-if="order.is_retry_payment_summary[0] === 1"
              color="primary"
              class="mt-5"
              block
              @click="() => {
                setSubscriptionRefillId(order.is_retry_payment_summary[1])
                isChangePaymentMethodDrawerOpen = true
              }"
            >
              Retry Payment
            </VBtn>
            <VBtn
              v-if="order.is_retry_payment_summary[0] === 2"
              color="primary"
              class="mt-5"
              block
              @click="isRetryPaymentConfirmDialogVisible = true"
            >
              Retry Payment
            </VBtn>
          </VCardText>
        </VCard>

        <!-- 👉 Prescription Pickup Location -->
        <VCard
          v-if="order.is_medicine_pickup_at_local_pharmacy === 1 && !isEmpty(order.local_pharmacy_details)"
          class="mb-6"
          title="Prescription Pickup Location"
        >
          <VCardText>
            <div>
              <div
                v-if="order.local_pharmacy_details?.PharmacyName"
                class="text-high-emphasis font-weight-bold mb-1"
              >
                {{ order.local_pharmacy_details?.PharmacyName }}
              </div>
              <div v-if="order.local_pharmacy_details?.Address1">
                {{ order.local_pharmacy_details?.Address1 }}
                <span v-if="order.local_pharmacy_details?.Address2">
                  , {{ order.local_pharmacy_details?.Address2 }}
                </span>
              </div>
              <div v-if="order.local_pharmacy_details?.City">
                {{ order.local_pharmacy_details?.City }}
              </div>
              <div>
                <span v-if="order.local_pharmacy_details?.State">{{ order.local_pharmacy_details?.State }}</span>
                <span v-if="order.local_pharmacy_details?.ZipCode">-{{ order.local_pharmacy_details?.ZipCode }} </span>
              </div>
              <div class="mt-2">
                <div v-if="order.local_pharmacy_details?.PharmacyId">
                  Pharmacy ID: {{ order.local_pharmacy_details?.PharmacyId }}
                </div>
                <div v-if="order.local_pharmacy_details?.PrimaryPhone">
                  Phone: {{ order.local_pharmacy_details?.PrimaryPhone }}
                </div>
                <div v-if="order.local_pharmacy_details?.PrimaryFax">
                  Fax: {{ order.local_pharmacy_details?.PrimaryFax }}
                </div>
              </div>
            </div>

            <!-- Locate Pharmacy -->
            <div
              v-if="order.local_pharmacy_details?.PharmacyDirection"
              class="mt-5"
            >
              <a
                :href="order.local_pharmacy_details?.PharmacyDirection"
                class="dt-link"
                target="_blank"
                rel="noreferrer noopener"
              >
                <VIcon
                  icon="tabler-map-pin"
                  size="18"
                  start
                /> Locate Pharmacy
              </a>
            </div>
          </VCardText>
        </VCard>

        <!-- 👉 Shipping Details -->
        <VCard
          class="mb-6"
          :title="order.is_medicine_pickup_at_local_pharmacy === 1 ? 'Address' : 'Shipping Details'"
        >
          <VCardText>
            <div>
              <div v-if="order.is_medicine_pickup_at_local_pharmacy !== 1">
                <div class="d-flex justify-space-between align-center gap-4 mb-1">
                  <h6 class="text-h6">
                    Shipping Method
                  </h6>
                </div>
                <div class="text-medium-emphasis mb-4">
                  {{ order?.pharmacy_shipment_method_title }}
                </div>
              </div>

              <div
                v-if="order.is_medicine_pickup_at_local_pharmacy !== 1"
                class="d-flex justify-space-between align-center gap-4 mb-1"
              >
                <h6 class="text-h6">
                  Shipping Address
                </h6>
              </div>
              <div class="text-medium-emphasis">
                {{ order.address_line_1 }} <br>
                <span v-if="order.address_line_2">
                  {{ order.address_line_2 }} <br>
                </span>
                {{ order.city }}, {{ order.state }}-{{ order.zipcode }} <br>
                {{ order.country }}
              </div>
            </div>
          </VCardText>
        </VCard>

        <!-- 👉 Refund Details -->
        <VCard
          v-if="!isEmpty(order.refund_request_detail)"
          class="mb-6"
          :title="[0, 3].includes(order.refund_request_detail?.refund_status) ? 'Refund Request' : 'Refund History'"
        >
          <VCardText>
            <div class="d-flex justify-space-between py-1 text-sm">
              <div>Refund Amount</div>
              <div class="font-weight-medium text-end">
                {{ formatCurrency(order.refund_request_detail?.refund_amount) }} <br />
                <button
                  v-if="order.refund_request_detail?.refund_status === 0 || order.refund_request_detail?.refund_status === 3"
                  class="text-link text-sm text-decoration-underline"
                  @click="isEditRefundAmountDialogVisible = true"
                >
                  Edit
                </button>
              </div>
            </div>
            <div class="d-flex justify-space-between py-1 text-sm">
              <div>Refund Status</div>
              <div class="font-weight-medium">
                <VChip
                  size="x-small"
                  label
                  :color="resolveRefundStatusColor(order.refund_request_detail?.refund_status)"
                >
                  {{ order.refund_request_detail?.refund_status_text }}
                </VChip>
              </div>
            </div>
            <div
              v-if="order.refund_request_detail?.refund_transaction_id"
              class="d-flex justify-space-between py-1 text-sm"
            >
              <div>Transaction ID</div>
              <div class="font-weight-medium">
                {{ order.refund_request_detail?.refund_transaction_id ?? '-' }}
              </div>
            </div>
            <div
              v-if="!isEmpty(order.refund_request_detail?.cancel_reason)"
              class="d-flex justify-space-between py-1 text-sm"
            >
              <div class="text-no-wrap">
                Cancelation Reason
              </div>
              <div
                class="font-weight-medium text-end"
                style="max-width: 220px;"
              >
                {{ order.refund_request_detail?.cancel_reason }}
              </div>
            </div>
            <div
              v-if="!isEmpty(order.refund_request_detail?.action_by_user_name) || !isEmpty(order.refund_request_detail?.action_by_user_role)"
              class="d-flex justify-space-between py-1 text-sm"
            >
              <div>Action By</div>
              <div class="font-weight-medium">
                <span v-if="!isEmpty(order.refund_request_detail?.action_by_user_name) && !isEmpty(order.refund_request_detail?.action_by_user_role)">
                  {{ order.refund_request_detail?.action_by_user_name }} ({{ order.refund_request_detail?.action_by_user_role }})
                </span>
                <span v-else-if="!isEmpty(order.refund_request_detail?.action_by_user_name) && isEmpty(order.refund_request_detail?.action_by_user_role)">
                  {{ order.refund_request_detail?.action_by_user_name }}
                </span>
                <span v-else-if="isEmpty(order.refund_request_detail?.action_by_user_name) && !isEmpty(order.refund_request_detail?.action_by_user_role)">
                  {{ order.refund_request_detail?.action_by_user_role }}
                </span>
              </div>
            </div>
            <div class="d-flex justify-space-between py-1 text-sm">
              <div>Created At</div>
              <div class="font-weight-medium">
                {{ order.refund_request_detail?.created_date }}
                {{ order.refund_request_detail?.created_time }}
              </div>
            </div>
            <div class="d-flex justify-space-between py-1 text-sm">
              <div>Updated At</div>
              <div class="font-weight-medium">
                {{ order.refund_request_detail?.updated_date }}
                {{ order.refund_request_detail?.updated_time }}
              </div>
            </div>

            <div
              v-if="[0, 3].includes(order.refund_request_detail?.refund_status)"
              class="d-flex gap-3 mt-4"
            >
              <VBtn
                size="small"
                color="success"
                variant="tonal"
                @click="() => {
                  selectedRefundId = order.refund_request_detail?.id
                  isRefundApproveDialogVisible = true
                }"
              >
                Approve Refund
              </VBtn>
              <VBtn
                size="small"
                color="error"
                variant="tonal"
                @click="() => {
                  selectedRefundId = order.refund_request_detail?.id
                  isRefundDeclineDialogVisible = true
                }"
              >
                Cancel Refund
              </VBtn>
            </div>
          </VCardText>
        </VCard>

        <!-- 👉 Affiliate By -->
        <VCard
          v-if="!isEmpty(order?.affiliate_user)"
          class="mb-6"
          title="Affiliate By"
        >
          <VCardText>
            <div class="d-flex align-center py-1">
              <VAvatar
                v-if="order.affiliate_user.profile_picture"
                variant="tonal"
                size="40"
                class="me-3"
                :image="order.affiliate_user.profile_picture"
                rounded
              />
              <VAvatar
                v-else
                variant="tonal"
                size="40"
                class="me-3"
                rounded
              >
                {{ resolveInitials(order.affiliate_user.first_name + ' ' + order.affiliate_user.last_name) }}
              </VAvatar>
              <div>
                <h6 class="text-base">
                  {{ order.affiliate_user.first_name }} {{ order.affiliate_user.last_name }}
                </h6>
                <div class="d-flex flex-column">
                  <span class="text-body-2">{{ order.affiliate_user.email }}</span>
                  <span class="text-body-2">{{ formattedPhoneNumber(order.affiliate_user.phone_number) }}</span>
                </div>
              </div>
            </div>
            <VBtn
              variant="tonal"
              size="small"
              block
              class="mt-2"
              :to="{
                name: 'admin-affiliate-user-details',
                params: { userId: order.affiliate_user.id },
              }"
            >
              View Profile
            </VBtn>
          </VCardText>
        </VCard>
      </VCol>
    </VRow>

    <!-- 👉 Edit Shipping Status Dialog -->
    <!--
      <HLEditShippingStatusDrawer
      v-model:isDrawerOpen="isEditShippingStatusDialogVisible"
      @updated="fetchOrderDetails"
      />
    -->

    <!-- 👉 Payment History Dialog -->
    <PaymentHistoryDialog
      v-model:isDialogVisible="isPaymentHistoryDialogVisible"
      :payment-history-id="selectedPaymentHistoryId"
    />

    <!-- 👉 Confirm Pharmacy Order Cancelation -->
    <ConfirmDialog
      v-model:isDialogVisible="isCancelDialogVisible"
      confirmation-question="Are you sure you want to cancel order on Pharmacy?"
      :loading="cancelLoading"
      @confirm="handleCancelConfirmation"
    />

    <!-- 👉 Confirm Refund Approve -->
    <ConfirmDialog
      v-model:isDialogVisible="isRefundApproveDialogVisible"
      confirmation-question="Are you sure you want to approve this refund request?"
      :loading="refundActionLoading"
      @confirm="handleRefundApproveConfirmation"
    />

    <!-- 👉 Refund Decline Dialog -->
    <RefundDeclineDialog
      v-model:isDialogVisible="isRefundDeclineDialogVisible"
      @submit="handleRefundDeclineConfirmation"
    />

    <!-- 👉 Edit Refund Amount Dialog -->
    <EditRefundAmountDialog
      v-model:isDialogVisible="isEditRefundAmountDialogVisible"
      :refund-id="order.refund_request_detail?.id || ''"
      :refund-amount="order.refund_request_detail?.refund_amount || 0"
      @updated="fetchOrderDetails"
    />

    <!-- 👉 Change Payment Method -->
    <!--
      <HLOrderChangePaymentMethod
      v-model:isDrawerOpen="isChangePaymentMethodDrawerOpen"
      :order-id="order.order_id || ''"
      :payment-methods="paymentMethods"
      :current-payment-method-id="order?.current_payment_method_id"
      @updated="fetchOrderDetails"
      />
    -->

    <!-- 👉 Change Payment Method -->
    <HLSubscriptionChangePaymentMethod
      v-model:isDrawerOpen="isChangePaymentMethodDrawerOpen"
      :subscription-id="order?.subscription_id || ''"
      :payment-methods="paymentMethods"
      :current-payment-method-id="order?.current_payment_method_id || ''"
      @updated="fetchOrderDetails"
    />

    <!-- 👉 Confirm Retry Failed Payment -->
    <ConfirmDialog
      v-model:isDialogVisible="isRetryPaymentConfirmDialogVisible"
      confirmation-question="Would you like to retry capturing the authorized payment for this order?"
      :loading="retryPaymentLoading"
      @confirm="handleRetryCaptureAuthorizePayment"
    />

    <!-- 👉 Order Cancel Dialog -->
    <HLOrderCancelDialog
      v-model:isDialogVisible="isOrderCancelDialogVisible"
      :order-id="order.id || ''"
      :is-regular-order="order.is_enable_cancel_order_btn === 1"
      :is-refundable="order.status === 0 || order.status === 1 || order.is_enable_cancel_order_btn === 1"
      @canceled="fetchOrderDetails"
    />
  </div>
</template>
