<script setup>
import { isEmpty } from '@/@core/utils'
import useListCount from '@/composables/useListCount'
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { resolvePrescriptionStatus, resolveSubscriptionStatusColor } from '@/utils/admin'
import { calculateStartIndex, formattedPhoneNumber, resolveInitials } from '@/utils/helpers'
import { refDebounced } from '@vueuse/core'
import { computed, onMounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const globalData = useGlobalData()
const { showSnackbar } = globalData
const route = useRoute()
const router = useRouter()
const { loading: loadingCount, getListCount, countData } = useListCount()

const searchQuery = ref(route.query.search ?? null)
const debouncedSearchQuery = refDebounced(searchQuery, 300)
const selectedStatus = ref(!isNaN(parseInt(route.query.status)) ? parseInt(route.query.status) : null)

const dateRange = ref(
  route.query.from_date && route.query.to_date ? route.query.from_date + ' to ' + route.query.to_date : null,
)

const filterInterval = ref(route.query.interval ?? null)
const fromDate = ref(null)
const toDate = ref(null)
const rowPerPage = ref(10)
const currentPage = ref(!isNaN(parseInt(route.query.page)) ? parseInt(route.query.page) : 1)
const totalPage = ref(1)
const totalItems = ref(0)
const items = ref([])
const sortByColumnName = ref(route.query.sort_by ?? 'created_at')
const sortDirection = ref(route.query.sort_dir ?? 'desc')
const skeletonLoading = ref(true)

const userStats = computed(() => {
  return [
    {
      icon: 'tabler-receipt-2',
      color: 'primary',
      title: 'Total Subscriptions',
      stats: countData.value?.total_subscription || 0,
    },
    {
      icon: 'tabler-check',
      color: 'success',
      title: 'Active Subscriptions',
      stats: countData.value?.active_subscription || 0,
    },
    {
      icon: 'tabler-player-pause',
      color: 'warning',
      title: 'On-Hold Subscriptions',
      stats: countData.value?.on_hold_subscription || 0,
    },
    {
      icon: 'tabler-circle-x',
      color: 'error',
      title: 'Canceled Subscriptions',
      stats: countData.value?.canceled_subscription || 0,
    },
    {
      icon: 'tabler-exclamation-circle',
      color: 'secondary',
      title: 'Expired Subscriptions',
      stats: countData.value?.expired_subscription || 0,
    },
  ]
})

const intervalOptions = [
  { title: 'Monthly', value: 1 },
  { title: '2 Months', value: 2 },
  { title: '3 Months', value: 3 },
  { title: '4 Months', value: 4 },
  { title: '5 Months', value: 5 },
  { title: '6 Months', value: 6 },
  { title: '7 Months', value: 7 },
  { title: '8 Months', value: 8 },
  { title: '9 Months', value: 9 },
  { title: '10 Months', value: 10 },
  { title: '11 Months', value: 11 },
  { title: '12 Months', value: 12 },
]

onMounted(() => {
  fetchItems()
  getListCount('subscription/HL')
})

const updateRoute = () => {
  router.replace({
    query: {
      page: currentPage.value,
      per_page: rowPerPage.value,
      status: selectedStatus.value,
      search: searchQuery.value,
      sort_by: sortByColumnName.value,
      sort_dir: sortDirection.value,
      from_date: fromDate.value,
      to_date: toDate.value,
      interval: filterInterval.value,
    },
  })
}

watch(dateRange, () => {
  if (isEmpty(dateRange.value)) {
    fromDate.value = null
    toDate.value = null

    return
  }

  const dateArr = dateRange.value.split(' to ')

  if (dateArr.length === 1) {
    fromDate.value = dateArr[0] || null
    toDate.value = dateArr[0] || null
  } else if (dateArr.length === 2) {
    fromDate.value = dateArr[0] || null
    toDate.value = dateArr[1] || null
  } else {
    fromDate.value = null
    toDate.value = null
  }
})

watch([debouncedSearchQuery, selectedStatus, fromDate, toDate, rowPerPage, filterInterval], () => {
  currentPage.value = 1
  fetchItems()
})

watch([currentPage, sortByColumnName, sortDirection], () => {
  fetchItems()
})

const fetchItems = async () => {
  skeletonLoading.value = true

  try {
    const { data } = await ApiService.post('/admin/hl-subscription-list', {
      searchQuery: searchQuery.value,
      status: selectedStatus.value,
      perPage: rowPerPage.value,
      page: currentPage.value,
      sortByColumnName: sortByColumnName.value,
      isSortDirDesc: sortDirection.value,
      from_date: fromDate.value,
      to_date: toDate.value,
      subscription_interval_month: filterInterval.value,
    })

    if (data.status === 200) {
      const pagedData = data.subscriptions
      let startIndex = calculateStartIndex(pagedData.current_page, pagedData.per_page)
      if (currentPage.value > 1) {
        startIndex = (currentPage.value - 1) * rowPerPage.value + 1
      }

      items.value = pagedData.records.map((d, index) => ({
        ...d,
        sno: index + startIndex,
      }))
      totalPage.value = pagedData.totalPage == 0 ? 1 : pagedData.totalPage
      totalItems.value = pagedData.totalRecords
      updateRoute()
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    console.error(error)
    if (error.response.data.message) {
      showSnackbar(error.response.data.message, 'error')
    } else if (error.response.message) {
      showSnackbar(error.response.message, 'error')
    }
  } finally {
    skeletonLoading.value = false
  }
}

const sortColumn = columnName => {
  if (sortByColumnName.value !== '' && sortByColumnName.value === columnName && sortDirection.value === 'asc') {
    sortDirection.value = 'desc'
  } else {
    sortDirection.value = 'asc'
  }

  sortByColumnName.value = columnName
}

// 👉 search filters
const status = [
  {
    title: 'On Hold',
    value: 0,
  },
  {
    title: 'Active',
    value: 1,
  },
  {
    title: 'Canceled',
    value: 2,
  },
  {
    title: 'Expired',
    value: 3,
  },
]

// 👉 Computing pagination data
const paginationData = computed(() => {
  const currentPageValue = currentPage.value <= 0 ? 1 : currentPage.value

  const firstIndex = items.value.length
    ? (currentPageValue - 1) * rowPerPage.value + 1
    : 0

  const lastIndex = items.value.length + (currentPageValue - 1) * rowPerPage.value

  return `Showing ${firstIndex} to ${lastIndex} of ${totalItems.value} entries`
})
</script>

<template>
  <section>
    <VRow class="match-height">
      <VCol
        v-for="meta in userStats"
        :key="meta.title"
        cols="12"
        sm="4"
      >
        <VCard>
          <VCardText class="d-flex justify-space-between">
            <div>
              <span v-if="loadingCount">
                <Skeleton
                  width="10rem"
                  height="1.4rem"
                ></Skeleton>
              </span>
              <span
                v-else
                class="text-h5"
              >{{ meta.title }}</span>
              <div class="d-flex align-center gap-2 my-1">
                <h6 v-if="loadingCount">
                  <Skeleton
                    width="1rem"
                    height="1.5rem"
                  ></Skeleton>
                </h6>
                <h6
                  v-else
                  class="text-h4"
                >
                  {{ meta.stats }}
                </h6>
              </div>
            </div>

            <Skeleton
              v-if="loadingCount"
              size="3rem"
              class="me-2"
            ></Skeleton>
            <VAvatar
              v-else
              rounded
              size="3rem"
              variant="tonal"
              :color="meta.color"
              :icon="meta.icon"
            />
          </VCardText>
        </VCard>
      </VCol>

      <VCol cols="12">
        <VCard title="Filter">
          <!-- 👉 Filters -->
          <VCardText>
            <VRow>
              <VCol
                cols="12"
                sm="4"
              >
                <AppSelect
                  v-model="selectedStatus"
                  label="Subscription Status"
                  :items="status"
                  clearable
                  clear-icon="tabler-x"
                  placeholder="Select"
                />
              </VCol>
              <VCol
                cols="12"
                sm="4"
              >
                <AppSelect
                  v-model="filterInterval"
                  label="Subscription Interval"
                  :items="intervalOptions"
                  clearable
                  clear-icon="tabler-x"
                  placeholder="Select"
                />
              </VCol>
              <VCol
                cols="12"
                sm="4"
              >
                <AppDateTimePicker
                  v-model="dateRange"
                  label="Created Date"
                  placeholder="Select Date Range"
                  variant="outlined"
                  :config="{
                    mode: 'range',
                    dateFormat: 'm/d/Y',
                    onClose: function (selectedDates, dateStr, instance) {
                      if (selectedDates.length === 1) {
                        instance.setDate([selectedDates[0], selectedDates[0]], true)
                      }
                    },
                    maxDate: new Date(),
                    minDate: (new Date()).setFullYear((new Date()).getFullYear() - 120),
                  }"
                  clearable
                  clear-icon="tabler-x"
                />
              </VCol>
            </VRow>
          </VCardText>

          <VDivider />

          <VCardText class="d-flex flex-wrap py-4 gap-4">
            <div
              class="me-3"
              style="width: 80px"
            >
              <VSelect
                v-model="rowPerPage"
                density="compact"
                variant="outlined"
                :items="[10, 20, 30, 50]"
              />
            </div>

            <VSpacer />

            <div class="d-flex align-center flex-wrap gap-4">
              <!-- 👉 Search  -->
              <div style="width: 23rem">
                <VTextField
                  v-model="searchQuery"
                  placeholder="Search"
                  density="compact"
                  clearable
                  clear-icon="tabler-x"
                />
              </div>
            </div>
          </VCardText>

          <VDivider />

          <VTable class="text-no-wrap">
            <!-- 👉 table head -->
            <thead class="text-uppercase">
              <tr>
                <th scope="col">
                  #
                </th>
                <th scope="col">
                  SUBSCRIPTION ID
                </th>
                <th scope="col">
                  PRODUCT
                </th>
                <th scope="col">
                  USER
                </th>
                <th scope="col">
                  SUBSCRIPTION STATUS
                </th>
                <th scope="col">
                  Prescription Validity
                </th>
                <th
                  scope="col"
                  class="cursor-pointer"
                  @click="sortColumn('created_at')"
                >
                  <div class="w-125px">
                    CREATED AT
                    <span class="d-grid float-right">
                      <VIcon
                        size="10"
                        icon="tabler-chevron-up"
                      />
                      <VIcon
                        size="10"
                        icon="tabler-chevron-down"
                      />
                    </span>
                  </div>
                </th>
                <th
                  scope="col"
                  class="cursor-pointer"
                  @click="sortColumn('updated_at')"
                >
                  <div class="w-125px">
                    UPDATED AT
                    <span class="d-grid float-right">
                      <VIcon
                        size="10"
                        icon="tabler-chevron-up"
                      />
                      <VIcon
                        size="10"
                        icon="tabler-chevron-down"
                      />
                    </span>
                  </div>
                </th>
                <th scope="col">
                  ACTIONS
                </th>
              </tr>
            </thead>

            <!-- 👉 table body -->
            <tbody v-if="skeletonLoading">
              <tr
                v-for="n in 10"
                :key="n"
              >
                <td>
                  <Skeleton
                    width="2rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
                <td>
                  <Skeleton
                    width="6rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
                <td>
                  <Skeleton
                    width="12rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
                <td>
                  <Skeleton
                    width="5rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
                <td>
                  <Skeleton
                    width="8rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
                <td>
                  <Skeleton
                    width="8rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
                <td>
                  <Skeleton
                    width="8rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
                <td>
                  <Skeleton
                    width="8rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
                <td>
                  <Skeleton
                    width="8rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
              </tr>
            </tbody>
            <tbody v-else>
              <tr
                v-for="item in items"
                :key="item.id"
                style="height: 3.75rem"
              >
                <!-- 👉 NO -->
                <td>
                  {{ item.sno }}
                </td>

                <!-- 👉 Subscription ID -->
                <td>
                  <RouterLink
                    :to="{
                      name: 'admin-hl-manage-subscription',
                      params: { subscriptionId: item.id },
                      query: { ...route.query },
                    }"
                    class="dt-link text-base font-weight-medium"
                  >
                    {{ item.subscription_reference_id }}
                  </RouterLink>
                </td>

                <!-- 👉 Subscription -->
                <td class="title">
                  <div class="d-flex align-center py-1">
                    <VAvatar
                      v-if="item.product_img"
                      variant="tonal"
                      size="40"
                      class="me-3"
                      :image="item.product_img"
                      rounded
                    />
                    <VAvatar
                      v-else
                      variant="tonal"
                      size="40"
                      class="me-3"
                      rounded
                    >
                      {{ resolveInitials(item.product_name) }}
                    </VAvatar>
                    <div>
                      <h6 class="text-base">
                        {{ item.product_name }}
                      </h6>
                      <div class="d-flex flex-column">
                        <span class="d-block text-body-2">
                          {{ item.strength }} {{ item.strength_unit }} x {{ item.qty * item.subscription_interval }} units
                        </span>
                        <span class="d-block text-body-2">({{ item.subscription_interval * 30 }}-day supply)</span>
                        <span
                          v-if="item.pharmacy_name"
                          class="d-block text-body-2"
                        >
                          {{ item.pharmacy_name }}
                        </span>
                      </div>
                    </div>
                  </div>
                </td>

                <!-- 👉 User -->
                <td class="title">
                  <RouterLink
                    :to="{
                      name: 'admin-user-details',
                      params: { userId: item.user_id },
                    }"
                    class="d-flex align-center py-1"
                  >
                    <div>
                      <h6 class="text-base">
                        {{ item.full_name }}
                      </h6>
                      <div class="d-flex flex-column">
                        <span class="text-body-2">{{ item.email }}</span>
                        <span class="text-body-2">{{ formattedPhoneNumber(item.phone_number) }}</span>
                      </div>
                    </div>
                  </RouterLink>
                </td>

                <!-- 👉 Subscription Status -->
                <td>
                  <VChip
                    label
                    :color="resolveSubscriptionStatusColor(item.status)"
                    size="small"
                    class="text-capitalize"
                  >
                    {{ item.subscription_status }}
                  </VChip>
                </td>

                <!-- 👉 Prescription Validity -->
                <td class="pt-1">
                  <VChip
                    v-if="item.is_prescription_validity_status === 1 || item.is_prescription_validity_status === 2"
                    label
                    :color="resolvePrescriptionStatus(item.is_prescription_validity_status).color"
                    size="small"
                    class="text-capitalize"
                  >
                    {{ resolvePrescriptionStatus(item.is_prescription_validity_status).label }}
                  </VChip>
                  <span v-else> - </span>
                  <div
                    v-if="item.is_prescription_validity_status === 1"
                    class="text-body-2 mt-1"
                  >
                    {{ item.remaining_prescription_validity_days }} days left
                  </div>
                </td>

                <!-- 👉 Created at -->
                <td>
                  <div class="d-flex flex-column">
                    <span>{{ item.created_date }}</span>
                    <span>{{ item.created_time }}</span>
                  </div>
                </td>

                <!-- 👉 Updated at -->
                <td>
                  <div
                    v-if="item.updated_date !== item.created_date || item.updated_time !== item.created_time"
                    class="d-flex flex-column"
                  >
                    <span>{{ item.updated_date }}</span>
                    <span>{{ item.updated_time }}</span>
                  </div>
                  <div v-else>
                    -
                  </div>
                </td>

                <!-- 👉 Actions -->
                <td
                  class="text-center"
                  style="width: 5rem"
                >
                  <VBtn
                    v-tooltip.top="'View Details'"
                    icon
                    size="x-small"
                    color="default"
                    variant="text"
                    :to="{
                      name: 'admin-hl-manage-subscription',
                      params: { subscriptionId: item.id },
                      query: { ...route.query },
                    }"
                  >
                    <VIcon
                      size="22"
                      icon="tabler-eye"
                    />
                  </VBtn>
                </td>
              </tr>
            </tbody>

            <!-- 👉 table footer  -->
            <tfoot v-show="!skeletonLoading && items.length === 0">
              <tr>
                <td
                  colspan="8"
                  class="text-center"
                >
                  No data available
                </td>
              </tr>
            </tfoot>
          </VTable>

          <VDivider />

          <VCardText class="d-flex align-center flex-wrap justify-space-between gap-4 py-3 px-5">
            <span class="text-sm text-disabled">
              {{ paginationData }}
            </span>
            <VPagination
              v-model="currentPage"
              size="small"
              :total-visible="5"
              :length="totalPage"
            />
          </VCardText>
        </VCard>
      </VCol>
    </VRow>
  </section>
</template>

<style lang="scss">
.v-table__wrapper td.title {
  min-width: 200px;
  h6 {
    a {
      white-space: pre-line;
    }
  }
}
</style>
