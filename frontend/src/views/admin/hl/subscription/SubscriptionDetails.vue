<script setup>
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { processErrors } from '@/utils/errorHandler'
import {
  resolveSubscriptionStatusColor,
  resolvePaymentStatusColor,
  resolveTransactionTypeColor,
  resolvePrescriptionStatus,
  resolveRefundStatusColor,
} from '@/utils/admin'
import { computed, onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import { isCardExpired, formattedPhoneNumber, resolveInitials } from '@/utils/helpers'
import { isEmpty } from '@/@core/utils'
import { useUpdatePaymentMethod } from '@/store/updatePaymentMethod'

const route = useRoute()
const { showSnackbar } = useGlobalData()
const { setSubscriptionRefillId } = useUpdatePaymentMethod()

const subscriptionId = computed(() => route.params.subscriptionId)
const skeletonLoading = ref(true)
const subscription = ref({})
const nextRefillDateOptions = ref([])
const shipmentMethods = ref([])
const paymentMethods = ref([])

const snoozeLoading = ref(false)
const isSnoozeDialogVisible = ref(false)

const isCancelDialogVisible = ref(false)

const restartLoading = ref(false)
const isRestartDialogVisible = ref(false)

const generateRefillLoading = ref(false)
const isGenerateRefillDialogVisible = ref(false)

const notificationsRef = ref(null)
const isChangePaymentMethodDrawerOpen = ref(false)
const isEditShippingAddressDrawerOpen = ref(false)
const isChangeShipmentMethodDrawerOpen = ref(false)
const isChangeNextRefillDateDrawerOpen = ref(false)

const isPaymentHistoryDialogVisible = ref(false)
const selectedPaymentHistoryId = ref(null)

const selectedRefundId = ref(null)
const refundActionLoading = ref(false)
const isRefundApproveDialogVisible = ref(false)
const isRefundDeclineDialogVisible = ref(false)

const isRefundDetailsDialogVisible = ref(false)
const selectedRefundDetailsId = ref(null)
const editRefundAmountId = ref('')
const editRefundAmount = ref(0)
const isEditRefundAmountDialogVisible = ref(false)

onMounted(async () => {
  await fetchSubscriptionDetails()
})

async function fetchSubscriptionDetails() {
  try {
    skeletonLoading.value = true

    const { data } = await ApiService.get(`/admin/hl-manage-subscription/${subscriptionId.value}`)

    if (data.status === 200) {
      subscription.value = data.subscriptionDetails

      if (!isEmpty(data.subscriptionDetails.adjust_next_refill_dates)) {
        nextRefillDateOptions.value = data.subscriptionDetails.adjust_next_refill_dates
      }
      if (!isEmpty(data.subscriptionDetails.pharmacyShipmentMethods)) {
        shipmentMethods.value = data.subscriptionDetails.pharmacyShipmentMethods
      }
      if (!isEmpty(data.subscriptionDetails.user_payment_methods)) {
        paymentMethods.value = data.subscriptionDetails.user_payment_methods
      }
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    console.error(error)
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    skeletonLoading.value = false
  }
}

const isChangeNextRefillDateDisabled = computed(() => {
  const type = subscription.value?.subscription_action_type

  return [0, 1, 3, 5].includes(type) || subscription.value?.status === 0
})

const isCancelSubscriptionDisabled = computed(() => {
  const type = subscription.value?.subscription_action_type

  return [3, 5].includes(type)
})

async function handleSnoozeConfirmation(isConfirmed) {
  if (isConfirmed) {
    await snoozeNextRefill()
  }
}

async function snoozeNextRefill() {
  try {
    snoozeLoading.value = true

    const { data } = await ApiService.get(`/admin/snooze-hl-subscription/${subscriptionId.value}`)

    if (data.status === 200) {
      notificationsRef.value.reload()
      await fetchSubscriptionDetails()
      showSnackbar(data.message)
      isSnoozeDialogVisible.value = false
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    snoozeLoading.value = false
  }
}

async function handleRestartConfirmation(isConfirmed) {
  if (isConfirmed) {
    await restartSubscription()
  }
}

async function restartSubscription() {
  try {
    restartLoading.value = true

    const { data } = await ApiService.get(`/admin/reactive-hl-subscription/${subscriptionId.value}`)

    if (data.status === 200) {
      notificationsRef.value.reload()
      await fetchSubscriptionDetails()
      showSnackbar(data.message)
      isRestartDialogVisible.value = false
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    restartLoading.value = false
  }
}

async function handleGenerateRefillConfirmation(isConfirmed) {
  if (isConfirmed) {
    await generateNewRefill()
  }
}

async function generateNewRefill() {
  try {
    generateRefillLoading.value = true

    const { data } = await ApiService.get(`/admin/generate-hl-subscription-refill/${subscriptionId.value}`)

    if (data.status === 200) {
      notificationsRef.value.reload()
      await fetchSubscriptionDetails()
      showSnackbar(data.message)
      isGenerateRefillDialogVisible.value = false
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    generateRefillLoading.value = false
  }
}

async function handleRefundApproveConfirmation(isConfirmed) {
  if (isConfirmed) {
    await approveDeclineRefund({
      refund_id: selectedRefundId.value,
      refund_action_type: 'approve',
    })
  }
}

async function handleRefundDeclineConfirmation(cancelReason) {
  if (cancelReason) {
    await approveDeclineRefund({
      refund_id: selectedRefundId.value,
      refund_action_type: 'decline',
      cancel_reason: cancelReason,
    })
  }
}

async function approveDeclineRefund(requestBody) {
  try {
    refundActionLoading.value = true

    const { data } = await ApiService.post('/admin/refund-action', requestBody)

    if (data.status === 200) {
      selectedRefundId.value = null
      showSnackbar(data.message)
      fetchSubscriptionDetails()
      notificationsRef.value.reload()
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    isRefundApproveDialogVisible.value = false
    isRefundDeclineDialogVisible.value = false
    refundActionLoading.value = false
  }
}

function handleRefundAmountEdit(item) {
  editRefundAmount.value = item.refund_amount
  editRefundAmountId.value = item.id
  isEditRefundAmountDialogVisible.value = true
}

const isRetryPaymentConfirmDialogVisible = ref(false)
const retryPaymentLoading = ref(false)

async function handleRetryCaptureAuthorizePayment(isConfirmed) {
  if (isConfirmed) {
    await retryCaptureAuthorizePayment()
  }
}

async function retryCaptureAuthorizePayment() {
  try {
    retryPaymentLoading.value = true

    const { data } = await ApiService.get(
      `/admin/retry-capture-authorize-transaction/${subscription.value.is_retry_payment_summary[1]}`,
    )

    if (data.status === 200) {
      notificationsRef.value.reload()
      await fetchSubscriptionDetails()
      showSnackbar(data.message)
      isRetryPaymentConfirmDialogVisible.value = false
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    retryPaymentLoading.value = false
  }
}
</script>

<template>
  <div>
    <VRow v-if="skeletonLoading">
      <VCol cols="12">
        <div
          class="d-flex justify-center align-center"
          style="height: calc(100vh - 14rem)"
        >
          <VProgressCircular
            indeterminate
            size="48"
          />
        </div>
      </VCol>
    </VRow>
    <VRow v-else>
      <VCol
        cols="12"
        md="8"
      >
        <VCard
          :title="`${subscription.category_name} Subscription Plan`"
          class="mb-6"
        >
          <!-- 👉 Subscription details -->
          <VCardText>
            <VRow>
              <VCol cols="12">
                <div class="d-flex flex-column flex-sm-row gap-5 mb-5">
                  <VAvatar
                    rounded="sm"
                    size="80"
                    :image="subscription.product_img"
                  />
                  <div>
                    <h4 class="text-h4 font-weight-medium text-high-emphasis mb-1">
                      {{ subscription.product_name }}
                    </h4>
                    <p class="text-base">
                      <span>
                        {{ subscription.strength }} {{ subscription.strength_unit }} x {{ subscription.qty * subscription.subscription_interval }} units
                      </span>
                      <span> ({{ subscription.subscription_interval * 30 }}-day supply)</span>
                      <span class="d-block text-sm mt-1">
                        Total Quantity: {{ subscription.subscription_interval * subscription.qty }} {{ subscription.product_form + `${subscription.qty > 1 ? 's' : ''}` }} / {{ subscription.subscription_interval === 1 ? 'month' : `${subscription.subscription_interval} months` }}
                      </span>
                    </p>
                  </div>
                  <div
                    v-if="!isEmpty(subscription.is_prescription_validity_status) && subscription.is_prescription_validity_status !== 0"
                    class="ms-sm-auto"
                  >
                    <h6 class="text-base font-weight-medium text-high-emphasis mb-2">
                      Prescription Validity
                    </h6>
                    <div>
                      <VChip
                        label
                        :color="resolvePrescriptionStatus(subscription.is_prescription_validity_status).color"
                        size="small"
                        class="text-capitalize"
                      >
                        {{ resolvePrescriptionStatus(subscription.is_prescription_validity_status).label }}
                      </VChip>
                      <div
                        v-if="subscription.is_prescription_validity_status === 1"
                        class="text-body-2 mt-1"
                      >
                        {{ subscription.remaining_prescription_validity_days }} days left
                      </div>
                    </div>
                  </div>
                </div>
                <div class="sub-info-container">
                  <div>
                    <h6 class="text-base font-weight-medium text-high-emphasis mb-2">
                      Status
                    </h6>
                    <p class="text-sm">
                      <VChip
                        :color="resolveSubscriptionStatusColor(subscription.status)"
                        size="small"
                        label
                      >
                        {{ subscription.subscription_status }}
                      </VChip>
                    </p>
                  </div>
                  <div v-if="!isEmpty(subscription.subscription_reference_id)">
                    <h6 class="text-base font-weight-medium text-high-emphasis mb-2">
                      Subscription ID
                    </h6>
                    <p class="text-sm">
                      {{ subscription.subscription_reference_id }}
                    </p>
                  </div>
                  <div v-if="!isEmpty(subscription.pharmacy_name)">
                    <h6 class="text-base font-weight-medium text-high-emphasis mb-2">
                      Pharmacy
                    </h6>
                    <p class="text-sm">
                      {{ subscription.pharmacy_name }}
                    </p>
                  </div>
                  <div v-if="!isEmpty(subscription.subscribed_on)">
                    <h6 class="text-base font-weight-medium text-high-emphasis mb-2">
                      Subscribed On
                    </h6>
                    <p class="text-sm">
                      {{ subscription.subscribed_on }}
                    </p>
                  </div>
                  <div>
                    <h6 class="text-base font-weight-medium text-high-emphasis mb-2">
                      Plan Interval
                    </h6>
                    <p class="text-sm">
                      Billed every {{ subscription.subscription_interval }} month
                    </p>
                  </div>
                  <div v-if="!isEmpty(subscription.current_next_refill_date)">
                    <h6 class="text-base font-weight-medium text-high-emphasis mb-2">
                      Next Refill Date
                    </h6>
                    <p class="text-sm">
                      {{ subscription.current_next_refill_date }}
                    </p>
                  </div>
                  <div v-if="!isEmpty(subscription.next_follow_up_visit_date)">
                    <h6 class="text-base font-weight-medium text-high-emphasis mb-2">
                      Next Follow Up Date
                    </h6>
                    <p class="text-sm">
                      {{ subscription.next_follow_up_visit_date }}
                    </p>
                  </div>
                  <div v-if="!isEmpty(subscription.canceled_at)">
                    <h6 class="text-base font-weight-medium text-high-emphasis mb-2">
                      Canceled At
                    </h6>
                    <p class="text-sm d-flex flex-column">
                      <span>{{ subscription.canceled_at }}</span>
                      <span
                        v-if="subscription.action_by_user_name"
                        class="text-xs"
                      >
                        <span class="text-high-emphasis">Action by: </span>
                        <span v-if="subscription.action_by_user_name">{{ subscription.action_by_user_name + " " }}</span>
                        <span v-if="subscription.action_by_user_role">({{ subscription.action_by_user_role }})</span>
                      </span>
                    </p>
                  </div>
                  <div v-if="!isEmpty(subscription.expired_at)">
                    <h6 class="text-base font-weight-medium text-high-emphasis mb-2">
                      Expired At
                    </h6>
                    <p class="text-sm">
                      {{ subscription.expired_at }}
                    </p>
                  </div>
                </div>

                <div
                  v-if="subscription.is_removed_refill_statistics !== 1"
                  class="d-flex flex-column flex-sm-row flex-wrap gap-3"
                >
                  <VAlert variant="tonal">
                    <VAlertTitle class="mb-2">
                      <span class="text-h4">{{ subscription.total_refills_allowed }}</span>
                    </VAlertTitle>
                    <span class="text-body-1">Total Refills</span>
                  </VAlert>
                  <VAlert variant="tonal">
                    <VAlertTitle class="mb-2">
                      <span class="text-h4">{{ subscription.total_refill_used }}</span>
                    </VAlertTitle>
                    <span class="text-body-1">Used Refills</span>
                  </VAlert>
                  <VAlert variant="tonal">
                    <VAlertTitle class="mb-2">
                      <span class="text-h4">{{ subscription.remain_refill }}</span>
                    </VAlertTitle>
                    <span class="text-body-1">Remaining Refills</span>
                  </VAlert>
                </div>
                <VAlert
                  v-if="!isEmpty(subscription.dose_instruction)"
                  color="default"
                  variant="tonal"
                  class="mt-3"
                >
                  <VAlertTitle class="mb-1">
                    <span class="text-sm font-weight-bold text-high-emphasis">Dose Instruction</span>
                  </VAlertTitle>
                  <span class="text-body-1">
                    {{ subscription.dose_instruction }}
                  </span>
                </VAlert>
                <VAlert
                  v-if="!isEmpty(subscription.subscription_canceled_reason)"
                  color="error"
                  variant="tonal"
                  border-color="error"
                  class="mt-3"
                >
                  <VAlertTitle class="mb-1">
                    <span class="text-sm font-weight-bold text-high-emphasis">Cancelation Reason</span>
                  </VAlertTitle>
                  <span class="text-body-1">
                    {{ subscription.subscription_canceled_reason }}
                  </span>
                </VAlert>
                <VAlert
                  v-if="!isEmpty(subscription.expired_reason)"
                  color="error"
                  variant="tonal"
                  border-color="error"
                  class="mt-3"
                >
                  <VAlertTitle class="mb-1">
                    <span class="text-sm font-weight-bold text-high-emphasis">Expired Reason</span>
                  </VAlertTitle>
                  <span class="text-body-1">
                    {{ subscription.expired_reason }}
                  </span>
                </VAlert>

                <!-- Retry Failed Payment -->
                <VAlert
                  v-if="subscription.is_retry_payment_summary[0] === 1 || subscription.is_retry_payment_summary[0] === 2"
                  color="error"
                  variant="tonal"
                  class="mt-3"
                >
                  <div class="mb-0">
                    <div class="d-flex justify-space-between align-center">
                      <span>
                        Subscription is on hold due to a payment failure.
                      </span>
                      <VBtn
                        v-if="subscription.is_retry_payment_summary[0] === 1"
                        variant="tonal"
                        size="small"
                        @click="() => {
                          setSubscriptionRefillId(subscription.is_retry_payment_summary[1])
                          isChangePaymentMethodDrawerOpen = true
                        }"
                      >
                        Retry Payment
                      </VBtn>
                      <VBtn
                        v-if="subscription.is_retry_payment_summary[0] === 2"
                        variant="tonal"
                        size="small"
                        @click="isRetryPaymentConfirmDialogVisible = true"
                      >
                        Retry Payment
                      </VBtn>
                    </div>
                  </div>
                </VAlert>
              </VCol>
            </VRow>
          </VCardText>
        </VCard>

        <VRow class="match-height">
          <!-- 👉 Refills -->
          <VCol cols="12">
            <VCard title="Refills">
              <VCardText>
                <div
                  v-if="isEmpty(subscription.refills)"
                  class="d-flex flex-column align-center"
                >
                  <VIcon
                    icon="tabler-package-off"
                    size="48"
                  />
                  <p class="text-body-1 mt-4">
                    No refills
                  </p>
                </div>
                <VTable
                  v-else
                  class="text-no-wrap text-sm"
                >
                  <thead>
                    <tr>
                      <th class="text-uppercase text-body-2 font-weight-regular">
                        Order #
                      </th>
                      <th class="text-uppercase text-body-2 font-weight-regular">
                        Order Type
                      </th>
                      <th class="text-uppercase text-body-2 font-weight-regular">
                        Status
                      </th>
                      <th class="text-uppercase text-body-2 font-weight-regular">
                        Amount
                      </th>
                      <th class="text-uppercase text-body-2 font-weight-regular">
                        Created At
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr
                      v-for="item in subscription.refills"
                      :key="item.id"
                    >
                      <td>
                        <RouterLink
                          :to="{
                            name: 'admin-hl-order-details',
                            params: { orderId: item.id },
                            query: { ...route.query },
                          }"
                          class="text-link"
                        >
                          {{ item.order_no }}
                        </RouterLink>
                      </td>
                      <td>
                        {{ item.refill_number === 0 ? item.refill_type : `${item.refill_type} ${item.refill_number}` }}
                      </td>
                      <td>
                        <VChip
                          size="x-small"
                          label
                          :color="item.order_color"
                        >
                          {{ item.order_status }}
                        </VChip>
                      </td>
                      <td>
                        {{ formatCurrency(item.refill_amount) }}
                      </td>
                      <td>
                        <span class="d-block">{{ item.created_date }}</span>
                        <span class="d-block">{{ item.created_time }}</span>
                      </td>
                    </tr>
                  </tbody>
                </VTable>
              </VCardText>
            </VCard>
          </VCol>
          <!-- 👉 Recent Events -->
          <VCol cols="12">
            <HLSubscriptionRecentEvents
              ref="notificationsRef"
              :subscription-id="subscriptionId"
              @update-payment-method="isChangePaymentMethodDrawerOpen = true"
            />
          </VCol>

          <!-- 👉 Payment History -->
          <VCol cols="12">
            <VCard
              title="Payment History"
              class="mb-6"
            >
              <VCardText>
                <div
                  v-if="isEmpty(subscription.payment_histories)"
                  class="d-flex flex-column align-center"
                >
                  <VIcon
                    icon="tabler-package-off"
                    size="48"
                  />
                  <p class="text-body-1 mt-4">
                    No Payment History
                  </p>
                </div>
                <VTable
                  v-else
                  class="text-no-wrap text-sm"
                  fixed-header
                >
                  <thead>
                    <tr>
                      <th class="text-uppercase text-body-2">
                        Order Type
                      </th>
                      <th class="text-uppercase text-body-2">
                        Amount
                      </th>
                      <th class="text-uppercase text-body-2">
                        Transaction Type
                      </th>
                      <th class="text-uppercase text-body-2">
                        Status
                      </th>
                      <th class="text-uppercase text-body-2">
                        Transaction ID
                      </th>
                      <th class="text-uppercase text-body-2">
                        Payment Method
                      </th>
                      <th class="text-uppercase text-body-2">
                        Created At
                      </th>
                      <th class="text-uppercase text-body-2">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr
                      v-for="item in subscription.payment_histories"
                      :key="item.id"
                    >
                      <td>
                        {{ item.refill_number === 0 ? item.refill_type : `${item.refill_type} ${item.refill_number}` }}
                      </td>
                      <td>
                        <strong>{{ formatCurrency(item.amount) }}</strong>
                      </td>
                      <td>
                        <VChip
                          size="x-small"
                          label
                          :color="resolveTransactionTypeColor(item.transaction_type)"
                        >
                          {{ item.transaction_type_text }}
                        </VChip>
                      </td>
                      <td>
                        <VChip
                          size="x-small"
                          label
                          :color="resolvePaymentStatusColor(item.status)"
                        >
                          {{ item.payment_status_text }}
                        </VChip>
                      </td>
                      <td>
                        {{ item.transaction_id ?? '-' }}
                      </td>
                      <td>
                        <div>{{ item.card_brand_type }}</div>
                        <div>xxxx xxxx xxxx {{ item.card_number }}</div>
                      </td>
                      <td>
                        <span class="d-block">{{ item.created_date }}</span>
                        <span class="d-block">{{ item.created_time }}</span>
                      </td>
                      <td class="text-right">
                        <VTooltip text="View Details">
                          <template #activator="{ props }">
                            <VBtn
                              v-bind="props"
                              icon
                              color="default"
                              variant="text"
                              @click="() => {
                                selectedPaymentHistoryId = item.id
                                isPaymentHistoryDialogVisible = true
                              }"
                            >
                              <VIcon
                                icon="tabler-eye"
                                class="cursor-pointer"
                              />
                            </VBtn>
                          </template>
                        </VTooltip>
                      </td>
                    </tr>
                  </tbody>
                </VTable>
              </VCardText>
            </VCard>
          </VCol>

          <!-- 👉 Refund History -->
          <VCol cols="12">
            <VCard
              title="Refund History"
              class="mb-6"
            >
              <VCardText>
                <div
                  v-if="isEmpty(subscription.refund_request_history)"
                  class="d-flex flex-column align-center"
                >
                  <VIcon
                    icon="tabler-package-off"
                    size="48"
                  />
                  <p class="text-body-1 mt-4">
                    No Refund History
                  </p>
                </div>
                <VTable
                  v-else
                  class="text-no-wrap text-sm"
                >
                  <thead>
                    <tr>
                      <th class="text-uppercase text-body-2">
                        Order Type
                      </th>
                      <th class="text-uppercase text-body-2">
                        Refund Amount
                      </th>
                      <th class="text-uppercase text-body-2">
                        Refund Status
                      </th>
                      <th class="text-uppercase text-body-2">
                        Created At
                      </th>
                      <th class="text-uppercase text-body-2 text-end">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr
                      v-for="item in subscription.refund_request_history"
                      :key="item.id"
                    >
                      <td>
                        {{ item.refill_number === 0 ? item.refill_type : `${item.refill_type} ${item.refill_number}` }}
                      </td>
                      <td>
                        <strong>{{ formatCurrency(item.refund_amount) }}</strong> <br />
                        <button
                          v-if="item.refund_status === 0 || item.refund_status === 3"
                          class="text-link text-sm text-decoration-underline"
                          @click="handleRefundAmountEdit(item)"
                        >
                          Edit
                        </button>
                      </td>
                      <td>
                        <VChip
                          size="x-small"
                          label
                          :color="resolveRefundStatusColor(item.refund_status)"
                        >
                          {{ item.refund_status_text }}
                        </VChip>
                      </td>
                      <td>
                        <span class="d-block">{{ item.created_date }}</span>
                        <span class="d-block">{{ item.created_time }}</span>
                      </td>
                      <td>
                        <div
                          v-if="[0, 3].includes(item.refund_status)"
                          class="text-end"
                        >
                          <VTooltip text="Approve Refund">
                            <template #activator="{ props }">
                              <VBtn
                                v-bind="props"
                                size="x-small"
                                icon
                                color="success"
                                variant="tonal"
                                @click="() => {
                                  selectedRefundId = item.id
                                  isRefundApproveDialogVisible = true
                                }"
                              >
                                <VIcon
                                  icon="tabler-check"
                                  class="cursor-pointer"
                                  size="20"
                                />
                              </VBtn>
                            </template>
                          </VTooltip>
                          <VTooltip text="Decline Refund">
                            <template #activator="{ props }">
                              <VBtn
                                v-bind="props"
                                size="x-small"
                                icon
                                color="error"
                                variant="tonal"
                                class="ms-2"
                                @click="() => {
                                  selectedRefundId = item.id
                                  isRefundDeclineDialogVisible = true
                                }"
                              >
                                <VIcon
                                  icon="tabler-x"
                                  class="cursor-pointer"
                                  size="20"
                                />
                              </VBtn>
                            </template>
                          </VTooltip>
                        </div>
                        <div
                          v-else
                          class="text-end"
                        >
                          <VTooltip text="View Details">
                            <template #activator="{ props }">
                              <VBtn
                                v-bind="props"
                                size="x-small"
                                icon
                                color="default"
                                variant="text"
                                @click="() => {
                                  selectedRefundDetailsId = item.id
                                  isRefundDetailsDialogVisible = true
                                }"
                              >
                                <VIcon
                                  icon="tabler-eye"
                                  class="cursor-pointer"
                                  size="20"
                                />
                              </VBtn>
                            </template>
                          </VTooltip>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </VTable>
              </VCardText>
            </VCard>
          </VCol>
        </VRow>
      </VCol>
      <VCol
        cols="12"
        md="4"
      >
        <!-- 👉 Actions -->
        <VCard
          title="Actions"
          class="mb-6"
        >
          <VCardText class="d-flex flex-column gap-3">
            <VBtn
              v-if="!isEmpty(subscription.is_subscription_snoozed) && subscription.is_subscription_snoozed === 0"
              color="primary"
              block
              @click="isSnoozeDialogVisible = true"
            >
              Snooze next order
            </VBtn>
            <VBtn
              color="primary"
              block
              :disabled="subscription.is_new_refill_generate_btn_action !== 1"
              @click="isGenerateRefillDialogVisible = true"
            >
              Generate New Refill
            </VBtn>
            <VBtn
              color="primary"
              block
              :disabled="isChangeNextRefillDateDisabled"
              @click="isChangeNextRefillDateDrawerOpen = true"
            >
              Change Next Refill Date
            </VBtn>
            <VBtn
              color="error"
              block
              :disabled="isCancelSubscriptionDisabled"
              @click="isCancelDialogVisible = true"
            >
              Cancel Subscription
            </VBtn>
            <VBtn
              v-if="!isEmpty(subscription.is_reactive_canceled_subscription) && subscription.is_reactive_canceled_subscription === 1"
              color="primary"
              block
              @click="isRestartDialogVisible = true"
            >
              Restart Subscription
            </VBtn>
          </VCardText>
        </VCard>

        <!-- 👉 Summary -->
        <VCard title="Summary">
          <VCardText>
            <div class="d-flex gap-4 align-center">
              <VAvatar
                v-if="subscription?.user_details?.selfie"
                :image="subscription?.user_details?.selfie"
                variant="tonal"
                size="60"
                rounded="lg"
              />
              <VAvatar
                v-else
                variant="tonal"
                size="60"
              >
                {{ resolveInitials(subscription?.user_details?.full_name || 'Not Available') }}
              </VAvatar>
              <div>
                <h5 class="text-h5">
                  {{ subscription?.user_details?.full_name }}
                </h5>
                <p class="text-body-2 mb-0">
                  {{ subscription?.user_details?.email }}
                </p>
                <p class="text-body-2 mb-0">
                  {{ formattedPhoneNumber(subscription?.user_details?.phone_number) }}
                </p>
              </div>
            </div>

            <VDivider class="my-4 border-dashed" />

            <div>
              <h5 class="text-h5 mb-3">
                Visit Details
              </h5>
              <div>
                <div
                  v-if="!isEmpty(subscription.master_id)"
                  class="d-flex justify-space-between py-1 text-sm"
                >
                  <div>Master ID</div>
                  <div class="font-weight-medium">
                    <span>{{ subscription.master_id }}</span>
                  </div>
                </div>
              </div>
            </div>

            <VDivider
              v-if="!isEmpty(subscription?.pharmacy_prescription_script_number)"
              class="my-4 border-dashed"
            />

            <div v-if="!isEmpty(subscription?.pharmacy_prescription_script_number)">
              <h5 class="text-h5 mb-3">
                Pharmacy Prescription Details
              </h5>
              <div>
                <div class="d-flex justify-space-between py-1 text-sm">
                  <div>Prescription Script Number</div>
                  <div class="font-weight-medium">
                    <span>{{ subscription.pharmacy_prescription_script_number }}</span>
                  </div>
                </div>
              </div>
            </div>

            <VDivider
              v-if="!isEmpty(subscription?.doctor_response)"
              class="my-4 border-dashed"
            />

            <div v-if="!isEmpty(subscription?.doctor_response)">
              <h5 class="text-h5 mb-3">
                Doctor Response
              </h5>
              <div
                v-for="(item, index) in subscription?.doctor_response"
                :key="index"
                class="mb-1 text-sm"
              >
                <strong>{{ item.event }}</strong> at {{ item.date_time }}
              </div>
            </div>

            <VDivider class="my-4 border-dashed" />

            <div>
              <h5 class="text-h5 mb-3">
                Payment Details
              </h5>
              <div>
                <!-- Plan Amount -->
                <div
                  v-if="!isEmpty(subscription?.order?.subscription_plan_amount)"
                  class="d-flex justify-space-between py-1 text-sm"
                >
                  <div>{{ subscription?.subscription_interval }} month plan</div>
                  <div class="font-weight-medium">
                    <span>{{ formatCurrency(subscription?.order?.subscription_plan_amount) }}</span>
                  </div>
                </div>
                <!-- Discount -->
                <div
                  v-if="!isEmpty(subscription?.order?.promo_code_discount_amount) && subscription?.order?.promo_code_discount_amount !== 0"
                  class="d-flex justify-space-between align-center py-1 text-sm"
                >
                  <div>
                    <div>Promotion applied</div>
                    <div class="text-high-emphasis font-weight-medium">
                      {{ subscription?.order?.promo_code_name }} <span v-if="subscription?.order?.promo_code_type === 'percentage'">({{ subscription?.order?.promo_code_value }}%)</span>
                    </div>
                  </div>
                  <div class="font-weight-medium text-error">
                    <span>
                      -{{ formatCurrency(subscription?.order?.promo_code_discount_amount) }}
                    </span>
                  </div>
                </div>
                <!-- First Order Discount -->
                <div
                  v-if="!isEmpty(subscription?.order?.first_time_order_discount_amount) && subscription?.order?.first_time_order_discount_amount !== 0"
                  class="d-flex justify-space-between py-1 text-sm"
                >
                  <div>First Order Discount</div>
                  <div class="font-weight-medium text-error">
                    <span>
                      -{{ formatCurrency(subscription?.order?.first_time_order_discount_amount) }}
                    </span>
                  </div>
                </div>
                <!-- Subtotal -->
                <div
                  v-if="!isEmpty(subscription?.order?.sub_total) && subscription?.order?.sub_total > 0"
                  class="d-flex justify-space-between py-1 text-sm"
                >
                  <div>Subtotal</div>
                  <div class="font-weight-medium">
                    <span>{{ formatCurrency(subscription?.order?.sub_total) }}</span>
                  </div>
                </div>
                <!-- Consultation fee -->
                <div
                  v-if="!isEmpty(subscription?.order?.provider_consultation_fee)"
                  class="d-flex justify-space-between py-1 text-sm"
                >
                  <div>Physician and Processing Fee</div>
                  <div class="font-weight-medium">
                    <span
                      v-if="subscription?.order?.provider_consultation_fee === 0"
                      class="text-success"
                    >FREE</span>
                    <span v-else>{{ formatCurrency(subscription?.order?.provider_consultation_fee) }}</span>
                  </div>
                </div>
                <!-- Convenience fee -->
                <div
                  v-if="!isEmpty(subscription?.order?.convenience_fee) && subscription?.order?.convenience_fee !== 0"
                  class="d-flex justify-space-between py-1 text-sm"
                >
                  <div>Convenience fee</div>
                  <div class="font-weight-medium">
                    <span>{{ formatCurrency(subscription?.order?.convenience_fee) }}</span>
                  </div>
                </div>
                <!-- Shipping & handling cost -->
                <div
                  v-if="!isEmpty(subscription?.order?.shipping_handling_fee)"
                  class="d-flex justify-space-between py-1 text-sm"
                >
                  <div>Shipping & handling cost</div>
                  <div class="font-weight-medium">
                    <span
                      v-if="subscription?.order?.shipping_handling_fee === 0"
                      class="text-success"
                    >FREE</span>
                    <span v-else>{{ formatCurrency(subscription?.order?.shipping_handling_fee) }}</span>
                  </div>
                </div>
                <!-- Total -->
                <div
                  v-if="!isEmpty(subscription?.order?.total_amount) && subscription?.order?.total_amount !== 0"
                  class="d-flex justify-space-between py-1 text-base"
                >
                  <div>Total</div>
                  <div class="font-weight-semibold">
                    <span>{{ formatCurrency(subscription?.order?.total_amount) }}</span>
                  </div>
                </div>
              </div>
            </div>

            <VDivider
              v-if="
                subscription?.order?.subscription_payment_method_details?.card_last_4_digit
                  && subscription?.order?.subscription_payment_method_details?.card_expiry_year
                  && subscription?.order?.subscription_payment_method_details?.card_expiry_month
              "
              class="my-4 border-dashed"
            />

            <div
              v-if="
                subscription?.order?.subscription_payment_method_details?.card_last_4_digit
                  && subscription?.order?.subscription_payment_method_details?.card_expiry_year
                  && subscription?.order?.subscription_payment_method_details?.card_expiry_month
              "
            >
              <div class="d-flex justify-space-between align-center gap-4 mb-4">
                <h5 class="text-h5">
                  Payment Method
                </h5>
                <VBtn
                  v-if="subscription.subscription_action_type !== 3 && subscription.subscription_action_type !== 5"
                  color="primary"
                  size="x-small"
                  @click="() => {
                    setSubscriptionRefillId(null)
                    isChangePaymentMethodDrawerOpen = true
                  }"
                >
                  Change
                </VBtn>
              </div>
              <div>
                <div class="text-sm uppercase font-weight-medium text-zinc-500">
                  {{ subscription?.order?.subscription_payment_method_details?.card_brand_type }}
                </div>
                <div class="text-base">
                  xxxx xxxx xxxx {{ subscription?.order?.subscription_payment_method_details?.card_last_4_digit }}
                </div>
                <div class="w-full text-sm">
                  <VChip
                    v-if="isCardExpired(
                      subscription?.order?.subscription_payment_method_details?.card_expiry_year,
                      subscription?.order?.subscription_payment_method_details?.card_expiry_month
                    )"
                    class="mt-1"
                    color="error"
                    size="small"
                    label
                  >
                    Expired
                  </VChip>
                  <span v-else>
                    Expires {{ subscription?.order?.subscription_payment_method_details?.card_expiry_month }}/{{ subscription?.order?.subscription_payment_method_details?.card_expiry_year }}
                  </span>
                </div>
              </div>
            </div>

            <VDivider class="my-4 border-dashed" />

            <div>
              <h5 class="text-h5 mb-3">
                Shipping Details
              </h5>
              <div class="d-flex justify-space-between align-center gap-4 mb-1">
                <div class="d-flex align-baseline">
                  <h6 class="text-h6">
                    Shipping Method
                  </h6>
                  <VTooltip
                    v-if="Boolean(subscription.is_shipment_method_change)"
                    text="For upcoming refills"
                  >
                    <template #activator="{ props }">
                      <button
                        v-bind="props"
                        class="ms-1"
                      >
                        <VIcon
                          icon="tabler-info-circle"
                          size="x-small"
                        />
                      </button>
                    </template>
                  </VTooltip>
                </div>
                <div v-if="Boolean(subscription.is_shipment_method_change)">
                  <VBtn
                    v-if="subscription.subscription_action_type !== 3 && subscription.subscription_action_type !== 5"
                    color="primary"
                    size="x-small"
                    @click="isChangeShipmentMethodDrawerOpen = true"
                  >
                    Change
                  </VBtn>
                </div>
              </div>
              <div class="text-medium-emphasis mb-4">
                {{ subscription?.order?.pharmacy_shipment_method_title }}
              </div>

              <div class="d-flex justify-space-between align-center gap-4 mb-1">
                <h6 class="text-h6">
                  Shipping Address
                </h6>
                <VBtn
                  v-if="subscription.subscription_action_type !== 3 && subscription.subscription_action_type !== 5"
                  color="primary"
                  size="x-small"
                  @click="isEditShippingAddressDrawerOpen = true"
                >
                  Edit
                </VBtn>
              </div>
              <div class="text-medium-emphasis">
                {{ subscription?.order?.shipping_address?.address_line_1 }} <br>
                <span v-if="subscription?.order?.shipping_address?.address_line_2">
                  {{ subscription?.order?.shipping_address?.address_line_2 }} <br>
                </span>
                {{ subscription?.order?.shipping_address?.city }}, {{ subscription?.order?.shipping_address?.state }}-{{ subscription?.order?.shipping_address?.zipcode }} <br>
                {{ subscription?.order?.shipping_address?.country }}
              </div>
            </div>

            <VDivider
              v-if="!isEmpty(subscription?.affiliate_user)"
              class="my-4 border-dashed"
            />

            <div v-if="!isEmpty(subscription?.affiliate_user)">
              <h5 class="text-h5 mb-3">
                Affiliate By
              </h5>
              <div class="d-flex align-center py-1">
                <VAvatar
                  v-if="subscription.affiliate_user.profile_picture"
                  variant="tonal"
                  size="40"
                  class="me-3"
                  :image="subscription.affiliate_user.profile_picture"
                  rounded
                />
                <VAvatar
                  v-else
                  variant="tonal"
                  size="40"
                  class="me-3"
                  rounded
                >
                  {{ resolveInitials(subscription.affiliate_user.first_name + ' ' + subscription.affiliate_user.last_name) }}
                </VAvatar>
                <div>
                  <h6 class="text-base">
                    {{ subscription.affiliate_user.first_name }} {{ subscription.affiliate_user.last_name }}
                  </h6>
                  <div class="d-flex flex-column">
                    <span class="text-body-2">{{ subscription.affiliate_user.email }}</span>
                    <span class="text-body-2">{{ formattedPhoneNumber(subscription.affiliate_user.phone_number) }}</span>
                  </div>
                </div>
              </div>
              <VBtn
                variant="tonal"
                size="small"
                block
                class="mt-2"
                :to="{
                  name: 'admin-affiliate-user-details',
                  params: { userId: subscription.affiliate_user.id },
                }"
              >
                View Profile
              </VBtn>
            </div>
          </VCardText>
        </VCard>
      </VCol>
    </VRow>

    <!-- 👉 Change Payment Method -->
    <HLSubscriptionChangePaymentMethod
      v-model:isDrawerOpen="isChangePaymentMethodDrawerOpen"
      :subscription-id="subscriptionId"
      :payment-methods="paymentMethods"
      :current-payment-method-id="subscription?.order?.subscription_payment_method_details?.id"
      @updated="fetchSubscriptionDetails"
    />

    <!-- 👉 Change Shipment Method -->
    <HLSubscriptionChangeShipmentMethod
      v-model:isDrawerOpen="isChangeShipmentMethodDrawerOpen"
      :subscription-id="subscriptionId"
      :shipment-methods="shipmentMethods"
      :current-shipment-method-id="subscription?.order?.current_pharmacy_shipment_id"
      @updated="fetchSubscriptionDetails"
    />

    <!-- 👉 Update Shipping Address -->
    <HLSubscriptionEditShippingAddress
      v-model:isDrawerOpen="isEditShippingAddressDrawerOpen"
      :subscription-id="subscriptionId"
      :shipping-address="subscription?.order?.shipping_address"
      @updated="fetchSubscriptionDetails"
    />

    <!-- 👉 Change Next Refill Date -->
    <HLSubscriptionChangeNextRefillDate
      v-model:isDrawerOpen="isChangeNextRefillDateDrawerOpen"
      :subscription-id="subscriptionId"
      :refill-options="nextRefillDateOptions"
      @updated="fetchSubscriptionDetails"
    />

    <!-- 👉 Confirm Next Order Snooze -->
    <ConfirmDialog
      v-model:isDialogVisible="isSnoozeDialogVisible"
      confirmation-question="Are you sure you want to snooze next refill?"
      :loading="snoozeLoading"
      @confirm="handleSnoozeConfirmation"
    />

    <!-- 👉 Confirm Subscription Cancelation -->
    <HLSubscriptionCancelDialog
      v-model:isDialogVisible="isCancelDialogVisible"
      :subscription-id="subscriptionId"
      @canceled="fetchSubscriptionDetails"
    />

    <!-- 👉 Confirm Subscription Restart -->
    <ConfirmDialog
      v-model:isDialogVisible="isRestartDialogVisible"
      confirmation-question="Are you sure you want to restart this subscription?"
      :loading="restartLoading"
      @confirm="handleRestartConfirmation"
    />

    <!-- 👉 Confirm Generate New Refill -->
    <ConfirmDialog
      v-model:isDialogVisible="isGenerateRefillDialogVisible"
      confirmation-question="Are you sure you want to generate new refill for this subscription?"
      :loading="generateRefillLoading"
      @confirm="handleGenerateRefillConfirmation"
    />

    <!-- 👉 Payment History Dialog -->
    <PaymentHistoryDialog
      v-model:isDialogVisible="isPaymentHistoryDialogVisible"
      :payment-history-id="selectedPaymentHistoryId"
    />

    <!-- 👉 Confirm Refund Approve -->
    <ConfirmDialog
      v-model:isDialogVisible="isRefundApproveDialogVisible"
      confirmation-question="Are you sure you want to approve this refund request?"
      :loading="refundActionLoading"
      @confirm="handleRefundApproveConfirmation"
    />

    <!-- 👉 Refund Decline Dialog -->
    <RefundDeclineDialog
      v-model:isDialogVisible="isRefundDeclineDialogVisible"
      @submit="handleRefundDeclineConfirmation"
    />

    <!-- 👉 Refund Details Dialog -->
    <RefundDetailsDialog
      v-model:isDialogVisible="isRefundDetailsDialogVisible"
      :refund-history-id="selectedRefundDetailsId"
    />

    <!-- 👉 Edit Refund Amount Dialog -->
    <EditRefundAmountDialog
      v-model:isDialogVisible="isEditRefundAmountDialogVisible"
      :refund-id="editRefundAmountId"
      :refund-amount="editRefundAmount"
      @updated="fetchSubscriptionDetails"
    />

    <!-- 👉 Confirm Retry Failed Payment -->
    <ConfirmDialog
      v-model:isDialogVisible="isRetryPaymentConfirmDialogVisible"
      confirmation-question="Would you like to retry capturing the authorized payment for this order?"
      :loading="retryPaymentLoading"
      @confirm="handleRetryCaptureAuthorizePayment"
    />
  </div>
</template>
