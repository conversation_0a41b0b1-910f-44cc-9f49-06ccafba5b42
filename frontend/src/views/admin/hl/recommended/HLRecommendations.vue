<script setup>
import { isEmpty } from '@/@core/utils'
import { requiredValidator } from '@/@core/utils/validators'
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { processErrors } from '@/utils/errorHandler'
import { IconCaretUpDownFilled, IconListDetails } from '@tabler/icons-vue'
import Image from 'primevue/image'
import { onMounted, ref } from 'vue'

const { showSnackbar } = useGlobalData()

const VIEW_SECTION = {
  LOADING_VIEW: 'LOADING_VIEW',
  EMPTY_VIEW: 'EMPTY_VIEW',
  RECOMMENDED_VIEW: 'RECOMMENDED_VIEW',
  FORM_VIEW: 'FORM_VIEW',
}

const currentView = ref(VIEW_SECTION.LOADING_VIEW)

const pageHeading = computed(() => {
  if (currentView.value === VIEW_SECTION.RECOMMENDED_VIEW) {
    return 'Recommended Products'
  } else if (currentView.value === VIEW_SECTION.FORM_VIEW) {
    return editId.value ? 'Edit Recommendation' : 'Add Recommendation'
  }

  return null
})

const formRef = ref(null)
const recommendedProducts = ref([])
const products = ref([])
const selectedProduct = ref(null)
const selectedStrength = ref(null)
const isLoading = ref(false)
const serverErrors = ref([])
const editId = ref(null)
const deleteId = ref(null)
const isDeleteDialogVisible = ref(false)
const deleteLoading = ref(false)
const draggingItemId = ref(null)

function onProductChange() {
  selectedStrength.value = null
}

const productOptions = computed(() => {
  return products.value || []
})

const strengthOptions = computed(() => {
  if (selectedProduct.value) {
    return products.value
      ?.find(p => p.id === selectedProduct.value)
      ?.hl_product_strengths
  }

  return []
})

onMounted(async () => {
  await Promise.all([getRecommendedProductList(), getProductList()])
})

async function getRecommendedProductList() {
  try {
    const { data } = await ApiService.get('/admin/get-hl-product-recommended-lists')

    if (data.status === 200) {
      if (!isEmpty(data.recommendhl_product_lists)) {
        recommendedProducts.value = data.recommendhl_product_lists
        currentView.value = VIEW_SECTION.RECOMMENDED_VIEW
      } else {
        currentView.value = VIEW_SECTION.EMPTY_VIEW
      }
    } else {
      if (data.message) {
        showSnackbar(data.message, 'error')
      } else {
        showSnackbar(processErrors(data)[0], 'error')
      }
    }
  } catch (error) {
    showSnackbar(processErrors(error)[0], 'error')
  }
}

async function getProductList() {
  try {
    const { data } = await ApiService.get('/admin/get-hl-product-recommendation-lists')

    if (data.status === 200) {
      products.value = data.productList
    } else {
      if (data.message) {
        showSnackbar(data.message, 'error')
      } else {
        showSnackbar(processErrors(data)[0], 'error')
      }
    }
  } catch (error) {
    showSnackbar(processErrors(error)[0], 'error')
  }
}

function onClickFormCancel() {
  if (!isEmpty(recommendedProducts.value)) {
    currentView.value = VIEW_SECTION.RECOMMENDED_VIEW
  } else {
    currentView.value = VIEW_SECTION.EMPTY_VIEW
  }

  selectedProduct.value = null
  selectedStrength.value = null
  editId.value = null
  serverErrors.value = []
}

async function handleSubmit() {
  formRef.value?.validate().then(async result => {
    if (result.valid) {
      await addUpdateProductRecommendation()
    }
  })
}

async function addUpdateProductRecommendation() {
  try {
    isLoading.value = true
    serverErrors.value = null

    const postData = {
      'hl_product_id': selectedProduct.value,
      'hl_product_strength_id': selectedStrength.value,
    }

    if (editId.value) {
      postData['hl_product_recommendation_id'] = editId.value
    }

    const { data } = await ApiService.post('/admin/add-edit-hl-product-recommendations', postData)

    if (data.status === 200) {
      showSnackbar(data.message)
      onClickFormCancel()
      getRecommendedProductList()
    } else {
      serverErrors.value = processErrors(data)
    }
  } catch (error) {
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    isLoading.value = false
  }
}

function onClickEdit(product) {
  currentView.value = VIEW_SECTION.FORM_VIEW
  editId.value = product.id
  selectedProduct.value = product.hl_product_id
  selectedStrength.value = product.hl_product_strength_id
}

function onClickDelete(product) {
  deleteId.value = product.id
  isDeleteDialogVisible.value = true
}

async function handleDeleteConfirmation(isConfirmed) {
  if (isConfirmed) {
    await deleteRecommendation()
  }
}

async function deleteRecommendation() {
  try {
    deleteLoading.value = true

    const { data } = await ApiService.delete(`/admin/delete-hl-recommended-product/${deleteId.value}`)

    if (data.status === 200) {
      showSnackbar(data.message)
      deleteId.value = null
      getRecommendedProductList()
    } else {
      if (data.message) {
        showSnackbar(data.message, 'error')
      } else {
        showSnackbar(processErrors(data)[0], 'error')
      }
    }
  } catch (error) {
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    deleteLoading.value = false
    isDeleteDialogVisible.value = false
  }
}

const dragStart = itemId => {
  draggingItemId.value = itemId
}

const dragOver = itemId => {
  event.preventDefault()

  const draggedItemIndex = recommendedProducts.value.findIndex(item => item.id === draggingItemId.value)
  const targetItemIndex = recommendedProducts.value.findIndex(item => item.id === itemId)

  if (draggedItemIndex !== -1 && targetItemIndex !== -1 && draggedItemIndex !== targetItemIndex) {
    const draggedItem = recommendedProducts.value.splice(draggedItemIndex, 1)[0]

    recommendedProducts.value.splice(targetItemIndex, 0, draggedItem)
    recommendedProducts.value = recommendedProducts.value.map((d, index) => ({ ...d, sno: index + 1 }))
  }
}

const drop = event => {
  event.preventDefault()
}

const dragEnd = () => {
  const itemsIds = recommendedProducts.value.map(item => item.id)

  ApiService
    .post('/admin/reorder-hl-product-recommendations', { ids: itemsIds })
    .then(({ data }) => {
      if (data.status === 200) {
        //
      } else {
        showSnackbar(data.message, 'error')
      }
    })
    .catch(error => {
      console.error(error)
    })
    .finally(() => {
      draggingItemId.value = null
    })
}
</script>

<template>
  <div>
    <VCard>
      <VCardTitle
        v-if="pageHeading"
        class="pa-6"
      >
        <div class="text-h5">
          {{ pageHeading }}
        </div>
      </VCardTitle>

      <!-- 👉 Loading View -->
      <VCardText v-if="currentView === VIEW_SECTION.LOADING_VIEW">
        <div
          class="d-flex flex-column align-center justify-center"
          style="height: 200px;"
        >
          <VProgressCircular
            size="42"
            indeterminate
          />
        </div>
      </VCardText>

      <!-- 👉 No Recommendations -->
      <VCardText v-if="currentView === VIEW_SECTION.EMPTY_VIEW">
        <div class="d-flex flex-column align-center gap-2">
          <IconListDetails
            size="80"
            stroke-width="1.5"
          />
          <div class="text-lg">
            No product recommendations added
          </div>
          <div class="mt-6">
            <VBtn @click="currentView = VIEW_SECTION.FORM_VIEW">
              Add New Recommendation
            </VBtn>
          </div>
        </div>
      </VCardText>

      <!-- 👉 Recommended List -->
      <VCardText v-if="currentView === VIEW_SECTION.RECOMMENDED_VIEW">
        <VCard
          v-for="product in recommendedProducts"
          :key="product.id"
          class="pa-3 border-sm border-dashed mb-3 d-flex align-center gap-4"
          :draggable="true"
          @dragstart="dragStart(product.id)"
          @dragover="dragOver(product.id)"
          @drop="drop"
          @dragend="dragEnd"
        >
          <div>
            <IconCaretUpDownFilled style="height: 1.5rem; width: 1.5rem;" />
          </div>
          <div class="d-flex flex-column flex-md-row gap-4">
            <div>
              <VAvatar
                variant="tonal"
                color="primary"
                size="100"
                class="me-3 rounded-lg"
              >
                <Image
                  :src="product.strength_image"
                  height="100"
                  width="100"
                  image-class="object-fit-cover"
                  preview
                />
              </VAvatar>
            </div>
            <div>
              <div class="mb-2">
                <h5 class="text-h5">
                  {{ product.product_name }}
                </h5>
                <p class="text-sm">
                  {{ product.pharmacy_name }}
                </p>
              </div>
              <div class="d-flex gap-2">
                <VChip
                  variant="flat"
                  color="primary"
                >
                  {{ product.strength }}{{ product.strength_unit }}
                </VChip>
                <VChip
                  variant="flat"
                  color="primary"
                >
                  {{ product.qty }} units/mo
                </VChip>
                <VChip
                  variant="flat"
                  color="primary"
                >
                  {{ formatCurrency(product.base_price_per_pill) }}/unit
                </VChip>
              </div>
              <div class="mt-4 d-flex gap-4">
                <VBtn
                  size="small"
                  variant="tonal"
                  @click="onClickEdit(product)"
                >
                  Edit recommendation
                </VBtn>
                <VBtn
                  color="error"
                  size="small"
                  variant="tonal"
                  @click="onClickDelete(product)"
                >
                  Delete recommendation
                </VBtn>
              </div>
            </div>
          </div>
        </VCard>
        <div class="mt-6">
          <VBtn
            block
            @click="currentView = VIEW_SECTION.FORM_VIEW"
          >
            Add New Recommendation
          </VBtn>
        </div>
      </VCardText>

      <!-- 👉 Recommend new product Form -->
      <VCardText v-if="currentView === VIEW_SECTION.FORM_VIEW">
        <VAlert
          v-if="!isEmpty(serverErrors)"
          type="error"
          variant="tonal"
          title="Error!"
          class="mb-6"
        >
          <ul class="mb-0">
            <li
              v-for="error in serverErrors"
              :key="error"
              class="mb-0"
            >
              {{ error }}
            </li>
          </ul>
        </VAlert>

        <VForm
          ref="formRef"
          @submit.prevent="handleSubmit"
        >
          <VRow>
            <VCol cols="12">
              <VSelect
                v-model="selectedProduct"
                label="Select Product"
                :items="productOptions"
                item-title="product_name"
                item-value="id"
                :rules="[requiredValidator]"
                @update:model-value="onProductChange"
              >
                <template #selection="{ item }">
                  <span>
                    {{ item?.raw?.product_name }} <span v-if="item?.raw?.pharmacy_name">
                      ({{ item?.raw?.pharmacy_name }})
                    </span>
                  </span>
                </template>
                <template #item="{ props, item }">
                  <VListItem
                    v-bind="props"
                    class="py-2"
                    :title="item?.raw?.product_name"
                  >
                    <div class="text-sm">
                      {{ item?.raw?.pharmacy_name }}
                    </div>
                  </VListItem>
                </template>
              </VSelect>
            </VCol>
            <VCol
              v-if="selectedProduct"
              cols="12"
            >
              <VSelect
                v-model="selectedStrength"
                label="Select Strength"
                :items="strengthOptions"
                item-value="id"
                :rules="[requiredValidator]"
              >
                <template #selection="{ item }">
                  <span>{{ item?.raw?.strength }} {{ item?.raw?.strength_unit }}</span>
                </template>
                <template #item="{ props, item }">
                  <VListItem
                    v-bind="props"
                    :title="`${item?.raw?.strength} ${item?.raw?.strength_unit}`"
                    class="py-2"
                  ></VListItem>
                </template>
              </VSelect>
            </VCol>

            <VCol cols="12">
              <div class="d-flex justify-end gap-3">
                <VBtn
                  type="button"
                  variant="tonal"
                  color="secondary"
                  :disabled="isLoading"
                  @click="onClickFormCancel"
                >
                  Cancel
                </VBtn>
                <VBtn
                  type="submit"
                  :loading="isLoading"
                >
                  Submit
                </VBtn>
              </div>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>

    <!-- 👉 Delete confirm dialog -->
    <ConfirmDialog
      v-model:isDialogVisible="isDeleteDialogVisible"
      confirmation-question="Are you sure you want to delete this recommendation?"
      :loading="deleteLoading"
      @confirm="handleDeleteConfirmation"
    />
  </div>
</template>
