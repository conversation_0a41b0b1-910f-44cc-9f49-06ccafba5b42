<script setup>
import ChangePassword from './ChangePassword.vue'
import TwoFactorAuth from './TwoFactorAuth.vue'

const profileTab = ref(null)

const tabs = [
  {
    icon: 'tabler-password',
    title: 'Change Password',
  },
  {
    icon: 'tabler-auth-2fa',
    title: 'Two Factor Authentication',
  },
]
</script>

<template>
  <VRow>
    <VCol cols="12">
      <VTabs
        v-model="profileTab"
        class="v-tabs-pill"
      >
        <VTab
          v-for="tab in tabs"
          :key="tab.icon"
        >
          <VIcon
            :size="18"
            :icon="tab.icon"
            class="me-1"
          />
          <span>{{ tab.title }}</span>
        </VTab>
      </VTabs>

      <VWindow
        v-model="profileTab"
        class="mt-6 disable-tab-transition"
        :touch="false"
      >
        <VWindowItem>
          <ChangePassword />
        </VWindowItem>

        <VWindowItem>
          <TwoFactorAuth />
        </VWindowItem>
      </VWindow>
    </VCol>
  </VRow>
</template>
