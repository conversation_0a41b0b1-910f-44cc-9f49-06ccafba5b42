<script setup>
import { emailValidator, requiredValidator } from '@/@core/utils/validators'
import useCaptcha from '@/composables/useCaptcha'
import { useAppAbility } from '@/plugins/casl/useAppAbility'
import { alertWarning } from '@/plugins/sweetalert2'
import ApiService from '@/services/ApiService'
import { useAuthStore } from '@/store/auth'
import { processErrors } from '@/utils/errorHandler'
import { useGenerateImageVariant } from '@core/composable/useGenerateImageVariant'
import authV2LoginIllustrationBorderedDark from '@images/pages/auth-v2-login-illustration-bordered-dark.png'
import authV2LoginIllustrationBorderedLight from '@images/pages/auth-v2-login-illustration-bordered-light.png'
import authV2LoginIllustrationDark from '@images/pages/auth-v2-login-illustration-dark.png'
import authV2LoginIllustrationLight from '@images/pages/auth-v2-login-illustration-light.png'
import loginImg from '@images/pages/login-page.svg'
import authV2MaskDark from '@images/pages/misc-mask-dark.png'
import authV2MaskLight from '@images/pages/misc-mask-light.png'
import { VNodeRenderer } from '@layouts/components/VNodeRenderer'
import { themeConfig } from '@themeConfig'
import { onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const $cookies = inject('$cookies')

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()
const ability = useAppAbility()
const { getRecaptchaToken } = useCaptcha()

const formRef = ref()
const serverErrors = ref([])
const isLoading = ref(false)
const is2faEnabled = ref(false)

const form = ref({
  email: '',
  password: '',
  authentication_code: '',
})

onMounted(() => {
  showResetPasswordLinkExpiredPopup()
})

const isPasswordVisible = ref(false)
const authThemeImg = useGenerateImageVariant(authV2LoginIllustrationLight, authV2LoginIllustrationDark, authV2LoginIllustrationBorderedLight, authV2LoginIllustrationBorderedDark, true)
const authThemeMask = useGenerateImageVariant(authV2MaskLight, authV2MaskDark)

const handleSubmit = async () => {
  formRef.value?.validate().then(async ({ valid }) => {
    if (valid) {
      isLoading.value = true

      const captchaToken = await getRecaptchaToken('login')

      const postData = {
        ...form.value,
        'g-recaptcha-response': captchaToken,
      }

      login(postData)
    }
  })
}

const login = async values => {
  try {
    isLoading.value = true
    serverErrors.value = []

    let { data } = await ApiService.post('/admin/login', values)

    if (data.status === 200) {
      if (data['2fa_enabled'] && data['2fa_enabled'] === 1) {
        is2faEnabled.value = true
        isLoading.value = false

        return
      }

      authStore.setAuth(data)

      const adminAbility = [{ action: 'manage', subject: 'all' }]

      $cookies.set('userAbilities', adminAbility)
      ability.update(adminAbility)

      if (data.userData.role === 'superadmin') {
        await router.replace({ name: route.query.to ? String(route.query.to) : 'admin-dashboard' })
      } else {
        await router.replace({ name: route.query.to ? String(route.query.to) : 'user-subscription' })
      }
    } else {
      isLoading.value = false
      serverErrors.value = processErrors(data)
    }
  } catch (error) {
    isLoading.value = false
    serverErrors.value = processErrors(error)
    console.log(error)
  }
}

const showResetPasswordLinkExpiredPopup = () => {
  if (sessionStorage.getItem('resetLinkExpired') === '1') {
    alertWarning.fire({
      title: 'Link Expired!',
      html: 'We apologize, but it appears that the link to reset your password has expired. Please request a new link to reset your password.',
      confirmButtonColor: '#1565FF',
    })
    sessionStorage.removeItem('resetLinkExpired')
  }
}
</script>

<template>
  <VRow
    no-gutters
    class="auth-wrapper bg-surface"
    style="min-height: 100vh;"
  >
    <VCol
      md="8"
      class="d-none d-md-flex"
    >
      <div class="position-relative bg-background rounded-lg w-100 ma-8 me-0">
        <div class="d-flex align-center justify-center w-100 h-100">
          <VImg
            max-width="505"
            :src="loginImg"
            class="auth-illustration mt-16 mb-2"
          />
        </div>

        <!--
          <VImg
          class="auth-footer-mask pb-0"
          :src="authThemeMask"
          />
        -->
      </div>
    </VCol>

    <VCol
      cols="12"
      md="4"
      class="auth-card-v2 d-flex align-center justify-center"
    >
      <VCard
        flat
        :max-width="500"
        class="mt-12 mt-sm-0 pa-4"
      >
        <VCardText>
          <VNodeRenderer
            :nodes="themeConfig.app.logo"
            class="mb-6"
          />
          <h5 class="text-h5 mb-1">
            Welcome to <span class="text-capitalize">{{ themeConfig.app.title }}</span>! 👋🏻
          </h5>
          <p class="mb-0">
            Please sign-in to your account and start the adventure
          </p>
        </VCardText>
        <VCardText>
          <VAlert
            v-if="serverErrors.length > 0"
            title="Authentication failed!"
            color="error"
            variant="tonal"
            class="mb-6"
            type="error"
            closable
          >
            <ul class="mb-0">
              <li
                v-for="error in serverErrors"
                :key="error"
                class="mb-0"
              >
                {{ error }}
              </li>
            </ul>
          </VAlert>

          <VForm
            ref="formRef"
            @submit.prevent="handleSubmit"
          >
            <VRow>
              <!-- email -->
              <VCol
                v-if="is2faEnabled"
                cols="12"
              >
                <AppTextField
                  v-model="form.authentication_code"
                  label="2FA Code"
                  :rules="[requiredValidator]"
                  autofocus
                />
              </VCol>

              <!-- email -->
              <VCol
                v-show="!is2faEnabled"
                cols="12"
              >
                <AppTextField
                  v-model="form.email"
                  autofocus
                  label="Email"
                  type="email"
                  :rules="[requiredValidator, emailValidator]"
                />
              </VCol>

              <!-- password -->
              <VCol cols="12">
                <AppTextField
                  v-show="!is2faEnabled"
                  v-model="form.password"
                  label="Password"
                  :type="isPasswordVisible ? 'text' : 'password'"
                  :append-inner-icon="isPasswordVisible ? 'tabler-eye-off' : 'tabler-eye'"
                  :rules="[requiredValidator]"
                  @click:append-inner="isPasswordVisible = !isPasswordVisible"
                />

                <div
                  v-if="!is2faEnabled"
                  class="d-flex align-center flex-wrap justify-end mt-2 mb-4"
                >
                  <RouterLink
                    class="text-primary ms-2 mb-1"
                    :to="{ name: 'admin-forgot-password' }"
                  >
                    Forgot Password?
                  </RouterLink>
                </div>

                <VBtn
                  block
                  type="submit"
                  :disabled="isLoading"
                >
                  {{ isLoading ? 'Please wait...' : 'Login' }}
                </VBtn>
              </VCol>
            </VRow>
          </VForm>
        </VCardText>
      </VCard>
    </VCol>
  </VRow>
</template>

<style lang="scss">
@use "@core/scss/template/pages/page-auth.scss";
</style>
