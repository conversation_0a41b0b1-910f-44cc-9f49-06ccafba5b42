<script setup>
import { emailValidator, requiredValidator } from '@/@core/utils/validators'
import ApiService from '@/services/ApiService'
import useCaptcha from '@/composables/useCaptcha'
import { useGenerateImageVariant } from '@core/composable/useGenerateImageVariant'
import authV2ForgotPasswordIllustrationDark from '@images/pages/auth-v2-forgot-password-illustration-dark.png'
import authV2ForgotPasswordIllustrationLight from '@images/pages/auth-v2-forgot-password-illustration-light.png'
import authV2MaskDark from '@images/pages/misc-mask-dark.png'
import authV2MaskLight from '@images/pages/misc-mask-light.png'
import { VNodeRenderer } from '@layouts/components/VNodeRenderer'
import { themeConfig } from '@themeConfig'
import { useRouter } from 'vue-router'
import { processErrors } from '@/utils/errorHandler'
import { alertInfo } from '@/plugins/sweetalert2'

const router = useRouter()
const { getRecaptchaToken } = useCaptcha()

const authThemeImg = useGenerateImageVariant(authV2ForgotPasswordIllustrationLight, authV2ForgotPasswordIllustrationDark)
const authThemeMask = useGenerateImageVariant(authV2MaskLight, authV2MaskDark)
const formRef = ref()
const serverErrors = ref([])
const isLoading = ref(false)
const email = ref('')

const handleSubmit = async () => {
  formRef.value?.validate().then(async ({ valid }) => {
    if (valid) {
      isLoading.value = true

      const capcthaToken = await getRecaptchaToken('forgotPassword')

      const postData = {
        email: email.value,
        'g-recaptcha-response': capcthaToken,
      }

      forgotPassword(postData)
    }
  })
}

const forgotPassword = async values => {
  try {
    isLoading.value = true
    serverErrors.value = []

    let { data } = await ApiService.post('/admin/forgot-password', values)

    if (data.status === 200) {
      alertInfo.fire({
        text: data.message,
      }).then(() => {
        router.push({ name: 'admin-login' })
      })
    } else {
      isLoading.value = false
      serverErrors.value = processErrors(data)
    }
  } catch (error) {
    isLoading.value = false
    serverErrors.value = processErrors(error)
    console.log(error)
  }
}
</script>

<template>
  <VRow
    class="auth-wrapper bg-surface"
    no-gutters
  >
    <VCol
      md="8"
      class="d-none d-md-flex"
    >
      <div class="position-relative bg-background rounded-lg w-100 ma-8 me-0">
        <div class="d-flex align-center justify-center w-100 h-100">
          <VImg
            max-width="368"
            :src="authThemeImg"
            class="auth-illustration mt-16 mb-2"
          />
        </div>

        <VImg
          class="auth-footer-mask"
          :src="authThemeMask"
        />
      </div>
    </VCol>

    <VCol
      cols="12"
      md="4"
      class="auth-card-v2 d-flex align-center justify-center"
    >
      <VCard
        flat
        :max-width="500"
        class="mt-12 mt-sm-0 pa-4"
      >
        <VCardText>
          <VNodeRenderer
            :nodes="themeConfig.app.logo"
            class="mb-6"
          />
          <h5 class="text-h5 mb-1">
            Forgot Password?
          </h5>
          <p class="mb-0">
            Enter your email and we'll send you instructions to reset your password
          </p>
        </VCardText>

        <VCardText>
          <VAlert
            v-if="serverErrors.length > 0"
            title="Error!"
            color="error"
            variant="tonal"
            class="mb-6"
            type="error"
            closable
          >
            <ul class="mb-0">
              <li
                v-for="error in serverErrors"
                :key="error"
                class="mb-0"
              >
                {{ error }}
              </li>
            </ul>
          </VAlert>

          <VForm
            ref="formRef"
            @submit.prevent="handleSubmit"
          >
            <VRow>
              <!-- email -->
              <VCol cols="12">
                <AppTextField
                  v-model="email"
                  autofocus
                  label="Email"
                  type="email"
                  :rules="[requiredValidator, emailValidator]"
                />
              </VCol>

              <!-- Reset link -->
              <VCol cols="12">
                <VBtn
                  block
                  type="submit"
                  :disabled="isLoading"
                >
                  {{ isLoading ? 'Please wait...' : 'Send Reset Link' }}
                </VBtn>
              </VCol>

              <!-- back to login -->
              <VCol cols="12">
                <RouterLink
                  class="d-flex align-center justify-center"
                  :to="{ name: 'admin-login' }"
                >
                  <VIcon
                    icon="tabler-chevron-left"
                    class="flip-in-rtl"
                  />
                  <span>Back to login</span>
                </RouterLink>
              </VCol>
            </VRow>
          </VForm>
        </VCardText>
      </VCard>
    </VCol>
  </VRow>
</template>

<style lang="scss">
@use "@core/scss/template/pages/page-auth.scss";
</style>
