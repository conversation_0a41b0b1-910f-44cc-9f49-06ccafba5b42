<script setup>
import { confirmed<PERSON><PERSON><PERSON><PERSON>, passwordValidator, requiredV<PERSON>da<PERSON> } from '@validators'
import { useGlobalData } from '@/store/global'
import { ref } from 'vue'
import router from '@/router'
import { isEmptyObject, isNullOrUndefined } from '@/@core/utils'
import { removeKeyFromObject } from '@/utils/helpers'
import ApiService from '@/services/ApiService'
import { processErrors } from '@/utils/errorHandler'

const { showSnackbar } = useGlobalData()

const refForm = ref()
const isLoading = ref(false)
const inputErrors = ref({})
const serverErrors = ref([])
const isCurrentPasswordVisible = ref(false)
const isNewPasswordVisible = ref(false)
const isConfirmPasswordVisible = ref(false)

const form = ref({
  current_password: '',
  new_password: '',
  password_confirmation: '',
})

const onFormSubmit = () => {
  refForm.value?.validate().then(({ valid }) => {
    if (valid) {
      changePassword()
    }
  })
}

async function changePassword() {
  try {
    isLoading.value = true
    inputErrors.value = {}
    serverErrors.value = []

    let { data } = await ApiService.post('/admin/change-password', form.value)

    if (data.status === 200) {
      await router.push({ name: 'admin-dashboard' })
      showSnackbar(data.message)
      refForm.value?.reset()
      refForm.value?.resetValidation()
    } else {
      isLoading.value = false
      if (data.errors) {
        inputErrors.value = data.errors
      }
    }
  } catch (error) {
    isLoading.value = false
    serverErrors.value = processErrors(error)
    console.log(error)
  }
}

function removeKeyFromInputErrors(key) {
  inputErrors.value = removeKeyFromObject(inputErrors.value, key)
}
</script>

<template>
  <VRow>
    <!-- SECTION: Change Password -->
    <VCol cols="12">
      <VCard title="Change Password">
        <VForm
          ref="refForm"
          @submit.prevent="onFormSubmit"
        >
          <VCardText class="pt-0">
            <!-- 👉 Validation Errors -->
            <VAlert
              v-if="serverErrors.length > 0"
              type="error"
              variant="tonal"
              title="Validation failed!"
              class="mb-6"
            >
              <ul class="mb-0">
                <li
                  v-for="error in serverErrors"
                  :key="error"
                  class="mb-0"
                >
                  {{ error }}
                </li>
              </ul>
            </VAlert>

            <!-- 👉 Current Password -->
            <VRow class="mb-3">
              <VCol
                cols="12"
                md="6"
              >
                <!-- 👉 current password -->
                <VTextField
                  v-model="form.current_password"
                  :type="isCurrentPasswordVisible ? 'text' : 'password'"
                  :append-inner-icon="isCurrentPasswordVisible ? 'tabler-eye' : 'tabler-eye-off'"
                  label="Current Password"
                  name="current_password"
                  :rules="[requiredValidator]"
                  autocomplete="off"
                  @click:append-inner="isCurrentPasswordVisible = !isCurrentPasswordVisible"
                  @keyup="removeKeyFromInputErrors('current_password')"
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['current_password'])"
                  class="text-sm text-error mb-0 ms-3"
                >
                  {{ inputErrors['current_password'][0] }}
                </p>
              </VCol>
            </VRow>

            <!-- 👉 New Password -->
            <VRow>
              <VCol
                cols="12"
                md="6"
              >
                <!-- 👉 new password -->
                <VTextField
                  v-model="form.new_password"
                  :type="isNewPasswordVisible ? 'text' : 'password'"
                  :append-inner-icon="isNewPasswordVisible ? 'tabler-eye' : 'tabler-eye-off'"
                  label="New Password"
                  name="new_password"
                  :rules="[requiredValidator, passwordValidator]"
                  autocomplete="off"
                  @click:append-inner="isNewPasswordVisible = !isNewPasswordVisible"
                  @keyup="removeKeyFromInputErrors('new_password')"
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['new_password'])"
                  class="text-sm text-error mb-0 ms-3"
                >
                  {{ inputErrors['new_password'][0] }}
                </p>
              </VCol>

              <VCol
                cols="12"
                md="6"
              >
                <!-- 👉 confirm password -->
                <VTextField
                  v-model="form.password_confirmation"
                  :type="isConfirmPasswordVisible ? 'text' : 'password'"
                  :append-inner-icon="isConfirmPasswordVisible ? 'tabler-eye' : 'tabler-eye-off'"
                  label="Confirm New Password"
                  name="password_confirmation"
                  :rules="[requiredValidator, confirmedValidator(form.password_confirmation, form.new_password)]"
                  autocomplete="off"
                  @click:append-inner="isConfirmPasswordVisible = !isConfirmPasswordVisible"
                  @keyup="removeKeyFromInputErrors('password_confirmation')"
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['password_confirmation'])"
                  class="text-sm text-error mb-0 ms-3"
                >
                  {{ inputErrors['password_confirmation'][0] }}
                </p>
              </VCol>
            </VRow>

            <!-- 👉 Password Requirements -->
            <VAlert
              variant="tonal"
              color="info"
              class="mb-4 mt-6"
            >
              <VAlertTitle class="mb-2">
                Ensure that these requirements are met
              </VAlertTitle>
              <ul class="mb-0">
                <li>Password must be at least 8 characters long</li>
                <li>Include at least one uppercase letter or number or symbol</li>
              </ul>
            </VAlert>
          </VCardText>

          <!-- 👉 Action Buttons -->
          <VCardText class="d-flex flex-wrap gap-4">
            <VBtn
              type="submit"
              :loading="isLoading"
            >
              Submit
            </VBtn>

            <VBtn
              type="reset"
              color="secondary"
              variant="tonal"
              :disabled="isLoading"
            >
              Reset
            </VBtn>
          </VCardText>
        </VForm>
      </VCard>
    </VCol>
    <!-- !SECTION -->
  </VRow>
</template>
