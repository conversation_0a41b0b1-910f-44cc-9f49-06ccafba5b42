<script setup>
import { onMounted, ref } from 'vue'
import QrcodeVue from 'qrcode.vue'
import ApiService from '@/services/ApiService'
import { processErrors } from '@/utils/errorHandler'
import { useGlobalData } from '@/store/global'

const { showSnackbar } = useGlobalData()
const skeletonLoading = ref(false)
const qrCodeUrl = ref('')
const secret = ref('')
const verificationCode = ref('')
const twoFactorEnabled = ref(false)
const enableBtnLoading = ref(false)
const verifyBtnLoading = ref(false)
const disableBtnLoading = ref(false)
const is2faEnableActive = ref(false)
const is2faDisableActive = ref(false)
const password = ref('')
const isPasswordVisible = ref(false)

onMounted(async () => {
  await get2faStatus()
})

const get2faStatus = async () => {
  try {
    skeletonLoading.value = true

    const { data } = await ApiService.post('/admin/2fa/status')

    if (data.status === 200) {
      twoFactorEnabled.value = data.is_2fa_enabled
    } else {
      showSnackbar(processErrors(data)[0], 'error')
    }
  } catch (error) {
    console.error('Error enabling 2FA:', error)
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    skeletonLoading.value = false
  }
}

const enable2FA = async () => {
  try {
    enableBtnLoading.value = true

    const { data } = await ApiService.post('/admin/2fa/enable', {
      password: password.value,
    })

    if (data.status === 200) {
      qrCodeUrl.value = data.qrCodeUrl
      secret.value = data.secret
      is2faEnableActive.value = false
      isPasswordVisible.value = false
    } else {
      showSnackbar(processErrors(data)[0], 'error')
    }
  } catch (error) {
    console.error('Error enabling 2FA:', error)
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    enableBtnLoading.value = false
    password.value = ''
  }
}

const verifyCode = async () => {
  try {
    verifyBtnLoading.value = true

    const { data } = await ApiService.post('/admin/2fa/verify', {
      code: verificationCode.value,
    })

    if (data.status === 200) {
      twoFactorEnabled.value = true
      verificationCode.value = ''
      showSnackbar(data.message)
    } else {
      showSnackbar(processErrors(data)[0], 'error')
    }
  } catch (error) {
    console.error('Error verifying 2FA code:', error)
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    verifyBtnLoading.value = false
  }
}

const disable2FA = async () => {
  try {
    disableBtnLoading.value = true

    const { data } = await ApiService.post('/admin/2fa/disable', {
      password: password.value,
    })

    if (data.status === 200) {
      twoFactorEnabled.value = false
      is2faDisableActive.value = false
      isPasswordVisible.value = false
      qrCodeUrl.value = ''
      secret.value = ''
      showSnackbar(data.message)
    } else {
      showSnackbar(processErrors(data)[0], 'error')
    }
  } catch (error) {
    console.error('Error disabling 2FA:', error)
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    disableBtnLoading.value = false
    password.value = ''
  }
}
</script>

<template>
  <!-- 👉 Loading View -->
  <div v-if="skeletonLoading">
    <div
      class="d-flex justify-center align-center"
      style="height: calc(100vh - 14rem)"
    >
      <VProgressCircular
        indeterminate
        size="48"
      />
    </div>
  </div>

  <!-- 👉 Content View -->
  <div v-else>
    <VCard
      v-if="!twoFactorEnabled && !qrCodeUrl && !secret"
      title="Two-Factor Authentication"
    >
      <VCardText v-if="!is2faEnableActive">
        <VBtn
          @click="() => {
            password = ''
            is2faEnableActive = true
          }"
        >
          Enable 2FA
        </VBtn>
      </VCardText>
      <VCardText v-if="is2faEnableActive">
        <p class="text-body-1">
          To enable 2FA, please confirm your password to proceed
        </p>
        <VTextField
          v-model="password"
          :type="isPasswordVisible ? 'text' : 'password'"
          :append-inner-icon="isPasswordVisible ? 'tabler-eye-off' : 'tabler-eye'"
          placeholder="Enter password"
          style="max-width: 400px"
          @click:append-inner="isPasswordVisible = !isPasswordVisible"
        />
        <VBtn
          class="mt-4"
          :loading="enableBtnLoading"
          @click="enable2FA"
        >
          Confirm Password
        </VBtn>
        <VBtn
          :disabled="enableBtnLoading"
          variant="tonal"
          color="secondary"
          class="mt-4 ms-2"
          @click="() => {
            password = ''
            is2faEnableActive = false
          }"
        >
          Cancel
        </VBtn>
      </VCardText>
    </VCard>

    <VCard
      v-if="!twoFactorEnabled && qrCodeUrl && secret"
      title="Set up Two-Factor Authentication"
    >
      <VCardText>
        <div v-if="qrCodeUrl">
          <p class="text-body-1 mb-3">
            Scan this QR code with your authenticator app:
          </p>
          <qrcode-vue
            :value="qrCodeUrl"
            :size="200"
            level="H"
          ></qrcode-vue>
          <p class="text-body-1 mt-3">
            Or enter this code manually: <span class="text-high-emphasis font-weight-medium">{{ secret }}</span>
          </p>
        </div>
        <div class="mt-6">
          <p class="text-body-1 mb-3">
            Enter the verification code generated by your authenticator app:
          </p>
          <VTextField
            v-model="verificationCode"
            placeholder="Enter verification code"
            maxlength="6"
          />
          <VBtn
            :loading="verifyBtnLoading"
            class="mt-4"
            @click="verifyCode"
          >
            Verify and Enable 2FA
          </VBtn>
        </div>
      </VCardText>
    </VCard>

    <VCard
      v-if="twoFactorEnabled"
      title="Two-Factor Authentication"
    >
      <VCardText v-if="!is2faDisableActive">
        <p class="text-success font-weight-medium text-subtitle-1">
          2FA is Active
        </p>

        <VBtn
          @click="() => {
            password = ''
            is2faDisableActive = true
          }"
        >
          Disable 2FA
        </VBtn>
      </VCardText>
      <VCardText v-if="is2faDisableActive">
        <p class="text-body-1">
          To disable 2FA, please confirm your password to proceed
        </p>
        <VTextField
          v-model="password"
          :type="isPasswordVisible ? 'text' : 'password'"
          :append-inner-icon="isPasswordVisible ? 'tabler-eye-off' : 'tabler-eye'"
          placeholder="Enter password"
          style="max-width: 400px"
          @click:append-inner="isPasswordVisible = !isPasswordVisible"
        />
        <VBtn
          :loading="disableBtnLoading"
          class="mt-4"
          @click="disable2FA"
        >
          Confirm Password
        </VBtn>
        <VBtn
          :disabled="disableBtnLoading"
          variant="tonal"
          color="secondary"
          class="mt-4 ms-2"
          @click="() => {
            password = ''
            is2faDisableActive = false
          }"
        >
          Cancel
        </VBtn>
      </VCardText>
    </VCard>
  </div>
</template>
