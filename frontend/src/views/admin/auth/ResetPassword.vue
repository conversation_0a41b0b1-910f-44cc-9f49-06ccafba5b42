<script setup>
import { useGenerateImageVariant } from '@core/composable/useGenerateImageVariant'
import { VNodeRenderer } from '@layouts/components/VNodeRenderer'
import { themeConfig } from '@themeConfig'
import authV2ResetPasswordIllustrationDark from '@images/pages/auth-v2-reset-password-illustration-dark.png'
import authV2ResetPasswordIllustrationLight from '@images/pages/auth-v2-reset-password-illustration-light.png'
import authV2MaskDark from '@images/pages/misc-mask-dark.png'
import authV2MaskLight from '@images/pages/misc-mask-light.png'
import { useRoute, useRouter } from 'vue-router'
import useCaptcha from '@/composables/useCaptcha'
import { confirmedValidator, passwordValidator, requiredValidator } from '@/@core/utils/validators'
import ApiService from '@/services/ApiService'
import { processErrors } from '@/utils/errorHandler'
import { alertSuccess } from '@/plugins/sweetalert2'

const route = useRoute()
const router = useRouter()
const { getRecaptchaToken } = useCaptcha()

const authThemeImg = useGenerateImageVariant(authV2ResetPasswordIllustrationLight, authV2ResetPasswordIllustrationDark)
const authThemeMask = useGenerateImageVariant(authV2MaskLight, authV2MaskDark)
const isPasswordVisible = ref(false)
const isConfirmPasswordVisible = ref(false)
const formRef = ref()
const serverErrors = ref([])
const isLoading = ref(false)

const form = ref({
  newPassword: '',
  confirmPassword: '',
})

const handleSubmit = async () => {
  formRef.value?.validate().then(async ({ valid }) => {
    if (valid) {
      isLoading.value = true

      const capcthaToken = await getRecaptchaToken('resetPassword')

      const postData = {
        email: route.query.email,
        token: route.query.token,
        password: form.value.newPassword,
        password_confirmation: form.value.confirmPassword,
        'g-recaptcha-response': capcthaToken,
      }

      resetPassword(postData)
    }
  })
}

const resetPassword = async values => {
  try {
    isLoading.value = true
    serverErrors.value = []

    let { data } = await ApiService.post('/admin/reset-password', values)

    if (data.status === 200) {
      alertSuccess.fire({
        text: data.message,
      }).then(() => {
        router.push({ name: 'admin-login' })
      })
    } else {
      isLoading.value = false
      serverErrors.value = processErrors(data)
    }
  } catch (error) {
    isLoading.value = false
    serverErrors.value = processErrors(error)
    console.log(error)
  }
}
</script>

<template>
  <VRow
    no-gutters
    class="auth-wrapper bg-surface"
  >
    <VCol
      md="8"
      class="d-none d-md-flex"
    >
      <div class="position-relative bg-background rounded-lg w-100 ma-8 me-0">
        <div class="d-flex align-center justify-center w-100 h-100">
          <VImg
            max-width="400"
            :src="authThemeImg"
            class="auth-illustration mt-16 mb-2"
          />
        </div>

        <VImg
          class="auth-footer-mask"
          :src="authThemeMask"
        />
      </div>
    </VCol>

    <VCol
      cols="12"
      md="4"
      class="auth-card-v2 d-flex align-center justify-center"
    >
      <VCard
        flat
        :max-width="500"
        class="mt-12 mt-sm-0 pa-4"
      >
        <VCardText>
          <VNodeRenderer
            :nodes="themeConfig.app.logo"
            class="mb-6"
          />

          <h5 class="text-h5 mb-1">
            Reset Password
          </h5>
          <p class="mb-0">
            for <span class="font-weight-bold">{{ route.query.email }}</span>
          </p>
        </VCardText>

        <VCardText>
          <VAlert
            v-if="serverErrors.length > 0"
            title="Error!"
            color="error"
            variant="tonal"
            class="mb-6"
            type="error"
            closable
          >
            <ul class="mb-0">
              <li
                v-for="error in serverErrors"
                :key="error"
                class="mb-0"
              >
                {{ error }}
              </li>
            </ul>
          </VAlert>

          <VForm
            ref="formRef"
            @submit.prevent="handleSubmit"
          >
            <VRow>
              <!-- password -->
              <VCol cols="12">
                <AppTextField
                  v-model="form.newPassword"
                  autofocus
                  label="New Password"
                  :type="isPasswordVisible ? 'text' : 'password'"
                  :append-inner-icon="isPasswordVisible ? 'tabler-eye-off' : 'tabler-eye'"
                  :rules="[requiredValidator, passwordValidator]"
                  @click:append-inner="isPasswordVisible = !isPasswordVisible"
                />
              </VCol>

              <!-- Confirm Password -->
              <VCol cols="12">
                <AppTextField
                  v-model="form.confirmPassword"
                  label="Confirm Password"
                  :type="isConfirmPasswordVisible ? 'text' : 'password'"
                  :append-inner-icon="isConfirmPasswordVisible ? 'tabler-eye-off' : 'tabler-eye'"
                  :rules="[requiredValidator, confirmedValidator(form.confirmPassword, form.newPassword)]"
                  @click:append-inner="isConfirmPasswordVisible = !isConfirmPasswordVisible"
                />
              </VCol>

              <!-- Set password -->
              <VCol cols="12">
                <VBtn
                  block
                  type="submit"
                  :disabled="isLoading"
                >
                  {{ isLoading ? 'Please wait...' : 'Set New Password' }}
                </VBtn>
              </VCol>

              <!-- back to login -->
              <VCol cols="12">
                <RouterLink
                  class="d-flex align-center justify-center"
                  :to="{ name: 'admin-login' }"
                >
                  <VIcon
                    icon="tabler-chevron-left"
                    class="flip-in-rtl"
                  />
                  <span>Back to login</span>
                </RouterLink>
              </VCol>
            </VRow>
          </VForm>
        </VCardText>
      </VCard>
    </VCol>
  </VRow>
</template>

<style lang="scss">
@use "@core/scss/template/pages/page-auth.scss";
</style>
