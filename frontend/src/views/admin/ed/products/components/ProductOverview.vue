<script setup>
import { isEmpty } from '@/@core/utils'
import router from '@/router'
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { processErrors } from '@/utils/errorHandler'
import { computed, onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const { showSnackbar } = useGlobalData()
const productId = computed(() => route.params.product_id)
const productOverview = ref('')
const productName = ref('')
const skeletonLoading = ref(false)
const isLoading = ref(false)
const errors = ref([])

onMounted(async () => {
  await fetchProductOverview()
})

async function fetchProductOverview() {
  try {
    skeletonLoading.value = true

    const { data } = await ApiService.get(`/admin/retrieve-ed-product-instructions/${productId.value}`)

    if (data.status === 200) {
      productOverview.value = data.productInstruction || ''
      productName.value = data.productName || ''
    } else {
      errors.value = processErrors(data)
    }
  } catch (error) {
    errors.value = processErrors(error)
  } finally {
    skeletonLoading.value = false
  }
}

async function updateProductOverview() {
  try {
    if (isEmpty(productOverview.value)) {
      errors.value = ['Product overview is required']

      return
    }

    isLoading.value = true

    const { data } = await ApiService.post('/admin/update-ed-product-instructions', {
      id: productId.value,
      product_instruction: productOverview.value,
    })

    if (data.status === 200) {
      showSnackbar(data.message)
      router.push({ name: 'admin-ed-products' })
    } else {
      errors.value = processErrors(data)
    }
  } catch (error) {
    errors.value = processErrors(error)
  } finally {
    isLoading.value = false
  }
}
</script>

<template>
  <div>
    <VCard>
      <VCardTitle>
        <div class="text-h5 pa-6">
          Product Overview <span v-if="!isEmpty(productName)">for {{ productName }}</span>
        </div>
      </VCardTitle>
      <!-- 👉 Loading View -->
      <VCardText v-if="skeletonLoading">
        <div
          class="d-flex flex-column align-center justify-center"
          style="height: 200px;"
        >
          <VProgressCircular
            size="42"
            indeterminate
          />
        </div>
      </VCardText>

      <VCardText v-if="!skeletonLoading">
        <VAlert
          v-if="!isEmpty(errors)"
          type="error"
          variant="tonal"
          title="Error!"
          class="mb-6"
        >
          <ul class="mb-0">
            <li
              v-for="error in errors"
              :key="error"
              class="mb-0"
            >
              {{ error }}
            </li>
          </ul>
        </VAlert>
        <VRow>
          <VCol cols="12">
            <TiptapEditor
              v-model="productOverview"
              class="border rounded basic-editor"
              editor-height="400px"
            />
          </VCol>
        </VRow>
      </VCardText>
      <VCardActions
        v-if="!skeletonLoading"
        class="pa-6 pt-0 d-flex justify-end"
      >
        <VBtn
          color="secondary"
          variant="tonal"
          style="min-width: 150px"
          :disabled="isLoading"
          @click="() => ($router.go(-1))"
        >
          Back
        </VBtn>
        <VBtn
          :loading="isLoading"
          variant="elevated"
          style="min-width: 150px"
          @click="updateProductOverview"
        >
          Submit
        </VBtn>
      </VCardActions>
    </VCard>
  </div>
</template>
