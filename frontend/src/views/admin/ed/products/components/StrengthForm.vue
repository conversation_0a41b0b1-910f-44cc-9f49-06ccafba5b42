<script setup>
import { isEmpty, isEmptyObject, isNullOrUndefined, parseFloatOrZero } from '@/@core/utils'
import { requiredValidator } from '@/@core/utils/validators'
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { processErrors } from '@/utils/errorHandler'
import { ceilToDecimalPlaces, formatCurrency, isNumber, removeKeyFromObject } from '@/utils/helpers'
import { useObjectUrl } from '@vueuse/core'
import { v4 as uuidv4 } from 'uuid'
import { computed, onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'

const props = defineProps({
  formType: {
    type: String,
    default: 'edit',
  },
  productData: {
    type: Object,
    required: true,
  },
  strengths: {
    type: Array,
    required: true,
  },
  strengthId: {
    type: String,
    default: null,
  },
})

const emit = defineEmits(['updateList', 'closeForm'])

const route = useRoute()
const { showSnackbar } = useGlobalData()

const formStrengthRef = ref()
const activeFrequencyTab = ref('daily')

const productId = ref(route.params.product_id)
const isStrengthLoading = ref(false)
const isStrengthImgUploading = ref(false)
const strengthObj = ref({})
const inputStrengthImageRef = ref()
const strengthImage = ref({ file: null, url: '' })
const inputErrors = ref({})
const serverErrors = ref([])
const strengths = ref([])
const isEmptyStrengthImg = ref(false)
const renderForm = ref(false)

onMounted(() => {
  renderForm.value = false

  strengths.value = JSON.parse(JSON.stringify(props.strengths))
  strengths.value = strengths.value.map(strength => {
    strength.strength_id = uuidv4()

    return strength
  })

  if (props.formType === 'edit' && !isEmpty(props.strengthId)) {
    handleEditStrength()
  } else {
    addNewStrength()
  }

  renderForm.value = true
})

const handleEditStrength = () => {
  const strength = strengths.value?.find(s => s.id === props.strengthId)

  strengthObj.value = {
    ...strength,
    frequency: [...strength.frequency],
    qtys: strength.ed_product_qtys?.map(qty => {
      const quantityId = uuidv4()

      qty.quantity_id = quantityId

      qty.subscription_plans = qty.ed_product_subscription_plans?.map(plan => ({
        ...plan,
        plan_id: uuidv4(),
        quantity_id: quantityId,
      }))

      delete qty.ed_product_subscription_plans

      return qty
    }),
  }

  delete strengthObj.value.ed_product_qtys

  strengthImage.value = {
    file: null,
    url: strength.strength_image,
  }

  handleFrequencyChange()
}

const handleSelectImg = () => {
  inputStrengthImageRef.value?.click()
}

const onStrengthFileChange = $event => {
  const { files } = $event.target
  if (!files || files.length === 0) return
  const file = files[0]

  if (file.type.slice(0, 6) !== 'image/') {
    showSnackbar('Only image files are allowed', 'error')

    return false
  }
  if (file.size > 3 * 1024 * 1024) {
    showSnackbar('File size should be less than 3MB', 'error')

    return false
  }

  removeKeyFromInputErrors('strength_image')
  isEmptyStrengthImg.value = false

  strengthObj.value.strength_image = file
  strengthImage.value = {
    file: file,
    url: useObjectUrl(file).value,
  }

  if (!isEmpty(props.strengthId)) {
    (async () => {
      await updateStrengthImage()
    })()
  }
}

const frequencies = [
  {
    title: 'Daily',
    value: 'daily',
  },
  {
    title: 'As Needed',
    value: 'as_needed',
  },
]

const recommendedOptions = [
  {
    title: 'Yes',
    value: 1,
  },
  {
    title: 'No',
    value: 0,
  },
]

const strengthUnits = [
  { title: 'mg', value: 'mg' },
  { title: 'mcg', value: 'mcg' },
  { title: 'g', value: 'g' },
  { title: 'mL', value: 'mL' },
  { title: 'L', value: 'L' },
  { title: 'IU', value: 'IU' },
]

const intervals = [
  { value: 1, title: 'Monthly' },
  { value: 2, title: '2 Months' },
  { value: 3, title: '3 Months' },
  { value: 4, title: '4 Months' },
  { value: 5, title: '5 Months' },
  { value: 6, title: '6 Months' },
  { value: 7, title: '7 Months' },
  { value: 8, title: '8 Months' },
  { value: 9, title: '9 Months' },
  { value: 10, title: '10 Months' },
  { value: 11, title: '11 Months' },
  { value: 12, title: 'Yearly' },
]

const selectedIntervals = ref([])

const availableIntervals = plan => {
  const selectedIntervalsForQuantity = Object.values(selectedIntervals.value)
    .filter(selectedInterval => {
      return selectedInterval.quantity_id === plan.quantity_id
    })

  return intervals.filter(
    interval => !selectedIntervalsForQuantity.some(selectedInterval => selectedInterval.interval === interval.value),
  )
}

const updateSelectedIntervals = plan => {
  selectedIntervals.value[plan.plan_id] = { quantity_id: plan.quantity_id, interval: plan.interval }
}

const addNewStrength = () => {
  const strengthId = uuidv4()

  const newStrength = {
    strength_id: strengthId,
    strength: '',
    product_type: null,
    strength_unit: 'mg',
    strength_image: '',
    ndc: '',
    med_id: '',
    dose_instruction: '',
    drug_name: '',
    is_recommended: 0,
    frequency: ['daily', 'as_needed'],
  }

  const dailyQty = initQty(strengthId, 'daily')
  const asNeededQty = initQty(strengthId, 'as_needed')

  strengthObj.value = {
    ...newStrength,
    qtys: [
      dailyQty,
      asNeededQty,
    ],
  }
}

const initQty = (strengthId, frequency) => {
  const quantityId = uuidv4()
  const initialQty = frequency === 'daily' ? 30 : ''

  const newQuantity = {
    quantity_id: quantityId,
    strength_id: strengthId,
    qty: initialQty,
    frequency: frequency,
    is_recommended: 0,
  }

  const plan1 = initPlan(quantityId, 1)
  const plan2 = initPlan(quantityId, 3)
  const plan3 = initPlan(quantityId, 6)

  return { ...newQuantity, subscription_plans: [plan1, plan2, plan3] }
}

const addNewQuantity = (strengthId, frequency) => {
  const quantityId = uuidv4()
  const initialQty = frequency === 'daily' ? 30 : ''

  const newQuantity = {
    quantity_id: quantityId,
    qty: initialQty,
    frequency: frequency,
    is_recommended: 0,
    strength_id: strengthId,
    subscription_plans: [],
  }

  strengthObj.value.qtys.push(newQuantity)

  addNewPlan(quantityId, 1)
  addNewPlan(quantityId, 3)
  addNewPlan(quantityId, 6)
}

const initPlan = (quantityId, interval) => {
  const planId = uuidv4()

  selectedIntervals.value[planId] = { quantity_id: quantityId, interval: interval }

  return {
    plan_id: planId,
    quantity_id: quantityId,
    interval: interval,
    is_recommended: 0,
    plan_interval_price: undefined,
    product_cost: undefined,
    plan_interval_discount_type: 'fixed',
    plan_interval_discount: undefined,
  }
}

const addNewPlan = (quantityId, interval) => {
  const planId = uuidv4()

  const newPlan = {
    plan_id: planId,
    quantity_id: quantityId,
    interval: interval,
    is_recommended: 0,
    plan_interval_price: undefined,
    product_cost: undefined,
    plan_interval_discount_type: 'fixed',
    plan_interval_discount: undefined,
  }

  strengthObj.value.qtys = strengthObj.value.qtys.map(qty => {
    if (qty.quantity_id === quantityId) {
      qty.subscription_plans.push(newPlan)
    }

    return qty
  })

  selectedIntervals.value[planId] = { quantity_id: quantityId, interval: interval }
}

const deleteQuantity = quantity => {
  strengthObj.value.qtys = strengthObj.value.qtys.filter(qty => qty.quantity_id !== quantity.quantity_id)
}

const deletePlan = plan => {
  // remove interval from selectedIntervals
  delete selectedIntervals.value[plan.plan_id]

  // remove plan item from strengthObj subscription_plans is an array of objects inside qtys array
  strengthObj.value.qtys = strengthObj.value.qtys.map(qty => {
    qty.subscription_plans = qty.subscription_plans.filter(p => p.plan_id !== plan.plan_id)

    return qty
  })
}

const handleFrequencyChange = () => {
  const frequency = strengthObj.value.frequency

  if (frequency.length === 0) {
    return
  } else if (frequency.length === 1) {
    activeFrequencyTab.value = strengthObj.value.frequency[0]
  } else {
    activeFrequencyTab.value = 'daily'
  }

  if (frequency.includes('daily') && dailyQuantity.value?.length === 0) {
    addNewQuantity(strengthObj.value.strength_id, 'daily')
  }

  if (frequency.includes('as_needed') && asNeededQuantity.value?.length === 0) {
    addNewQuantity(strengthObj.value.strength_id, 'as_needed')
  }
}

const quantityForStrength = frequency => {
  return strengthObj.value.qtys.filter(qty => qty.frequency === frequency)
}

const dailyQuantity = computed(() => {
  return quantityForStrength('daily')
})

const asNeededQuantity = computed(() => {
  return quantityForStrength('as_needed')
})

const isStrengthRecommendDisabled = computed(() => {
  return false

  return strengths.value?.some(s => (s.id !== strengthObj.value.id) && s.is_recommended === 1)
})

const isPlanRecommendDisabled = plan => {
  const strength = strengthObj.value
  const quantity = strength.qtys?.find(qty => qty.quantity_id === plan.quantity_id)
  const plans = quantity.subscription_plans

  return plans?.some(p => {
    return p.plan_id !== plan.plan_id && p.is_recommended === 1
  })
}

const duplicateStrengthValidator = () => {
  const editedStrength = strengthObj.value

  let hasDuplicate = false

  strengths.value?.forEach(s => {
    if (
      s.strength === editedStrength.strength &&
      s.strength_unit === editedStrength.strength_unit &&
      s.product_type === editedStrength.product_type &&
      s.strength_id !== editedStrength.strength_id
    ) {
      hasDuplicate = true
    }
  })

  return !hasDuplicate || 'Strength already exists'
}

const duplicateQtyValidator = quantity => {
  if (isEmpty(quantity.qty)) return true

  let hasDuplicate = false

  asNeededQuantity.value.forEach(qty => {
    if (qty.qty === quantity.qty && qty.quantity_id !== quantity.quantity_id) {
      hasDuplicate = true
    }
  })

  return !hasDuplicate || 'Quantity already exists'
}

const maxDiscountValidator = plan => {
  if (isEmpty(plan.plan_interval_discount)) return true

  if (plan.plan_interval_discount_type === 'fixed') {
    return (parseFloatOrZero(plan.plan_interval_discount) < parseFloatOrZero(plan.plan_interval_price)) || 'Discount must be less than selling price'
  } else if (plan.plan_interval_discount_type === 'percentage') {
    return (parseFloatOrZero(plan.plan_interval_discount) < 100) || 'Discount must be less than 100%'
  } else {
    return true
  }
}

const maxProductCostValidator = plan => {
  if (isEmpty(plan.product_cost) || isEmpty(plan.plan_interval_price)) return true

  return (parseFloatOrZero(plan.product_cost) <= parseFloatOrZero(plan.plan_interval_price)) || 'Base price should not exceed selling price'
}

const calculatedPriceForPlan = plan => {
  const strength = strengthObj.value
  const quantity = strength.qtys?.find(qty => qty.quantity_id === plan.quantity_id)
  if (!quantity) return

  let intervalPrice = 0
  let intervalPriceAfterDiscount = 0

  if (plan.plan_interval_discount_type === 'fixed') {
    intervalPrice = ceilToDecimalPlaces(parseFloatOrZero(plan.plan_interval_price))
    intervalPriceAfterDiscount = ceilToDecimalPlaces(intervalPrice - parseFloatOrZero(plan.plan_interval_discount))
  } else if (plan.plan_interval_discount_type === 'percentage') {
    intervalPrice = ceilToDecimalPlaces(parseFloatOrZero(plan.plan_interval_price))
    intervalPriceAfterDiscount = ceilToDecimalPlaces(intervalPrice - (intervalPrice * parseFloatOrZero(plan.plan_interval_discount) / 100))
  }

  const monthlyPrice = ceilToDecimalPlaces(intervalPrice / parseFloatOrZero(plan.interval))
  const monthlyPriceAfterDiscount = ceilToDecimalPlaces(intervalPriceAfterDiscount / parseFloatOrZero(plan.interval))

  const unitPrice = monthlyPrice > 0 && quantity.qty > 0 ? ceilToDecimalPlaces(parseFloatOrZero(monthlyPrice) / parseFloatOrZero(quantity.qty)) : 0
  const unitPriceAfterDiscount = monthlyPriceAfterDiscount > 0 && quantity.qty > 0 ? ceilToDecimalPlaces(parseFloatOrZero(monthlyPriceAfterDiscount) / parseFloatOrZero(quantity.qty)) : 0

  const netRevenue = ceilToDecimalPlaces(intervalPriceAfterDiscount - parseFloatOrZero(plan.product_cost))

  return {
    unitPrice: `
      Unit Price: <span class="text-base font-weight-bold text-high-emphasis">${formatCurrency(unitPriceAfterDiscount)}</span> ${plan.plan_interval_discount > 0 ? `(before discount: ${formatCurrency(unitPrice)})` : ''}
    `,
    monthlyPrice: `
      Monthly price: <span class="text-base font-weight-bold text-high-emphasis">${formatCurrency(monthlyPriceAfterDiscount)}</span> ${plan.plan_interval_discount > 0 ? `(before discount: ${formatCurrency(monthlyPrice)})` : ''}
    `,
    intervalPrice: `
      Total billed every ${plan.interval} ${plan.interval === 1 ? 'month' : 'months'}: <span class="text-base font-weight-bold text-high-emphasis">${formatCurrency(intervalPriceAfterDiscount)}</span> ${plan.plan_interval_discount > 0 ? `(before discount: ${formatCurrency(intervalPrice)})` : ''}
    `,
    netRevenue: `Net Revenue: <span class="text-base font-weight-bold text-high-emphasis ${netRevenue <= 0 ? 'text-error': ''}">${formatCurrency(netRevenue)}</span>`,
  }
}

function getDiscountHint(plan) {
  if (plan.plan_interval_discount_type === 'percentage') {
    const totalAmount = parseFloatOrZero(plan.plan_interval_price)
    const discountPercentage = parseFloatOrZero(plan.plan_interval_discount)
    const discountAmount = (totalAmount * discountPercentage) / 100

    return `${formatCurrency(discountAmount.toFixed(2))} off`
  }

  return undefined
}

function removeKeyFromInputErrors(key) {
  inputErrors.value = removeKeyFromObject(inputErrors.value, key)
}

const handleStrengthSubmit = () => {
  formStrengthRef.value?.validate().then(async result => {
    isEmptyStrengthImg.value = isEmpty(strengthObj.value.strength_image)

    if (result.valid && !isEmptyStrengthImg.value) {
      await addOrUpdateStrength()
    } else {
      scrollToFormTop()
    }
  })
}

const addOrUpdateStrength = async () => {
  try {
    isStrengthLoading.value = true
    serverErrors.value = []
    inputErrors.value = {}

    const formData = createStrengthFormData()

    let endpointUrl = '/admin/add-ed-product-strength-data'
    if (!isEmpty(props.strengthId)) {
      endpointUrl = '/admin/edit-ed-product-strength-data'
    }

    const { data } = await ApiService.post(endpointUrl, formData)

    if (data.status === 200) {
      emit('updateList', true)
      resetStrengthForm()
      showSnackbar(data.message)
    } else {
      if (data.errors) {
        inputErrors.value = data.errors
      }

      // serverErrors.value = processErrors(data)
      scrollToFormTop()
    }
  } catch (error) {
    if (error.response.errors) {
      inputErrors.value = error.response.errors
    }
    serverErrors.value = processErrors(error)
    scrollToFormTop()
  } finally {
    isStrengthLoading.value = false
  }
}

const gotoNextFreq = () => {
  activeFrequencyTab.value = 'as_needed'
  scrollToFormTop()
}

const scrollToFormTop = () => {
  // scrollToElement('strength-form')
  formStrengthRef.value?.scrollIntoView({ behavior: 'smooth', block: 'start' })
}

const resetStrengthForm = () => {
  formStrengthRef.value?.reset()
  strengthObj.value = {}
  strengthImage.value = { file: null, url: '' }
  emit('closeForm', true)
}

function createStrengthFormData() {
  const formData = new FormData()

  formData.append('ed_product_id', productId.value)

  if (!isEmpty(props.strengthId)) {
    formData.append('strength_id', props.strengthId)
  }

  const strength = strengthObj.value

  Object.keys(strength).forEach(key => {
    if (key === 'qtys') {
      let qtysArr = strength[key]

      if (strength.frequency?.includes('daily') && !strength.frequency?.includes('as_needed')) {
        qtysArr = qtysArr.filter(qty => qty.frequency === 'daily')
      }
      if (strength.frequency?.includes('as_needed') && !strength.frequency?.includes('daily')) {
        qtysArr = qtysArr.filter(qty => qty.frequency === 'as_needed')
      }

      qtysArr.forEach((qty, qtyIndex) => {
        Object.keys(qty).forEach(qtyKey => {
          if (qtyKey === 'subscription_plans') {
            qty[qtyKey].forEach((plan, planIndex) => {
              Object.keys(plan).forEach(planKey => {
                formData.append(
                  `ed_product_qtys[${qtyIndex}][ed_product_subscription_plans][${planIndex}][${planKey}]`, // key
                  !isEmpty(plan[planKey]) ? plan[planKey] : '', // value
                )
              })
            })
          } else {
            formData.append(
              `ed_product_qtys[${qtyIndex}][${qtyKey}]`, // key
              !isEmpty(qty[qtyKey]) ? qty[qtyKey] : '', // value
            )
          }
        })
      })
    } else if (key === 'frequency') {
      strength[key].forEach((freq, freqIndex) => {
        formData.append(
          `${key}[${freqIndex}]`, // key
          !isEmpty(freq) ? freq : '', // value
        )
      })
    } else if (key === 'strength_image' && strength[key] instanceof File) {
      formData.append(key, strength[key], strength[key].name)
    } else {
      formData.append(
        key,
        !isEmpty(strength[key]) ? strength[key] : '',
      )
    }
  })

  return formData
}

const updateStrengthImage = async () => {
  try {
    isStrengthImgUploading.value = true
    serverErrors.value = []

    const formData = new FormData()

    formData.append('strength_id', props.strengthId)
    formData.append('strength_image', strengthImage.value.file, strengthImage.value.file.name)

    const { data } = await ApiService.post('/admin/update-ed-product-strength-image', formData)

    if (data.status === 200) {
      strengthObj.value.strength_image = data.strengthImage
      showSnackbar(data.message)
      strengthImage.value = { file: null, url: data.strengthImage }
      emit('updateList', true)
    } else {
      serverErrors.value = processErrors(data)
      scrollToFormTop()
    }
  } catch (error) {
    serverErrors.value = processErrors(error)
    scrollToFormTop()
  } finally {
    isStrengthImgUploading.value = false
  }
}
</script>

<template>
  <VCard
    :title="formType === 'edit' ? 'Update Strength' : 'Add Strength'"
    class="mb-5"
    variant="flat"
  >
    <VForm
      v-if="renderForm"
      id="strength-form"
      ref="formStrengthRef"
      @submit.prevent="handleStrengthSubmit"
    >
      <VAlert
        v-if="isEmptyObject(inputErrors) && serverErrors.length > 0"
        type="error"
        variant="tonal"
        title="Validation failed!"
        class="mb-6"
      >
        <ul class="mb-0">
          <li
            v-for="error in serverErrors"
            :key="error"
            class="mb-0"
          >
            {{ error }}
          </li>
        </ul>
      </VAlert>

      <VCardText>
        <VRow>
          <VCol
            cols="12"
            md="2"
          >
            <VTextField
              v-model="strengthObj.strength"
              label="Strength"
              :rules="[requiredValidator, duplicateStrengthValidator]"
              @keypress="isNumber($event)"
            />
            <p
              v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.strength)"
              class="text-sm text-error mb-0 ms-3"
            >
              {{ inputErrors.strength[0] }}
            </p>
          </VCol>

          <VCol
            cols="12"
            md="2"
          >
            <VSelect
              v-model="strengthObj.strength_unit"
              label="Strength Unit"
              :items="strengthUnits"
              :rules="[requiredValidator]"
            />
            <p
              v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.strength_unit)"
              class="text-sm text-error mb-0 ms-3"
            >
              {{ inputErrors.strength_unit[0] }}
            </p>
          </VCol>

          <VCol
            cols="12"
            md="4"
          >
            <VSelect
              v-model="strengthObj.product_type"
              label="Product Type"
              :items="[
                { title: 'Generic', value: 'generic' },
                { title: 'Branded', value: 'branded' }
              ]"
              :rules="[requiredValidator]"
            />
            <p
              v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.product_type)"
              class="text-sm text-error mb-0 ms-3"
            >
              {{ inputErrors.product_type[0] }}
            </p>
          </VCol>

          <VCol
            cols="12"
            md="4"
          >
            <VSelect
              v-model="strengthObj.frequency"
              label="Frequency"
              :items="frequencies"
              multiple
              chips
              clearable
              :rules="[requiredValidator]"
              @update:model-value="handleFrequencyChange"
            />
            <p
              v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.frequency)"
              class="text-sm text-error mb-0 ms-3"
            >
              {{ inputErrors.frequency[0] }}
            </p>
          </VCol>

          <VCol
            cols="12"
            md="4"
          >
            <VTextField
              v-model="strengthObj.ndc"
              label="NDC / Product ID"
              :rules="[requiredValidator]"
            />
            <p
              v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.ndc)"
              class="text-sm text-error mb-0 ms-3"
            >
              {{ inputErrors.ndc[0] }}
            </p>
          </VCol>

          <VCol
            cols="12"
            md="4"
          >
            <VTextField
              v-model="strengthObj.med_id"
              label="Med ID"
              :rules="[requiredValidator]"
            />
            <p
              v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.med_id)"
              class="text-sm text-error mb-0 ms-3"
            >
              {{ inputErrors.med_id[0] }}
            </p>
          </VCol>

          <VCol
            cols="12"
            md="4"
          >
            <VTextField
              v-model="strengthObj.drug_name"
              label="Drug Name"
              :rules="[requiredValidator]"
            />
            <p
              v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.drug_name)"
              class="text-sm text-error mb-0 ms-3"
            >
              {{ inputErrors.drug_name[0] }}
            </p>
          </VCol>

          <VCol
            cols="12"
            md="4"
          >
            <VSelect
              v-model="strengthObj.is_recommended"
              label="Is Recommended"
              :items="recommendedOptions"
              :rules="[requiredValidator]"
              :disabled="isStrengthRecommendDisabled"
            />
            <p
              v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.is_recommended)"
              class="text-sm text-error mb-0 ms-3"
            >
              {{ inputErrors.is_recommended[0] }}
            </p>
          </VCol>

          <VCol cols="12">
            <VTextarea
              v-model="strengthObj.dose_instruction"
              label="Dose Instruction"
              :rules="[requiredValidator]"
              rows="2"
            />
            <p
              v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.dose_instruction)"
              class="text-sm text-error mb-0 ms-3"
            >
              {{ inputErrors.dose_instruction[0] }}
            </p>
          </VCol>

          <VCol cols="12">
            <VCardText class="d-flex pa-0">
              <VAvatar
                rounded
                size="80"
                class="me-6"
                color="primary"
                variant="tonal"
              >
                <VImg
                  v-if="strengthImage.url"
                  :src="strengthImage.url"
                />
                <VIcon
                  v-else
                  size="80"
                  icon="tabler-box"
                />
              </VAvatar>
              <div class="d-flex flex-column justify-center gap-4">
                <div class="d-flex flex-wrap gap-2">
                  <VBtn
                    color="primary"
                    variant="tonal"
                    :loading="isStrengthImgUploading"
                    @click="handleSelectImg"
                  >
                    <VIcon
                      icon="tabler-cloud-upload"
                      class="d-sm-none"
                    />
                    <span class="d-none d-sm-block">Select Image</span>
                  </VBtn>

                  <input
                    ref="inputStrengthImageRef"
                    type="file"
                    name="file"
                    accept="image/*"
                    hidden
                    @input="onStrengthFileChange($event)"
                  />
                </div>
                <p class="text-body-2 mb-0">
                  Allowed only image files with size less than 2MB.
                </p>
              </div>
            </VCardText>
            <p
              v-if="isEmptyStrengthImg"
              class="text-sm text-error mb-0 ms-3 mt-2"
            >
              Strength image is required
            </p>
            <p
              v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.strength_image)"
              class="text-sm text-error mb-0 ms-3 mt-2"
            >
              {{ inputErrors.strength_image[0] }}
            </p>
          </VCol>
        </VRow>
        <VRow>
          <VCol
            cols="12"
            class="mt-5"
          >
            <h5 class="text-h5">
              Quantities
            </h5>
          </VCol>
          <VCol
            cols="12"
            md="3"
          >
            <div class="pe-3">
              <VTabs
                v-model="activeFrequencyTab"
                direction="vertical"
                color="primary"
                class="v-tabs-pill"
              >
                <VTab
                  v-if="strengthObj.frequency?.includes('daily')"
                  value="daily"
                >
                  <VIcon
                    icon="tabler-calendar-clock"
                    class="me-2"
                  />
                  <div class="text-truncate font-weight-medium text-start">
                    Daily
                  </div>
                </VTab>
                <VTab
                  v-if="strengthObj.frequency?.includes('as_needed')"
                  value="as_needed"
                >
                  <VIcon
                    icon="tabler-timeline-event"
                    class="me-2"
                  />
                  <div class="text-truncate font-weight-medium text-start">
                    As Needed
                  </div>
                </VTab>
              </VTabs>
            </div>
          </VCol>

          <VDivider :vertical="!$vuetify.display.smAndDown" />

          <VCol
            cols="12"
            md="9"
          >
            <VWindow
              v-model="activeFrequencyTab"
              class="w-100"
              :touch="false"
            >
              <VWindowItem
                v-if="strengthObj.frequency?.includes('daily')"
                value="daily"
              >
                <div class="d-flex flex-column gap-y-4 ps-3">
                  <div>
                    <VRow
                      v-for="(quantity, qtyIndex) in dailyQuantity"
                      :key="quantity.quantity_id"
                      class="qty-wrapper"
                    >
                      <VCol cols="12">
                        <h6 class="text-h6">
                          Quantity: {{ quantity.qty }} {{ productData.product_form }} / month
                        </h6>
                      </VCol>

                      <VCol
                        cols="12"
                        md="4"
                      >
                        <VTextField
                          v-model="quantity.qty"
                          label="Quantity"
                          :rules="[requiredValidator]"
                          @keypress="isNumber($event)"
                        />
                        <p
                          v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors[`ed_product_qtys.${qtyIndex}.qty`])"
                          class="text-sm text-error mb-0 ms-3"
                        >
                          {{ inputErrors[`ed_product_qtys.${qtyIndex}.qty`][0] }}
                        </p>
                      </VCol>

                      <VCol
                        cols="12"
                        md="4"
                      >
                        <VSelect
                          v-model="quantity.is_recommended"
                          label="Is Recommended"
                          :items="recommendedOptions"
                          :rules="[requiredValidator]"
                        />
                        <p
                          v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors[`ed_product_qtys.${qtyIndex}.is_recommended`])"
                          class="text-sm text-error mb-0 ms-3"
                        >
                          {{ inputErrors[`ed_product_qtys.${qtyIndex}.is_recommended`][0] }}
                        </p>
                      </VCol>

                      <VCol cols="12">
                        <div>
                          <h5 class="mb-5 ms-1">
                            Subscription Plans
                          </h5>

                          <VRow
                            v-for="(plan, planIndex) in quantity.subscription_plans"
                            :key="plan.plan_id"
                            class="border-sm border-dashed rounded-lg mx-1 my-3"
                          >
                            <VCol
                              cols="12"
                              md="4"
                            >
                              <VSelect
                                v-model="plan.interval"
                                label="Plan Interval"
                                :items="availableIntervals(plan)"
                                :rules="[requiredValidator]"
                                @update:model-value="updateSelectedIntervals(plan)"
                              />
                              <p
                                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors[`ed_product_qtys.${qtyIndex}.ed_product_subscription_plans.${planIndex}.interval`])"
                                class="text-sm text-error mb-0 ms-3"
                              >
                                {{ inputErrors[`ed_product_qtys.${qtyIndex}.ed_product_subscription_plans.${planIndex}.interval`][0] }}
                              </p>
                            </VCol>

                            <VCol
                              cols="12"
                              md="4"
                            >
                              <VTextField
                                v-model="plan.product_cost"
                                label="Base Price"
                                :rules="[requiredValidator, maxProductCostValidator(plan)]"
                                prefix="$"
                                @keypress="isNumber($event)"
                              />
                              <p
                                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors[`ed_product_qtys.${qtyIndex}.ed_product_subscription_plans.${planIndex}.product_cost`])"
                                class="text-sm text-error mb-0 ms-3"
                              >
                                {{ inputErrors[`ed_product_qtys.${qtyIndex}.ed_product_subscription_plans.${planIndex}.product_cost`][0] }}
                              </p>
                            </VCol>

                            <VCol
                              cols="12"
                              md="4"
                            >
                              <VTextField
                                v-model="plan.plan_interval_price"
                                label="Selling Price"
                                :rules="[requiredValidator]"
                                prefix="$"
                                @keypress="isNumber($event)"
                              />
                              <p
                                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors[`ed_product_qtys.${qtyIndex}.ed_product_subscription_plans.${planIndex}.plan_interval_price`])"
                                class="text-sm text-error mb-0 ms-3"
                              >
                                {{ inputErrors[`ed_product_qtys.${qtyIndex}.ed_product_subscription_plans.${planIndex}.plan_interval_price`][0] }}
                              </p>
                            </VCol>

                            <VCol cols="12">
                              <VRadioGroup
                                v-model="plan.plan_interval_discount_type"
                                label="Discount Type"
                                :rules="[requiredValidator]"
                                required
                                class="d-flex"
                              >
                                <VRadio
                                  label="Fixed Amount"
                                  value="fixed"
                                  class="ms-2"
                                />
                                <VRadio
                                  label="Percentage (of the total amount)"
                                  value="percentage"
                                  class="ms-2"
                                />
                              </VRadioGroup>
                              <p
                                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors[`ed_product_qtys.${qtyIndex}.ed_product_subscription_plans.${planIndex}.plan_interval_discount_type`])"
                                class="text-sm text-error mb-0 ms-3"
                              >
                                {{ inputErrors[`ed_product_qtys.${qtyIndex}.ed_product_subscription_plans.${planIndex}.plan_interval_discount_type`][0] }}
                              </p>
                            </VCol>

                            <VCol
                              cols="12"
                              md="4"
                            >
                              <VTextField
                                v-model="plan.plan_interval_discount"
                                label="Discount"
                                :rules="[maxDiscountValidator(plan)]"
                                :maxlength="plan.plan_interval_discount_type === 'fixed' ? 10 : 2"
                                :prefix="plan.plan_interval_discount_type === 'fixed' ? '$' : ''"
                                :suffix="plan.plan_interval_discount_type === 'percentage' ? '%' : ''"
                                :hint="getDiscountHint(plan)"
                                persistent-hint
                                @keypress="isNumber($event)"
                              />
                              <p
                                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors[`ed_product_qtys.${qtyIndex}.ed_product_subscription_plans.${planIndex}.plan_interval_discount`])"
                                class="text-sm text-error mb-0 ms-3"
                              >
                                {{ inputErrors[`ed_product_qtys.${qtyIndex}.ed_product_subscription_plans.${planIndex}.plan_interval_discount`][0] }}
                              </p>
                            </VCol>

                            <VCol
                              cols="12"
                              md="4"
                            >
                              <VSelect
                                v-model="plan.is_recommended"
                                label="Is Recommended"
                                :items="recommendedOptions"
                                :rules="[requiredValidator]"
                                :disabled="isPlanRecommendDisabled(plan)"
                              />
                              <p
                                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors[`ed_product_qtys.${qtyIndex}.ed_product_subscription_plans.${planIndex}.is_recommended`])"
                                class="text-sm text-error mb-0 ms-3"
                              >
                                {{ inputErrors[`ed_product_qtys.${qtyIndex}.ed_product_subscription_plans.${planIndex}.is_recommended`][0] }}
                              </p>
                            </VCol>

                            <VCol
                              cols="12"
                              class="text-body-2"
                            >
                              <VAlert
                                color="secondary"
                                variant="tonal"
                                class="alert-fix"
                              >
                                <span
                                  class="d-block"
                                  v-html="calculatedPriceForPlan(plan).unitPrice"
                                ></span>
                                <span
                                  class="d-block"
                                  v-html="calculatedPriceForPlan(plan).monthlyPrice"
                                ></span>
                                <span
                                  class="d-block"
                                  v-html="calculatedPriceForPlan(plan).intervalPrice"
                                ></span>
                                <span
                                  class="d-block"
                                  v-html="calculatedPriceForPlan(plan).netRevenue"
                                ></span>
                              </VAlert>
                            </VCol>

                            <VCol v-if="planIndex !== 0">
                              <VBtn
                                color="error"
                                variant="tonal"
                                @click="deletePlan(plan)"
                              >
                                <VIcon
                                  icon="tabler-trash"
                                  start
                                /> Delete Plan
                              </VBtn>
                            </VCol>

                            <!--
                              <v-divider
                              v-if="
                              quantity.subscription_plans?.length > 1 &&
                              planIndex < quantity.subscription_plans?.length - 1
                              "
                              class="my-5 mx-4"
                              ></v-divider>
                            -->
                          </VRow>

                          <VBtn
                            v-if="quantity.subscription_plans?.length < 12"
                            color="primary"
                            class="mt-5"
                            @click="addNewPlan(quantity.quantity_id, null)"
                          >
                            <VIcon
                              icon="tabler-plus"
                              start
                            /> Add Plan
                          </VBtn>
                        </div>
                      </VCol>
                    </VRow>
                  </div>
                </div>
              </VWindowItem>

              <VWindowItem
                v-if="strengthObj.frequency?.includes('as_needed')"
                value="as_needed"
              >
                <div class="d-flex flex-column gap-y-4 ps-3">
                  <div>
                    <VRow
                      v-for="(quantity, qtyIndex) in asNeededQuantity"
                      :key="quantity.quantity_id"
                      class="qty-wrapper"
                    >
                      <VCol cols="12">
                        <h6 class="text-h6">
                          Quantity: {{ isEmpty(quantity.qty) ? 0 : quantity.qty }} {{ productData.product_form }} / month
                        </h6>
                      </VCol>
                      <VCol
                        cols="12"
                        md="4"
                      >
                        <VTextField
                          v-model="quantity.qty"
                          label="Quantity"
                          :rules="[requiredValidator, duplicateQtyValidator(quantity)]"
                          @keypress="isNumber($event)"
                        />
                        <p
                          v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors[`ed_product_qtys.${qtyIndex}.qty`])"
                          class="text-sm text-error mb-0 ms-3"
                        >
                          {{ inputErrors[`ed_product_qtys.${qtyIndex}.qty`][0] }}
                        </p>
                      </VCol>

                      <VCol
                        cols="12"
                        md="4"
                      >
                        <VSelect
                          v-model="quantity.is_recommended"
                          label="Is Recommended"
                          :items="recommendedOptions"
                          :rules="[requiredValidator]"
                        />
                        <p
                          v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors[`ed_product_qtys.${qtyIndex}.is_recommended`])"
                          class="text-sm text-error mb-0 ms-3"
                        >
                          {{ inputErrors[`ed_product_qtys.${qtyIndex}.is_recommended`][0] }}
                        </p>
                      </VCol>

                      <VCol cols="12">
                        <div>
                          <h5 class="mb-5 ms-1">
                            Subscription Plans
                          </h5>

                          <VRow
                            v-for="(plan, planIndex) in quantity.subscription_plans"
                            :key="plan.plan_id"
                            class="border-sm border-dashed rounded-lg mx-1 my-3"
                          >
                            <VCol
                              cols="12"
                              md="4"
                            >
                              <VSelect
                                v-model="plan.interval"
                                label="Plan Interval"
                                :items="availableIntervals(plan)"
                                :rules="[requiredValidator]"
                                @update:model-value="updateSelectedIntervals(plan)"
                              />
                              <p
                                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors[`ed_product_qtys.${qtyIndex}.ed_product_subscription_plans.${planIndex}.interval`])"
                                class="text-sm text-error mb-0 ms-3"
                              >
                                {{ inputErrors[`ed_product_qtys.${qtyIndex}.ed_product_subscription_plans.${planIndex}.interval`][0] }}
                              </p>
                            </VCol>

                            <VCol
                              cols="12"
                              md="4"
                            >
                              <VTextField
                                v-model="plan.product_cost"
                                label="Base Price"
                                :rules="[requiredValidator, maxProductCostValidator(plan)]"
                                prefix="$"
                                @keypress="isNumber($event)"
                              />
                              <p
                                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors[`ed_product_qtys.${qtyIndex}.ed_product_subscription_plans.${planIndex}.product_cost`])"
                                class="text-sm text-error mb-0 ms-3"
                              >
                                {{ inputErrors[`ed_product_qtys.${qtyIndex}.ed_product_subscription_plans.${planIndex}.product_cost`][0] }}
                              </p>
                            </VCol>

                            <VCol
                              cols="12"
                              md="4"
                            >
                              <VTextField
                                v-model="plan.plan_interval_price"
                                label="Selling Price"
                                :rules="[requiredValidator]"
                                prefix="$"
                                @keypress="isNumber($event)"
                              />
                              <p
                                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors[`ed_product_qtys.${qtyIndex}.ed_product_subscription_plans.${planIndex}.plan_interval_price`])"
                                class="text-sm text-error mb-0 ms-3"
                              >
                                {{ inputErrors[`ed_product_qtys.${qtyIndex}.ed_product_subscription_plans.${planIndex}.plan_interval_price`][0] }}
                              </p>
                            </VCol>

                            <VCol cols="12">
                              <VRadioGroup
                                v-model="plan.plan_interval_discount_type"
                                label="Discount Type"
                                :rules="[requiredValidator]"
                                required
                                class="d-flex"
                              >
                                <VRadio
                                  label="Fixed Amount"
                                  value="fixed"
                                  class="ms-2"
                                />
                                <VRadio
                                  label="Percentage (of the total amount)"
                                  value="percentage"
                                  class="ms-2"
                                />
                              </VRadioGroup>
                              <p
                                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors[`ed_product_qtys.${qtyIndex}.ed_product_subscription_plans.${planIndex}.plan_interval_discount_type`])"
                                class="text-sm text-error mb-0 ms-3"
                              >
                                {{ inputErrors[`ed_product_qtys.${qtyIndex}.ed_product_subscription_plans.${planIndex}.plan_interval_discount_type`][0] }}
                              </p>
                            </VCol>

                            <VCol
                              cols="12"
                              md="4"
                            >
                              <VTextField
                                v-model="plan.plan_interval_discount"
                                label="Discount"
                                :rules="[maxDiscountValidator(plan)]"
                                :maxlength="plan.plan_interval_discount_type === 'fixed' ? 10 : 2"
                                :prefix="plan.plan_interval_discount_type === 'fixed' ? '$' : ''"
                                :suffix="plan.plan_interval_discount_type === 'percentage' ? '%' : ''"
                                :hint="getDiscountHint(plan)"
                                persistent-hint
                                @keypress="isNumber($event)"
                              />
                              <p
                                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors[`ed_product_qtys.${qtyIndex}.ed_product_subscription_plans.${planIndex}.plan_interval_discount`])"
                                class="text-sm text-error mb-0 ms-3"
                              >
                                {{ inputErrors[`ed_product_qtys.${qtyIndex}.ed_product_subscription_plans.${planIndex}.plan_interval_discount`][0] }}
                              </p>
                            </VCol>

                            <VCol
                              cols="12"
                              md="4"
                            >
                              <VSelect
                                v-model="plan.is_recommended"
                                label="Is Recommended"
                                :items="recommendedOptions"
                                :rules="[requiredValidator]"
                                :disabled="isPlanRecommendDisabled(plan)"
                              />
                              <p
                                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors[`ed_product_qtys.${qtyIndex}.ed_product_subscription_plans.${planIndex}.is_recommended`])"
                                class="text-sm text-error mb-0 ms-3"
                              >
                                {{ inputErrors[`ed_product_qtys.${qtyIndex}.ed_product_subscription_plans.${planIndex}.is_recommended`][0] }}
                              </p>
                            </VCol>

                            <VCol
                              cols="12"
                              class="text-body-2"
                            >
                              <VAlert
                                color="secondary"
                                variant="tonal"
                                class="alert-fix"
                              >
                                <span
                                  class="d-block"
                                  v-html="calculatedPriceForPlan(plan).unitPrice"
                                ></span>
                                <span
                                  class="d-block"
                                  v-html="calculatedPriceForPlan(plan).monthlyPrice"
                                ></span>
                                <span
                                  class="d-block"
                                  v-html="calculatedPriceForPlan(plan).intervalPrice"
                                ></span>
                                <span
                                  class="d-block"
                                  v-html="calculatedPriceForPlan(plan).netRevenue"
                                ></span>
                              </VAlert>
                            </VCol>

                            <VCol v-if="planIndex !== 0">
                              <VBtn
                                color="error"
                                variant="tonal"
                                @click="deletePlan(plan)"
                              >
                                <VIcon
                                  icon="tabler-trash"
                                  start
                                /> Delete Plan
                              </VBtn>
                            </VCol>
                            <!--
                              <v-divider
                              v-if="quantity.subscription_plans?.length > 1 && planIndex < quantity.subscription_plans?.length - 1"
                              class="my-5 mx-4"
                              ></v-divider>
                            -->
                          </VRow>

                          <VBtn
                            v-if="quantity.subscription_plans?.length < 12"
                            color="primary"
                            class="mt-5"
                            @click="addNewPlan(quantity.quantity_id, null)"
                          >
                            <VIcon
                              icon="tabler-plus"
                              start
                            /> Add Plan
                          </VBtn>
                        </div>
                      </VCol>

                      <VCol
                        v-if="qtyIndex !== 0"
                        cols="12"
                      >
                        <VBtn
                          color="error"
                          block
                          variant="tonal"
                          @click="deleteQuantity(quantity)"
                        >
                          <VIcon
                            icon="tabler-trash"
                            start
                          /> Delete Quantity ({{ quantity.qty || 0 }} {{ productData.product_form }} / month)
                        </VBtn>
                      </VCol>
                    </VRow>

                    <VBtn
                      color="primary"
                      class="mt-8"
                      block
                      @click="addNewQuantity(strengthObj.strength_id, 'as_needed')"
                    >
                      <VIcon
                        icon="tabler-plus"
                        start
                      /> Add New Quantity
                    </VBtn>
                  </div>
                </div>
              </VWindowItem>
            </VWindow>
          </VCol>
        </VRow>
      </VCardText>
      <VCardActions class="pa-6 pt-2 justify-end">
        <VBtn
          type="button"
          color="secondary"
          variant="tonal"
          @click="resetStrengthForm"
        >
          Discard
        </VBtn>
        <VBtn
          v-if="strengthObj.frequency?.length > 1 && activeFrequencyTab === 'daily'"
          type="button"
          color="primary"
          variant="elevated"
          @click="gotoNextFreq"
        >
          Next
        </VBtn>
        <VBtn
          v-if="activeFrequencyTab === 'as_needed' || strengthObj.frequency?.length === 1"
          type="submit"
          color="primary"
          variant="elevated"
          :loading="isStrengthLoading"
        >
          Save Strength
        </VBtn>
      </VCardActions>
    </VForm>
  </VCard>
</template>
