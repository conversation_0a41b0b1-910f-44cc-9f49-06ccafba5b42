<script setup>
import { ref, onMounted } from 'vue'
import { useGlobalData } from '@/store/global'
import ApiService from '@/services/ApiService'
import { processErrors } from '@/utils/errorHandler'
import CardStatistics from './CardStatistics.vue'
import { formatCurrency } from '@/utils/helpers'
import CardGrowth from './CardGrowth.vue'

const { showSnackbar } = useGlobalData()
const skeletonLoading = ref(false)

const statsValues = ref({
  total_orders: 0,
  total_subscriptions: 0,
  total_users: 0,
  total_sales: 0,
  total_revenue: 0,
  total_consultation: 0,
  total_shipment_cost: 0,
})

const cardStatisticsValues = computed(() => [
  {
    title: 'Total Orders',
    color: 'primary',
    stats: statsValues.value.total_orders,
    icon: 'tabler-packages',
    to: 'orders',
  },
  {
    title: 'Total Subscriptions',
    color: 'error',
    stats: statsValues.value.total_subscriptions,
    icon: 'tabler-receipt-dollar',
    to: 'subscriptions',
  },
  {
    title: 'Total Users',
    color: 'info',
    stats: statsValues.value.total_users,
    icon: 'tabler-users',
    to: 'users',
  },
])

const cardStatisticsValuesRow2 = computed(() => [
  {
    title: 'Total Sales',
    color: 'info',
    stats: formatCurrency(statsValues.value.total_sales),
    icon: 'tabler-shopping-cart',
  },
  {
    title: 'Total Revenue',
    color: 'success',
    stats: formatCurrency(statsValues.value.total_revenue),
    icon: 'tabler-receipt-dollar',
  },
  {
    title: 'Total Consult Fees',
    color: 'error',
    stats: formatCurrency(statsValues.value.total_consultation),
    icon: 'tabler-receipt-dollar',
  },
  {
    title: 'Total Shipping Cost',
    color: 'warning',
    stats: formatCurrency(statsValues.value.total_shipment_cost),
    icon: 'tabler-receipt-dollar',
  },
])

const revenueGrowth = ref({})
const salesGrowth = ref({})

onMounted(() => {
  fetchData()
})

async function fetchData() {
  skeletonLoading.value = true

  try {
    const { data } = await ApiService.get('/admin/dashboard-statistics')

    if (data.status === 200) {
      statsValues.value.total_orders = data.statistics.total_orders
      statsValues.value.total_subscriptions= data.statistics.total_subscriptions
      statsValues.value.total_users = data.statistics.total_users
      statsValues.value.total_sales = data.statistics.total_sales
      statsValues.value.total_revenue = data.statistics.total_revenue
      statsValues.value.total_consultation = data.statistics.total_consultation
      statsValues.value.total_shipment_cost = data.statistics.total_shipment_cost

      revenueGrowth.value = data.this_week_revenue_growth
      salesGrowth.value = data.this_week_sales
    } else {
      showSnackbar(processErrors(data)[0], 'error')
    }
  } catch (error) {
    console.error(error)
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    skeletonLoading.value = false
  }
}
</script>

<template>
  <VRow class="match-height">
    <VCol cols="12">
      <div class="d-flex flex-wrap gap-6">
        <CardStatistics
          v-for="statistics in cardStatisticsValues"
          :key="statistics.title"
          v-bind="statistics"
          :loading="skeletonLoading"
          class="flex-grow-1 h-auto"
          style="flex: 1 1 0;"
        />
      </div>
    </VCol>
    <VCol cols="12">
      <div class="d-flex flex-wrap gap-6">
        <CardStatistics
          v-for="statistics in cardStatisticsValuesRow2"
          :key="statistics.title"
          v-bind="statistics"
          :loading="skeletonLoading"
          class="flex-grow-1 h-auto"
          style="flex: 1 1 0;"
        />
      </div>
    </VCol>

    <!--
      <VCol
      v-for="statistics in cardStatisticsValuesRow2"
      :key="statistics.title"
      cols="12"
      sm="6"
      md="2"
      >
      <VCard>
      <VCardText>
      <VAvatar
      :color="statistics.color"
      variant="tonal"
      rounded
      size="52"
      >
      <VIcon :icon="statistics.icon" />
      </VAvatar>

      <VSpacer class="my-8" />

      <Skeleton
      v-if="skeletonLoading"
      height="1.2rem"
      width="4rem"
      />
      <p
      v-else
      class="mb-2"
      >
      {{ statistics.title }}
      </p>
      <Skeleton
      v-if="skeletonLoading"
      class="mt-2"
      height="1.5rem"
      width="6rem"
      />
      <h5
      v-else
      class="text-h5 mt-2"
      >
      {{ statistics.stats }}
      </h5>
      </VCardText>
      </VCard>
      </VCol>
    -->

    <VCol
      cols="12"
      md="6"
    >
      <CardGrowth
        title="Revenue Growth"
        :loading="skeletonLoading"
        :chart-data="revenueGrowth?.chart_data || {}"
        :amount="revenueGrowth?.amount || 0"
        :percentage="revenueGrowth?.percentage || 0"
      />
    </VCol>
    <VCol
      cols="12"
      md="6"
    >
      <CardGrowth
        title="Sales Growth"
        :loading="skeletonLoading"
        :chart-data="salesGrowth?.chart_data || {}"
        :amount="salesGrowth?.amount || 0"
        :percentage="salesGrowth?.percentage || 0"
      />
    </VCol>
  </VRow>
</template>
