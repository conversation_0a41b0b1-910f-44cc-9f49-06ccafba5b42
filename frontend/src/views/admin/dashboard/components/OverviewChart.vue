<script setup>
import { computed, ref, watch, onMounted } from 'vue'
import VueApexCharts from 'vue3-apexcharts'
import { useTheme } from 'vuetify'
import { hexToRgb } from '@layouts/utils'
import { useGlobalData } from '@/store/global'
import { isEmpty } from '@/@core/utils'
import ApiService from '@/services/ApiService'
import { processErrors } from '@/utils/errorHandler'

const { showSnackbar } = useGlobalData()
const vuetifyTheme = useTheme()
const refVueApexChart = ref()
const skeletonLoading = ref(true)
const recordType = ref('order')
const filterInterval = ref('day')
const filterDateRange = ref(null)
const fromDate = ref(null)
const toDate = ref(null)
const filterOrderStatus = ref(null)
const filterSubscriptionStatus = ref(null)

const orderStatusFilterOptions = [
  { title: 'Pending Visit', value: 0 },
  { title: 'Awaiting for Prescription', value: 1 },
  { title: 'Canceled', value: 2 },
  { title: 'Prescription Issued', value: 3 },
  { title: 'Payment Failed', value: 4 },
  { title: 'Pending Payment', value: 5 },
  { title: 'Prescription Not Issued', value: 6 },
  { title: 'Preparing for Shipment', value: 7 },
  { title: 'In Transit', value: 8 },
  { title: 'Out for Delivery', value: 9  },
  { title: 'Delivered', value: 10 },
]

const subscriptionStatusFilterOptions = [
  { title: 'On Hold', value: 0 },
  { title: 'Active', value: 1 },
  { title: 'Canceled', value: 2 },
  { title: 'Expired', value: 3 },
]

const chartTabs = [
  {
    title: 'Orders',
    icon: 'tabler-shopping-cart',
    value: 'order',
  },
  {
    title: 'Subscriptions',
    icon: 'tabler-receipt-dollar',
    value: 'subscription',
  },
  {
    title: 'Users',
    icon: 'tabler-users',
    value: 'user',
  },
  {
    title: 'Sales',
    icon: 'tabler-currency-dollar',
    value: 'sales',
  },
  {
    title: 'Revenue',
    icon: 'tabler-currency-dollar',
    value: 'revenue',
  },
]

const intervalOptions = [
  {
    title: 'Daily',
    value: 'day',
  },
  {
    title: 'Weekly',
    value: 'week',
  },
  {
    title: 'Monthly',
    value: 'month',
  },
  {
    title: 'Yearly',
    value: 'year',
  },
]

const xAxisData = ref([])

const chartSeries = ref([
  {
    data: [],
  },
])

const statsValues = ref([])

const chartOptions = computed(() => {
  const currentTheme = vuetifyTheme.current.value.colors
  const variableTheme = vuetifyTheme.current.value.variables
  const labelPrimaryColor = `rgba(${hexToRgb(currentTheme.primary)},${variableTheme['dragged-opacity']})`
  const legendColor = `rgba(${hexToRgb(currentTheme['on-background'])},${variableTheme['high-emphasis-opacity']})`
  const borderColor = `rgba(${hexToRgb(String(variableTheme['border-color']))},${variableTheme['border-opacity']})`
  const labelColor = `rgba(${hexToRgb(currentTheme['on-surface'])},${variableTheme['disabled-opacity']})`

  let yAxisTitle = ''
  if (recordType.value === 'order') {
    yAxisTitle = 'No of Orders'
  } else if (recordType.value === 'subscription') {
    yAxisTitle = 'No of Subscriptions'
  } else if (recordType.value === 'user') {
    yAxisTitle = 'No of Users'
  } else if (recordType.value === 'revenue') {
    yAxisTitle = 'Revenue'
  } else if (recordType.value === 'sales') {
    yAxisTitle = 'Sales'
  }

  return {
    chart: {
      parentHeightOffset: 0,
      type: 'area',
      toolbar: { show: false },
      zoom: {
        enabled: false,
      },
    },
    grid: {
      show: false,

      // padding: {
      //   top: 0,
      //   bottom: 0,
      //   left: -10,
      //   right: -10,
      // },
    },
    colors: [
      'rgb(130, 107, 248)',
      'rgb(0, 212, 189)',
      'rgb(253, 216, 53)',
      'rgb(255, 161, 161)',
      'rgb(50, 186, 255)',
      'rgb(0,207,232)',
      'rgb(255,159,67)',
      'rgb(234,84,85)',
    ],
    dataLabels: {
      enabled: false,
    },
    legend: {
      position: 'top',
      horizontalAlign: 'right',
      labels: {
        colors: [legendColor],
      },
    },
    tooltip: {
      theme: 'dark',
      y: {
        formatter(val) {
          if (recordType.value === 'revenue' || recordType.value === 'sales') {
            if (val > 1000) {
              return `$${parseFloat(val / 1000).toFixed(3)}k`
            } else {
              return `$${val}`
            }
          } else {
            if (val > 1000) {
              return `${parseFloat(val / 1000).toFixed(3)}k`
            } else {
              return `${val}`
            }
          }
        },
      },
    },
    xaxis: {
      title: {
        text: `Time Period (in ${filterInterval.value}s)`,
        style: {
          color: labelColor,
          fontSize: '12px',
          fontFamily: 'Public Sans',
        },
      },
      categories: xAxisData.value,
      axisBorder: {
        show: true,
        color: borderColor,
      },
      axisTicks: { show: false },
      labels: {
        style: {
          colors: labelColor,
          fontSize: '11px',
          fontFamily: 'Public Sans',
        },
      },
    },
    yaxis: {
      title: {
        text: yAxisTitle,
        style: {
          color: labelColor,
          fontSize: '12px',
          fontFamily: 'Public Sans',
        },
      },
      labels: {
        formatter(val) {
          if (recordType.value === 'revenue' || recordType.value === 'sales') {
            if (val > 1000) {
              return `$${parseFloat(val / 1000).toFixed(2)}k`
            } else {
              return `$${val}`
            }
          } else {
            if (val > 1000) {
              return `${parseFloat(val / 1000).toFixed(2)}k`
            } else {
              return `${val}`
            }
          }
        },
        style: {
          fontSize: '12px',
          colors: labelColor,
          fontFamily: 'Public Sans',
        },
      },
    },
  }
})

onMounted(() => {
  fetchData()
})

async function fetchData() {
  updateChartData([], [])
  skeletonLoading.value = true

  const postData = {
    filter_type: recordType.value,
    mode: filterInterval.value,
    from_date: filterInterval.value === 'day' ? fromDate.value : null,
    to_date: filterInterval.value === 'day' ? toDate.value : null,
  }

  if (recordType.value === 'order') {
    postData['order_status'] = filterOrderStatus.value
  }
  if (recordType.value === 'subscription') {
    postData['subscription_status'] = filterSubscriptionStatus.value
  }

  try {
    const { data } = await ApiService.post('/admin/dashboard-chart', postData)
    if (data.status === 200) {
      if (!isEmpty(data.info.x_axis) && !isEmpty(data.info.y_axis)) {
        updateChartData(data.info.x_axis, data.info.y_axis)
      }
      statsValues.value = !isEmpty(data.info.data) ? data.info.data : []
    } else {
      showSnackbar(processErrors(data)[0], 'error')
    }
  } catch (error) {
    console.error(error)
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    skeletonLoading.value = false
  }
}

const updateChartData = (xAxisArr, yAxisArr) => {
  xAxisData.value = xAxisArr
  chartSeries.value = yAxisArr.map(series => {
    return {
      name: series.title,
      data: series.value,
    }
  })
}

watch([recordType, filterOrderStatus, filterSubscriptionStatus, fromDate, toDate], () => {
  fetchData()
})

watch(filterInterval, newVal => {
  if (newVal !== 'day' && filterDateRange.value !== null) {
    filterDateRange.value = null
  }
  fetchData()
})

watch(filterDateRange, () => {
  if (isEmpty(filterDateRange.value)) {
    fromDate.value = null
    toDate.value = null

    return
  }

  const dateArr = filterDateRange.value.split(' to ')

  if (dateArr.length === 2) {
    fromDate.value = dateArr[0] || null
    toDate.value = dateArr[1] || null
  } else {
    fromDate.value = null
    toDate.value = null
  }

  if (!isEmpty(fromDate.value) && !isEmpty(toDate.value)) {
    filterInterval.value = 'day'
  }
})
</script>

<template>
  <VCard>
    <template #title>
      <VCardTitle>
        <span>Statistics Overview</span>
        <div
          v-if="statsValues.length > 0"
          class="mt-3 mt-md-0 float-md-right"
        >
          <VChip
            v-for="data in statsValues"
            :key="data.key"
            size="large"
            rounded="0"
            color="primary"
            variant="tonal"
            class="text-capitalize"
            style="border: thin solid rgb(var(--v-theme-primary))"
          >
            <Skeleton
              v-if="skeletonLoading"
              height="1rem"
              width="160px"
            ></Skeleton>
            <span v-else>{{ data.key + ": " + data.value }}</span>
          </VChip>
        </div>
      </VCardTitle>
    </template>
    <VCardText>
      <VRow class="mb-3">
        <VCol
          cols="12"
          md="6"
        >
          <VBtnToggle
            v-model="filterInterval"
            density="compact"
            color="primary"
            variant="outlined"
            divided
            mandatory
          >
            <VBtn
              v-for="(item, index) in intervalOptions"
              :key="index"
              :value="item.value"
            >
              {{ item.title }}
            </VBtn>
          </VBtnToggle>
        </VCol>
        <VCol
          cols="12"
          md="6"
        >
          <VRow class="justify-end">
            <VCol
              v-if="recordType === 'order'"
              cols="12"
              sm="6"
            >
              <AppSelect
                v-model="filterOrderStatus"
                label="Order Status"
                placeholder="Select"
                :items="orderStatusFilterOptions"
                clearable
              />
            </VCol>
            <VCol
              v-if="recordType === 'subscription'"
              cols="12"
              sm="6"
            >
              <AppSelect
                v-model="filterSubscriptionStatus"
                label="Subscription Status"
                placeholder="Select"
                :items="subscriptionStatusFilterOptions"
                clearable
              />
            </VCol>
            <VCol
              cols="12"
              sm="6"
            >
              <AppDateTimePicker
                v-model="filterDateRange"
                label="Date Range"
                placeholder="Select"
                :config="{
                  mode: 'range',
                  dateFormat: 'm/d/Y',
                  maxDate: new Date(),
                }"
                clearable
                clear-icon="tabler-x"
              />
            </VCol>
          </VRow>
        </VCol>
      </VRow>

      <VSlideGroup
        v-model="recordType"
        class="pb-1"
        show-arrows
        mandatory
      >
        <VSlideGroupItem
          v-for="item in chartTabs"
          :key="item.value"
          v-slot="{ isSelected, toggle }"
          :value="item.value"
        >
          <div
            style="block-size: 94px; inline-size: 110px"
            :style="isSelected ? 'border-color:rgb(var(--v-theme-primary)) !important' : ''"
            :class="isSelected ? 'border' : 'border border-dashed'"
            class="d-flex flex-column justify-center align-center cursor-pointer rounded px-5 py-2 me-6"
            @click="toggle"
          >
            <VAvatar
              rounded
              size="38"
              :color="isSelected ? 'primary' : 'secondary'"
              variant="tonal"
              class="mb-2"
            >
              <VIcon :icon="item.icon" />
            </VAvatar>
            <p class="mb-0 font-weight-medium">
              {{ item.title }}
            </p>
          </div>
        </VSlideGroupItem>
      </VSlideGroup>

      <div v-if="skeletonLoading">
        <div
          class="d-flex justify-center align-center"
          style="height: 300px"
        >
          <VProgressCircular
            indeterminate
            size="40"
          />
        </div>
      </div>

      <VueApexCharts
        v-else
        ref="refVueApexChart"
        :key="recordType"
        :options="chartOptions"
        :series="chartSeries"
        height="300"
        class="mt-3"
      />
    </VCardText>
  </VCard>
</template>
