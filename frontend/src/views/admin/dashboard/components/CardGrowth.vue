<script setup>
import { formatCurrency } from '@/utils/helpers'
import { useTheme } from 'vuetify'
import VueApexCharts from 'vue3-apexcharts'
import { hexToRgb } from '@layouts/utils'
import { isEmpty } from '@/@core/utils'

const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  loading: {
    type: Boolean,
    default: false,
  },
  chartData: {
    type: Object,
    required: true,
  },
  amount: {
    type: [Number, String],
    required: true,
  },
  percentage: {
    type: [Number, String],
    required: true,
  },
})

const vuetifyTheme = useTheme()

const chartXAxis = computed(() => {
  if (isEmpty(props.chartData) || isEmpty(props.chartData.x_axis)) {
    return ['S', 'M', 'T', 'W', 'T', 'F', 'S']
  }

  return props.chartData.x_axis
})

const chartSeries = computed(() => {
  if (isEmpty(props.chartData) || isEmpty(props.chartData.y_axis)) {
    return [{ data: [0, 0, 0, 0, 0, 0, 0] }]
  }

  return [{ data: props.chartData.y_axis }]
})

const chartOptions = computed(() => {
  const currentTheme = vuetifyTheme.current.value.colors
  const variableTheme = vuetifyTheme.current.value.variables
  const labelSuccessColor = `rgba(${hexToRgb(currentTheme.success)},0.16)`
  const labelColor = `rgba(${hexToRgb(currentTheme['on-surface'])},${variableTheme['disabled-opacity']})`

  return {
    chart: {
      type: 'bar',
      parentHeightOffset: 0,
      toolbar: { show: false },
    },
    plotOptions: {
      bar: {
        barHeight: '80%',
        columnWidth: '30%',
        startingShape: 'rounded',
        endingShape: 'rounded',
        borderRadius: 6,
        distributed: true,
      },
    },
    tooltip: { enabled: false },
    grid: {
      show: false,
      padding: {
        top: -20,
        bottom: -12,
        left: -10,
        right: 0,
      },
    },
    colors: [
      labelSuccessColor,
      labelSuccessColor,
      labelSuccessColor,
      labelSuccessColor,
      currentTheme.success,
      labelSuccessColor,
      labelSuccessColor,
    ],
    dataLabels: { enabled: false },
    legend: { show: false },
    xaxis: {
      categories: chartXAxis.value,
      axisBorder: { show: false },
      axisTicks: { show: false },
      labels: {
        style: {
          colors: labelColor,
          fontSize: '13px',
          fontFamily: 'Public sans',
        },
      },
    },
    yaxis: { labels: { show: false } },
    states: { hover: { filter: { type: 'none' } } },
    responsive: [
      {
        breakpoint: 1471,
        options: { plotOptions: { bar: { columnWidth: '50%' } } },
      },
      {
        breakpoint: 1350,
        options: { plotOptions: { bar: { columnWidth: '57%' } } },
      },
      {
        breakpoint: 1032,
        options: { plotOptions: { bar: { columnWidth: '60%' } } },
      },
      {
        breakpoint: 992,
        options: {
          plotOptions: {
            bar: {
              columnWidth: '40%',
              borderRadius: 8,
            },
          },
        },
      },
      {
        breakpoint: 855,
        options: {
          plotOptions: {
            bar: {
              columnWidth: '50%',
              borderRadius: 6,
            },
          },
        },
      },
      {
        breakpoint: 440,
        options: { plotOptions: { bar: { columnWidth: '40%' } } },
      },
      {
        breakpoint: 381,
        options: { plotOptions: { bar: { columnWidth: '45%' } } },
      },
    ],
  }
})
</script>

<template>
  <VCard>
    <VCardText class="d-flex justify-space-between">
      <div class="d-flex flex-column">
        <div class="mb-auto">
          <h6 class="text-h6 text-no-wrap mb-2">
            {{ props.title }}
          </h6>
          <p class="mb-0 text-sm">
            Weekly Report
          </p>
        </div>

        <div
          v-if="loading"
          class="mt-6"
        >
          <Skeleton
            width="6rem"
            height="1.2rem"
            class="mb-3"
          ></Skeleton>
          <Skeleton
            width="4rem"
            height="1.5rem"
            class="mb-0"
          ></Skeleton>
        </div>
        <div v-else>
          <h4 class="text-h4 mb-2">
            {{ formatCurrency(props.amount) }}
          </h4>
          <VChip
            v-if="props.percentage !== 0"
            label
            :color="props.percentage > 0 ? 'success' : 'error'"
          >
            {{ props.percentage }}%
          </VChip>
        </div>
      </div>
      <div v-if="!loading">
        <VueApexCharts
          :options="chartOptions"
          :series="chartSeries"
          :height="150"
        />
      </div>
    </VCardText>
  </VCard>
</template>
