<script setup>
import { isEmpty } from '@/@core/utils'

const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  color: {
    type: String,
    required: false,
    default: 'primary',
  },
  icon: {
    type: String,
    required: true,
  },
  stats: {
    type: [String, Number],
    required: true,
  },
  loading: {
    type: Boolean,
    default: false,
  },
  to: {
    type: String,
    default: '',
  },
})
</script>

<template>
  <VCard :to="props.to">
    <VCardText class="d-flex align-center justify-space-between gap-4">
      <div class="d-flex justify-space-between gap-6">
        <VAvatar
          v-if="props.icon"
          size="52"
          rounded
          variant="tonal"
          :color="props.color"
          class="mt-1"
        >
          <VIcon :icon="props.icon" />
        </VAvatar>
        <div style="min-width: 100px;">
          <Skeleton
            v-if="props.loading"
            height="1.2rem"
            width="6rem"
          />
          <span
            v-else
            class="text-body-2"
          >{{ props.title }}</span>
          <Skeleton
            v-if="props.loading"
            class="mt-2"
            height="1.5rem"
            width="8rem"
          />
          <h4
            v-else
            class="text-h4 mt-1"
          >
            {{ props.stats }}
          </h4>
        </div>
      </div>
      <div v-if="!isEmpty(props.to)">
        <VIcon
          icon="tabler-chevron-right"
          size="24"
        />
      </div>
    </VCardText>
  </VCard>
</template>
