<script setup>
import { requiredValidator } from '@validators'
import { useGlobalData } from '@/store/global'
import { useRouter } from 'vue-router'
import { scrollToTop } from '@/utils/helpers'
import ApiService from '@/services/ApiService'
import { processErrors } from '@/utils/errorHandler'
import { watch } from 'vue'
import { PerfectScrollbar } from 'vue3-perfect-scrollbar'
import { isEmpty } from '@/@core/utils'
import { IconCheck, IconCircleCheck, IconCircleX } from '@tabler/icons-vue'

const props = defineProps({
  isDrawerOpen: {
    type: Boolean,
    required: true,
  },
  stateId: {
    type: Number,
    required: false,
  },
})

const emit = defineEmits(['update:isDrawerOpen', 'updated'])

const router = useRouter()
const globalData = useGlobalData()
const { showSnackbar } = globalData

const skeletonLoading = ref(true)
const loading = ref(false)
const serverErrors = ref([])
const refForm = ref()

const formData = ref({
  name: undefined,
  code: undefined,
  type: undefined,
  treatments: [],
})

const whiteLabelTreatmentsDetail = ref([])

function resolveTreatmentVisitType(category) {
  const treatment = whiteLabelTreatmentsDetail.value?.find(t => t.name === category)

  return treatment?.visit_type
}

const treatmentOptions = [
  { title: 'Erectile Dysfunction (ED)', value: 'ED' },
  { title: 'Hair Loss', value: 'HL' },
  { title: 'Weight Loss', value: 'WL' },
]

const typeOptions = [
  { title: 'State', value: 'state' },
  { title: 'Territory', value: 'territory' },
]

watch(props, async () => {
  if (props.isDrawerOpen && !isEmpty(props.stateId)) {
    await fetchStateDetails()
  }
})

async function fetchStateDetails() {
  try {
    skeletonLoading.value = true

    const { data } = await ApiService.post('/admin/view-state', { id: props.stateId })

    if (data.status === 200) {
      const state = data.stateData

      formData.value.name = state.name
      formData.value.code = state.code
      formData.value.type = state.type
      formData.value.treatments = state.treatments

      whiteLabelTreatmentsDetail.value = state.whitelbl_treatments_detail
    } else {
      showSnackbar(data.message, 'error')
      router.push({ name: 'admin-states' })
    }
  } catch (error) {
    console.error(error)
    showSnackbar(error.message || 'Something went wrong!', 'error')
    router.push({ name: 'admin-states' })
  } finally {
    skeletonLoading.value = false
  }
}

const resetForm = () => {
  emit('update:isDrawerOpen', false)
  refForm.value?.reset()
  refForm.value?.resetValidation()
}

const onFormSubmit = () => {
  refForm.value?.validate().then(async ({ valid }) => {
    if (valid) {
      serverErrors.value = []
      loading.value = true

      const postData = { ...formData.value, id: props.stateId }

      try {
        const { data } = await ApiService.post('/admin/update-state', postData)

        if (data.status === 200) {
          await router.push({ name: 'admin-states' })
          showSnackbar(data.message)
          emit('updated')
          resetForm()
        } else {
          serverErrors.value = processErrors(data)
        }
      } catch (error) {
        console.error(error)
        serverErrors.value = processErrors(error)
      } finally {
        loading.value = false
      }
    } else {
      scrollToTop()
    }
  })
}

const handleDrawerModelValueUpdate = val => {
  emit('update:isDrawerOpen', val)
}
</script>

<template>
  <VNavigationDrawer
    :model-value="props.isDrawerOpen"
    temporary
    location="end"
    width="400"
    class="category-navigation-drawer scrollable-content"
    @update:model-value="handleDrawerModelValueUpdate"
  >
    <!-- 👉 Header -->
    <AppDrawerHeaderSection
      title="Edit State / Territory"
      @cancel="$emit('update:isDrawerOpen', false)"
    />

    <VDivider />

    <PerfectScrollbar :options="{ wheelPropagation: false }">
      <VForm
        ref="refForm"
        @submit.prevent="onFormSubmit"
      >
        <VCard flat>
          <VCardText>
            <VAlert
              v-if="serverErrors.length > 0"
              type="error"
              variant="tonal"
              title="Validation failed!"
              closable
              class="mb-8"
            >
              <ul>
                <li
                  v-for="error in serverErrors"
                  :key="error"
                >
                  {{ error }}
                </li>
              </ul>
            </VAlert>

            <VRow>
              <VCol cols="12">
                <span v-if="skeletonLoading">
                  <Skeleton height="2rem"></Skeleton>
                </span>
                <VTextField
                  v-else
                  v-model="formData.name"
                  label="Name"
                  :rules="[requiredValidator]"
                />
              </VCol>

              <VCol cols="12">
                <span v-if="skeletonLoading">
                  <Skeleton height="2rem"></Skeleton>
                </span>
                <VTextField
                  v-else
                  v-model="formData.code"
                  label="Code"
                  :rules="[requiredValidator]"
                  maxlength="2"
                />
              </VCol>

              <VCol cols="12">
                <span v-if="skeletonLoading">
                  <Skeleton height="2rem"></Skeleton>
                </span>
                <VSelect
                  v-else
                  v-model="formData.type"
                  label="Type"
                  :items="typeOptions"
                  :rules="[requiredValidator]"
                  maxlength="2"
                />
              </VCol>

              <VCol cols="12">
                <span v-if="skeletonLoading">
                  <Skeleton
                    height="1.5rem"
                    class="mb-3"
                  ></Skeleton>
                  <Skeleton
                    height="1.5rem"
                    class="mb-3"
                  ></Skeleton>
                  <Skeleton
                    height="1.5rem"
                    class="mb-3"
                  ></Skeleton>
                </span>
                <div v-else>
                  <label class="d-block mb-3">Select treatments available in this state / territory:</label>
                  <div class="d-flex flex-wrap">
                    <div
                      v-for="item in treatmentOptions"
                      :key="item.value"
                      style="min-width: 220px;"
                    >
                      <VCheckbox
                        v-model="formData.treatments"
                        :label="item.title"
                        :value="item.value"
                        class="me-3"
                      />
                      <div class="text-sm d-flex gap-3">
                        <div class="d-flex align-center gap-1">
                          <IconCircleCheck
                            v-if="resolveTreatmentVisitType(item.value) === 'asynchronous'"
                            size="16"
                            stroke-width="2"
                            color="green"
                          />
                          <IconCircleX
                            v-else
                            size="16"
                            stroke-width="2"
                            color="tomato"
                          />
                          <span>Asynchronous Visit</span>
                        </div>
                        <div class="d-flex align-center gap-1">
                          <IconCircleCheck
                            v-if="resolveTreatmentVisitType(item.value) === 'synchronous'"
                            size="16"
                            stroke-width="2"
                            color="green"
                          />
                          <IconCircleX
                            v-else
                            size="16"
                            stroke-width="2"
                            color="tomato"
                          />
                          <span>Synchronous Visit</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </VCol>
            </VRow>
            <VRow class="mt-3">
              <VCol cols="12">
                <VBtn
                  type="submit"
                  class="me-3"
                  :disabled="skeletonLoading"
                  :loading="loading"
                  @click="refForm?.validate()"
                >
                  Submit
                </VBtn>
                <VBtn
                  variant="tonal"
                  color="secondary"
                  :disabled="loading"
                  @click="resetForm"
                >
                  Cancel
                </VBtn>
              </VCol>
            </VRow>
          </VCardText>
        </VCard>
      </VForm>
    </PerfectScrollbar>
  </VNavigationDrawer>
</template>
