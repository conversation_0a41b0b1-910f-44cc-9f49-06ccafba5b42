<script setup>
import { requiredValidator } from '@validators'
import { useGlobalData } from '@/store/global'
import { scrollToTop } from '@/utils/helpers'
import ApiService from '@/services/ApiService'
import { processErrors } from '@/utils/errorHandler'
import { PerfectScrollbar } from 'vue3-perfect-scrollbar'

const props = defineProps({
  isDrawerOpen: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits(['update:isDrawerOpen', 'added'])
const globalData = useGlobalData()
const { showSnackbar } = globalData

const loading = ref(false)
const serverErrors = ref([])
const refForm = ref()

const formData = ref({
  name: undefined,
  code: undefined,
  type: undefined,
  treatments: [],
})

const treatmentOptions = [
  { title: 'Erectile Dysfunction (ED)', value: 'ED' },
  { title: 'Hair Loss', value: 'HL' },
  { title: 'Weight Loss', value: 'WL' },
]

const typeOptions = [
  { title: 'State', value: 'state' },
  { title: 'Territory', value: 'territory' },
]

const resetForm = () => {
  emit('update:isDrawerOpen', false)
  refForm.value?.reset()
  refForm.value?.resetValidation()
}

const onFormSubmit = () => {
  refForm.value?.validate().then(async ({ valid }) => {
    if (valid) {
      serverErrors.value = []
      loading.value = true

      const postData = { ...formData.value }

      try {
        const response = await ApiService.post('/admin/add-state', postData)

        if (response.data.status === 200) {
          showSnackbar(response.data.message)
          emit('added')
          resetForm()
        } else {
          serverErrors.value = processErrors(response.data)
        }
      } catch (error) {
        console.error(error)
        serverErrors.value = processErrors(error)
      } finally {
        loading.value = false
      }
    } else {
      scrollToTop()
    }
  })
}

const handleDrawerModelValueUpdate = val => {
  emit('update:isDrawerOpen', val)
}
</script>

<template>
  <VNavigationDrawer
    :model-value="props.isDrawerOpen"
    temporary
    location="end"
    width="400"
    class="category-navigation-drawer scrollable-content"
    @update:model-value="handleDrawerModelValueUpdate"
  >
    <!-- 👉 Header -->
    <AppDrawerHeaderSection
      title="Add State / Territory"
      @cancel="$emit('update:isDrawerOpen', false)"
    />

    <VDivider />

    <PerfectScrollbar :options="{ wheelPropagation: false }">
      <VForm
        ref="refForm"
        @submit.prevent="onFormSubmit"
      >
        <VCard flat>
          <VCardText>
            <VAlert
              v-if="serverErrors.length > 0"
              type="error"
              variant="tonal"
              title="Validation failed!"
              closable
              class="mb-8"
            >
              <ul>
                <li
                  v-for="error in serverErrors"
                  :key="error"
                >
                  {{ error }}
                </li>
              </ul>
            </VAlert>

            <VRow>
              <VCol cols="12">
                <VTextField
                  v-model="formData.name"
                  label="Name"
                  :rules="[requiredValidator]"
                />
              </VCol>

              <VCol cols="12">
                <VTextField
                  v-model="formData.code"
                  label="Code"
                  :rules="[requiredValidator]"
                  maxlength="2"
                />
              </VCol>

              <VCol cols="12">
                <VSelect
                  v-model="formData.type"
                  label="Type"
                  :items="typeOptions"
                  :rules="[requiredValidator]"
                  maxlength="2"
                />
              </VCol>

              <VCol cols="12">
                <div>
                  <label class="d-block mb-3">Select treatments available in this state / territory:</label>
                  <div class="d-flex flex-wrap">
                    <div
                      v-for="item in treatmentOptions"
                      :key="item.value"
                      style="min-width: 220px;"
                    >
                      <VCheckbox
                        v-model="formData.treatments"
                        :label="item.title"
                        :value="item.value"
                        class="me-3"
                      />
                    </div>
                  </div>
                </div>
              </VCol>
            </VRow>
            <VRow class="mt-3">
              <VCol cols="12">
                <VBtn
                  type="submit"
                  class="me-3"
                  :loading="loading"
                  @click="refForm?.validate()"
                >
                  Submit
                </VBtn>
                <VBtn
                  variant="tonal"
                  color="secondary"
                  :disabled="loading"
                  @click="resetForm"
                >
                  Cancel
                </VBtn>
              </VCol>
            </VRow>
          </VCardText>
        </VCard>
      </VForm>
    </PerfectScrollbar>
  </VNavigationDrawer>
</template>
