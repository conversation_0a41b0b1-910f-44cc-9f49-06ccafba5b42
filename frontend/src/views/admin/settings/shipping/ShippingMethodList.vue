<script setup>
import { useGlobalData } from '@/store/global'
import { useRoute, useRouter } from 'vue-router'
import { isEmpty } from '@/@core/utils'
import ApiService from '@/services/ApiService'
import { capitalizeFirstChar, formatCurrency, isNumber } from '@/utils/helpers'
import { watch } from 'vue'
import { refDebounced } from '@vueuse/core'
import { IconCaretUpDownFilled } from '@tabler/icons-vue'
import { resolveShipmentMethodName } from '@/utils/admin'

const route = useRoute()
const router = useRouter()
const globalData = useGlobalData()
const { showSnackbar } = globalData

const loading = ref(false)
const skeletonLoading = ref(true)
const searchQuery = ref(route.query.searchQuery ?? null)
const debouncedSearchQuery = refDebounced(searchQuery, 300)
const selectedStatus = ref(!isNaN(parseInt(route.query.status)) ? parseInt(route.query.status) : null)
const rowPerPage = ref(10)
const currentPage = ref(1)
const totalPage = ref(1)
const totalItems = ref(0)
const itemsList = ref([])
const sortByColumnName = ref('')
const isSortDirDesc = ref('')
const isDialogVisible = ref(false)
const isDeleteDialogVisible = ref(false)
const itemId = ref('')
const itemStatus = ref(false)
const draggingItemId = ref(null)
const localPharmacyPickupStatus = ref(false)
const convenienceFee = ref('')
const shippingMethodDetails = ref({})
const isDetailsDialogVisible = ref(false)
const detailsLoading = ref(false)

onMounted(() => {
  fetchShippingMethods()
})

const updateRoute = () => {
  router.replace({
    query: {
      status: selectedStatus.value,
      searchQuery: searchQuery.value,
    },
  })
}

watch([debouncedSearchQuery, selectedStatus, rowPerPage], () => {
  currentPage.value = 1
  fetchShippingMethods()
})

watch([currentPage, sortByColumnName, isSortDirDesc], () => {
  fetchShippingMethods()
})

const fetchShippingMethods = async () => {
  try {
    skeletonLoading.value = true

    const requestBody = {
      searchQuery: searchQuery.value,
      is_active: selectedStatus.value,
      perPage: rowPerPage.value,
      page: currentPage.value,
      sortByColumnName: sortByColumnName.value,
      isSortDirDesc: isSortDirDesc.value,
    }

    const { data } = await ApiService.post('/admin/pharmacy-shipping-method-list', requestBody)

    if (data.status === 200) {
      let startIndex = 1
      if (currentPage.value > 1) {
        startIndex = (currentPage.value - 1) * rowPerPage.value + 1
      }

      itemsList.value = data.info.records.map((d, index) => ({ ...d, sno: index + startIndex }))
      totalPage.value = !data.info.totalPage && data.info.totalPage == 0
        ? 1
        : data.info.totalPage
      totalItems.value = data.info.totalRecords ? data.info.totalRecords : itemsList.value.length

      const localPharmacyDetails = data.local_pharmacy_pickup_details

      localPharmacyPickupStatus.value = localPharmacyDetails.is_local_pharmacy_pickup_status === 1 ? true : false
      convenienceFee.value = localPharmacyDetails.convenience_fee

      updateRoute()
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch(error) {
    console.error(error)
  } finally {
    skeletonLoading.value = false
  }
}

const onViewDialog = id => {
  isDetailsDialogVisible.value = true
  getShipmentMethod(id)
}

const getShipmentMethod = id => {
  detailsLoading.value = true

  ApiService.post('/admin/view-pharmacy-shipping-method', { id })
    .then(response => {
      if (response.data.status === 200) {
        shippingMethodDetails.value = response.data.info
      } else {
        showSnackbar(response.data.message, 'error')
      }
    })
    .catch(error => {
      console.error(error)
    })
    .finally(() => {
      detailsLoading.value = false
    })
}

const status = [
  {
    title: 'Active',
    value: 1,
  },
  {
    title: 'Inactive',
    value: 0,
  },
]

const resolveItemStatusVariant = stat => {
  if (stat === 1)
    return {
      color: 'success',
      status: 'Active',
    }
  if (stat === 0)
    return {
      color: 'secondary',
      status: 'Inactive',
    }

  return {
    color: 'primary',
    status: 'Active',
  }
}

const resolveItemStatus = stat => {
  if (stat === 1)
    return {
      color: 'success',
      status: true,
    }
  if (stat === 0)
    return {
      color: 'secondary',
      status: false,
    }

  return {
    color: 'primary',
    status: true,
  }
}

const onAccountStatusChange = (id, status) => {
  isDialogVisible.value = true
  itemId.value = id
  itemStatus.value = status
}

const changeItemStatus = () => {
  loading.value = true

  let itemsValue = itemsList.value
  let is_active = 1

  itemsValue.forEach((data, index) => {
    if (data.id === itemId.value) {
      if (itemsValue[index].is_active === 1) {
        itemsValue[index].is_active = 0
        is_active = 0
      } else {
        itemsValue[index].is_active = 1
        is_active = 1
      }
    }
  })

  ApiService.get(`/admin/update-pharmacy-shipping-method-status/${itemId.value}`)
    .then(response => {
      if (response.data.status === 200) {
        itemsList.value = itemsValue
        isDialogVisible.value = false

        itemId.value = ''
        itemStatus.value = false

        showSnackbar(response.data.message)

        fetchShippingMethods()
      } else {
        showSnackbar(response.data.message, 'error')
      }
    })
    .catch(error => {
      console.error(error)
    })
    .finally(() => {
      loading.value = false
    })
}

const onDeleteDialog = id => {
  isDeleteDialogVisible.value = true
  itemId.value = id
}

const deleteItem = () => {
  loading.value = true

  ApiService.delete(`/admin/delete-pharmacy-shipping-method/${itemId.value}`)
    .then(response => {
      if (response.data.status) {
        isDeleteDialogVisible.value = false
        itemId.value = ''
        showSnackbar(response.data.message)

        fetchShippingMethods()
      } else {
        showSnackbar(response.data.message, 'error')
      }
    })
    .catch(error => {
      console.error(error)
    })
    .finally(() => {
      loading.value = false
    })
}

const dragStart = itemId => {
  draggingItemId.value = itemId
}

const dragOver = itemId => {
  event.preventDefault()

  const draggedItemIndex = itemsList.value.findIndex(item => item.id === draggingItemId.value)
  const targetItemIndex = itemsList.value.findIndex(item => item.id === itemId)

  if (draggedItemIndex !== -1 && targetItemIndex !== -1 && draggedItemIndex !== targetItemIndex) {
    const draggedItem = itemsList.value.splice(draggedItemIndex, 1)[0]

    itemsList.value.splice(targetItemIndex, 0, draggedItem)
    itemsList.value = itemsList.value.map((d, index) => ({ ...d, sno: index + 1 }))
  }
}

const drop = event => {
  event.preventDefault()
}

const dragEnd = () => {
  const itemsIds = itemsList.value.map(item => item.id)

  ApiService
    .post('/admin/reorder-pharmacy-shipping-methods', { ids: itemsIds })
    .then(response => {
      if (response.data.status) {
        // showSnackbar(response.data.message)
      } else {
        showSnackbar(response.data.message, 'error')
      }
    })
    .catch(error => {
      console.error(error)
    })
    .finally(() => {
      draggingItemId.value = null
    })
}

const paginationData = computed(() => {
  const currentPageValue = currentPage.value <= 0 ? 1 : currentPage.value
  const firstIndex = itemsList.value.length ? (currentPageValue - 1) * rowPerPage.value + 1 : 0
  const lastIndex = itemsList.value.length + (currentPageValue - 1) * rowPerPage.value

  return `Showing ${firstIndex} to ${lastIndex} of ${totalItems.value} entries`
})

const switchLabel = label => {
  if (label) {
    return 'Active'
  } else {
    return 'Inactive'
  }
}

watch(localPharmacyPickupStatus, async () => {
  if (!localPharmacyPickupStatus.value) {
    await updateLocalPharmacyPickupDetails()
  }
})

const updateLocalPharmacyPickupDetails = async () => {
  loading.value = true

  const postData = {
    is_local_pharmacy_pickup_status: localPharmacyPickupStatus.value ? 1 : 0,
  }

  if (localPharmacyPickupStatus.value) {
    postData['convenience_fee'] = convenienceFee.value
  }

  ApiService
    .post('/admin/update-local-pharmacy-pickup-details', postData)
    .then(({ data }) => {
      if (data.status === 200) {
        showSnackbar(data.message)

        const localPharmacyDetails = data.local_pharmacy_pickup_details

        localPharmacyPickupStatus.value = localPharmacyDetails.is_local_pharmacy_pickup_status === 1 ? true : false
        convenienceFee.value = localPharmacyDetails.convenience_fee
      } else {
        showSnackbar(data.message, 'error')
      }
    })
    .catch(error => {
      console.error(error)
    })
    .finally(() => {
      loading.value = false
    })
}
</script>

<template>
  <section>
    <VRow>
      <VCol cols="12">
        <VCard
          title="Prescription Pickup Location"
          class="mb-5"
        >
          <VCardText v-if="skeletonLoading">
            <Skeleton height="2rem" />
          </VCardText>
          <VCardText v-else>
            <VRow>
              <VCol cols="12">
                <VSwitch
                  v-model="localPharmacyPickupStatus"
                  inset
                  :label="switchLabel(localPharmacyPickupStatus)"
                />
              </VCol>
              <VCol
                v-if="localPharmacyPickupStatus"
                cols="12"
                sm="6"
              >
                <VTextField
                  v-model="convenienceFee"
                  label="Convenience fee"
                  prefix="$"
                  type="text"
                  @keypress="isNumber($event)"
                />
              </VCol>
              <VCol
                v-if="localPharmacyPickupStatus"
                cols="12"
              >
                <VBtn
                  :loading="loading"
                  @click="updateLocalPharmacyPickupDetails"
                >
                  Submit
                </VBtn>
              </VCol>
            </VRow>
          </VCardText>
        </VCard>

        <VCard title="Shipping Methods">
          <!-- 👉 Filters -->
          <VCardText>
            <VRow>
              <VCol
                cols="12"
                sm="4"
              >
                <VSelect
                  v-model="selectedStatus"
                  label="Select Status"
                  :items="status"
                  clearable
                  clear-icon="tabler-x"
                />
              </VCol>
            </VRow>
          </VCardText>

          <VDivider />

          <VCardText class="d-flex flex-wrap py-4 gap-4">
            <VSpacer />

            <div class="d-flex align-center flex-wrap gap-4">
              <!-- 👉 Search  -->
              <div style="width: 23rem">
                <VTextField
                  v-model="searchQuery"
                  placeholder="Search"
                  density="compact"
                  clearable
                  clear-icon="tabler-x"
                />
              </div>
              <!-- 👉 Add button -->
              <VBtn
                variant="tonal"
                color="primary"
                :to="{ name: 'admin-shipping-method-add' }"
              >
                Add Shipping Method
              </VBtn>
            </div>
          </VCardText>

          <VDivider />

          <VTable class="text-no-wrap">
            <!-- 👉 table head -->
            <thead>
              <tr>
                <th scope="col"></th>
                <th scope="col">
                  NO
                </th>
                <th scope="col">
                  SHIPPING LABEL
                </th>
                <th scope="col">
                  COST
                </th>
                <th scope="col">
                  SHIPMENT METHOD ID
                </th>
                <th scope="col">
                  STATUS
                </th>
                <th scope="col">
                  ACTIVE/INACTIVE
                </th>
                <th scope="col">
                  CREATED AT
                </th>
                <th scope="col">
                  ACTIONS
                </th>
              </tr>
            </thead>

            <!-- 👉 table body -->
            <tbody v-if="skeletonLoading">
              <tr
                v-for="n in 10"
                :key="n"
              >
                <td>
                  <Skeleton
                    width="2rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
                <td>
                  <Skeleton
                    width="2rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
                <td>
                  <Skeleton
                    width="8rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
                <td>
                  <Skeleton
                    width="5rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
                <td>
                  <Skeleton
                    width="12rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
                <td>
                  <Skeleton
                    width="5rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
                <td>
                  <Skeleton
                    width="8rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
                <td>
                  <Skeleton
                    width="8rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
                <td>
                  <Skeleton
                    width="8rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
              </tr>
            </tbody>
            <tbody v-else>
              <tr
                v-for="item in itemsList"
                :key="item.id"
                :draggable="true"
                style="height: 3.75rem"
                @dragstart="dragStart(item.id)"
                @dragover="dragOver(item.id)"
                @drop="drop"
                @dragend="dragEnd"
              >
                <!-- 👉 Drag Drop Order -->
                <td class="drag-style-hover d-flex justify-center align-center">
                  <IconCaretUpDownFilled style="height: 1.5rem; width: 1.5rem;" />
                </td>

                <td>
                  {{ item.sno }}
                </td>

                <td class="title">
                  <h6
                    class="dt-link"
                    @click="onViewDialog(item.id)"
                  >
                    {{ item.title }}
                  </h6>
                </td>

                <td>
                  <span v-if="item.cost === 0">FREE</span>
                  <span v-else>{{ formatCurrency(item.cost) }}</span>
                </td>

                <td>
                  <div v-if="!isEmpty(item.pharmacy_shipment_method_id)">
                    <span class="d-block">
                      {{ resolveShipmentMethodName(item.pharmacy_shipment_method_id) }}
                      <span class="text-sm">
                        (ID: {{ item.pharmacy_shipment_method_id }})
                      </span>
                    </span>
                    <span
                      v-if="item.pharmacy_id"
                      class="d-block text-xs"
                    >Pharmacy: {{ capitalizeFirstChar(item.pharmacy_id) }}</span>
                  </div>
                  <div v-else>
                    -
                  </div>
                </td>

                <td>
                  <VChip
                    label
                    :color="resolveItemStatusVariant(item.is_active).color"
                    size="small"
                    class="text-capitalize"
                  >
                    {{ resolveItemStatusVariant(item.is_active).status }}
                  </VChip>
                </td>

                <td>
                  <VSwitch
                    v-model="resolveItemStatus(item.is_active).status"
                    inset
                    @change="onAccountStatusChange(item.id, resolveItemStatus(item.is_active).status)"
                  />
                </td>

                <td>
                  <div class="d-flex flex-column">
                    <span>{{ item.created_date }}</span>
                    <span>{{ item.created_time }}</span>
                  </div>
                </td>

                <!-- 👉 Actions -->
                <td
                  class="text-center"
                  style="width: 5rem"
                >
                  <VMenu>
                    <template #activator="{ props }">
                      <VBtn
                        v-bind="props"
                        variant="tonal"
                        color="secondary"
                        rounded="lg"
                        size="x-small"
                        icon
                      >
                        <VIcon
                          icon="mdi-dots-horizontal"
                          size="22"
                        />
                      </VBtn>
                    </template>

                    <VList>
                      <VListItem @click="onViewDialog(item.id)">
                        <VListItemTitle>
                          <div class="font-weight-medium">
                            View
                          </div>
                        </VListItemTitle>
                      </VListItem>

                      <VListItem
                        :to="{
                          name: 'admin-shipping-method-edit',
                          params: { id: item.id },
                          query: {
                            status: selectedStatus,
                            searchQuery: searchQuery,
                          },
                        }"
                      >
                        <VListItemTitle>
                          <div class="font-weight-medium">
                            Edit
                          </div>
                        </VListItemTitle>
                      </VListItem>

                      <VListItem @click="onDeleteDialog(item.id)">
                        <VListItemTitle>
                          <div class="font-weight-medium text-error">
                            Delete
                          </div>
                        </VListItemTitle>
                      </VListItem>
                    </VList>
                  </VMenu>
                </td>
              </tr>
            </tbody>

            <!-- 👉 table footer  -->
            <tfoot v-show="!skeletonLoading && itemsList.length === 0">
              <tr>
                <td
                  colspan="9"
                  class="text-center"
                >
                  No data available
                </td>
              </tr>
            </tfoot>
          </VTable>

          <VDivider />

          <VCardText class="d-flex align-center flex-wrap justify-space-between gap-4 py-3 px-5">
            <span class="text-sm text-disabled">
              {{ paginationData }}
            </span>
          </VCardText>
        </VCard>
      </VCol>
    </VRow>

    <!-- 👉 Change Status -->
    <VDialog
      v-model="isDialogVisible"
      persistent
      class="v-dialog-sm"
    >
      <!-- Dialog close btn -->
      <DialogCloseBtn @click="isDialogVisible = !isDialogVisible" />

      <!-- Dialog Content -->
      <VCard title="Confirm Status Change">
        <VCardText> Are you sure you want to change shipping method status? </VCardText>

        <VCardText class="d-flex justify-end gap-3 flex-wrap">
          <VBtn
            color="secondary"
            variant="tonal"
            @click="isDialogVisible = false"
          >
            No
          </VBtn>
          <VBtn
            :loading="loading"
            @click="changeItemStatus"
          >
            Yes
          </VBtn>
        </VCardText>
      </VCard>
    </VDialog>

    <!-- 👉 Delete -->
    <VDialog
      v-model="isDeleteDialogVisible"
      persistent
      class="v-dialog-sm"
    >
      <!-- Dialog close btn -->
      <DialogCloseBtn @click="isDeleteDialogVisible = !isDeleteDialogVisible" />

      <!-- Dialog Content -->
      <VCard title="Confirm Deletion">
        <VCardText> Are you sure you want to delete this shipping method? </VCardText>

        <VCardText class="d-flex justify-end gap-3 flex-wrap">
          <VBtn
            color="secondary"
            variant="tonal"
            @click="isDeleteDialogVisible = false"
          >
            No
          </VBtn>
          <VBtn
            :loading="loading"
            @click="deleteItem"
          >
            Yes
          </VBtn>
        </VCardText>
      </VCard>
    </VDialog>

    <!-- 👉 view details -->
    <VDialog
      v-model="isDetailsDialogVisible"
      persistent
      class="v-dialog-lg"
    >
      <DialogCloseBtn @click="isDetailsDialogVisible = !isDetailsDialogVisible" />
      <VCard title="Shipping Method">
        <VCardText>
          <VRow v-if="detailsLoading">
            <VCol
              cols="12"
              class="d-flex justify-center"
            >
              <VProgressCircular
                indeterminate
                color="primary"
                class="py-8"
              />
            </VCol>
          </VRow>
          <VRow v-else>
            <VCol cols="6">
              <div class="text-body-2 fw-semibold">
                Shipping Label
              </div>
              <div class="text-body-1">
                {{ shippingMethodDetails.title }}
              </div>
            </VCol>
            <VCol cols="6">
              <div class="text-body-2 fw-semibold">
                Cost
              </div>
              <div
                v-if="shippingMethodDetails.cost === 0"
                class="text-body-1"
              >
                FREE
              </div>
              <div
                v-else
                class="text-body-1"
              >
                {{ formatCurrency(shippingMethodDetails.cost) }}
              </div>
            </VCol>
            <VCol cols="6">
              <div class="text-body-2 fw-semibold">
                Status
              </div>
              <div class="text-body-1">
                <VChip
                  label
                  :color="resolveItemStatusVariant(shippingMethodDetails.is_active).color"
                  size="small"
                  class="text-capitalize"
                >
                  {{ resolveItemStatusVariant(shippingMethodDetails.is_active).status }}
                </VChip>
              </div>
            </VCol>
            <VCol cols="6">
              <div class="text-body-2 fw-semibold">
                Shipment Method ID
              </div>
              <div class="text-body-1">
                {{ resolveShipmentMethodName(shippingMethodDetails.pharmacy_shipment_method_id) }} ({{
                  shippingMethodDetails.pharmacy_shipment_method_id
                }})
              </div>
            </VCol>
            <VCol cols="12">
              <div class="text-body-2 fw-semibold">
                Description
              </div>
              <div class="text-body-1">
                {{ shippingMethodDetails.description || "-" }}
              </div>
            </VCol>
          </VRow>
        </VCardText>

        <VCardText
          v-if="!detailsLoading"
          class="d-flex justify-center"
        >
          <VBtn
            :to="{
              name: 'admin-shipping-method-edit',
              params: { id: shippingMethodDetails.id },
              query: {
                status: selectedStatus,
                searchQuery: searchQuery,
              },
            }"
          >
            Edit
          </VBtn>
        </VCardText>
      </VCard>
    </VDialog>
  </section>
</template>

<style lang="scss">
.drag-style-hover {
  cursor: move;
}
.v-table__wrapper td.title {
  min-width: 200px;
  h6 {
    a {
      white-space: pre-line;
    }
  }
}
</style>
