<script setup>
import { requiredValidator } from '@validators'
import { useGlobalData } from '@/store/global'
import { useRouter } from 'vue-router'
import { isNumber } from '@/utils/helpers'
import ApiService from '@/services/ApiService'
import { processErrors } from '@/utils/errorHandler'
import { gogomedsShipmentMethodIds, hallandaleShipmentMethodIds } from '@/utils/admin'

const router = useRouter()
const globalData = useGlobalData()
const { showSnackbar } = globalData

const loading = ref(false)
const serverErrors = ref([])
const refForm = ref()

const formData = ref({
  title: undefined,
  description: undefined,
  pharmacy_id: undefined,
  pharmacy_shipment_method_id: undefined,
  cost: undefined,
  is_active: true,
})

const pharmacyOptions = [
  { title: 'GoGoMeds Pharmacy', value: 'gogomeds' },

  // { title: 'Hallandale Pharmacy', value: 'hallandale' },
]

const shipmentMethodOptions = computed(() => {
  if (formData.value.pharmacy_id === 'gogomeds') {
    return gogomedsShipmentMethodIds
  } else if (formData.value.pharmacy_id === 'hallandale') {
    return hallandaleShipmentMethodIds
  } else {
    return []
  }
})

onMounted(() => {
  //
})

const switchLabel = label => {
  if (label) {
    return 'Active'
  } else {
    return 'Inactive'
  }
}

const onFormSubmit = () => {
  refForm.value?.validate().then(async ({ valid }) => {
    if (valid) {
      serverErrors.value = []
      loading.value = true

      const values = {
        ...formData.value,
        is_active: formData.value.is_active === true ? 1 : 0,
      }

      const updatedValues = values

      try {
        const response = await ApiService.post('/admin/add-new-pharmacy-shipping-method', updatedValues)

        if (response.data.status === 200) {
          showSnackbar(response.data.message)
          refForm.value?.reset()
          refForm.value?.resetValidation()
          router.push({ name: 'admin-shipping-methods' })
        } else {
          serverErrors.value = processErrors(response.data)
        }
      } catch (error) {
        console.error(error)
        serverErrors.value = processErrors(error)
      } finally {
        loading.value = false
      }
    } else {
      window.scrollTo({
        top: 0,
        left: 0,
        behavior: 'smooth',
      })
    }
  })
}
</script>

<template>
  <VRow>
    <VCol cols="12">
      <VForm
        ref="refForm"
        @submit.prevent="onFormSubmit"
      >
        <VCard title="Add Shipping Method">
          <VCardText>
            <VAlert
              v-if="serverErrors.length > 0"
              type="error"
              variant="tonal"
              title="Validation failed!"
              closable
              class="mb-8"
            >
              <ul>
                <li
                  v-for="error in serverErrors"
                  :key="error"
                >
                  {{ error }}
                </li>
              </ul>
            </VAlert>

            <VRow>
              <VCol cols="12">
                <VTextField
                  v-model="formData.title"
                  label="Shipping Label"
                  :rules="[requiredValidator]"
                />
              </VCol>

              <VCol cols="12">
                <VTextarea
                  v-model="formData.description"
                  label="Description"
                  rows="3"
                />
              </VCol>

              <VCol
                cols="12"
                md="4"
              >
                <VSelect
                  v-model="formData.pharmacy_id"
                  label="Select Pharmacy"
                  placeholder="Select"
                  :items="pharmacyOptions"
                  :rules="[requiredValidator]"
                />
              </VCol>

              <VCol
                cols="12"
                md="4"
              >
                <VSelect
                  v-model="formData.pharmacy_shipment_method_id"
                  label="Select Shipment Method"
                  :items="shipmentMethodOptions"
                  :rules="[requiredValidator]"
                >
                  <template #selection="{ item }">
                    <div>
                      <span>{{ item?.raw?.title }}</span> <span class="text-body-2">(Shipment Id: {{ item?.raw?.value }})</span>
                    </div>
                  </template>
                  <template #item="{ props, item }">
                    <div
                      v-bind="props"
                      class="v-list-item"
                    >
                      <span class="inline-block me-1">{{ item?.raw?.title }}</span>
                      <span class="inline-block text-body-2">(Shipment Id: {{ item?.raw?.value }})</span>
                    </div>
                  </template>
                </VSelect>
              </VCol>

              <VCol
                cols="12"
                md="4"
              >
                <VTextField
                  v-model="formData.cost"
                  label="Cost"
                  :rules="[requiredValidator]"
                  class="mb-1"
                  prefix="$"
                  @keypress="isNumber($event)"
                />
              </VCol>
            </VRow>
            <VRow class="mt-3">
              <VCol
                cols="12"
                md="2"
              >
                <VSwitch
                  v-model="formData.is_active"
                  inset
                  :label="switchLabel(formData.is_active)"
                />
              </VCol>

              <VCol cols="12">
                <VBtn
                  type="submit"
                  :loading="loading"
                  @click="refForm?.validate()"
                >
                  Submit
                </VBtn>
                <VBtn
                  type="button"
                  :to="{ name: 'admin-shipping-methods' }"
                  variant="outlined"
                  class="ms-2"
                >
                  Back
                </VBtn>
              </VCol>
            </VRow>
          </VCardText>
        </VCard>
      </VForm>
    </VCol>
  </VRow>
</template>

<style scoped lang="scss">
.v-list-item {
  &:hover {
    background-color: rgba(115, 103, 240, 0.12);
    cursor: pointer;
  }
}
</style>
