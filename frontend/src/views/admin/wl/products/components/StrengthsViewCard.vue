<script setup>
import { isEmpty, parseFloatOrZero } from '@/@core/utils'
import { alertWarning } from '@/plugins/sweetalert2'
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { ceilToDecimalPlaces, formatCurrency } from '@/utils/helpers'
import Image from 'primevue/image'
import { ref } from 'vue'
import StrengthForm from './StrengthForm.vue'
import { processErrors } from '@/utils/errorHandler'

const props = defineProps({
  productData: {
    type: Object,
    required: true,
  },
  strengths: {
    type: Array,
    required: true,
  },
  equivalentProductStrengthList: {
    type: Array,
    default: new Array(),
  },
})

const emit = defineEmits(['updateList'])

const globalData = useGlobalData()
const { showSnackbar }  = globalData

const selectedStrengthId = ref('')
const isStrengthImgUploading = ref(false)
const strengthIdForImgUpdate = ref('')
const strengthImage = ref({ file: null, url: '' })

const handleDeleteStrength = strengthId => {
  alertWarning.fire({
    title: 'Confirm Deletion',
    text: 'Are you sure you want to delete this strength?',
    icon: 'warning',
    showCancelButton: true,
  }).then(result => {
    if (result.isConfirmed) {
      deleteStrength(strengthId)
    }
  })
}

const deleteStrength = async strengthId => {
  try {
    globalData.circularProgress = true

    const { data } = await ApiService.delete(`/admin/delete-wl-product-strength/${strengthId}`)

    if (data.status === 200) {
      showSnackbar(data.message)
      emit('updateList', true)
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    console.error(error)
  } finally {
    globalData.circularProgress = false
  }
}

const resolveItemStatus = status => {
  if (status === 1) {
    return {
      label: 'Active',
      status: true,
      color: 'success',
    }
  } else {
    return {
      label: 'Inactive',
      status: false,
      color: 'secondary',
    }
  }
}

const onStrengthStatusChange = (strength, status) => {
  alertWarning.fire({
    title: 'Confirm Status Change',
    text: `Are you sure you want to ${status ? 'deactivate' : 'activate'} ${strength.strength + ' ' + strength.strength_unit} strength?`,
    icon: 'warning',
    showCancelButton: true,
  }).then(result => {
    if (result.isConfirmed) {
      toggleStrengthStatus(strength.id)
    }
  })
}

const toggleStrengthStatus = async id => {
  try {
    globalData.circularProgress = true

    const { data } = await ApiService.get(`/admin/update-wl-product-strength-status/${id}`)

    if (data.status === 200) {
      showSnackbar(data.message)
      emit('updateList', true)
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    console.error(error)
  } finally {
    globalData.circularProgress = false
  }
}

// const onPlanStatusChange = (plan, status) => {
//   alertWarning.fire({
//     title: 'Confirm Status Change',
//     text: `Are you sure you want to ${status ? 'deactivate' : 'activate'} this package?`,
//     icon: 'warning',
//     showCancelButton: true,
//   }).then(result => {
//     if (result.isConfirmed) {
//       togglePlanStatus(plan.id)
//     }
//   })
// }

// const togglePlanStatus = async id => {
//   try {
//     globalData.circularProgress = true

//     const { data } = await ApiService.get(`/admin/update-wl-subscription-plan-status/${id}`)

//     if (data.status === 200) {
//       showSnackbar(data.message)
//       emit('updateList', true)
//     } else {
//       showSnackbar(data.message, 'error')
//     }
//   } catch (error) {
//     console.error(error)
//   } finally {
//     globalData.circularProgress = false
//   }
// }

function getDiscountHint(plan) {
  if (plan.plan_interval_discount_type === 'percentage' && plan.plan_interval_discount) {
    const totalAmount = parseFloatOrZero(plan.plan_interval_price)
    const discountPercentage = parseFloatOrZero(plan.plan_interval_discount)
    const discountAmount = (totalAmount * discountPercentage) / 100

    return `${formatCurrency(discountAmount.toFixed(2))} off`
  }

  return undefined
}

const handleSelectImg = strength => {
  strengthIdForImgUpdate.value = strength.id

  const fileInput = document.getElementById(`strength_img_${strength.id}`)
  if (fileInput) {
    fileInput.click()
  }
}

const onStrengthFileChange = $event => {
  const { files } = $event.target
  if (!files || files.length === 0) return
  const file = files[0]

  if (file.type.slice(0, 6) !== 'image/') {
    showSnackbar('Only image files are allowed', 'error')

    return false
  }
  if (file.size > 3 * 1024 * 1024) {
    showSnackbar('File size should be less than 3MB', 'error')

    return false
  }

  strengthImage.value = {
    file: file,
    url: useObjectUrl(file).value,
  }

  if (!isEmpty(strengthIdForImgUpdate.value)) {
    (async () => {
      await updateStrengthImage()
    })()
  }
}

const updateStrengthImage = async () => {
  try {
    isStrengthImgUploading.value = true

    const formData = new FormData()

    formData.append('strength_id', strengthIdForImgUpdate.value)
    formData.append('strength_image', strengthImage.value.file, strengthImage.value.file.name)

    const { data } = await ApiService.post('/admin/update-wl-product-strength-image', formData)

    if (data.status === 200) {
      showSnackbar(data.message)
      strengthImage.value = { file: null, url: data.strengthImage }
      emit('updateList', true)
    } else {
      showSnackbar(processErrors(data)[0], 'error')
    }
  } catch (error) {
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    isStrengthImgUploading.value = false
  }
}
</script>

<template>
  <VExpansionPanels>
    <VExpansionPanel
      v-for="strength in strengths"
      :key="strength.id"
      class="mb-5"
    >
      <VExpansionPanelTitle class="pa-5">
        <h5 class="text-h5">
          Strength: {{ strength.strength }} {{ strength.strength_unit }}
        </h5>
      </VExpansionPanelTitle>
      <VExpansionPanelText>
        <StrengthForm
          v-if="selectedStrengthId === strength.id"
          form-type="edit"
          :strength-id="strength.id"
          :product-data="productData"
          :strengths="strengths"
          :equivalent-product-strength-list="equivalentProductStrengthList"
          @update-list="emit('updateList', true)"
          @close-form="selectedStrengthId = ''"
        />
        <VCard
          v-else
          variant="flat"
        >
          <VCardText>
            <div class="d-flex flex-column flex-md-row">
              <div class="me-0 me-md-5">
                <VAvatar
                  variant="tonal"
                  color="primary"
                  size="150"
                  class="rounded-lg"
                >
                  <Image
                    v-if="strength.strength_image"
                    :src="strength.strength_image"
                    height="150"
                    width="150"
                    image-class="object-fit-cover"
                    preview
                  />
                  <div v-else>
                    <VIcon
                      icon="tabler-photo"
                      size="50"
                    />
                  </div>
                </VAvatar>
                <div class="mt-3">
                  <VBtn
                    size="small"
                    variant="tonal"
                    block
                    :loading="isStrengthImgUploading"
                    @click="handleSelectImg(strength)"
                  >
                    <VIcon
                      icon="tabler-cloud-upload"
                      start
                    />
                    Update
                  </VBtn>
                  <input
                    :id="`strength_img_${strength.id}`"
                    type="file"
                    name="file"
                    accept="image/*"
                    hidden
                    @input="onStrengthFileChange($event)"
                  />
                </div>
              </div>
              <VTable class="w-100 table-fix">
                <tbody>
                  <tr>
                    <td class="text-base text-no-wrap">
                      Strength
                    </td>
                    <td class="text-base">
                      {{ strength.strength }} {{ strength.strength_unit }}
                    </td>
                  </tr>
                  <tr>
                    <td class="text-base text-no-wrap">
                      NDC / Product ID
                    </td>
                    <td class="text-base">
                      {{ strength.ndc }}
                    </td>
                  </tr>
                  <tr>
                    <td class="text-base text-no-wrap">
                      Med ID
                    </td>
                    <td class="text-base">
                      {{ !isEmpty(strength.med_id) ? strength.med_id : '-' }}
                    </td>
                  </tr>
                  <tr>
                    <td class="text-base text-no-wrap">
                      Drug Name
                    </td>
                    <td class="text-base">
                      {{ !isEmpty(strength.drug_name) ? strength.drug_name : '-' }}
                    </td>
                  </tr>
                  <tr v-if="strength.provider_qty || strength.pharmacy_qty">
                    <td class="text-base text-no-wrap">
                      Quantity
                    </td>
                    <td class="text-base">
                      <div class="d-flex gap-5">
                        <span v-if="strength.provider_qty">For Provider: {{ strength.provider_qty }}</span>
                        <span v-if="strength.pharmacy_qty">For Pharmacy: {{ strength.pharmacy_qty }}</span>
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td class="text-base text-no-wrap">
                      Month
                    </td>
                    <td class="text-base">
                      {{ !isEmpty(strength.drug_month) ? strength.drug_month : '-' }}
                    </td>
                  </tr>
                  <tr>
                    <td class="text-base text-no-wrap">
                      Dose Instruction
                    </td>
                    <td class="text-base">
                      {{ !isEmpty(strength.dose_instruction) ? strength.dose_instruction : '-' }}
                    </td>
                  </tr>
                  <tr>
                    <td class="text-base text-no-wrap">
                      Status
                    </td>
                    <td class="text-base">
                      <VSwitch
                        v-model="resolveItemStatus(strength.is_active).status"
                        :label="resolveItemStatus(strength.is_active).label"
                        inset
                        @change="onStrengthStatusChange(strength, resolveItemStatus(strength.is_active).status)"
                      />
                    </td>
                  </tr>
                </tbody>
              </VTable>
            </div>

            <VCardActions class="pa-0 mt-2 d-flex justify-space-between">
              <div>
                <VBtn
                  variant="text"
                  @click="selectedStrengthId = strength.id"
                >
                  <VIcon
                    icon="tabler-edit"
                    start
                  /> Edit Strength
                </VBtn>
                <VBtn
                  variant="text"
                  color="error"
                  @click="handleDeleteStrength(strength.id)"
                >
                  <VIcon
                    icon="tabler-trash"
                    start
                  /> Delete Strength
                </VBtn>
              </div>

              <div
                v-if="strength.updated_date && strength.updated_time"
                class="text-xs text-end"
              >
                <div>
                  Last updated: {{ strength.updated_date }} {{ strength.updated_time }}
                </div>
                <div v-if="strength.action_by_user_name">
                  By: {{ strength.action_by_user_name }}
                </div>
              </div>
            </VCardActions>

            <div class="mt-5">
              <p class="text-subtitle-1 mb-1">
                Monthly Package
              </p>
              <div class="d-flex flex-wrap gap-3">
                <VCard
                  v-for="plan in strength.wl_product_subscription_plans"
                  :key="plan.id"
                  class="text-base text-wrap flex-grow-1 border-md border-dashed"
                  variant="flat"
                >
                  <VCardText class="pa-4">
                    <span
                      v-if="plan.product_cost"
                      class="d-block"
                    >
                      Base price: <span class="text-high-emphasis font-weight-bold">
                        {{ formatCurrency(plan.product_cost) }}
                      </span>
                    </span>
                    <span
                      v-if="plan.subscription_plan_price_after_discount"
                      class="d-block"
                    >
                      Selling price: <span class="text-high-emphasis font-weight-bold">
                        {{ formatCurrency(plan.subscription_plan_price_after_discount) }}
                      </span>
                    </span>
                    <span
                      v-if="plan.plan_interval_discount"
                      class="d-block"
                    >
                      Discount: <span class="text-high-emphasis font-weight-bold">
                        <span v-if="plan.plan_interval_discount_type === 'percentage'">
                          {{ plan.plan_interval_discount }}% <span v-if="getDiscountHint(plan)">({{ getDiscountHint(plan) }})</span>
                        </span>
                        <span v-else>{{ formatCurrency(plan.plan_interval_discount) }}</span>
                      </span>
                    </span>
                    <span
                      v-if="plan.product_cost"
                      class="d-block"
                    >
                      Net Revenue: <span class="text-high-emphasis font-weight-bold">
                        {{ formatCurrency(ceilToDecimalPlaces(parseFloatOrZero(plan.subscription_plan_price_after_discount) - parseFloatOrZero(plan.product_cost))) }}
                      </span>
                    </span>

                    <!--
                      <span class="d-flex align-items-center mt-4">
                      <span>Status:</span>
                      <span class="ms-2">
                      <VSwitch
                      v-model="resolveItemStatus(plan.is_active).status"
                      :label="resolveItemStatus(plan.is_active).label"
                      inset
                      @change="onPlanStatusChange(plan, resolveItemStatus(plan.is_active).status)"
                      />
                      </span>
                      </span>
                    -->

                    <div
                      v-if="plan.updated_date && plan.updated_time"
                      class="text-xs mt-6"
                    >
                      <div>
                        Last updated: {{ plan.updated_date }} {{ plan.updated_time }}
                      </div>
                      <div v-if="plan.action_by_user_name">
                        By: {{ plan.action_by_user_name }}
                      </div>
                    </div>
                  </VCardText>
                </VCard>
              </div>
            </div>
          </VCardText>
        </VCard>
      </VExpansionPanelText>
    </VExpansionPanel>
  </VExpansionPanels>
</template>
