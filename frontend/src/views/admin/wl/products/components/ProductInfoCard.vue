<script setup>
import { isEmpty } from '@/@core/utils'
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { processErrors } from '@/utils/errorHandler'
import { useObjectUrl } from '@vueuse/core'
import Image from 'primevue/image'
import { useRoute } from 'vue-router'

const props = defineProps({
  productData: {
    type: Object,
    required: true,
  },
  loading: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['edit', 'imgUpdated', 'updateList'])

const handleProductEdit = () => {
  emit('edit', true)
}

const route = useRoute()
const productId = ref(route.params.product_id)
const { showSnackbar } = useGlobalData()
const inputProductImageRef = ref(null)
const productImage = ref({ file: null, url: '' })
const isProductImgUploading = ref(false)
const stockChangeLoading = ref(false)
const isStockChangeDialogVisible = ref(false)

const onFileChange = $event => {
  const { files } = $event.target
  if (!files || files.length === 0) return
  const file = files[0]

  if (file.type.slice(0, 6) !== 'image/') {
    showSnackbar('Only image files are allowed', 'error')

    return false
  }
  if (file.size > 3 * 1024 * 1024) {
    showSnackbar('File size should be less than 3MB', 'error')

    return false
  }

  productImage.value = { file, url: useObjectUrl(file).value ?? '' }

  // formProductData.value.product_image = file

  if (!isEmpty(productId.value)) {
    (async () => {
      const formData = new FormData()

      formData.append('id', productId.value)
      formData.append('product_image', productImage.value.file, productImage.value.file.name)

      await updateProductImage(formData)
    })()
  }

  return true
}

const updateProductImage = async formData => {
  try {
    isProductImgUploading.value = true

    const { data } = await ApiService.post('/admin/update-wl-product-image', formData)

    if (data.status === 200) {
      showSnackbar(data.message)
      emit('imgUpdated', data.productImage)
    } else {
      showSnackbar(processErrors(data)[0], 'error')
    }
  } catch (error) {
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    isProductImgUploading.value = false
  }
}

function resolveStockStatus(status) {
  return { status: Boolean(status) }
}

const onStockStatusChange = () => {
  isStockChangeDialogVisible.value = true
}

async function toggleStockAvailability() {
  try {
    stockChangeLoading.value = true

    const { data } = await ApiService.get(`/admin/update-wl-product-stock-availability-status/${productId.value}`)

    if (data.status === 200) {
      showSnackbar(data.message)
      emit('updateList')
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    stockChangeLoading.value = false
    isStockChangeDialogVisible.value = false
  }
}
</script>

<template>
  <VCard
    v-if="loading"
    class="mb-5"
    title="Product Info"
  >
    <VCardText>
      <div class="d-flex flex-column flex-md-row">
        <div class="me-0 me-md-5">
          <Skeleton
            class="me-3"
            height="150px"
            width="150px"
          />
        </div>
        <VTable class="w-100 table-fix">
          <tbody>
            <tr>
              <td class="text-base">
                <Skeleton height="20px" />
              </td>
            </tr>
            <tr>
              <td class="text-base">
                <Skeleton height="20px" />
              </td>
            </tr>
            <tr>
              <td class="text-base">
                <Skeleton height="20px" />
              </td>
            </tr>
            <tr>
              <td class="text-base">
                <Skeleton height="70px" />
              </td>
            </tr>
          </tbody>
        </VTable>
      </div>
    </VCardText>
  </VCard>
  <VCard
    v-else
    class="mb-5"
    title="Product Info"
  >
    <VCardText>
      <div class="d-flex flex-column flex-md-row">
        <div class="me-0 me-md-5">
          <VAvatar
            variant="tonal"
            color="primary"
            size="150"
            class="rounded-lg"
          >
            <Image
              v-if="productData.product_image"
              :src="productData.product_image"
              height="150"
              width="150"
              image-class="object-fit-cover"
              preview
            />
            <div v-else>
              <VIcon
                icon="tabler-photo"
                size="50"
              />
            </div>
          </VAvatar>
          <div class="mt-3">
            <VBtn
              size="small"
              variant="tonal"
              block
              :loading="isProductImgUploading"
              @click="inputProductImageRef.click()"
            >
              <VIcon
                icon="tabler-cloud-upload"
                start
              />
              Update
            </VBtn>
            <input
              ref="inputProductImageRef"
              type="file"
              name="file"
              accept="image/*"
              hidden
              @input="onFileChange($event)"
            />
          </div>
        </div>
        <VTable class="w-100 table-fix">
          <tbody>
            <tr>
              <td class="text-base text-no-wrap">
                Product Name
              </td>
              <td class="text-base">
                {{ !isEmpty(productData.product_name) ? productData.product_name : "-" }}
              </td>
            </tr>
            <tr>
              <td class="text-base text-no-wrap">
                Active Ingredient
              </td>
              <td class="text-base">
                {{ !isEmpty(productData.active_ingredient) ? productData.active_ingredient : "-" }}
              </td>
            </tr>
            <tr>
              <td class="text-base text-no-wrap">
                Product Form
              </td>
              <td class="text-base">
                {{ !isEmpty(productData.product_form) ? productData.product_form : "-" }}
              </td>
            </tr>
            <tr v-if="!isEmpty(productData.product_type)">
              <td class="text-base text-no-wrap">
                Product Type
              </td>
              <td class="text-base">
                {{ !isEmpty(productData.product_type) ? productData.product_type : "-" }}
              </td>
            </tr>
            <tr>
              <td class="text-base text-no-wrap">
                Product Pharmacy
              </td>
              <td class="text-base">
                {{ !isEmpty(productData.pharmacy_name) ? productData.pharmacy_name : "-" }}
              </td>
            </tr>
            <tr>
              <td class="text-base text-no-wrap">
                Product Description
              </td>
              <td class="text-base">
                <span
                  v-if="!isEmpty(productData.product_description)"
                  v-html="productData.product_description"
                />
                <span v-else> - </span>
              </td>
            </tr>
            <tr>
              <td class="text-base text-no-wrap">
                Stock availability
              </td>
              <td class="text-base">
                <div class="d-flex gap-2 align-center">
                  <VSwitch
                    v-model="resolveStockStatus(productData.is_stock_available).status"
                    inset
                    @change="onStockStatusChange"
                  />
                  <span>{{ productData.is_stock_available === 1 ? 'In stock' : 'Out of stock' }}</span>
                </div>
              </td>
            </tr>
          </tbody>
        </VTable>
      </div>
    </VCardText>
    <VCardActions class="d-flex justify-space-between">
      <VBtn
        variant="text"
        @click="handleProductEdit"
      >
        <VIcon icon="tabler-edit" /> Edit
      </VBtn>
      <div
        v-if="productData.updated_date && productData.updated_time"
        class="text-xs text-end"
      >
        <div>
          Last updated: {{ productData.updated_date }} {{ productData.updated_time }}
        </div>
        <div v-if="productData.action_by_user_name">
          By: {{ productData.action_by_user_name }}
        </div>
      </div>
    </VCardActions>
  </VCard>

  <!-- 👉 Confirm Stock availability change -->
  <ConfirmDialog
    v-model:isDialogVisible="isStockChangeDialogVisible"
    confirmation-question="Are you sure you want to update stock availability?"
    :loading="stockChangeLoading"
    @confirm="(isConfirmed) => {
      if (isConfirmed) {
        toggleStockAvailability()
      }
    }"
  />
</template>
