<script setup>
import { isEmpty, isEmptyObject, isNullOrUndefined, parseFloatOrZero } from '@/@core/utils'
import { requiredValidator } from '@/@core/utils/validators'
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { processErrors } from '@/utils/errorHandler'
import { ceilToDecimalPlaces, formatCurrency, isNumber, removeKeyFromObject } from '@/utils/helpers'
import { useObjectUrl } from '@vueuse/core'
import { v4 as uuidv4 } from 'uuid'
import { computed, onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'

const props = defineProps({
  formType: {
    type: String,
    default: 'edit',
  },
  productData: {
    type: Object,
    required: true,
  },
  strengths: {
    type: Array,
    required: true,
  },
  strengthId: {
    type: String,
    default: null,
  },
  equivalentProductStrengthList: {
    type: Array,
    default: new Array(),
  },
})

const emit = defineEmits(['updateList', 'closeForm'])

const route = useRoute()
const { showSnackbar } = useGlobalData()
const formStrengthRef = ref()
const productId = ref(route.params.product_id)
const isStrengthLoading = ref(false)
const strengthObj = ref({})
const isStrengthImgUploading = ref(false)
const inputStrengthImageRef = ref()
const strengthImage = ref({ file: null, url: '' })
const inputErrors = ref({})
const serverErrors = ref([])
const strengths = ref([])
const isEmptyStrengthImg = ref(false)
const renderForm = ref(false)

onMounted(() => {
  renderForm.value = false

  strengths.value = JSON.parse(JSON.stringify(props.strengths))
  strengths.value = strengths.value.map(strength => {
    strength.strength_id = uuidv4()

    return strength
  })

  if (props.formType === 'edit' && !isEmpty(props.strengthId)) {
    handleEditStrength()
  } else {
    addNewStrength()
  }

  renderForm.value = true
})

const handleEditStrength = () => {
  const strength = strengths.value?.find(s => s.id === props.strengthId)

  strengthObj.value = {
    ...strength,
    subscription_plans: strength.wl_product_subscription_plans?.map(plan => ({
      ...plan,
      plan_id: uuidv4(),
    })),
  }

  delete strengthObj.value.wl_product_subscription_plans

  strengthImage.value = {
    file: null,
    url: strength.strength_image,
  }
}

const handleSelectImg = () => {
  inputStrengthImageRef.value?.click()
}

const onStrengthFileChange = $event => {
  const { files } = $event.target
  if (!files || files.length === 0) return
  const file = files[0]

  if (file.type.slice(0, 6) !== 'image/') {
    showSnackbar('Only image files are allowed', 'error')

    return false
  }
  if (file.size > 3 * 1024 * 1024) {
    showSnackbar('File size should be less than 3MB', 'error')

    return false
  }

  removeKeyFromInputErrors('strength_image')
  isEmptyStrengthImg.value = false

  strengthObj.value.strength_image = file
  strengthImage.value = {
    file: file,
    url: useObjectUrl(file).value,
  }

  if (!isEmpty(props.strengthId)) {
    (async () => {
      await updateStrengthImage()
    })()
  }
}

const updateStrengthImage = async () => {
  try {
    isStrengthImgUploading.value = true
    serverErrors.value = []

    const formData = new FormData()

    formData.append('strength_id', props.strengthId)
    formData.append('strength_image', strengthImage.value.file, strengthImage.value.file.name)

    const { data } = await ApiService.post('/admin/update-hl-product-strength-image', formData)

    if (data.status === 200) {
      strengthObj.value.strength_image = data.strengthImage
      showSnackbar(data.message)
      strengthImage.value = { file: null, url: data.strengthImage }
      emit('updateList', true)
    } else {
      serverErrors.value = processErrors(data)
      scrollToFormTop()
    }
  } catch (error) {
    serverErrors.value = processErrors(error)
    scrollToFormTop()
  } finally {
    isStrengthImgUploading.value = false
  }
}

function removeKeyFromInputErrors(key) {
  inputErrors.value = removeKeyFromObject(inputErrors.value, key)
}

const equivalentStrengths = computed(() => {
  return props.equivalentProductStrengthList.map(item => {
    return {
      title: `${item.wl_product?.product_name} - ${item.strength}${item.strength_unit}`,
      value: item.id,
    }
  })
})

const strengthUnits = [
  { title: 'mg', value: 'mg' },
  { title: 'mcg', value: 'mcg' },
  { title: 'g', value: 'g' },
  { title: 'mL', value: 'mL' },
  { title: 'L', value: 'L' },
  { title: 'IU', value: 'IU' },
]

const drugMonthItems = [
  { value: 1, title: 'Month 1' },
  { value: 2, title: 'Month 2' },
  { value: 3, title: 'Month 3' },
  { value: 4, title: 'Month 4' },
  { value: 5, title: 'Month 5' },
  { value: 6, title: 'Month 6' },
]

const addNewStrength = () => {
  const strengthId = uuidv4()

  const newStrength = {
    strength_id: strengthId,
    strength: '',
    strength_unit: 'mg',
    strength_image: '',
    strength_description: '',
    ndc: '',
    med_id: '',
    dose_instruction: '',
    drug_name: '',
    drug_month: undefined,
    provider_qty: '',
    pharmacy_qty: '',
    equivalent_products: [],
  }

  const plan1 = initPlan(1)

  strengthObj.value = {
    ...newStrength,
    subscription_plans: [plan1],
  }
}

const initPlan = () => {
  const planId = uuidv4()

  return {
    plan_id: planId,
    plan_interval_price: undefined,
    product_cost: undefined,
    plan_interval_discount_type: 'fixed',
    plan_interval_discount: undefined,
  }
}

const duplicateStrengthValidator = () => {
  const editedStrength = strengthObj.value

  let hasDuplicate = false

  strengths.value?.forEach(s => {
    if (
      s.strength === editedStrength.strength &&
      s.strength_unit === editedStrength.strength_unit &&
      s.strength_id !== editedStrength.strength_id
    ) {
      hasDuplicate = true
    }
  })

  return !hasDuplicate || 'Strength already exists'
}

const maxDiscountValidator = plan => {
  if (isEmpty(plan.plan_interval_discount)) return true

  if (plan.plan_interval_discount_type === 'fixed') {
    return (parseFloatOrZero(plan.plan_interval_discount) < parseFloatOrZero(plan.plan_interval_price)) || 'Discount must be less than selling price'
  } else if (plan.plan_interval_discount_type === 'percentage') {
    return (parseFloatOrZero(plan.plan_interval_discount) < 100) || 'Discount must be less than 100%'
  } else {
    return true
  }
}

const maxProductCostValidator = plan => {
  if (isEmpty(plan.product_cost) || isEmpty(plan.plan_interval_price)) return true

  return (parseFloatOrZero(plan.product_cost) <= parseFloatOrZero(plan.plan_interval_price)) || 'Base price should not exceed selling price'
}

function calculateNetRevenue(plan) {
  const intervalPrice = ceilToDecimalPlaces(parseFloatOrZero(plan.plan_interval_price))
  let intervalPriceAfterDiscount = 0

  if (plan.plan_interval_discount_type === 'fixed') {
    intervalPriceAfterDiscount = ceilToDecimalPlaces(intervalPrice - parseFloatOrZero(plan.plan_interval_discount))
  } else if (plan.plan_interval_discount_type === 'percentage') {
    intervalPriceAfterDiscount = ceilToDecimalPlaces(intervalPrice - (intervalPrice * parseFloatOrZero(plan.plan_interval_discount) / 100))
  }

  const netRevenue = ceilToDecimalPlaces(intervalPriceAfterDiscount - parseFloatOrZero(plan.product_cost))

  return `Net Revenue: <span class="text-base font-weight-bold text-high-emphasis ${netRevenue <= 0 ? 'text-error': ''}">${formatCurrency(netRevenue)}</span>`
}

function getDiscountHint(plan) {
  if (plan.plan_interval_discount_type === 'percentage') {
    const totalAmount = parseFloatOrZero(plan.plan_interval_price)
    const discountPercentage = parseFloatOrZero(plan.plan_interval_discount)
    const discountAmount = (totalAmount * discountPercentage) / 100

    return `${formatCurrency(discountAmount.toFixed(2))} off`
  }

  return undefined
}

const handleStrengthSubmit = () => {
  formStrengthRef.value?.validate().then(async result => {
    isEmptyStrengthImg.value = isEmpty(strengthObj.value.strength_image)

    if (result.valid && !isEmptyStrengthImg.value) {
      await addOrUpdateStrength()
    } else {
      scrollToFormTop()
    }
  })
}

const addOrUpdateStrength = async () => {
  try {
    isStrengthLoading.value = true
    serverErrors.value = []
    inputErrors.value = {}

    const formData = createStrengthFormData()

    let endpointUrl = '/admin/add-wl-product-strength-data'
    if (!isEmpty(props.strengthId)) {
      endpointUrl = '/admin/edit-wl-product-strength-data'
    }

    const { data } = await ApiService.post(endpointUrl, formData)

    if (data.status === 200) {
      emit('updateList', true)
      resetStrengthForm()
      showSnackbar(data.message)
    } else {
      if (data.errors) {
        inputErrors.value = data.errors
      } else {
        showSnackbar(data.message, 'error')
      }
      scrollToFormTop()
    }
  } catch (error) {
    if (error.response.errors) {
      inputErrors.value = error.response.errors
    }
    serverErrors.value = processErrors(error)
    scrollToFormTop()
  } finally {
    isStrengthLoading.value = false
  }
}

const scrollToFormTop = () => {
  formStrengthRef.value?.scrollIntoView({ behavior: 'smooth', block: 'start' })
}

const resetStrengthForm = () => {
  formStrengthRef.value?.reset()
  strengthObj.value = {}
  emit('closeForm', true)
}

function createStrengthFormData() {
  const formData = new FormData()

  formData.append('wl_product_id', productId.value)

  if (!isEmpty(props.strengthId)) {
    formData.append('strength_id', props.strengthId)
  }

  const strength = strengthObj.value

  Object.keys(strength).forEach(key => {
    if (key === 'subscription_plans') {
      strength[key].forEach((plan, planIndex) => {
        Object.keys(plan).forEach(planKey => {
          if (planKey !== 'plan_id') {
            formData.append(
              `wl_product_subscription_plans[${planIndex}][${planKey}]`,
              !isEmpty(plan[planKey]) ? plan[planKey] : '',
            )
          }
        })
      })
    } else if (key === 'strength_image' && strength[key] instanceof File) {
      formData.append(key, strength[key], strength[key].name)
    } else if (key === 'equivalent_products') {
      strength[key].forEach((freq, freqIndex) => {
        formData.append(
          `${key}[${freqIndex}]`, // key
          !isEmpty(freq) ? freq : '', // value
        )
      })
    } else {
      formData.append(
        key,
        !isEmpty(strength[key]) ? strength[key] : '',
      )
    }
  })

  return formData
}
</script>

<template>
  <VCard
    :title="formType === 'edit' ? 'Update Strength' : 'Add Strength'"
    class="mb-5"
    variant="flat"
  >
    <VForm
      v-if="renderForm"
      id="strength-form"
      ref="formStrengthRef"
      @submit.prevent="handleStrengthSubmit"
    >
      <VAlert
        v-if="isEmptyObject(inputErrors) && serverErrors.length > 0"
        type="error"
        variant="tonal"
        title="Validation failed!"
        class="mb-6"
      >
        <ul class="mb-0">
          <li
            v-for="error in serverErrors"
            :key="error"
            class="mb-0"
          >
            {{ error }}
          </li>
        </ul>
      </VAlert>

      <VCardText>
        <VRow>
          <VCol
            cols="12"
            md="4"
          >
            <VTextField
              v-model="strengthObj.strength"
              label="Strength"
              :rules="[requiredValidator, duplicateStrengthValidator]"
              @keypress="isNumber($event)"
            />
            <p
              v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.strength)"
              class="text-sm text-error mb-0 ms-3"
            >
              {{ inputErrors.strength[0] }}
            </p>
          </VCol>

          <VCol
            cols="12"
            md="4"
          >
            <VSelect
              v-model="strengthObj.strength_unit"
              label="Strength Unit"
              :items="strengthUnits"
              :rules="[requiredValidator]"
            />
            <p
              v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.strength_unit)"
              class="text-sm text-error mb-0 ms-3"
            >
              {{ inputErrors.strength_unit[0] }}
            </p>
          </VCol>

          <VCol cols="12">
            <label class="d-block mb-2 ms-1">Strength Description</label>
            <TiptapEditor
              v-model="strengthObj.strength_description"
              class="border rounded basic-editor"
            />
            <p
              v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.strength_description)"
              class="text-sm text-error mb-0 ms-3"
            >
              {{ inputErrors.strength_description[0] }}
            </p>
          </VCol>

          <VCol
            cols="12"
            md="4"
          >
            <VTextField
              v-model="strengthObj.ndc"
              label="NDC / Product ID"
              :rules="[requiredValidator]"
            />
            <p
              v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.ndc)"
              class="text-sm text-error mb-0 ms-3"
            >
              {{ inputErrors.ndc[0] }}
            </p>
          </VCol>

          <VCol
            cols="12"
            md="4"
          >
            <VTextField
              v-model="strengthObj.med_id"
              label="Med ID"
              :rules="[requiredValidator]"
            />
            <p
              v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.med_id)"
              class="text-sm text-error mb-0 ms-3"
            >
              {{ inputErrors.med_id[0] }}
            </p>
          </VCol>

          <VCol
            cols="12"
            md="4"
          >
            <VTextField
              v-model="strengthObj.drug_name"
              label="Drug Name"
              :rules="[requiredValidator]"
            />
            <p
              v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.drug_name)"
              class="text-sm text-error mb-0 ms-3"
            >
              {{ inputErrors.drug_name[0] }}
            </p>
          </VCol>

          <VCol
            cols="12"
            md="4"
          >
            <VTextField
              v-model="strengthObj.provider_qty"
              label="Quantity for Provider"
              :rules="[requiredValidator]"
              hint="To be used by provider to write quantity on the Rx (e.g. 3ml, 5ml etc)"
              persistent-hint
            />
            <p
              v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.provider_qty)"
              class="text-sm text-error mb-0 ms-3"
            >
              {{ inputErrors.provider_qty[0] }}
            </p>
          </VCol>

          <VCol
            cols="12"
            md="4"
          >
            <VTextField
              v-model="strengthObj.pharmacy_qty"
              label="Quantity for Pharmacy"
              :rules="[requiredValidator]"
              hint="To be used by pharmacies to dispense correct quantity (e.g. 1, 2 etc. concluding 1 vial or 2 vials)"
              persistent-hint
            />
            <p
              v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.pharmacy_qty)"
              class="text-sm text-error mb-0 ms-3"
            >
              {{ inputErrors.pharmacy_qty[0] }}
            </p>
          </VCol>

          <VCol
            cols="12"
            md="4"
          >
            <VSelect
              v-model="strengthObj.drug_month"
              label="Dose Month"
              :items="drugMonthItems"
              :rules="[requiredValidator]"
            />
            <p
              v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.drug_month)"
              class="text-sm text-error mb-0 ms-3"
            >
              {{ inputErrors.drug_month[0] }}
            </p>
          </VCol>

          <VCol
            cols="12"
            md="4"
          >
            <VSelect
              v-model="strengthObj.equivalent_products"
              label="Equivalent Products"
              :items="equivalentStrengths"
              chips
              multiple
              clearable
            />
            <p
              v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.equivalent_products)"
              class="text-sm text-error mb-0 ms-3"
            >
              {{ inputErrors.equivalent_products[0] }}
            </p>
          </VCol>

          <VCol
            cols="12"
            md="8"
          >
            <VTextarea
              v-model="strengthObj.dose_instruction"
              label="Dose Instruction"
              :rules="[requiredValidator]"
              rows="2"
            />
            <p
              v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.dose_instruction)"
              class="text-sm text-error mb-0 ms-3"
            >
              {{ inputErrors.dose_instruction[0] }}
            </p>
          </VCol>

          <VCol
            v-if="formType !== 'edit'"
            cols="12"
          >
            <VCardText class="d-flex pa-0">
              <VAvatar
                rounded
                size="80"
                class="me-6"
                color="primary"
                variant="tonal"
              >
                <VImg
                  v-if="strengthImage.url"
                  :src="strengthImage.url"
                />
                <VIcon
                  v-else
                  size="80"
                  icon="tabler-box"
                />
              </VAvatar>
              <div class="d-flex flex-column justify-center gap-4">
                <div class="d-flex flex-wrap gap-2">
                  <VBtn
                    color="primary"
                    variant="tonal"
                    :loading="isStrengthImgUploading"
                    @click="handleSelectImg"
                  >
                    <VIcon
                      icon="tabler-cloud-upload"
                      class="d-sm-none"
                    />
                    <span class="d-none d-sm-block">Select Image</span>
                  </VBtn>

                  <input
                    ref="inputStrengthImageRef"
                    type="file"
                    name="file"
                    accept="image/*"
                    hidden
                    @input="onStrengthFileChange($event)"
                  />
                </div>
                <p class="text-body-2 mb-0">
                  Allowed only image files with size less than 2MB.
                </p>
              </div>
            </VCardText>
            <p
              v-if="isEmptyStrengthImg"
              class="text-sm text-error mb-0 ms-3 mt-2"
            >
              Strength image is required
            </p>
            <p
              v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.strength_image)"
              class="text-sm text-error mb-0 ms-3 mt-2"
            >
              {{ inputErrors.strength_image[0] }}
            </p>
          </VCol>
        </VRow>

        <VRow>
          <VCol cols="12">
            <div>
              <h4 class="mb-3 ms-1">
                Monthly Package
              </h4>

              <VRow
                v-for="(plan, planIndex) in strengthObj.subscription_plans"
                :key="plan.plan_id"
                class="border-sm border-dashed rounded-lg mx-1 my-3"
              >
                <VCol
                  cols="12"
                  md="4"
                >
                  <VTextField
                    v-model="plan.product_cost"
                    label="Base Price"
                    :rules="[requiredValidator, maxProductCostValidator(plan)]"
                    prefix="$"
                    @keypress="isNumber($event)"
                  />
                  <p
                    v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors[`wl_product_subscription_plans.${planIndex}.product_cost`])"
                    class="text-sm text-error mb-0 ms-3"
                  >
                    {{ inputErrors[`wl_product_subscription_plans.${planIndex}.product_cost`][0] }}
                  </p>
                </VCol>

                <VCol
                  cols="12"
                  md="4"
                >
                  <VTextField
                    v-model="plan.plan_interval_price"
                    label="Selling Price"
                    :rules="[requiredValidator]"
                    prefix="$"
                    @keypress="isNumber($event)"
                  />
                  <p
                    v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors[`wl_product_subscription_plans.${planIndex}.plan_interval_price`])"
                    class="text-sm text-error mb-0 ms-3"
                  >
                    {{ inputErrors[`wl_product_subscription_plans.${planIndex}.plan_interval_price`][0] }}
                  </p>
                </VCol>

                <VCol cols="12">
                  <VRadioGroup
                    v-model="plan.plan_interval_discount_type"
                    label="Discount Type"
                    :rules="[requiredValidator]"
                    required
                    class="d-flex"
                  >
                    <VRadio
                      label="Fixed Amount"
                      value="fixed"
                      class="ms-2"
                    />
                    <VRadio
                      label="Percentage (of the total amount)"
                      value="percentage"
                      class="ms-2"
                    />
                  </VRadioGroup>
                  <p
                    v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors[`wl_product_subscription_plans.${planIndex}.plan_interval_discount_type`])"
                    class="text-sm text-error mb-0 ms-3"
                  >
                    {{ inputErrors[`wl_product_subscription_plans.${planIndex}.plan_interval_discount_type`][0] }}
                  </p>
                </VCol>

                <VCol
                  cols="12"
                  md="4"
                >
                  <VTextField
                    v-model="plan.plan_interval_discount"
                    label="Discount"
                    :rules="[maxDiscountValidator(plan)]"
                    :maxlength="plan.plan_interval_discount_type === 'fixed' ? 10 : 2"
                    :prefix="plan.plan_interval_discount_type === 'fixed' ? '$' : ''"
                    :suffix="plan.plan_interval_discount_type === 'percentage' ? '%' : ''"
                    :hint="getDiscountHint(plan)"
                    persistent-hint
                    @keypress="isNumber($event)"
                  />
                  <p
                    v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors[`wl_product_subscription_plans.${planIndex}.plan_interval_discount`])"
                    class="text-sm text-error mb-0 ms-3"
                  >
                    {{ inputErrors[`wl_product_subscription_plans.${planIndex}.plan_interval_discount`][0] }}
                  </p>
                </VCol>

                <VCol
                  cols="12"
                  class="text-body-2"
                >
                  <VAlert
                    color="secondary"
                    variant="tonal"
                    class="alert-fix"
                  >
                    <span
                      class="d-block"
                      v-html="calculateNetRevenue(plan)"
                    ></span>
                  </VAlert>
                </VCol>

                <!--
                  <VCol v-if="planIndex !== 0">
                  <VBtn
                  color="error"
                  variant="tonal"
                  @click="deletePlan(plan)"
                  >
                  <VIcon
                  icon="tabler-trash"
                  start
                  /> Delete Plan
                  </VBtn>
                  </VCol>
                -->
              </VRow>

              <!--
                <VBtn
                v-if="strengthObj.subscription_plans?.length < 12"
                color="primary"
                class="mt-5"
                @click="addNewPlan(null)"
                >
                <VIcon
                icon="tabler-plus"
                start
                /> Add Plan
                </VBtn>
              -->
            </div>
          </VCol>
        </VRow>
      </VCardText>
      <VCardActions class="pa-6 pt-2 justify-end">
        <VBtn
          type="button"
          color="secondary"
          variant="tonal"
          @click="resetStrengthForm"
        >
          Discard
        </VBtn>
        <VBtn
          type="submit"
          color="primary"
          variant="elevated"
          :loading="isStrengthLoading"
        >
          Save Strength
        </VBtn>
      </VCardActions>
    </VForm>
  </VCard>
</template>
