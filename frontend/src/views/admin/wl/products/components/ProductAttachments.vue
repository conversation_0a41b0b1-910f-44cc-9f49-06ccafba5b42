<script setup>
import { isEmpty } from '@/@core/utils'
import { requiredValidator } from '@/@core/utils/validators'
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { processErrors } from '@/utils/errorHandler'
import { IconListDetails } from '@tabler/icons-vue'
import { computed, onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import { useObjectUrl } from '@vueuse/core'

const { showSnackbar } = useGlobalData()
const route = useRoute()

const VIEW_SECTION = {
  LOADING_VIEW: 'LOADING_VIEW',
  EMPTY_VIEW: 'EMPTY_VIEW',
  LIST_VIEW: 'LIST_VIEW',
  FORM_VIEW: 'FORM_VIEW',
}

const currentView = ref(VIEW_SECTION.LOADING_VIEW)
const productId = computed(() => route.params.product_id)
const formRef = ref(null)
const attachments = ref([])
const productName = ref('')
const inputAttachmentRef = ref(null)
const attachmentTitle = ref('')
const attachmentFile = ref({ file: null, url: '' })
const fileError = ref('')
const isLoading = ref(false)
const serverErrors = ref([])
const editId = ref(null)
const deleteId = ref(null)
const isDeleteDialogVisible = ref(false)
const deleteLoading = ref(false)

const pageHeading = computed(() => {
  if (currentView.value === VIEW_SECTION.LIST_VIEW || currentView.value === VIEW_SECTION.EMPTY_VIEW) {
    return 'Product Attachments'
  } else if (currentView.value === VIEW_SECTION.FORM_VIEW) {
    return editId.value ? 'Edit Attachment' : 'Add Attachment'
  }

  return null
})

onMounted(async () => {
  await getProductAttachments()
})

async function getProductAttachments() {
  try {
    const { data } = await ApiService.get(`/admin/list-wl-product-attachment/${productId.value}`)

    if (data.status === 200) {
      productName.value = data.productName || ''

      if (!isEmpty(data.productData)) {
        attachments.value = data.productData
        currentView.value = VIEW_SECTION.LIST_VIEW
      } else {
        currentView.value = VIEW_SECTION.EMPTY_VIEW
      }
    } else {
      if (data.message) {
        showSnackbar(data.message, 'error')
      } else {
        showSnackbar(processErrors(data)[0], 'error')
      }
    }
  } catch (error) {
    showSnackbar(processErrors(error)[0], 'error')
  }
}

const onFileChange = $event => {
  fileError.value = ''

  const { files } = $event.target
  if (!files || files.length === 0) return
  const file = files[0]

  if (file.type !== 'application/pdf') {
    showSnackbar('Only PDF files are allowed', 'error')

    return false
  }

  if (file.size > 3 * 1024 * 1024) {
    showSnackbar('File size should be less than 3MB', 'error')

    return false
  }

  attachmentFile.value = { file, url: useObjectUrl(file).value ?? '' }

  return true
}

function onClickFormCancel() {
  if (!isEmpty(attachments.value)) {
    currentView.value = VIEW_SECTION.LIST_VIEW
  } else {
    currentView.value = VIEW_SECTION.EMPTY_VIEW
  }

  editId.value = null
  serverErrors.value = []
}

function resetForm() {
  attachmentFile.value = { file: null, url: '' }
  attachmentTitle.value = ''
  serverErrors.value = []
}

function resetFileSelection() {
  attachmentFile.value = { file: null, url: '' }
  inputAttachmentRef.value?.reset()
}

async function validateFile() {
  return new Promise(resolve => {
    if (!isEmpty(attachmentFile.value.file) && !isEmpty(attachmentFile.value.url)) {
      resolve(true)
    } else {
      resolve(false)
    }
  })
}

async function handleSubmit() {
  const isFileValid = await validateFile()

  fileError.value = !isFileValid ? 'Attachment file is required' : null

  formRef.value?.validate().then(async result => {
    if (result.valid && isFileValid) {
      await addProductAttachment()
    }
  })
}

async function addProductAttachment() {
  try {
    isLoading.value = true
    serverErrors.value = null

    const formData = new FormData()

    formData.append('wl_product_id', productId.value)
    formData.append('title', attachmentTitle.value)
    formData.append('attachment', attachmentFile.value.file, attachmentFile.value.file.name)

    const { data } = await ApiService.post('/admin/add-wl-product-attachment', formData)

    if (data.status === 200) {
      showSnackbar(data.message)
      onClickFormCancel()
      getProductAttachments()
      resetForm()
    } else {
      serverErrors.value = processErrors(data)
    }
  } catch (error) {
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    isLoading.value = false
  }
}

function onClickDelete(item) {
  deleteId.value = item.id
  isDeleteDialogVisible.value = true
}

async function handleDeleteConfirmation(isConfirmed) {
  if (isConfirmed) {
    await deleteAttachment()
  }
}

async function deleteAttachment() {
  try {
    deleteLoading.value = true

    const { data } = await ApiService.delete(`/admin/delete-wl-product-attachment/${deleteId.value}`)

    if (data.status === 200) {
      showSnackbar(data.message)
      deleteId.value = null
      getProductAttachments()
    } else {
      if (data.message) {
        showSnackbar(data.message, 'error')
      } else {
        showSnackbar(processErrors(data)[0], 'error')
      }
    }
  } catch (error) {
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    deleteLoading.value = false
    isDeleteDialogVisible.value = false
  }
}
</script>

<template>
  <div>
    <VCard>
      <VCardTitle
        v-if="pageHeading"
        class="pa-6"
      >
        <h5 class="text-h5">
          {{ pageHeading }}
        </h5>
        <p class="text-subtitle-2 text-muted">
          {{ productName }}
        </p>
      </VCardTitle>

      <!-- 👉 Loading View -->
      <VCardText v-if="currentView === VIEW_SECTION.LOADING_VIEW">
        <div
          class="d-flex flex-column align-center justify-center"
          style="height: 200px;"
        >
          <VProgressCircular
            size="42"
            indeterminate
          />
        </div>
      </VCardText>

      <!-- 👉 No Attachments -->
      <VCardText v-if="currentView === VIEW_SECTION.EMPTY_VIEW">
        <div class="d-flex flex-column align-center gap-2">
          <IconListDetails
            size="80"
            stroke-width="1.5"
          />
          <div class="text-lg">
            No attachments found
          </div>
          <div class="mt-6">
            <VBtn @click="currentView = VIEW_SECTION.FORM_VIEW">
              Add New Attachment
            </VBtn>
          </div>
        </div>
      </VCardText>

      <!-- 👉 Attachments -->
      <VCardText v-if="currentView === VIEW_SECTION.LIST_VIEW">
        <VCard
          v-for="item in attachments"
          :key="item.id"
          class="pa-3 border-sm border-dashed mb-3 d-flex align-center gap-4"
        >
          <div class="d-flex flex-column flex-md-row gap-4">
            <div>
              <div class="text-xl mb-2">
                {{ item.title }}
              </div>
              <div class="text-sm text-muted">
                <div v-if="item.created_date_time">
                  Added on: {{ item.created_date_time }}
                </div>
                <div v-if="item.action_by_user_name && item.action_by_user_role">
                  Added by: {{ item.action_by_user_name }}({{ item.action_by_user_role }})
                </div>
              </div>
              <div class="mt-4 d-flex gap-4">
                <a
                  :href="item.attachment"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <VBtn
                    color="primary"
                    size="small"
                    variant="tonal"
                  >
                    View Attachment
                  </VBtn>
                </a>
                <VBtn
                  color="error"
                  size="small"
                  variant="tonal"
                  @click="onClickDelete(item)"
                >
                  Delete Attachment
                </VBtn>
              </div>
            </div>
          </div>
        </VCard>
        <div class="mt-6">
          <VBtn
            block
            @click="currentView = VIEW_SECTION.FORM_VIEW"
          >
            Add New Attachment
          </VBtn>
        </div>
      </VCardText>

      <!-- 👉 Form -->
      <VCardText v-if="currentView === VIEW_SECTION.FORM_VIEW">
        <VAlert
          v-if="!isEmpty(serverErrors)"
          type="error"
          variant="tonal"
          title="Error!"
          class="mb-6"
        >
          <ul class="mb-0">
            <li
              v-for="error in serverErrors"
              :key="error"
              class="mb-0"
            >
              {{ error }}
            </li>
          </ul>
        </VAlert>

        <VForm
          ref="formRef"
          @submit.prevent="handleSubmit"
        >
          <VRow>
            <VCol cols="12">
              <VTextField
                v-model="attachmentTitle"
                label="Attachment Title"
                :rules="[requiredValidator]"
              />
            </VCol>

            <VCol
              cols="12"
              md="6"
            >
              <p class="mb-2">
                Attachment File
              </p>
              <div
                class="dropzone"
                @click="inputAttachmentRef.click()"
              >
                <div v-if="attachmentFile.url && attachmentFile.file">
                  <VIcon
                    icon="tabler-file"
                    size="32"
                    class="mb-2"
                  />
                  <div class="text-sm text-muted">
                    {{ attachmentFile.file.name }}
                  </div>
                  <VBtn
                    size="small"
                    color="default"
                    variant="tonal"
                    class="mt-2"
                    @click.stop="resetFileSelection"
                  >
                    Clear Selection
                  </VBtn>
                </div>
                <div v-else>
                  <VIcon
                    icon="tabler-upload"
                    size="32"
                    class="mb-2"
                  />
                  <div class="text-sm text-muted">
                    Click to upload
                  </div>
                </div>
              </div>
              <p
                v-if="!isEmpty(fileError)"
                class="text-sm text-error mt-1"
              >
                {{ fileError }}
              </p>
              <VFileInput
                ref="inputAttachmentRef"
                accept="application/pdf"
                class="d-none"
                @input="onFileChange($event)"
              />
            </VCol>

            <VCol cols="12">
              <div class="d-flex justify-end gap-3">
                <VBtn
                  type="button"
                  variant="tonal"
                  color="secondary"
                  :disabled="isLoading"
                  @click="onClickFormCancel"
                >
                  Cancel
                </VBtn>
                <VBtn
                  type="submit"
                  :loading="isLoading"
                >
                  Submit
                </VBtn>
              </div>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>

    <!-- 👉 Delete confirm dialog -->
    <ConfirmDialog
      v-model:isDialogVisible="isDeleteDialogVisible"
      confirmation-question="Are you sure you want to delete this attachment?"
      :loading="deleteLoading"
      @confirm="handleDeleteConfirmation"
    />
  </div>
</template>

<style lang="scss" scoped>
.dropzone {
  padding: 2rem;
  border: 2px dashed #cecece8f;
  border-radius: 8px;
  text-align: center;
  transition: background-color 0.3s;
  cursor: pointer;
}

.dropzone:hover {
  // background-color: rgba(255, 255, 255, 0.05);
}

.hidden-input {
  display: none;
}

.file-label {
  cursor: pointer;
}
</style>
