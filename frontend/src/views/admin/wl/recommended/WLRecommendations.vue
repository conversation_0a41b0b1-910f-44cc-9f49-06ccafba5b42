<script setup>
import { isEmpty } from '@/@core/utils'
import { requiredValidator } from '@/@core/utils/validators'
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { processErrors } from '@/utils/errorHandler'
import { IconListDetails } from '@tabler/icons-vue'
import Image from 'primevue/image'
import { onMounted, ref } from 'vue'

const { showSnackbar } = useGlobalData()

const VIEW_SECTION = {
  LOADING_VIEW: 'LOADING_VIEW',
  EMPTY_VIEW: 'EMPTY_VIEW',
  RECOMMENDED_VIEW: 'RECOMMENDED_VIEW',
  FORM_VIEW: 'FORM_VIEW',
}

const currentView = ref(VIEW_SECTION.LOADING_VIEW)

const pageHeading = computed(() => {
  if (currentView.value === VIEW_SECTION.RECOMMENDED_VIEW) {
    return 'Recommended Products'
  } else if (currentView.value === VIEW_SECTION.FORM_VIEW) {
    return editId.value ? 'Edit Recommendation' : 'Add Recommendation'
  }

  return null
})

const formRef = ref(null)
const recommendedProducts = ref([])
const products = ref([])
const stateOptions = ref([])
const selectedProduct = ref(null)
const selectedStates = ref([])
const isLoading = ref(false)
const serverErrors = ref([])
const editId = ref(null)
const deleteId = ref(null)
const isDeleteDialogVisible = ref(false)
const deleteLoading = ref(false)

function onProductChange() {
  selectedStates.value = []
}

const productOptions = computed(() => {
  return products.value || []
})

onMounted(async () => {
  await Promise.all([getRecommendedProductList(), getProductList(), fetchStates()])
})

const fetchStates = async () => {
  try {
    const { data } = await ApiService.get('/state-list/WL')

    if (data.status === 200) {
      stateOptions.value = data.stateData
    } else {
      console.error(data)
    }
  } catch (error) {
    console.error(error)
  }
}

async function getRecommendedProductList() {
  try {
    const { data } = await ApiService.get('/admin/get-wl-product-recommended-lists')

    if (data.status === 200) {
      if (!isEmpty(data.wl_products_state_recommendations)) {
        recommendedProducts.value = data.wl_products_state_recommendations
        currentView.value = VIEW_SECTION.RECOMMENDED_VIEW
      } else {
        currentView.value = VIEW_SECTION.EMPTY_VIEW
      }
    } else {
      if (data.message) {
        showSnackbar(data.message, 'error')
      } else {
        showSnackbar(processErrors(data)[0], 'error')
      }
    }
  } catch (error) {
    showSnackbar(processErrors(error)[0], 'error')
  }
}

async function getProductList() {
  try {
    const { data } = await ApiService.get('/admin/get-wl-product-recommendation-lists')

    if (data.status === 200) {
      products.value = data.productList
    } else {
      if (data.message) {
        showSnackbar(data.message, 'error')
      } else {
        showSnackbar(processErrors(data)[0], 'error')
      }
    }
  } catch (error) {
    showSnackbar(processErrors(error)[0], 'error')
  }
}

function onClickFormCancel() {
  if (!isEmpty(recommendedProducts.value)) {
    currentView.value = VIEW_SECTION.RECOMMENDED_VIEW
  } else {
    currentView.value = VIEW_SECTION.EMPTY_VIEW
  }

  selectedProduct.value = null
  selectedStates.value = []
  editId.value = null
  serverErrors.value = []
}

async function handleSubmit() {
  formRef.value?.validate().then(async result => {
    if (result.valid) {
      await addUpdateProductRecommendation()
    }
  })
}

async function addUpdateProductRecommendation() {
  try {
    isLoading.value = true
    serverErrors.value = null

    const postData = {
      'wl_product_id': selectedProduct.value,
      'allowed_states': selectedStates.value,
    }

    if (editId.value) {
      postData['wl_product_recommendation_id'] = editId.value
    }

    const { data } = await ApiService.post('/admin/add-edit-wl-product-state-recommendations', postData)

    if (data.status === 200) {
      showSnackbar(data.message)
      onClickFormCancel()
      getRecommendedProductList()
    } else {
      serverErrors.value = processErrors(data)
    }
  } catch (error) {
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    isLoading.value = false
  }
}

function selectAll() {
  if (selectedStates.value.length === stateOptions.value.length) {
    selectedStates.value = []
  } else {
    selectedStates.value = stateOptions.value.map(state => state.code)
  }
}

function onClickEdit(product) {
  currentView.value = VIEW_SECTION.FORM_VIEW
  editId.value = product.id
  selectedProduct.value = product.wl_product_id
  selectedStates.value = product.allowed_states
}

function onClickDelete(product) {
  deleteId.value = product.id
  isDeleteDialogVisible.value = true
}

async function handleDeleteConfirmation(isConfirmed) {
  if (isConfirmed) {
    await deleteRecommendation()
  }
}

async function deleteRecommendation() {
  try {
    deleteLoading.value = true

    const { data } = await ApiService.delete(`/admin/delete-wl-recommended-product/${deleteId.value}`)

    if (data.status === 200) {
      showSnackbar(data.message)
      deleteId.value = null
      getRecommendedProductList()
    } else {
      if (data.message) {
        showSnackbar(data.message, 'error')
      } else {
        showSnackbar(processErrors(data)[0], 'error')
      }
    }
  } catch (error) {
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    deleteLoading.value = false
    isDeleteDialogVisible.value = false
  }
}
</script>

<template>
  <div>
    <VCard>
      <VCardTitle
        v-if="pageHeading"
        class="pa-6"
      >
        <div class="text-h5">
          {{ pageHeading }}
        </div>
      </VCardTitle>

      <!-- 👉 Loading View -->
      <VCardText v-if="currentView === VIEW_SECTION.LOADING_VIEW">
        <div
          class="d-flex flex-column align-center justify-center"
          style="height: 200px;"
        >
          <VProgressCircular
            size="42"
            indeterminate
          />
        </div>
      </VCardText>

      <!-- 👉 No Recommendations -->
      <VCardText v-if="currentView === VIEW_SECTION.EMPTY_VIEW">
        <div class="d-flex flex-column align-center gap-2">
          <IconListDetails
            size="80"
            stroke-width="1.5"
          />
          <div class="text-lg">
            No product recommendations added
          </div>
          <div class="mt-6">
            <VBtn @click="currentView = VIEW_SECTION.FORM_VIEW">
              Add New Recommendation
            </VBtn>
          </div>
        </div>
      </VCardText>

      <!-- 👉 Recommended List -->
      <VCardText v-if="currentView === VIEW_SECTION.RECOMMENDED_VIEW">
        <VCard
          v-for="product in recommendedProducts"
          :key="product.id"
          class="pa-3 border-sm border-dashed mb-3 d-flex align-center gap-4"
        >
          <div class="d-flex flex-column flex-md-row gap-4">
            <div>
              <VAvatar
                variant="tonal"
                color="primary"
                size="100"
                class="me-3 rounded-lg"
              >
                <Image
                  :src="product.product_image"
                  height="100"
                  width="100"
                  image-class="object-fit-cover"
                  preview
                />
              </VAvatar>
            </div>
            <div>
              <div class="text-xl mb-2">
                {{ product.product_name }}
              </div>
              <div class="d-flex gap-2">
                Allowed States: {{ product.allowed_states.join(', ') }}
              </div>
              <div class="mt-4 d-flex gap-4">
                <VBtn
                  size="small"
                  variant="tonal"
                  @click="onClickEdit(product)"
                >
                  Edit recommendation
                </VBtn>
                <VBtn
                  color="error"
                  size="small"
                  variant="tonal"
                  @click="onClickDelete(product)"
                >
                  Delete recommendation
                </VBtn>
              </div>
            </div>
          </div>
        </VCard>
        <div class="mt-6">
          <VBtn
            block
            @click="currentView = VIEW_SECTION.FORM_VIEW"
          >
            Add New Recommendation
          </VBtn>
        </div>
      </VCardText>

      <!-- 👉 Recommend new product Form -->
      <VCardText v-if="currentView === VIEW_SECTION.FORM_VIEW">
        <VAlert
          v-if="!isEmpty(serverErrors)"
          type="error"
          variant="tonal"
          title="Error!"
          class="mb-6"
        >
          <ul class="mb-0">
            <li
              v-for="error in serverErrors"
              :key="error"
              class="mb-0"
            >
              {{ error }}
            </li>
          </ul>
        </VAlert>

        <VForm
          ref="formRef"
          @submit.prevent="handleSubmit"
        >
          <VRow>
            <VCol cols="12">
              <VSelect
                v-model="selectedProduct"
                label="Select Product"
                :items="productOptions"
                item-title="product_name"
                item-value="id"
                :rules="[requiredValidator]"
                @update:model-value="onProductChange"
              />
            </VCol>

            <VCol cols="12">
              <div class="mt-6">
                <label class="d-block mb-3">Select States where this product is available</label>
                <VCheckbox
                  label="Select All"
                  class="me-3"
                  :indeterminate="selectedStates.length > 0 && selectedStates.length !== stateOptions.length"
                  :model-value="selectedStates.length === stateOptions.length"
                  @change="selectAll"
                />
                <div class="d-flex flex-wrap">
                  <div
                    v-for="state in stateOptions"
                    :key="state.code"
                    style="min-width: 220px;"
                  >
                    <VCheckbox
                      v-model="selectedStates"
                      :label="state.name"
                      :value="state.code"
                      class="me-3"
                    />
                  </div>
                </div>
              </div>
            </VCol>

            <VCol cols="12">
              <div class="d-flex justify-end gap-3">
                <VBtn
                  type="button"
                  variant="tonal"
                  color="secondary"
                  :disabled="isLoading"
                  @click="onClickFormCancel"
                >
                  Cancel
                </VBtn>
                <VBtn
                  type="submit"
                  :loading="isLoading"
                >
                  Submit
                </VBtn>
              </div>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>

    <!-- 👉 Delete confirm dialog -->
    <ConfirmDialog
      v-model:isDialogVisible="isDeleteDialogVisible"
      confirmation-question="Are you sure you want to delete this recommendation?"
      :loading="deleteLoading"
      @confirm="handleDeleteConfirmation"
    />
  </div>
</template>
