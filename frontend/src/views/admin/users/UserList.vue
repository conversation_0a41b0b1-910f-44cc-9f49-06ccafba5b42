<script setup>
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { calculateStartIndex, formattedPhoneNumber, resolveInitials } from '@/utils/helpers'
import { refDebounced } from '@vueuse/core'
import { computed, onMounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import useListCount from '@/composables/useListCount'
import { isEmpty } from '@/@core/utils'

const globalData = useGlobalData()
const { showSnackbar } = globalData
const route = useRoute()
const router = useRouter()
const { loading: loadingCount, getListCount, countData } = useListCount()

const searchQuery = ref(route.query.search ?? null)
const debouncedSearchQuery = refDebounced(searchQuery, 300)
const selectedStatus = ref(!isNaN(parseInt(route.query.status)) ? parseInt(route.query.status) : null)

const dateRange = ref(
  route.query.from_date && route.query.to_date ? route.query.from_date + ' to ' + route.query.to_date : null,
)

const fromDate = ref(null)
const toDate = ref(null)
const rowPerPage = ref(10)
const currentPage = ref(!isNaN(parseInt(route.query.page)) ? parseInt(route.query.page) : 1)
const totalPage = ref(1)
const totalItems = ref(0)
const items = ref([])
const sortByColumnName = ref(route.query.sort_by ?? 'created_at')
const sortDirection = ref(route.query.sort_dir ?? 'desc')
const isChangeAccountStatusDialogVisible = ref(false)
const itemId = ref('')
const itemStatus = ref(false)
const skeletonLoading = ref(true)
const changeStatusLoading = ref(false)

const userStats = computed(() => {
  return [
    {
      icon: 'tabler-users',
      color: 'primary',
      title: 'Total Users',
      stats: countData.value?.total_users || 0,
    },
    {
      icon: 'tabler-users-plus',
      color: 'success',
      title: 'Active Users',
      stats: countData.value?.active_users || 0,
    },
    {
      icon: 'tabler-users-minus',
      color: 'error',
      title: 'Inactive Users',
      stats: countData.value?.inactive_users || 0,
    },
  ]
})

onMounted(() => {
  fetchItems()
  getListCount('user')
})

const updateRoute = () => {
  router.replace({
    query: {
      page: currentPage.value,
      per_page: rowPerPage.value,
      status: selectedStatus.value,
      search: searchQuery.value,
      sort_by: sortByColumnName.value,
      sort_dir: sortDirection.value,
      from_date: fromDate.value,
      to_date: toDate.value,
    },
  })
}

watch(dateRange, () => {
  if (isEmpty(dateRange.value)) {
    fromDate.value = null
    toDate.value = null

    return
  }

  const dateArr = dateRange.value.split(' to ')

  if (dateArr.length === 1) {
    fromDate.value = dateArr[0] || null
    toDate.value = dateArr[0] || null
  } else if (dateArr.length === 2) {
    fromDate.value = dateArr[0] || null
    toDate.value = dateArr[1] || null
  } else {
    fromDate.value = null
    toDate.value = null
  }
})

watch([debouncedSearchQuery, selectedStatus, fromDate, toDate, rowPerPage], () => {
  currentPage.value = 1
  fetchItems()
})

watch([currentPage, sortByColumnName, sortDirection], () => {
  fetchItems()
})

const fetchItems = async () => {
  skeletonLoading.value = true

  try {
    const { data } = await ApiService.post('/admin/users-list', {
      searchQuery: searchQuery.value,
      user_account_status: selectedStatus.value,
      perPage: rowPerPage.value,
      page: currentPage.value,
      sortByColumnName: sortByColumnName.value,
      isSortDirDesc: sortDirection.value,
      from_date: fromDate.value,
      to_date: toDate.value,
    })

    if (data.status === 200) {
      let startIndex = calculateStartIndex(data.users.current_page, data.users.per_page)
      if (currentPage.value > 1) {
        startIndex = (currentPage.value - 1) * rowPerPage.value + 1
      }

      items.value = data.users.records.map((d, index) => ({
        ...d,
        sno: index + startIndex,
      }))
      totalPage.value = data.users.totalPage == 0 ? 1 : data.users.totalPage
      totalItems.value = data.users.totalRecords
      updateRoute()
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    console.error(error)
    if (error.response.data.message) {
      showSnackbar(error.response.data.message, 'error')
    } else if (error.response.message) {
      showSnackbar(error.response.message, 'error')
    }
  } finally {
    skeletonLoading.value = false
  }
}

const sortColumn = columnName => {
  if (sortByColumnName.value !== '' && sortByColumnName.value === columnName && sortDirection.value === 'asc') {
    sortDirection.value = 'desc'
  } else {
    sortDirection.value = 'asc'
  }

  sortByColumnName.value = columnName
}

// 👉 search filters
const status = [
  {
    title: 'Active',
    value: 1,
  },
  {
    title: 'Inactive',
    value: 0,
  },
]

const resolveItemStatusVariant = stat => {
  if (stat === 1) {
    return { color: 'success', status: 'Active' }
  } else {
    return { color: 'secondary', status: 'Inactive' }
  }
}

const resolveItemStatus = stat => {
  if (stat === 1) {
    return { color: 'success', status: true }
  } else {
    return { color: 'secondary', status: false }
  }
}

const onAccountStatusChange = (id, status) => {
  isChangeAccountStatusDialogVisible.value = true
  itemId.value = id
  itemStatus.value = status
}

async function handleStatusChangeConfirmation(isConfirmed) {
  if (isConfirmed) {
    await changeItemStatus()
  }
}

const changeItemStatus = async () => {
  changeStatusLoading.value = true

  let itemsValue = items.value
  let itemIndex = itemsValue.findIndex(item => item.id === itemId.value)

  if (itemIndex === -1) {
    console.error('Item not found')
    changeStatusLoading.value = false

    return
  }

  let newStatus = itemsValue[itemIndex].user_account_status === 1 ? 0 : 1

  ApiService
    .get(`/admin/user-account-status/${itemId.value}`)
    .then(response => {
      if (response.data.status === 200) {
        itemsValue[itemIndex].user_account_status = newStatus
        items.value = itemsValue
        itemId.value = ''
        itemStatus.value = false
        showSnackbar(response.data.message)

        fetchItems()
        getListCount('user') // Update list count
      } else {
        showSnackbar(response.data.message, 'error')
      }
    })
    .catch(error => {
      console.error(error)
    })
    .finally(() => {
      changeStatusLoading.value = false
      isChangeAccountStatusDialogVisible.value = false
    })
}

// 👉 Computing pagination data
const paginationData = computed(() => {
  const currentPageValue = currentPage.value <= 0 ? 1 : currentPage.value

  const firstIndex = items.value.length
    ? (currentPageValue - 1) * rowPerPage.value + 1
    : 0

  const lastIndex = items.value.length + (currentPageValue - 1) * rowPerPage.value

  return `Showing ${firstIndex} to ${lastIndex} of ${totalItems.value} entries`
})
</script>

<template>
  <section>
    <VRow class="match-height">
      <VCol
        v-for="meta in userStats"
        :key="meta.title"
        cols="12"
        sm="4"
      >
        <VCard>
          <VCardText class="d-flex justify-space-between">
            <div>
              <span v-if="loadingCount">
                <Skeleton
                  width="10rem"
                  height="1.4rem"
                ></Skeleton>
              </span>
              <span
                v-else
                class="text-h5"
              >{{ meta.title }}</span>
              <div class="d-flex align-center gap-2 my-1">
                <h6 v-if="loadingCount">
                  <Skeleton
                    width="1rem"
                    height="1.5rem"
                  ></Skeleton>
                </h6>
                <h6
                  v-else
                  class="text-h4"
                >
                  {{ meta.stats }}
                </h6>
              </div>
            </div>

            <Skeleton
              v-if="loadingCount"
              size="3rem"
              class="me-2"
            ></Skeleton>
            <VAvatar
              v-else
              rounded
              size="3rem"
              variant="tonal"
              :color="meta.color"
              :icon="meta.icon"
            />
          </VCardText>
        </VCard>
      </VCol>

      <VCol cols="12">
        <VCard title="Filter">
          <!-- 👉 Filters -->
          <VCardText>
            <VRow>
              <VCol
                cols="12"
                sm="4"
              >
                <AppSelect
                  v-model="selectedStatus"
                  label="Account Status"
                  :items="status"
                  clearable
                  clear-icon="tabler-x"
                  placeholder="Select"
                />
              </VCol>
              <VCol
                cols="12"
                sm="4"
              >
                <AppDateTimePicker
                  v-model="dateRange"
                  label="Created Date"
                  placeholder="Select Date Range"
                  variant="outlined"
                  :config="{
                    mode: 'range',
                    dateFormat: 'm/d/Y',
                    onClose: function (selectedDates, dateStr, instance) {
                      if (selectedDates.length === 1) {
                        instance.setDate([selectedDates[0], selectedDates[0]], true)
                      }
                    },
                    maxDate: new Date(),
                    minDate: (new Date()).setFullYear((new Date()).getFullYear() - 120),
                  }"
                  clearable
                  clear-icon="tabler-x"
                />
              </VCol>
            </VRow>
          </VCardText>

          <VDivider />

          <VCardText class="d-flex flex-wrap py-4 gap-4">
            <div
              class="me-3"
              style="width: 80px"
            >
              <VSelect
                v-model="rowPerPage"
                density="compact"
                variant="outlined"
                :items="[10, 20, 30, 50]"
              />
            </div>

            <VSpacer />

            <div class="d-flex align-center flex-wrap gap-4">
              <!-- 👉 Search  -->
              <div style="width: 23rem">
                <VTextField
                  v-model="searchQuery"
                  placeholder="Search"
                  density="compact"
                  clearable
                  clear-icon="tabler-x"
                />
              </div>
            </div>
          </VCardText>

          <VDivider />

          <VTable class="text-no-wrap">
            <!-- 👉 table head -->
            <thead>
              <tr>
                <th scope="col">
                  #
                </th>
                <th scope="col">
                  USER
                </th>
                <th scope="col">
                  STATUS
                </th>
                <th scope="col">
                  ACTIVE/INACTIVE
                </th>
                <th scope="col">
                  Affiliate by
                </th>
                <th
                  scope="col"
                  class="cursor-pointer"
                  @click="sortColumn('created_at')"
                >
                  <div class="w-125px">
                    JOINED AT
                    <span class="d-grid float-right">
                      <VIcon
                        size="10"
                        icon="tabler-chevron-up"
                      />
                      <VIcon
                        size="10"
                        icon="tabler-chevron-down"
                      />
                    </span>
                  </div>
                </th>
                <th
                  scope="col"
                  class="cursor-pointer"
                  @click="sortColumn('last_login_at')"
                >
                  <div class="w-125px">
                    LAST LOGIN
                    <span class="d-grid float-right">
                      <VIcon
                        size="10"
                        icon="tabler-chevron-up"
                      />
                      <VIcon
                        size="10"
                        icon="tabler-chevron-down"
                      />
                    </span>
                  </div>
                </th>
                <th
                  scope="col"
                  class="cursor-pointer"
                  @click="sortColumn('last_activity_at')"
                >
                  <div class="w-150px">
                    Last Activity
                    <span class="d-grid float-right">
                      <VIcon
                        size="10"
                        icon="tabler-chevron-up"
                      />
                      <VIcon
                        size="10"
                        icon="tabler-chevron-down"
                      />
                    </span>
                  </div>
                </th>
                <th scope="col">
                  ACTIONS
                </th>
              </tr>
            </thead>

            <!-- 👉 table body -->
            <tbody v-if="skeletonLoading">
              <tr
                v-for="n in 10"
                :key="n"
              >
                <td
                  v-for="i in 9"
                  :key="i"
                >
                  <Skeleton
                    width="8rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
              </tr>
            </tbody>
            <tbody v-else>
              <tr
                v-for="item in items"
                :key="item.id"
                style="height: 3.75rem"
              >
                <!-- 👉 NO -->
                <td>
                  {{ item.sno }}
                </td>

                <!-- 👉 User -->
                <td class="title">
                  <RouterLink
                    :to="{
                      name: 'admin-user-details',
                      params: { userId: item.id },
                      query: { ...route.query },
                    }"
                    class="d-flex align-center py-1"
                  >
                    <VAvatar
                      v-if="item.selfie"
                      variant="tonal"
                      size="40"
                      class="me-3"
                      :image="item.selfie"
                      rounded
                    />
                    <VAvatar
                      v-else
                      variant="tonal"
                      size="40"
                      class="me-3"
                      rounded
                    >
                      {{ resolveInitials(item.full_name) }}
                    </VAvatar>
                    <div>
                      <h6 class="text-base">
                        {{ item.full_name }}
                      </h6>
                      <div class="d-flex flex-column">
                        <span class="text-body-2">{{ item.email }}</span>
                        <span class="text-body-2">{{ formattedPhoneNumber(item.phone_number) }}</span>
                      </div>
                    </div>
                  </RouterLink>
                </td>

                <!-- 👉 Status -->
                <td>
                  <VChip
                    label
                    :color="resolveItemStatusVariant(item.user_account_status).color"
                    size="small"
                    class="text-capitalize"
                  >
                    {{ resolveItemStatusVariant(item.user_account_status).status }}
                  </VChip>
                </td>

                <!-- 👉 Status Change -->
                <td>
                  <VSwitch
                    v-model="resolveItemStatus(item.user_account_status).status"
                    inset
                    @change="onAccountStatusChange(item.id, resolveItemStatus(item.user_account_status).status)"
                  />
                </td>

                <!-- 👉 Affiliate by -->
                <td class="title">
                  <RouterLink
                    v-if="!isEmpty(item.affiliate_user)"
                    :to="{
                      name: 'admin-affiliate-user-details',
                      params: { userId: item.affiliate_user.id },
                    }"
                    class="d-block"
                  >
                    <div class="d-flex align-center py-1">
                      <VAvatar
                        v-if="item.affiliate_user.profile_picture"
                        variant="tonal"
                        size="40"
                        class="me-3"
                        :image="item.affiliate_user.profile_picture"
                        rounded
                      />
                      <VAvatar
                        v-else
                        variant="tonal"
                        size="40"
                        class="me-3"
                        rounded
                      >
                        {{ resolveInitials(item.affiliate_user.first_name + ' ' + item.affiliate_user.last_name) }}
                      </VAvatar>
                      <div>
                        <h6 class="text-base">
                          {{ item.affiliate_user.first_name }} {{ item.affiliate_user.last_name }}
                        </h6>
                        <div class="d-flex flex-column">
                          <span class="text-body-2">{{ item.affiliate_user.email }}</span>
                          <span class="text-body-2">{{ formattedPhoneNumber(item.affiliate_user.phone_number) }}</span>
                        </div>
                      </div>
                    </div>
                  </RouterLink>
                  <span v-else> - </span>
                </td>

                <!-- 👉 Joined At -->
                <td>
                  <div class="d-flex flex-column">
                    <span>{{ item.joined_date }}</span>
                    <span>{{ item.joined_time }}</span>
                  </div>
                </td>

                <!-- 👉 Last Login -->
                <td>
                  <div class="d-flex flex-column">
                    <span>{{ item.last_login_date }}</span>
                    <span>{{ item.last_login_time }}</span>
                  </div>
                </td>

                <!-- 👉 Last Activity -->
                <td>
                  <div
                    v-if="item.last_activity_date && item.last_activity_time"
                    class="d-flex flex-column"
                  >
                    <span>{{ item.last_activity_date }}</span>
                    <span>{{ item.last_activity_time }}</span>
                  </div>
                  <div v-else>
                    -
                  </div>
                </td>

                <!-- 👉 Actions -->
                <td
                  class="text-center"
                  style="width: 5rem"
                >
                  <VBtn
                    v-tooltip.top="'View Details'"
                    icon
                    size="x-small"
                    color="default"
                    variant="text"
                    :to="{
                      name: 'admin-user-details',
                      params: { userId: item.id },
                      query: { ...route.query },
                    }"
                  >
                    <VIcon
                      size="22"
                      icon="tabler-eye"
                    />
                  </VBtn>
                </td>
              </tr>
            </tbody>

            <!-- 👉 table footer  -->
            <tfoot v-show="!skeletonLoading && items.length === 0">
              <tr>
                <td
                  colspan="7"
                  class="text-center"
                >
                  No data available
                </td>
              </tr>
            </tfoot>
          </VTable>

          <VDivider />

          <VCardText class="d-flex align-center flex-wrap justify-space-between gap-4 py-3 px-5">
            <span class="text-sm text-disabled">
              {{ paginationData }}
            </span>
            <VPagination
              v-model="currentPage"
              size="small"
              :total-visible="5"
              :length="totalPage"
            />
          </VCardText>
        </VCard>
      </VCol>
    </VRow>

    <!-- 👉 Change account Status confirm dialog -->
    <ConfirmDialog
      v-model:isDialogVisible="isChangeAccountStatusDialogVisible"
      confirmation-question="Are you sure you want to change account status for this user?"
      :loading="changeStatusLoading"
      @confirm="handleStatusChangeConfirmation"
    />
  </section>
</template>

<style lang="scss">
.v-table__wrapper td.title {
  min-width: 200px;
  h6 {
    a {
      white-space: pre-line;
    }
  }
}
</style>
