<script setup>
import { isEmpty } from '@/@core/utils'
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { calculateStartIndex, formattedPhoneNumber, resolveInitials } from '@/utils/helpers'
import { refDebounced } from '@vueuse/core'
import { onMounted, ref, watch } from 'vue'

const globalData = useGlobalData()
const { showSnackbar } = globalData

const searchQuery = ref('')
const debouncedSearchQuery = refDebounced(searchQuery, 300)
const dateRange = ref('')
const fromDate = ref(null)
const toDate = ref(null)
const rowPerPage = ref(10)
const currentPage = ref(1)
const totalPage = ref(1)
const totalItems = ref(0)
const items = ref([])
const sortByColumnName = ref('created_at')
const sortDirection = ref('desc')
const isDeleteDialogVisible = ref(false)
const itemId = ref('')
const skeletonLoading = ref(true)

onMounted(() => {
  fetchItems()
})

watch(dateRange, () => {
  if (isEmpty(dateRange.value)) {
    fromDate.value = null
    toDate.value = null

    return
  }

  const dateArr = dateRange.value.split(' to ')

  if (dateArr.length === 1) {
    fromDate.value = dateArr[0] || null
    toDate.value = dateArr[0] || null
  } else if (dateArr.length === 2) {
    fromDate.value = dateArr[0] || null
    toDate.value = dateArr[1] || null
  } else {
    fromDate.value = null
    toDate.value = null
  }
})

watch([debouncedSearchQuery, fromDate, toDate, rowPerPage], () => {
  currentPage.value = 1
  fetchItems()
})

watch([currentPage, sortByColumnName, sortDirection], () => {
  fetchItems()
})

const fetchItems = async () => {
  skeletonLoading.value = true
  try {
    const { data } = await ApiService.post('/admin/interested-users-list', {
      searchQuery: searchQuery.value,
      perPage: rowPerPage.value,
      page: currentPage.value,
      sortByColumnName: sortByColumnName.value,
      isSortDirDesc: sortDirection.value,
      from_date: fromDate.value,
      to_date: toDate.value,
    })

    if (data.status === 200) {
      const usersData = data.users

      let startIndex = calculateStartIndex(usersData.current_page, usersData.per_page)
      if (currentPage.value > 1) {
        startIndex = (currentPage.value - 1) * rowPerPage.value + 1
      }
      items.value = usersData.records.map((d, index) => ({
        ...d,
        sno: index + startIndex,
      }))
      totalPage.value = usersData.totalPage == 0 ? 1 : usersData.totalPage
      totalItems.value = usersData.totalRecords
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    console.error(error)
    if (error.response?.data?.message) {
      showSnackbar(error.response.data.message, 'error')
    } else if (error.response?.message) {
      showSnackbar(error.response.message, 'error')
    }
  } finally {
    skeletonLoading.value = false
  }
}


const sortColumn = columnName => {
  if (sortByColumnName.value !== '' && sortByColumnName.value === columnName && sortDirection.value === 'asc') {
    sortDirection.value = 'desc'
  } else {
    sortDirection.value = 'asc'
  }

  sortByColumnName.value = columnName
}

// 👉 search filters
const status = [
  {
    title: 'Active',
    value: 1,
  },
  {
    title: 'Inactive',
    value: 0,
  },
]

// const onDeleteDialog = id => {
//   isDeleteDialogVisible.value = true
//   itemId.value = id
// }

// const deleteUser = () => {
//   globalData.circularProgress = true

//   ApiService
//     .delete(`/admin/delete-interested-user/${itemId.value}`)
//     .then(response => {
//       if (response.data.status === 200) {
//         isDeleteDialogVisible.value = false
//         itemId.value = ''
//         showSnackbar(response.data.message)

//         fetchItems()
//       } else if (response.data.status === 409) {
//         isDeleteDialogVisible.value = false
//         if (response.data.message) {
//           showSnackbar(response.data.message, 'error')
//         }
//       } else {
//         if (response.data.message) {
//           showSnackbar(response.data.message, 'error')
//         }
//       }
//     })
//     .catch(error => {
//       console.error(error)
//     })
//     .finally(() => {
//       globalData.circularProgress = false
//     })
// }

function getAge(dob) {
  if (dob) {
    const age = new Date().getFullYear() - new Date(dob).getFullYear()

    return age
  }
}

// 👉 Computing pagination data
const paginationData = computed(() => {
  const currentPageValue = currentPage.value <= 0 ? 1 : currentPage.value

  const firstIndex = items.value.length
    ? (currentPageValue - 1) * rowPerPage.value + 1
    : 0

  const lastIndex = items.value.length + (currentPageValue - 1) * rowPerPage.value

  return `Showing ${firstIndex} to ${lastIndex} of ${totalItems.value} entries`
})
</script>

<template>
  <section>
    <VRow>
      <VCol cols="12">
        <VCard title="Filter">
          <!-- 👉 Filters -->
          <VCardText>
            <VRow>
              <VCol
                cols="12"
                sm="4"
              >
                <AppDateTimePicker
                  v-model="dateRange"
                  label="Created Date"
                  placeholder="Select Date Range"
                  variant="outlined"
                  :config="{
                    mode: 'range',
                    dateFormat: 'm/d/Y',
                    onClose: function (selectedDates, dateStr, instance) {
                      if (selectedDates.length === 1) {
                        instance.setDate([selectedDates[0], selectedDates[0]], true)
                      }
                    },
                    maxDate: new Date(),
                    minDate: (new Date()).setFullYear((new Date()).getFullYear() - 120),
                  }"
                  clearable
                  clear-icon="tabler-x"
                />
              </VCol>
            </VRow>
          </VCardText>

          <VDivider />

          <VCardText class="d-flex flex-wrap py-4 gap-4">
            <div
              class="me-3"
              style="width: 80px"
            >
              <VSelect
                v-model="rowPerPage"
                density="compact"
                variant="outlined"
                :items="[10, 20, 30, 50]"
              />
            </div>

            <VSpacer />

            <div class="d-flex align-center flex-wrap gap-4">
              <!-- 👉 Search  -->
              <div style="width: 23rem">
                <VTextField
                  v-model="searchQuery"
                  placeholder="Search"
                  density="compact"
                  clearable
                  clear-icon="tabler-x"
                />
              </div>
            </div>
          </VCardText>

          <VDivider />

          <VTable class="text-no-wrap">
            <!-- 👉 table head -->
            <thead>
              <tr>
                <th scope="col">
                  #
                </th>
                <th scope="col">
                  USER
                </th>
                <th scope="col">
                  GENDER
                </th>
                <th scope="col">
                  DOB
                </th>
                <th scope="col">
                  STATE
                </th>
                <th scope="col">
                  RACE
                </th>
                <th scope="col">
                  Affiliate by
                </th>
                <th
                  scope="col"
                  class="cursor-pointer"
                  @click="sortColumn('created_at')"
                >
                  <div class="w-125px">
                    CREATED AT
                    <span class="d-grid float-right">
                      <VIcon
                        size="10"
                        icon="tabler-chevron-up"
                      />
                      <VIcon
                        size="10"
                        icon="tabler-chevron-down"
                      />
                    </span>
                  </div>
                </th>
                <th
                  scope="col"
                  class="cursor-pointer"
                  @click="sortColumn('updated_at')"
                >
                  <div class="w-125px">
                    UPDATED AT
                    <span class="d-grid float-right">
                      <VIcon
                        size="10"
                        icon="tabler-chevron-up"
                      />
                      <VIcon
                        size="10"
                        icon="tabler-chevron-down"
                      />
                    </span>
                  </div>
                </th>
                <!--
                  <th scope="col">
                  ACTIONS
                  </th>
                -->
              </tr>
            </thead>

            <!-- 👉 table body -->
            <tbody v-if="skeletonLoading">
              <tr
                v-for="n in 10"
                :key="n"
              >
                <td
                  v-for="i in 9"
                  :key="i"
                >
                  <Skeleton
                    width="8rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
              </tr>
            </tbody>
            <tbody v-else>
              <tr
                v-for="item in items"
                :key="item.id"
                style="height: 3.75rem"
              >
                <!-- 👉 NO -->
                <td>
                  {{ item.sno }}
                </td>

                <!-- 👉 User -->
                <td>
                  <div class="d-flex flex-column py-1">
                    <span
                      v-if="item.first_name && item.last_name"
                      class="text-body-1"
                    >
                      {{ item.first_name }} {{ item.last_name }}
                    </span>
                    <span
                      v-if="item.email"
                      class="text-body-2"
                    >
                      {{ item.email }}
                    </span>
                    <span
                      v-if="item.phone_number"
                      class="text-body-2"
                    >
                      {{ formattedPhoneNumber(item.phone_number) }}
                    </span>
                    <span v-if="isEmpty(item.first_name) && isEmpty(item.last_name) && isEmpty(item.email) && isEmpty(item.phone_number)">
                      -
                    </span>
                  </div>
                </td>

                <!-- 👉 Gender -->
                <td>
                  {{ item.sex ?? '-' }}
                </td>

                <!-- 👉 DOB -->
                <td>
                  <span v-if="item.dob">{{ item.dob }} ({{ getAge(item.dob) }} years)</span>
                  <span v-else> - </span>
                </td>

                <!-- 👉 STATE -->
                <td>
                  {{ item.state ?? '-' }}
                </td>

                <!-- 👉 Race -->
                <td>
                  {{ item.race ?? '-' }}
                </td>

                <!-- 👉 Affiliate by -->
                <td class="title">
                  <RouterLink
                    v-if="!isEmpty(item.affiliate_user)"
                    :to="{
                      name: 'admin-affiliate-user-details',
                      params: { userId: item.affiliate_user.id },
                    }"
                    class="d-block"
                  >
                    <div class="d-flex align-center py-1">
                      <VAvatar
                        v-if="item.affiliate_user.profile_picture"
                        variant="tonal"
                        size="40"
                        class="me-3"
                        :image="item.affiliate_user.profile_picture"
                        rounded
                      />
                      <VAvatar
                        v-else
                        variant="tonal"
                        size="40"
                        class="me-3"
                        rounded
                      >
                        {{ resolveInitials(item.affiliate_user.first_name + ' ' + item.affiliate_user.last_name) }}
                      </VAvatar>
                      <div>
                        <h6 class="text-base">
                          {{ item.affiliate_user.first_name }} {{ item.affiliate_user.last_name }}
                        </h6>
                        <div class="d-flex flex-column">
                          <span class="text-body-2">{{ item.affiliate_user.email }}</span>
                          <span class="text-body-2">{{ formattedPhoneNumber(item.affiliate_user.phone_number) }}</span>
                        </div>
                      </div>
                    </div>
                  </RouterLink>
                  <span v-else> - </span>
                </td>

                <!-- 👉 Created At -->
                <td>
                  <div class="d-flex flex-column">
                    <span>{{ item.created_date }}</span>
                    <span>{{ item.created_time }}</span>
                  </div>
                </td>

                <!-- 👉 Updated At -->
                <td>
                  <div
                    v-if="item.updated_date !== item.created_date || item.updated_time !== item.created_time"
                    class="d-flex flex-column"
                  >
                    <span>{{ item.updated_date }}</span>
                    <span>{{ item.updated_time }}</span>
                  </div>
                  <div v-else>
                    -
                  </div>
                </td>

                <!-- 👉 Actions -->
                <!--
                  <td
                  class="text-center"
                  style="width: 5rem"
                  >
                  <VBtn
                  icon
                  size="x-small"
                  color="default"
                  variant="text"
                  @click="onDeleteDialog(item.id)"
                  >
                  <VIcon
                  size="22"
                  icon="tabler-trash"
                  />
                  </VBtn>
                  </td>
                -->
              </tr>
            </tbody>

            <!-- 👉 table footer  -->
            <tfoot v-show="!skeletonLoading && items.length === 0">
              <tr>
                <td
                  colspan="5"
                  class="text-center"
                >
                  No data available
                </td>
              </tr>
            </tfoot>
          </VTable>

          <VDivider />

          <VCardText class="d-flex align-center flex-wrap justify-space-between gap-4 py-3 px-5">
            <span class="text-sm text-disabled">
              {{ paginationData }}
            </span>
            <VPagination
              v-model="currentPage"
              size="small"
              :total-visible="5"
              :length="totalPage"
            />
          </VCardText>
        </VCard>
      </VCol>
    </VRow>

    <!-- 👉 Delete -->
    <!--
      <VDialog
      v-model="isDeleteDialogVisible"
      persistent
      class="v-dialog-sm"
      >
      <DialogCloseBtn @click="isDeleteDialogVisible = !isDeleteDialogVisible" />
      <VCard title="Are you sure?">
      <VCardText> Are you sure you want to delete this item? </VCardText>

      <VCardText class="d-flex justify-end gap-3 flex-wrap">
      <VBtn
      color="secondary"
      variant="tonal"
      @click="isDeleteDialogVisible = false"
      >
      No
      </VBtn>
      <VBtn @click="deleteUser">
      Yes
      </VBtn>
      </VCardText>
      </VCard>
      </VDialog>
    -->
  </section>
</template>
