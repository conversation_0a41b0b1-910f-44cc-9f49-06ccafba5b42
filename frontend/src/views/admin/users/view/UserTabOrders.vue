<script setup>
import { isEmpty } from '@/@core/utils'
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import Skeleton from 'primevue/skeleton'
import { computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const userId = computed(() => route.params.userId)
const { showSnackbar } = useGlobalData()

const skeletonLoading = ref(false)
const currentPage = ref(1)
const orders = ref([])
const totalPage = ref(0)
const totalRecords = ref(0)
const isShippingActivityDialogVisible = ref(false)
const shippingActivityList = ref([])

onMounted(async () => {
  await fetchOrders()
})

async function fetchOrders() {
  try {
    skeletonLoading.value = true

    const { data } = await ApiService.post('/admin/user-orders', {
      user_id: userId.value,
      page: currentPage.value,
    })

    if (data.status === 200) {
      if (currentPage.value === 1) {
        orders.value = data.orders.records
      } else {
        orders.value = [...orders.value, ...data.orders.records]
      }
      totalPage.value = data.orders.totalPage
      totalRecords.value = data.orders.totalRecords
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    console.error(error)
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    skeletonLoading.value = false
  }
}

async function handlePagination() {
  if (!skeletonLoading.value && currentPage.value < totalPage.value) {
    currentPage.value++
    await fetchOrders()
  }
}
</script>

<template>
  <!-- 👉 Orders -->
  <VRow v-if="!skeletonLoading && !isEmpty(orders)">
    <VCol
      v-for="order in orders"
      :key="order.id"
      cols="12"
    >
      <VCard :title="`Order# ${order.order_no}`">
        <VCardText>
          <VRow>
            <VCol cols="12">
              <VRow class="mb-5">
                <VCol
                  cols="12"
                  sm="2"
                  lg="2"
                >
                  <VAvatar
                    rounded="sm"
                    size="80"
                    :image="order.product_img"
                  />
                </VCol>
                <VCol
                  cols="12"
                  sm="4"
                >
                  <h6 class="text-h5 font-weight-medium text-high-emphasis mb-1">
                    {{ order.product_name }}
                  </h6>
                  <p
                    v-if="String(order.category_name)?.toLowerCase() === 'wl'"
                    class="text-sm mb-0"
                  >
                    <span class="d-block">
                      {{ order.strength }} {{ order.strength_unit }} / weekly
                    </span>
                  </p>
                  <p
                    v-else
                    class="text-sm mb-0"
                  >
                    <span class="d-block">
                      {{ order.strength }} {{ order.strength_unit }} x {{ order.qty * order.subscription_interval }} units
                    </span>
                    <span class="d-block"> ({{ order.subscription_interval * 30 }}-day supply)</span>
                  </p>
                </VCol>
                <VCol
                  cols="12"
                  sm="3"
                >
                  <h6 class="text-base font-weight-medium text-high-emphasis mb-2">
                    Order Created
                  </h6>
                  <p class="text-sm mb-0">
                    <span class="d-block">{{ order.created_date }}</span>
                    <span class="d-block">{{ order.created_time }}</span>
                  </p>
                </VCol>
                <VCol
                  cols="12"
                  sm="3"
                >
                  <h6 class="text-base font-weight-medium text-high-emphasis mb-2">
                    Order Number
                  </h6>
                  <p class="text-sm mb-0">
                    {{ order.order_no }}
                  </p>
                </VCol>
              </VRow>

              <div class="d-flex flex-column flex-sm-row flex-wrap gap-3 mb-5">
                <VAlert variant="tonal">
                  <VAlertTitle class="mb-2">
                    <span class="text-body-1">Order Status</span>
                  </VAlertTitle>
                  <VChip
                    :color="order.order_color"
                    size="small"
                    label
                  >
                    {{ order.order_status }}
                  </VChip>
                </VAlert>
                <VAlert
                  v-if="!isEmpty(order.refill_number) && !isEmpty(order.total_refills) && String(order.category_name)?.toLowerCase() !== 'wl'"
                  variant="tonal"
                >
                  <VAlertTitle class="mb-2">
                    <span class="text-body-1">Refill Number</span>
                  </VAlertTitle>
                  <span class="text-body-1">{{ order.refill_number }} of {{ order.total_refills }}</span>
                </VAlert>
                <VAlert
                  v-if="!isEmpty(order.drug_month) && String(order.category_name)?.toLowerCase() === 'wl'"
                  variant="tonal"
                >
                  <VAlertTitle class="mb-2">
                    <span class="text-body-1">Dosage Month</span>
                  </VAlertTitle>
                  <span class="text-body-1">Month {{ order.drug_month }}</span>
                </VAlert>
                <VAlert variant="tonal">
                  <VAlertTitle class="mb-2">
                    <span class="text-body-1">Total</span>
                  </VAlertTitle>
                  <span class="text-body-1">
                    {{ formatCurrency(order.refill_amount) }}
                  </span>
                </VAlert>
              </div>
              <VRow>
                <VCol
                  cols="12"
                  sm="4"
                >
                  <h6 class="text-base font-weight-medium text-high-emphasis mb-2">
                    Shipping Address
                  </h6>
                  <p class="text-sm mb-0">
                    <span>{{ order.address_line_1 }} <br /></span>
                    <span v-if="order.address_line_2">{{ order.address_line_2 }} <br /></span>
                    <span>{{ order.city }} <br /></span>
                    <span>{{ order.state }}-{{ order.zipcode }} <br /></span>
                  </p>
                </VCol>
                <VCol
                  cols="12"
                  sm="4"
                >
                  <h6 class="text-base font-weight-medium text-high-emphasis mb-2">
                    Transaction ID
                  </h6>
                  <p class="text-sm mb-0">
                    <span v-if="order.paypal_transaction_id">{{ order.paypal_transaction_id }}</span>
                    <span v-else> - </span>
                  </p>
                </VCol>
                <VCol
                  cols="12"
                  sm="4"
                >
                  <h6 class="text-base font-weight-medium text-high-emphasis mb-2">
                    Refill Tracking number
                  </h6>
                  <p class="text-sm mb-0">
                    {{ order.tracking_no || '-' }}
                  </p>
                  <button
                    v-if="!isEmpty(order.shipping_activities)"
                    class="mt-2 btn-link"
                    @click="() => {
                      shippingActivityList = order.shipping_activities
                      isShippingActivityDialogVisible = true
                    }"
                  >
                    Track Your Refill
                  </button>
                </VCol>
              </VRow>
            </VCol>
            <VCol cols="12">
              <div class="d-flex flex-column flex-sm-row gap-4">
                <VBtn
                  :to="{
                    name: `admin-${order.category_name.toLowerCase()}-order-details`,
                    params: { orderId: order.id },
                    query: { ...route.query },
                  }"
                >
                  View Details
                </VBtn>

                <!--
                  <VBtn
                  color="error"
                  variant="tonal"
                  >
                  Cancel Subscription
                  </VBtn>
                -->
              </div>
            </VCol>
          </VRow>
        </VCardText>
      </VCard>
    </VCol>
  </VRow>
  <!-- 👉 Orders Skeleton Loader -->
  <VRow v-if="skeletonLoading">
    <VCol
      v-for="s in 2"
      :key="s"
      cols="12"
    >
      <VCard>
        <VCardTitle class="pa-6">
          <Skeleton
            height="1.5rem"
            width="10rem"
          />
        </VCardTitle>
        <VCardText>
          <VRow>
            <VCol cols="12">
              <VRow class="mb-5">
                <VCol
                  cols="12"
                  sm="2"
                  lg="2"
                >
                  <Skeleton
                    height="80px"
                    width="80px"
                  />
                </VCol>
                <VCol
                  cols="12"
                  sm="4"
                >
                  <Skeleton
                    height="1.4rem"
                    width="8rem"
                    class="mb-2"
                  />
                  <Skeleton
                    height="1rem"
                    width="10rem"
                    class="mb-2"
                  />
                  <Skeleton
                    height="1rem"
                    width="4rem"
                  />
                </VCol>
                <VCol
                  cols="12"
                  sm="3"
                >
                  <Skeleton
                    height="1.2rem"
                    width="8rem"
                    class="mb-2"
                  />
                  <Skeleton
                    height="1.2rem"
                    width="10rem"
                    class="mb-2"
                  />
                </VCol>
                <VCol
                  cols="12"
                  sm="3"
                >
                  <Skeleton
                    height="1.2rem"
                    width="8rem"
                    class="mb-2"
                  />
                  <Skeleton
                    height="1.2rem"
                    width="10rem"
                    class="mb-2"
                  />
                </VCol>
              </VRow>
            </VCol>

            <VRow>
              <VCol
                cols="12"
                sm="4"
              >
                <Skeleton
                  height="1.2rem"
                  width="8rem"
                  class="mb-2"
                />
                <Skeleton
                  height="1.2rem"
                  width="10rem"
                  class="mb-2"
                />
              </VCol>
              <VCol
                cols="12"
                sm="4"
              >
                <Skeleton
                  height="1.2rem"
                  width="8rem"
                  class="mb-2"
                />
                <Skeleton
                  height="1.2rem"
                  width="10rem"
                  class="mb-2"
                />
              </VCol>
              <VCol
                cols="12"
                sm="4"
              >
                <Skeleton
                  height="1.2rem"
                  width="8rem"
                  class="mb-2"
                />
                <Skeleton
                  height="1.2rem"
                  width="10rem"
                  class="mb-2"
                />
              </VCol>
            </VRow>
          </VRow>
        </VCardText>
      </VCard>
    </VCol>
  </VRow>

  <!-- 👉 No Orders -->
  <VRow v-if="!skeletonLoading && isEmpty(orders)">
    <VCol cols="12">
      <VCard>
        <VCardText class="text-center">
          <VIcon
            icon="tabler-package-off"
            size="80"
          />
          <h5 class="text-h5 mt-5">
            No Orders
          </h5>
        </VCardText>
      </VCard>
    </VCol>
  </VRow>

  <div
    v-if="currentPage < totalPage"
    class="text-center mt-6"
  >
    <VBtn
      size="small"
      @click="handlePagination"
    >
      See More
    </VBtn>
  </div>

  <ShippingActivityDialog
    v-model:isDialogVisible="isShippingActivityDialogVisible"
    :shipping-activities="shippingActivityList"
  />
</template>
