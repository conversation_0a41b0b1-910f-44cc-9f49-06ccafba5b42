<script setup>
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { processErrors } from '@/utils/errorHandler'
import { computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { isCardExpired, readAsDataURL } from '@/utils/helpers'
import { useUserDetailsStore } from '@/store/admin/userDetails'
import { storeToRefs } from 'pinia'
import Image from 'primevue/image'
import { isEmpty } from '@/@core/utils'

const route = useRoute()
const userId = computed(() => route.params.userId)
const { showSnackbar } = useGlobalData()
const userDetailsStore = useUserDetailsStore()
const { userData, loading: userDataLoading } = storeToRefs(userDetailsStore)

const paymentLoading = ref(false)
const paymentMethods = ref([])
const isCardDeleteDialogVisible = ref(false)
const cardIdToDelete = ref(null)
const cardDeleteLoading = ref(false)
const isCardSetAsPrimaryDialogVisible = ref(false)
const cardIdToSetAsPrimary = ref(null)
const cardPrimaryLoading = ref(false)

const isEditAddressDialogVisible = ref(false)

const currentShippingAddress = computed(() => {
  return userData.value.shipping_address || {
    address_line_1: null,
    address_line_2: null,
    city: null,
    state: null,
    zipcode: null,
  }
})

const docUploading = ref(null)
const inputGovtIdRef = ref(null)
const inputSelfieRef = ref(null)
const docGovtId = ref({ file: null, url: userData.value.government_document || null })
const docSelfie = ref({ file: null, url: userData.value.selfie || null })

onMounted(async () => {
  await fetchPaymentMethods()
})

async function fetchPaymentMethods() {
  try {
    paymentLoading.value = true

    const { data } = await ApiService.get(`/admin/user-payment-methods/${userId.value}`)

    if (data.status === 200) {
      paymentMethods.value = data.paymentMethodList
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    console.error(error)
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    paymentLoading.value = false
  }
}

function handlePrimaryCardChange(id) {
  cardIdToSetAsPrimary.value = id
  isCardSetAsPrimaryDialogVisible.value = true
}

async function handlePrimaryCardChangeConfirmation(isConfirmed) {
  if (isConfirmed) {
    await changePrimaryPaymentMethod(cardIdToSetAsPrimary.value)
  }
}

async function changePrimaryPaymentMethod(paymentMethodId) {
  try {
    cardPrimaryLoading.value = true

    const { data } = await ApiService.get(`/admin/change-default-user-payment-method/${paymentMethodId}`)

    if (data.status === 200) {
      await fetchPaymentMethods()
      showSnackbar(data.message)
      cardIdToSetAsPrimary.value = null
      isCardSetAsPrimaryDialogVisible.value = false
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    console.error(error)
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    cardPrimaryLoading.value = false
  }
}

function handleCardDelete(id) {
  cardIdToDelete.value = id
  isCardDeleteDialogVisible.value = true
}

async function handleDeleteConfirmation(isConfirmed) {
  if (isConfirmed) {
    await deletePaymentMethod(cardIdToDelete.value)
  }
}

async function deletePaymentMethod(paymentMethodId) {
  try {
    cardDeleteLoading.value = true

    const { data } = await ApiService.delete(`/admin/delete-user-payment-method/${paymentMethodId}`)

    if (data.status === 200) {
      await fetchPaymentMethods()
      showSnackbar(data.message)
      cardIdToDelete.value = null
      isCardDeleteDialogVisible.value = false
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    console.error(error)
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    cardDeleteLoading.value = false
  }
}

const onGovtIdChange = async $event => {
  const { files } = $event.target
  if (!files || files.length === 0) return
  const file = files[0]

  if (file.type.slice(0, 6) !== 'image/') {
    showSnackbar('Only image files are allowed', 'error')

    return false
  }
  if (file.size > 3 * 1024 * 1024) {
    showSnackbar('Govt. ID should be less than 3MB', 'error')

    return false
  }

  const url = await readAsDataURL(file)

  docGovtId.value = { file, url }

  await uploadDocument(docGovtId.value.url, 'government')

  return true
}

const onSelfieChange = async $event => {
  const { files } = $event.target
  if (!files || files.length === 0) return
  const file = files[0]

  if (file.type.slice(0, 6) !== 'image/') {
    showSnackbar('Only image files are allowed', 'error')

    return false
  }
  if (file.size > 3 * 1024 * 1024) {
    showSnackbar('Selfie should be less than 3MB', 'error')

    return false
  }

  const url = await readAsDataURL(file)

  docSelfie.value = { file, url }

  await uploadDocument(docSelfie.value.url, 'selfie')

  return true
}

const uploadDocument = async (document, docType) => {
  try {
    docUploading.value = docType

    const formData = new FormData()

    formData.append('user_id', userId.value)
    formData.append('document_type', docType)
    formData.append('document', document)

    const { data } = await ApiService.post('/admin/update-user-identity-document', formData)

    if (data.status === 200) {
      showSnackbar(data.message)
      await userDetailsStore.fetchUserDetails(userId.value)
    } else {
      showSnackbar(data.message, 'error')
      resetToOriginalDocs()
    }
  } catch (error) {
    showSnackbar(processErrors(error)[0], 'error')
    resetToOriginalDocs()
  } finally {
    docUploading.value = null
  }
}

function resetToOriginalDocs() {
  docGovtId.value = { file: null, url: userData.value.govtId || null }
  docSelfie.value = { file: null, url: userData.value.selfie || null }
}
</script>

<template>
  <VRow>
    <!-- 👉 Payment Methods -->
    <VCol cols="12">
      <VCard title="Payment Methods">
        <VCardText>
          <!-- skeleton loading Payment methods -->
          <div
            v-if="paymentLoading"
            class="d-flex flex-column gap-y-4"
          >
            <VCard
              v-for="i in 3"
              :key="i"
              border
              flat
            >
              <VCardText class="d-flex flex-sm-row flex-column pa-4">
                <div class="text-no-wrap">
                  <Skeleton
                    width="140px"
                    height="20px"
                    class="mb-2"
                  ></Skeleton>
                  <Skeleton
                    width="200px"
                    height="16px"
                    class="mb-2"
                  ></Skeleton>
                  <Skeleton
                    width="180px"
                    height="16px"
                  ></Skeleton>
                </div>

                <VSpacer />

                <div class="d-flex flex-column text-sm-end gap-2">
                  <div class="order-sm-0 order-1">
                    <div class="d-flex gap-2">
                      <Skeleton
                        width="100px"
                        height="26px"
                      />
                      <Skeleton
                        width="100px"
                        height="26px"
                      />
                    </div>
                  </div>
                </div>
              </VCardText>
            </VCard>
          </div>

          <!-- Payment methods -->
          <div
            v-else-if="paymentMethods.length > 0"
            class="d-flex flex-column gap-y-4"
          >
            <VCard
              v-for="card in paymentMethods"
              :key="card.name"
              border
              flat
            >
              <VCardText class="d-flex flex-sm-row flex-column pa-4">
                <div class="text-no-wrap">
                  <p class="text-base mb-1">
                    {{ card.card_brand_type }}
                    <VChip
                      v-if="card.is_default"
                      label
                      color="primary"
                      size="x-small"
                      class="ms-2"
                    >
                      Primary
                    </VChip>
                  </p>
                  <div class="text-body-1">
                    xxxx xxxx xxxx {{ card.card_last_4_digit }}
                  </div>
                  <VChip
                    v-if="isCardExpired(card.card_expiry_year, card.card_expiry_month)"
                    label
                    color="error"
                    size="x-small"
                  >
                    Expired
                  </VChip>
                  <span
                    v-else
                    class="text-body-2"
                  >
                    Card expires at {{ card.card_expiry_month }}/{{ card.card_expiry_year }}
                  </span>
                </div>

                <VSpacer />

                <div class="d-flex flex-column text-sm-end gap-2">
                  <div class="order-sm-0 order-1">
                    <VBtn
                      v-if="!card.is_default"
                      class="me-2"
                      size="small"
                      @click="handlePrimaryCardChange(card.id)"
                    >
                      Set as primary
                    </VBtn>
                    <VBtn
                      v-if="!card.is_default && !card.is_card_associated_with_active_order"
                      color="error"
                      size="small"
                      @click="handleCardDelete(card.id)"
                    >
                      Delete
                    </VBtn>
                    <v-tooltip
                      v-else-if="card.is_card_associated_with_active_order"
                      text="This card is associated with an active order"
                    >
                      <template #activator="{ props }">
                        <div
                          v-bind="props"
                          class="d-inline"
                        >
                          <VBtn
                            color="error"
                            size="small"
                            disabled
                          >
                            Delete
                          </VBtn>
                        </div>
                      </template>
                    </v-tooltip>
                  </div>
                </div>
              </VCardText>
            </VCard>
          </div>
          <!-- No Payment methods -->
          <div
            v-else
            class="text-center"
          >
            <VIcon
              icon="tabler-credit-card-off"
              size="80"
            />
            <h5 class="text-h5 mt-5">
              No Payment Methods
            </h5>
          </div>
        </VCardText>
      </VCard>
    </VCol>

    <VCol cols="12">
      <!-- 👉 Shipping Address -->
      <VCard title="Shipping Address">
        <template #append>
          <VBtn
            size="small"
            @click="isEditAddressDialogVisible = true"
          >
            Edit
          </VBtn>
        </template>

        <VCardText>
          <VRow>
            <VCol cols="12">
              <div v-if="userDataLoading">
                <Skeleton
                  width="240px"
                  height="20px"
                  class="mb-2"
                ></Skeleton>
                <Skeleton
                  width="240px"
                  height="20px"
                  class="mb-2"
                ></Skeleton>
                <Skeleton
                  width="240px"
                  height="20px"
                  class="mb-2"
                ></Skeleton>
                <Skeleton
                  width="240px"
                  height="20px"
                  class="mb-2"
                ></Skeleton>
              </div>
              <VTable
                v-else
                class="billing-address-table"
              >
                <tr>
                  <td class="w-125px">
                    <h6 class="text-h6 text-no-wrap mb-2 w-100px">
                      Address:
                    </h6>
                  </td>
                  <td>
                    <p class="text-body-1 mb-0">
                      <span class="d-block">{{ currentShippingAddress.address_line_1 ?? '-' }}</span>
                      <span
                        v-if="currentShippingAddress.address_line_2"
                        class="d-block"
                      >
                        {{ currentShippingAddress.address_line_2 }}
                      </span>
                    </p>
                  </td>
                </tr>
                <tr>
                  <td class="w-125px">
                    <h6 class="text-h6 text-no-wrap mb-2">
                      City:
                    </h6>
                  </td>
                  <td>
                    <p class="text-body-1 mb-0">
                      {{ currentShippingAddress.city ?? '-' }}
                    </p>
                  </td>
                </tr>
                <tr>
                  <td class="w-125px">
                    <h6 class="text-h6 text-no-wrap mb-2">
                      State / Territory:
                    </h6>
                  </td>
                  <td>
                    <p class="text-body-1 mb-0">
                      {{ currentShippingAddress.state ?? '-' }}
                    </p>
                  </td>
                </tr>
                <tr>
                  <td class="w-125px">
                    <h6 class="text-h6 text-no-wrap">
                      Zipcode:
                    </h6>
                  </td>
                  <td>
                    <p class="text-body-1 mb-0">
                      {{ currentShippingAddress.zipcode ?? '-' }}
                    </p>
                  </td>
                </tr>
                <tr>
                  <td class="w-125px">
                    <h6 class="text-h6 text-no-wrap">
                      Country:
                    </h6>
                  </td>
                  <td>
                    <p class="text-body-1 mb-0">
                      {{ currentShippingAddress.country ?? '-' }}
                    </p>
                  </td>
                </tr>
              </VTable>
            </VCol>
          </VRow>
        </VCardText>
      </VCard>
    </VCol>

    <VCol cols="12">
      <!-- 👉 User Documents -->
      <VCard title="Documents">
        <VCardText>
          <!-- 👉 Government ID -->
          <VRow>
            <VCol
              cols="12"
              md="6"
            >
              <VCard
                title="Government ID"
                class="rounded-lg"
                border
              >
                <VCardText>
                  <Image
                    v-if="docGovtId.url"
                    :src="docGovtId.url"
                    class="mx-auto w-100"
                    image-class="rounded-lg w-100 object-fit-cover"
                    height="200"
                    preview
                  />
                  <VAlert
                    v-else
                    variant="tonal"
                    color="primary"
                    class="d-flex justify-center align-center"
                    style="height: 200px"
                  >
                    <div class="text-center">
                      <VIcon
                        size="40"
                        icon="tabler-photo"
                        class="text-primary mb-2"
                      />
                      <div class="text-base">
                        ID not uploaded
                      </div>
                    </div>
                  </VAlert>

                  <div class="d-flex flex-wrap gap-2 mt-6">
                    <VBtn
                      color="primary"
                      :loading="docUploading === 'government'"
                      :disabled="!isEmpty(docUploading)"
                      block
                      @click="inputGovtIdRef.click()"
                    >
                      <VIcon
                        icon="tabler-cloud-upload"
                        start
                      />
                      <span>Upload New ID</span>
                    </VBtn>
                    <input
                      ref="inputGovtIdRef"
                      type="file"
                      name="file"
                      accept="image/*"
                      hidden
                      @input="onGovtIdChange($event)"
                    />
                  </div>
                </VCardText>
              </VCard>
            </VCol>
            <!-- 👉 Selfie -->
            <VCol
              cols="12"
              md="6"
            >
              <VCard
                title="Selfie"
                class="rounded-lg"
                border
              >
                <VCardText>
                  <Image
                    v-if="docSelfie.url"
                    :src="docSelfie.url"
                    class="mx-auto w-100"
                    image-class="rounded-lg w-100 object-fit-cover"
                    height="200"
                    preview
                  />
                  <VAlert
                    v-else
                    variant="tonal"
                    color="primary"
                    class="d-flex justify-center align-center"
                    style="height: 200px"
                  >
                    <div class="text-center">
                      <VIcon
                        size="40"
                        icon="tabler-photo"
                        class="text-primary mb-2"
                      />
                      <div class="text-base">
                        Selfie not uploaded
                      </div>
                    </div>
                  </VAlert>
                  <div class="d-flex flex-wrap gap-2 mt-6">
                    <VBtn
                      color="primary"
                      block
                      :loading="docUploading === 'selfie'"
                      :disabled="!isEmpty(docUploading)"
                      @click="inputSelfieRef.click()"
                    >
                      <VIcon
                        icon="tabler-cloud-upload"
                        start
                      />
                      <span>Upload New Selfie</span>
                    </VBtn>
                    <input
                      ref="inputSelfieRef"
                      type="file"
                      name="file"
                      accept="image/*"
                      hidden
                      @input="onSelfieChange($event)"
                    />
                  </div>
                </VCardText>
              </VCard>
            </VCol>
          </VRow>
        </VCardText>
      </VCard>
    </VCol>
  </VRow>

  <!-- 👉 Primary card change confirm dialog -->
  <ConfirmDialog
    v-model:isDialogVisible="isCardSetAsPrimaryDialogVisible"
    confirmation-question="Are you sure you want to set this card as primary?"
    :loading="cardPrimaryLoading"
    @confirm="handlePrimaryCardChangeConfirmation"
  />

  <!-- 👉 Delete card confirm dialog -->
  <ConfirmDialog
    v-model:isDialogVisible="isCardDeleteDialogVisible"
    confirmation-question="Are you sure you want to delete this card?"
    :loading="cardDeleteLoading"
    @confirm="handleDeleteConfirmation"
  />

  <!-- 👉 Edit Address dialog -->
  <EditAddressDialog
    v-model:isDrawerOpen="isEditAddressDialogVisible"
    :shipping-address="currentShippingAddress"
    @updated="() => {
      isEditAddressDialogVisible = false
      userDetailsStore.fetchUserDetails(userId)
    }"
  />
</template>
