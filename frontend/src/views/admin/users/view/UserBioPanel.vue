<script setup>
import { isEmpty } from '@/@core/utils'
import { avatarText } from '@/@core/utils/formatters'
import ApiService from '@/services/ApiService'
import { useUserDetailsStore } from '@/store/admin/userDetails'
import { useGlobalData } from '@/store/global'
import { formattedPhoneNumber, resolveInitials } from '@/utils/helpers'
import Image from 'primevue/image'
import { computed } from 'vue'
import { useRoute } from 'vue-router'

const globalData = useGlobalData()
const { showSnackbar } = globalData
const userDetailsStore = useUserDetailsStore()
const { userData, loading, isSMSNotificationsEnabled } = storeToRefs(userDetailsStore)

const route = useRoute()
const userId = computed(() => route.params.userId)

const isUserInfoEditDialogVisible = ref(false)
const isChangeAccountStatusDialogVisible = ref(false)
const changeStatusLoading = ref(false)
const toggleSmsLoading = ref(false)

const resolveUserStatusVariant = stat => {
  if (stat === 1)
    return { color: 'success', label: 'Active' }
  if (stat === 0)
    return { color: 'secondary', label: 'Inactive' }

  return { color: 'primary', label: 'Unknown' }
}

async function handleStatusChangeConfirmation(isConfirmed) {
  if (isConfirmed) {
    await changeAccountStatus()
  }
}

const changeAccountStatus = async () => {
  try {
    changeStatusLoading.value = true

    const { data } = await ApiService.get(`/admin/user-account-status/${userId.value}`)

    if (data.status === 200) {
      isChangeAccountStatusDialogVisible.value = false
      showSnackbar(data.message)
      userDetailsStore.setProfileUpdated()
      await userDetailsStore.fetchUserDetails(userId.value)
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch(error) {
    console.error(error)
  } finally {
    changeStatusLoading.value = false
  }
}

const onChangeSMSNotifications = async () => {
  try {
    toggleSmsLoading.value = true

    const { data } = await ApiService.get(`/admin/update-user-sms-notification/${userId.value}`)

    if (data.status === 200) {
      showSnackbar(data.message)
      userDetailsStore.setProfileUpdated()
      await userDetailsStore.fetchUserDetails(userId.value)
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch(error) {
    console.error(error)
  } finally {
    toggleSmsLoading.value = false
  }
}
</script>

<template>
  <VRow>
    <!-- SECTION User Details -->
    <VCol cols="12">
      <VCard v-if="userData">
        <VCardText class="text-center pt-15">
          <!-- 👉 Avatar -->
          <Skeleton
            v-if="loading"
            height="140px"
            width="140px"
            class="mx-auto"
          />
          <VAvatar
            v-else
            rounded
            :size="140"
            :color="!userData?.selfie ? 'primary' : undefined"
            :variant="!userData?.selfie ? 'tonal' : undefined"
          >
            <Image
              v-if="userData?.selfie"
              :src="userData?.selfie"
              :height="140"
              preview
            />
            <span
              v-else
              class="text-5xl font-weight-medium"
            >
              {{ avatarText(userData?.full_name) }}
            </span>
          </VAvatar>

          <Skeleton
            v-if="loading"
            height="1.5rem"
            width="12rem"
            class="mt-4 mx-auto"
          />
          <!-- 👉 User fullName -->
          <h6
            v-else
            class="text-h4 mt-4"
          >
            {{ userData.full_name }}
          </h6>
        </VCardText>

        <VDivider />

        <!-- 👉 Details -->
        <VCardText>
          <p class="text-sm text-uppercase text-disabled">
            Details
          </p>

          <!-- 👉 User Details list -->
          <VList
            v-if="loading"
            class="card-list mt-2"
          >
            <VListItem
              v-for="i in 7"
              :key="i"
            >
              <VListItemTitle class="mb-1">
                <div class="d-flex justify-space-between">
                  <Skeleton
                    height="1rem"
                    width="6rem"
                    class="d-inline-block"
                  />
                  <Skeleton
                    height="1rem"
                    width="10rem"
                    class="d-inline-block"
                  />
                </div>
              </VListItemTitle>
            </VListItem>
          </VList>
          <VList
            v-else
            class="card-list mt-2"
          >
            <VListItem>
              <VListItemTitle>
                <div class="d-flex justify-space-between">
                  <h6 class="text-h6">
                    Full name:
                  </h6>
                  <div class="text-body-1">
                    {{ userData.full_name }}
                  </div>
                </div>
              </VListItemTitle>
            </VListItem>

            <VListItem>
              <VListItemTitle>
                <div class="d-flex justify-space-between">
                  <h6 class="text-h6">
                    Email:
                  </h6>
                  <div class="text-body-1">
                    {{ userData.email }}
                  </div>
                </div>
              </VListItemTitle>
            </VListItem>

            <VListItem>
              <VListItemTitle>
                <div class="d-flex justify-space-between">
                  <h6 class="text-h6">
                    Phone Number:
                  </h6>
                  <div class="text-body-1">
                    {{ formattedPhoneNumber(userData.phone_number) }}
                  </div>
                </div>
              </VListItemTitle>
            </VListItem>

            <VListItem>
              <VListItemTitle>
                <div class="d-flex justify-space-between">
                  <h6 class="text-h6">
                    Status:
                  </h6>
                  <div class="text-body-1">
                    <VChip
                      label
                      size="small"
                      :color="resolveUserStatusVariant(userData.user_account_status).color"
                      class="text-capitalize"
                    >
                      {{ resolveUserStatusVariant(userData.user_account_status).label }}
                    </VChip>
                  </div>
                </div>
              </VListItemTitle>
            </VListItem>

            <VListItem>
              <VListItemTitle>
                <div class="d-flex justify-space-between">
                  <h6 class="text-h6">
                    Date of Birth:
                  </h6>
                  <div class="text-body-1">
                    {{ userData.date_of_birth }}
                  </div>
                </div>
              </VListItemTitle>
            </VListItem>

            <VListItem>
              <VListItemTitle>
                <div class="d-flex justify-space-between">
                  <h6 class="text-h6">
                    Sex assigned at birth:
                  </h6>
                  <div class="text-body-1">
                    {{ userData.sex }}
                  </div>
                </div>
              </VListItemTitle>
            </VListItem>

            <VListItem>
              <VListItemTitle>
                <div class="d-flex justify-space-between">
                  <h6 class="text-h6">
                    State / Territory:
                  </h6>
                  <div class="text-body-1">
                    {{ userData.state }}
                  </div>
                </div>
              </VListItemTitle>
            </VListItem>

            <VListItem>
              <VListItemTitle>
                <div class="d-flex justify-space-between">
                  <h6 class="text-h6">
                    SMS Notifications:
                  </h6>
                  <div class="text-body-1">
                    <VSwitch
                      v-model="isSMSNotificationsEnabled"
                      inset
                      :loading="toggleSmsLoading"
                      @change="onChangeSMSNotifications"
                    />
                  </div>
                </div>
              </VListItemTitle>
            </VListItem>
          </VList>
        </VCardText>

        <!-- 👉 Edit and Suspend button -->
        <VCardText
          v-if="!loading"
          class="d-flex justify-center"
        >
          <VBtn
            variant="elevated"
            class="me-4"
            @click="isUserInfoEditDialogVisible = true"
          >
            Edit
          </VBtn>

          <VBtn
            variant="tonal"
            :color="userData.user_account_status === 1 ? 'error' : 'success'"
            @click="isChangeAccountStatusDialogVisible = true"
          >
            {{ userData.user_account_status === 1 ? 'Disable Account' : 'Enable Account' }}
          </VBtn>
        </VCardText>
      </VCard>

      <!-- 👉 Affiliate By -->
      <VCard
        v-if="!isEmpty(userData?.affiliate_user)"
        class="mt-6"
        title="Affiliate By"
      >
        <VCardText>
          <div class="d-flex align-center py-1">
            <VAvatar
              v-if="userData.affiliate_user.profile_picture"
              variant="tonal"
              size="40"
              class="me-3"
              :image="userData.affiliate_user.profile_picture"
              rounded
            />
            <VAvatar
              v-else
              variant="tonal"
              size="40"
              class="me-3"
              rounded
            >
              {{ resolveInitials(userData.affiliate_user.first_name + ' ' + userData.affiliate_user.last_name) }}
            </VAvatar>
            <div>
              <h6 class="text-base">
                {{ userData.affiliate_user.first_name }} {{ userData.affiliate_user.last_name }}
              </h6>
              <div class="d-flex flex-column">
                <span class="text-body-2">{{ userData.affiliate_user.email }}</span>
                <span class="text-body-2">{{ formattedPhoneNumber(userData.affiliate_user.phone_number) }}</span>
              </div>
            </div>
          </div>
          <VBtn
            variant="tonal"
            size="small"
            block
            class="mt-2"
            :to="{
              name: 'admin-affiliate-user-details',
              params: { userId: userData.affiliate_user.id },
            }"
          >
            View Profile
          </VBtn>
        </VCardText>
      </VCard>
    </VCol>
    <!-- !SECTION -->
  </VRow>

  <!-- 👉 Edit user info dialog -->
  <UserInfoEditDrawer v-model:isDrawerOpen="isUserInfoEditDialogVisible" />

  <!-- 👉 Change Account status confirm dialog -->
  <ConfirmDialog
    v-model:isDialogVisible="isChangeAccountStatusDialogVisible"
    :confirmation-question="`Are you sure you want to ${userData.user_account_status === 1 ? 'disable' : 'enable'} this account?`"
    :loading="changeStatusLoading"
    @confirm="handleStatusChangeConfirmation"
  />
</template>

<style lang="scss" scoped>
.card-list {
  --v-card-list-gap: 0.75rem;
}

.text-capitalize {
  text-transform: capitalize !important;
}
</style>
