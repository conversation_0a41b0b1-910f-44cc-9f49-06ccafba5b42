<script setup>
import { isEmptyObject } from '@/@core/utils'
import { confirmedValidator, passwordValidator, requiredValidator } from '@/@core/utils/validators'
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { processErrors } from '@/utils/errorHandler'
import { computed, ref } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const userId = computed(() => route.params.userId)
const { showSnackbar } = useGlobalData()

const isNewPasswordVisible = ref(false)
const isConfirmPasswordVisible = ref(false)
const serverErrors = ref([])
const isLoading = ref(false)
const isLinkBtnLoading = ref(false)
const formRef = ref()

const form = ref({
  newPassword: '',
  confirmPassword: '',
})

const handleSubmit = async () => {
  formRef.value?.validate().then(async ({ valid }) => {
    if (valid) {
      isLoading.value = true

      const postData = {
        user_id: userId.value,
        password: form.value.newPassword,
        password_confirmation: form.value.confirmPassword,
      }

      resetPassword(postData)
    }
  })
}

const resetPassword = async values => {
  try {
    isLoading.value = true
    serverErrors.value = []

    let { data } = await ApiService.post('/admin/change-user-account-password', values)

    if (data.status === 200) {
      showSnackbar(data.message)
      formRef.value.reset()
    } else {
      serverErrors.value = processErrors(data)
    }
  } catch (error) {
    serverErrors.value = processErrors(error)
    console.log(error)
  } finally {
    isLoading.value = false
  }
}

const sendResetPasswordLink = async () => {
  try {
    isLinkBtnLoading.value = true
    serverErrors.value = []

    let { data } = await ApiService.get(`/admin/send-reset-password-link/${userId.value}`)

    if (data.status === 200) {
      showSnackbar(data.message)
      formRef.value.reset()
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    console.log(error)
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    isLinkBtnLoading.value = false
  }
}
</script>

<template>
  <VRow>
    <VCol cols="12">
      <!-- 👉 Change password -->
      <VCard title="Change Password">
        <VCardText>
          <VAlert
            variant="tonal"
            color="info"
            class="mb-4"
          >
            <VAlertTitle class="mb-2">
              Ensure that these requirements are met
            </VAlertTitle>
            <ul class="mb-0">
              <li>Password must be at least 8 characters long</li>
              <li>Include at least one uppercase letter or number or symbol</li>
            </ul>
          </VAlert>

          <VAlert
            v-if="serverErrors.length > 0"
            type="error"
            variant="tonal"
            title="Validation failed!"
            class="mb-6"
            closable
          >
            <ul class="mb-0">
              <li
                v-for="error in serverErrors"
                :key="error"
                class="mb-0"
              >
                {{ error }}
              </li>
            </ul>
          </VAlert>

          <VForm
            ref="formRef"
            @submit.prevent="handleSubmit"
          >
            <VRow>
              <VCol
                cols="12"
                md="6"
              >
                <AppTextField
                  v-model="form.newPassword"
                  label="New Password"
                  placeholder="············"
                  :type="isNewPasswordVisible ? 'text' : 'password'"
                  :append-inner-icon="isNewPasswordVisible ? 'tabler-eye-off' : 'tabler-eye'"
                  :rules="[requiredValidator, passwordValidator]"
                  @click:append-inner="isNewPasswordVisible = !isNewPasswordVisible"
                />
              </VCol>
              <VCol
                cols="12"
                md="6"
              >
                <AppTextField
                  v-model="form.confirmPassword"
                  label="Confirm Password"
                  placeholder="············"
                  :type="isConfirmPasswordVisible ? 'text' : 'password'"
                  :append-inner-icon="isConfirmPasswordVisible ? 'tabler-eye-off' : 'tabler-eye'"
                  :rules="[requiredValidator, confirmedValidator(form.confirmPassword, form.newPassword)]"
                  @click:append-inner="isConfirmPasswordVisible = !isConfirmPasswordVisible"
                />
              </VCol>

              <VCol cols="12">
                <VBtn
                  type="submit"
                  :loading="isLoading"
                  :disabled="isLinkBtnLoading"
                >
                  {{ isLoading ? 'Please wait...' : 'Change Password' }}
                </VBtn>
              </VCol>
            </VRow>
          </VForm>

          <div class="mt-6 mb-2 d-flex align-items-center">
            <VDivider class="mt-2 mx-2" />
            <span class="text-muted text-caption">OR</span>
            <VDivider class="mt-2 mx-2" />
          </div>

          <VBtn
            class="mt-4"
            color="primary"
            variant="elevated"
            :loading="isLinkBtnLoading"
            :disabled="isLoading"
            @click="sendResetPasswordLink"
          >
            Send Reset Password Link
          </VBtn>
        </VCardText>
      </VCard>
    </VCol>
  </VRow>
</template>
