<script setup>
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { processErrors } from '@/utils/errorHandler'
import Skeleton from 'primevue/skeleton'
import { computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { resolveSubscriptionStatusColor } from '@/utils/admin'

const route = useRoute()
const userId = computed(() => route.params.userId)
const { showSnackbar } = useGlobalData()

const skeletonLoading = ref(false)
const activeSubscriptions = ref([])

onMounted(async () => {
  await fetchActiveSubscriptions()
})

async function fetchActiveSubscriptions() {
  try {
    skeletonLoading.value = true

    const { data } = await ApiService.get(`/admin/user-active-subscriptions/${userId.value}`)

    if (data.status === 200) {
      activeSubscriptions.value = data.activeSubscriptions
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    console.error(error)
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    skeletonLoading.value = false
  }
}
</script>

<template>
  <!-- 👉 Active Subscriptions Skeleton Loader -->
  <VRow v-if="skeletonLoading">
    <VCol
      v-for="s in 2"
      :key="s"
      cols="12"
    >
      <VCard>
        <VCardTitle class="pa-6">
          <Skeleton
            height="1.5rem"
            width="10rem"
          />
        </VCardTitle>
        <VCardText>
          <VRow>
            <VCol cols="12">
              <VRow class="mb-5">
                <VCol
                  cols="12"
                  sm="2"
                  lg="2"
                >
                  <Skeleton
                    height="80px"
                    width="80px"
                  />
                </VCol>
                <VCol
                  cols="12"
                  sm="4"
                >
                  <Skeleton
                    height="1.4rem"
                    width="8rem"
                    class="mb-2"
                  />
                  <Skeleton
                    height="1rem"
                    width="10rem"
                    class="mb-2"
                  />
                  <Skeleton
                    height="1rem"
                    width="4rem"
                  />
                </VCol>
                <VCol
                  cols="12"
                  sm="3"
                >
                  <Skeleton
                    height="1.2rem"
                    width="8rem"
                    class="mb-2"
                  />
                  <Skeleton
                    height="1.2rem"
                    width="10rem"
                    class="mb-2"
                  />
                </VCol>
                <VCol
                  cols="12"
                  sm="3"
                >
                  <Skeleton
                    height="1.2rem"
                    width="8rem"
                    class="mb-2"
                  />
                  <Skeleton
                    height="1.2rem"
                    width="10rem"
                    class="mb-2"
                  />
                </VCol>
              </VRow>
            </VCol>

            <VCol cols="12">
              <div class="d-flex flex-column flex-sm-row gap-4">
                <Skeleton
                  height="2.3rem"
                  width="10rem"
                />
                <Skeleton
                  height="2.3rem"
                  width="10rem"
                />
              </div>
            </VCol>
          </VRow>
        </VCardText>
      </VCard>
    </VCol>
  </VRow>
  <!-- 👉 Active Subscriptions -->
  <VRow v-else-if="activeSubscriptions.length > 0">
    <VCol
      v-for="subscription in activeSubscriptions"
      :key="subscription.id"
      cols="12"
    >
      <VCard :title="`${subscription.category_name} Plan`">
        <VCardText>
          <VRow>
            <VCol cols="12">
              <VRow class="mb-5">
                <VCol
                  cols="12"
                  sm="2"
                  lg="2"
                >
                  <VAvatar
                    rounded="sm"
                    size="80"
                    :image="subscription.product_img"
                  />
                </VCol>
                <VCol
                  cols="12"
                  sm="4"
                >
                  <h6 class="text-h5 font-weight-medium text-high-emphasis mb-1">
                    {{ subscription.product_name }}
                  </h6>
                  <p
                    v-if="String(subscription.category_name)?.toLowerCase() === 'wl'"
                    class="text-sm"
                  >
                    <span class="d-block">
                      {{ subscription.strength }} {{ subscription.strength_unit }} / weekly
                    </span>
                  </p>
                  <p
                    v-else
                    class="text-sm"
                  >
                    <span class="d-block">
                      {{ subscription.strength }} {{ subscription.strength_unit }} x {{ subscription.qty * subscription.subscription_interval }} units
                    </span>
                    <span class="d-block"> ({{ subscription.subscription_interval * 30 }}-day supply)</span>
                  </p>
                </VCol>
                <VCol
                  cols="12"
                  sm="3"
                >
                  <h6 class="text-base font-weight-medium text-high-emphasis mb-2">
                    Subscription Status
                  </h6>
                  <p class="text-sm">
                    <VChip
                      :color="resolveSubscriptionStatusColor(subscription.status)"
                      size="small"
                      label
                    >
                      {{ subscription.order_status }}
                    </VChip>
                  </p>
                </VCol>
                <VCol
                  v-if="subscription.current_next_refill_date"
                  cols="12"
                  sm="3"
                >
                  <h6 class="text-base font-weight-medium text-high-emphasis mb-2">
                    Next Refill Date
                  </h6>
                  <p class="text-sm">
                    {{ subscription.current_next_refill_date ?? '-' }}
                  </p>
                </VCol>
                <VCol
                  v-if="subscription.next_followup_visit_date"
                  cols="12"
                  sm="3"
                >
                  <h6 class="text-base font-weight-medium text-high-emphasis mb-2">
                    Next Follow-up Date
                  </h6>
                  <p class="text-sm">
                    {{ subscription.next_followup_visit_date }}
                  </p>
                </VCol>
              </VRow>

              <div
                v-if="String(subscription.category_name)?.toLowerCase() !== 'wl'"
                class="d-flex flex-column flex-sm-row flex-wrap gap-3"
              >
                <VAlert variant="tonal">
                  <VAlertTitle class="mb-2">
                    <span class="text-h4">{{ subscription.total_refills_allowed }}</span>
                  </VAlertTitle>
                  <span class="text-body-1">Total Refills</span>
                </VAlert>
                <VAlert variant="tonal">
                  <VAlertTitle class="mb-2">
                    <span class="text-h4">{{ subscription.total_refill_used }}</span>
                  </VAlertTitle>
                  <span class="text-body-1">Used Refills</span>
                </VAlert>
                <VAlert variant="tonal">
                  <VAlertTitle class="mb-2">
                    <span class="text-h4">{{ subscription.remain_refill }}</span>
                  </VAlertTitle>
                  <span class="text-body-1">Remaining Refills</span>
                </VAlert>
              </div>
              <VDivider v-else />
            </VCol>

            <VCol cols="12">
              <div class="d-flex flex-column flex-sm-row gap-4">
                <VBtn
                  :to="{
                    name: `admin-${subscription.category_name?.toLowerCase()}-manage-subscription`,
                    params: { subscriptionId: subscription.id },
                    query: { ...route.query },
                  }"
                >
                  Manage Subscription
                </VBtn>

                <!--
                  <VBtn
                  color="error"
                  variant="tonal"
                  >
                  Cancel Subscription
                  </VBtn>
                -->
              </div>
            </VCol>
          </VRow>
        </VCardText>
      </VCard>
    </VCol>
  </VRow>

  <!-- 👉 No Active Subscriptions -->
  <VRow v-else>
    <VCol cols="12">
      <VCard>
        <VCardText class="text-center">
          <VIcon
            icon="tabler-credit-card-off"
            size="80"
          />
          <h5 class="text-h5 mt-5">
            No Active Subscriptions
          </h5>
        </VCardText>
      </VCard>
    </VCol>
  </VRow>
</template>
