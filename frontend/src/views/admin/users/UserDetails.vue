<script setup>
import { useGlobalData } from '@/store/global'
import { computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import UserBioPanel from './view/UserBioPanel.vue'
import UserTabAccount from './view/UserTabAccount.vue'
import UserTabSubscriptions from './view/UserTabSubscriptions.vue'
import UserTabOrders from './view/UserTabOrders.vue'
import UserTabSecurity from './view/UserTabSecurity.vue'
import UserTabAccountLogs from './view/UserTabAccountLogs.vue'
import UserTabLoginActivity from './view/UserTabLoginActivity.vue'
import UserTabPendingVisits from './view/UserTabPendingVisits.vue'
import { useUserDetailsStore } from '@/store/admin/userDetails'
import { storeToRefs } from 'pinia'

const route = useRoute()
const userId = computed(() => route.params.userId)
const { showSnackbar } = useGlobalData()
const userDetailsStore = useUserDetailsStore()
const { userData, errors } = storeToRefs(userDetailsStore)

const userTab = ref(null)

const tabs = [
  {
    icon: 'tabler-receipt-2',
    title: 'Subscriptions',
  },
  {
    icon: 'tabler-packages',
    title: 'Orders',
  },
  {
    icon: 'tabler-box',
    title: 'Pending Visits',
  },
  {
    icon: 'tabler-user-circle',
    title: 'Account',
  },
  {
    icon: 'tabler-lock',
    title: 'Security',
  },
  {
    icon: 'tabler-logs',
    title: 'Account Logs',
  },
  {
    icon: 'tabler-logs',
    title: 'Login Activity',
  },
]

onMounted(async () => {
  await userDetailsStore.fetchUserDetails(userId.value)
  if (errors.value.length > 0) {
    showSnackbar(errors.value[0].message, 'error')
  }
})
</script>

<template>
  <VRow>
    <VCol
      cols="12"
      md="5"
      lg="4"
    >
      <UserBioPanel />
    </VCol>

    <VCol
      cols="12"
      md="7"
      lg="8"
    >
      <VTabs
        v-model="userTab"
        class="v-tabs-pill"
      >
        <VTab
          v-for="tab in tabs"
          :key="tab.icon"
        >
          <VIcon
            :size="18"
            :icon="tab.icon"
            class="me-1"
          />
          <span>{{ tab.title }}</span>
        </VTab>
      </VTabs>

      <VWindow
        v-model="userTab"
        class="mt-6 disable-tab-transition"
        :touch="false"
      >
        <VWindowItem>
          <UserTabSubscriptions />
        </VWindowItem>

        <VWindowItem>
          <UserTabOrders />
        </VWindowItem>

        <VWindowItem>
          <UserTabPendingVisits />
        </VWindowItem>

        <VWindowItem>
          <UserTabAccount />
        </VWindowItem>

        <VWindowItem>
          <UserTabSecurity />
        </VWindowItem>

        <VWindowItem>
          <UserTabAccountLogs />
        </VWindowItem>

        <VWindowItem>
          <UserTabLoginActivity />
        </VWindowItem>
      </VWindow>
    </VCol>
  </VRow>
</template>
