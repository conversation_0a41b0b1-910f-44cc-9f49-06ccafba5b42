<script setup>
import { isEmpty } from '@/@core/utils'
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { resolvePrescriptionStatus, resolveSubscriptionStatusColor } from '@/utils/admin'
import { calculateStartIndex, formattedPhoneNumber, resolveInitials } from '@/utils/helpers'
import { refDebounced } from '@vueuse/core'
import { computed, onMounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import useListCount from '@/composables/useListCount'
import dayjs from 'dayjs'
import isoWeek from 'dayjs/plugin/isoWeek'

dayjs.extend(isoWeek)

const globalData = useGlobalData()
const { showSnackbar } = globalData
const route = useRoute()
const router = useRouter()
const { loading: loadingCount, getListCount, countData } = useListCount()

const searchQuery = ref(route.query.search ?? null)
const debouncedSearchQuery = refDebounced(searchQuery, 300)
const selectedStatus = ref(!isNaN(parseInt(route.query.status)) ? parseInt(route.query.status) : null)
const filterDateRange = ref(null)
const filterCustomDateRange = ref(null)
const filterCategory = ref(route.query.category ?? null)
const fromDate = ref(null)
const toDate = ref(null)
const rowPerPage = ref(10)
const currentPage = ref(!isNaN(parseInt(route.query.page)) ? parseInt(route.query.page) : 1)
const totalPage = ref(1)
const totalItems = ref(0)
const items = ref([])
const sortByColumnName = ref(route.query.sort_by ?? 'created_at')
const sortDirection = ref(route.query.sort_dir ?? 'desc')
const skeletonLoading = ref(true)

const categoryOptions = [
  { title: 'ED', value: 'ED' },
  { title: 'Hair Treatment', value: 'HL' },
  { title: 'Weight Loss', value: 'WL' },
]

const dateFilterOptions = [
  { title: 'Today', value: 'today' },
  { title: 'Yesterday', value: 'yesterday' },
  { title: 'This Week', value: 'this_week' },
  { title: 'Previous Week', value: 'last_week' },
  { title: 'This Month', value: 'this_month' },
  { title: 'Previous Month', value: 'last_month' },
  { title: 'This Year', value: 'this_year' },
  { title: 'Previous Year', value: 'last_year' },
  { title: 'Custom', value: 'custom' },
]

const userStats = computed(() => {
  return [
    {
      title: 'Total',
      stats: countData.value?.total_subscription || 0,
    },
    {
      title: 'Active',
      stats: countData.value?.active_subscription || 0,
    },
    {
      title: 'On-Hold',
      stats: countData.value?.on_hold_subscription || 0,
    },
    {
      title: 'Canceled',
      stats: countData.value?.canceled_subscription || 0,
    },
    {
      title: 'Expired',
      stats: countData.value?.expired_subscription || 0,
    },
  ]
})

onMounted(() => {
  fetchItems()
  getListCount('all_subscriptions')
})

const updateRoute = () => {
  router.replace({
    query: {
      page: currentPage.value,
      per_page: rowPerPage.value,
      status: selectedStatus.value,
      search: searchQuery.value,
      sort_by: sortByColumnName.value,
      sort_dir: sortDirection.value,
      from_date: fromDate.value,
      to_date: toDate.value,
      category: filterCategory.value,
    },
  })
}

watch(filterDateRange, (newVal, oldVal) => {
  if (newVal === oldVal) {
    return
  }

  const today = dayjs()

  switch (newVal) {
  case 'today':
    fromDate.value = today.format('M/D/YYYY')
    toDate.value = today.format('M/D/YYYY')
    break

  case 'yesterday':
    fromDate.value = today.subtract(1, 'day').format('M/D/YYYY')
    toDate.value = today.subtract(1, 'day').format('M/D/YYYY')
    break

  case 'this_week':
    fromDate.value = today.startOf('week').format('M/D/YYYY') // Week starts on Sunday
    toDate.value = today.format('M/D/YYYY')
    break

  case 'last_week':
    fromDate.value = today.subtract(1, 'week').startOf('week').format('M/D/YYYY') // Week starts on Sunday
    toDate.value = today.subtract(1, 'week').endOf('week').format('M/D/YYYY')
    break

  case 'this_month':
    fromDate.value = today.startOf('month').format('M/D/YYYY')
    toDate.value = today.format('M/D/YYYY')
    break

  case 'last_month':
    fromDate.value = today.subtract(1, 'month').startOf('month').format('M/D/YYYY')
    toDate.value = today.subtract(1, 'month').endOf('month').format('M/D/YYYY')
    break

  case 'this_year':
    fromDate.value = today.startOf('year').format('M/D/YYYY')
    toDate.value = today.format('M/D/YYYY')
    break

  case 'last_year':
    fromDate.value = today.subtract(1, 'year').startOf('year').format('M/D/YYYY')
    toDate.value = today.subtract(1, 'year').endOf('year').format('M/D/YYYY')
    break

  case 'custom':
    // Ensure `fromDate` and `toDate` are set separately in your UI logic
    break

  default:
    fromDate.value = null
    toDate.value = null
  }
})

watch(filterCustomDateRange, () => {
  if (isEmpty(filterCustomDateRange.value)) {
    fromDate.value = null
    toDate.value = null

    return
  }

  const dateArr = filterCustomDateRange.value.split(' to ')

  if (dateArr.length === 1) {
    fromDate.value = dateArr[0] || null
    toDate.value = dateArr[0] || null
  } else if (dateArr.length === 2) {
    fromDate.value = dateArr[0] || null
    toDate.value = dateArr[1] || null
  } else {
    fromDate.value = null
    toDate.value = null
  }
})

watch([debouncedSearchQuery, selectedStatus, fromDate, toDate, rowPerPage, filterCategory], () => {
  currentPage.value = 1
  fetchItems()
})

watch([currentPage, sortByColumnName, sortDirection], () => {
  fetchItems()
})

const fetchItems = async () => {
  skeletonLoading.value = true

  try {
    const { data } = await ApiService.post('/admin/all-subscriptions', {
      searchQuery: searchQuery.value,
      perPage: rowPerPage.value,
      page: currentPage.value,
      sortByColumnName: sortByColumnName.value,
      isSortDirDesc: sortDirection.value,
      from_date: fromDate.value,
      to_date: toDate.value,
      status: selectedStatus.value,
      category_name: filterCategory.value,
    })

    if (data.status === 200) {
      const pagedData = data.subscriptions
      let startIndex = calculateStartIndex(pagedData.current_page, pagedData.per_page)
      if (currentPage.value > 1) {
        startIndex = (currentPage.value - 1) * rowPerPage.value + 1
      }

      items.value = pagedData.records.map((d, index) => ({
        ...d,
        sno: index + startIndex,
      }))
      totalPage.value = pagedData.totalPage == 0 ? 1 : pagedData.totalPage
      totalItems.value = pagedData.totalRecords
      updateRoute()
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    console.error(error)
    if (error.response.data.message) {
      showSnackbar(error.response.data.message, 'error')
    } else if (error.response.message) {
      showSnackbar(error.response.message, 'error')
    }
  } finally {
    skeletonLoading.value = false
  }
}

const sortColumn = columnName => {
  if (sortByColumnName.value !== '' && sortByColumnName.value === columnName && sortDirection.value === 'asc') {
    sortDirection.value = 'desc'
  } else {
    sortDirection.value = 'asc'
  }

  sortByColumnName.value = columnName
}

// 👉 search filters
const status = [
  {
    title: 'On Hold',
    value: 0,
  },
  {
    title: 'Active',
    value: 1,
  },
  {
    title: 'Canceled',
    value: 2,
  },
  {
    title: 'Expired',
    value: 3,
  },
]

// 👉 Computing pagination data
const paginationData = computed(() => {
  const currentPageValue = currentPage.value <= 0 ? 1 : currentPage.value

  const firstIndex = items.value.length
    ? (currentPageValue - 1) * rowPerPage.value + 1
    : 0

  const lastIndex = items.value.length + (currentPageValue - 1) * rowPerPage.value

  return `Showing ${firstIndex} to ${lastIndex} of ${totalItems.value} entries`
})
</script>

<template>
  <section>
    <VRow class="match-height">
      <VCol
        cols="12"
        class="w-100"
      >
        <div class="d-flex flex-wrap gap-4">
          <VCard
            v-for="meta in userStats"
            :key="meta.title"
            class="flex-grow-1"
          >
            <VCardText class="d-flex justify-space-between py-3">
              <div>
                <span v-if="loadingCount">
                  <Skeleton
                    width="10rem"
                    height="1.4rem"
                  ></Skeleton>
                </span>
                <span
                  v-else
                  class="text-sm text-uppercase font-weight-bold text-muted"
                >{{ meta.title }}</span>
                <div class="d-flex align-center gap-2 my-1">
                  <h6 v-if="loadingCount">
                    <Skeleton
                      width="1rem"
                      height="1.5rem"
                    ></Skeleton>
                  </h6>
                  <h5
                    v-else
                    class="text-h5 font-weight-bold"
                  >
                    {{ meta.stats }}
                  </h5>
                </div>
              </div>
            </VCardText>
          </VCard>
        </div>
      </VCol>

      <VCol cols="12">
        <VCard title="Filter">
          <!-- 👉 Filters -->
          <VCardText>
            <VRow>
              <VCol
                cols="12"
                sm="3"
              >
                <AppSelect
                  v-model="filterCategory"
                  label="Category"
                  :items="categoryOptions"
                  clearable
                  clear-icon="tabler-x"
                  placeholder="Select"
                />
              </VCol>
              <VCol
                cols="12"
                sm="3"
              >
                <AppSelect
                  v-model="selectedStatus"
                  label="Subscription Status"
                  :items="status"
                  clearable
                  clear-icon="tabler-x"
                  placeholder="Select"
                />
              </VCol>
              <VCol
                cols="12"
                sm="3"
              >
                <AppSelect
                  v-model="filterDateRange"
                  label="Filter By Date"
                  :items="dateFilterOptions"
                  clearable
                  clear-icon="tabler-x"
                  placeholder="Select"
                />
              </VCol>
              <VCol
                v-if="filterDateRange === 'custom'"
                cols="12"
                sm="3"
              >
                <AppDateTimePicker
                  v-model="filterCustomDateRange"
                  label="Custom Date Range"
                  placeholder="Select Date Range"
                  variant="outlined"
                  :config="{
                    mode: 'range',
                    dateFormat: 'm/d/Y',
                    onClose: function (selectedDates, dateStr, instance) {
                      if (selectedDates.length === 1) {
                        instance.setDate([selectedDates[0], selectedDates[0]], true)
                      }
                    },
                    maxDate: new Date(),
                    minDate: (new Date()).setFullYear((new Date()).getFullYear() - 120),
                  }"
                  clearable
                  clear-icon="tabler-x"
                />
              </VCol>
            </VRow>
          </VCardText>

          <VDivider />

          <VCardText class="d-flex flex-wrap py-4 gap-4">
            <div
              class="me-3"
              style="width: 80px"
            >
              <VSelect
                v-model="rowPerPage"
                density="compact"
                variant="outlined"
                :items="[10, 20, 30, 50]"
              />
            </div>

            <VSpacer />

            <div class="d-flex align-center flex-wrap gap-4">
              <!-- 👉 Search  -->
              <div style="width: 23rem">
                <VTextField
                  v-model="searchQuery"
                  placeholder="Search"
                  density="compact"
                  clearable
                  clear-icon="tabler-x"
                />
              </div>
            </div>
          </VCardText>

          <VDivider />

          <VTable class="text-no-wrap">
            <!-- 👉 table head -->
            <thead class="text-uppercase">
              <tr>
                <th scope="col">
                  #
                </th>
                <th scope="col">
                  Subscription Id
                </th>
                <th scope="col">
                  Product
                </th>
                <th scope="col">
                  User
                </th>
                <th scope="col">
                  Category
                </th>
                <th scope="col">
                  Affiliate by
                </th>
                <th scope="col">
                  Status
                </th>
                <th scope="col">
                  Prescription Validity
                </th>
                <th
                  scope="col"
                  class="cursor-pointer"
                  @click="sortColumn('created_at')"
                >
                  <div class="w-125px">
                    Created at
                    <span class="d-grid float-right">
                      <VIcon
                        size="10"
                        icon="tabler-chevron-up"
                      />
                      <VIcon
                        size="10"
                        icon="tabler-chevron-down"
                      />
                    </span>
                  </div>
                </th>
                <th
                  scope="col"
                  class="cursor-pointer"
                  @click="sortColumn('updated_at')"
                >
                  <div class="w-125px">
                    Updated at
                    <span class="d-grid float-right">
                      <VIcon
                        size="10"
                        icon="tabler-chevron-up"
                      />
                      <VIcon
                        size="10"
                        icon="tabler-chevron-down"
                      />
                    </span>
                  </div>
                </th>
                <th scope="col">
                  Actions
                </th>
              </tr>
            </thead>

            <!-- 👉 table body -->
            <tbody v-if="skeletonLoading">
              <tr
                v-for="n in 10"
                :key="n"
              >
                <td
                  v-for="i in 10"
                  :key="i"
                >
                  <Skeleton
                    width="6rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
              </tr>
            </tbody>
            <tbody v-else>
              <tr
                v-for="item in items"
                :key="item.id"
                style="height: 3.75rem"
              >
                <!-- 👉 NO -->
                <td>
                  {{ item.sno }}
                </td>

                <!-- 👉 Subscription ID -->
                <td>
                  <RouterLink
                    :to="{
                      name: `admin-${item.category_name.toLowerCase()}-manage-subscription`,
                      params: { subscriptionId: item.id },
                      query: { ...route.query },
                    }"
                    class="dt-link text-base font-weight-medium"
                  >
                    {{ item.subscription_reference_id }}
                  </RouterLink>
                </td>

                <!-- 👉 Subscription -->
                <td class="title">
                  <div class="d-flex align-center py-1">
                    <VAvatar
                      v-if="item.product_img"
                      variant="tonal"
                      size="40"
                      class="me-3"
                      :image="item.product_img"
                      rounded
                    />
                    <VAvatar
                      v-else
                      variant="tonal"
                      size="40"
                      class="me-3"
                      rounded
                    >
                      {{ resolveInitials(item.product_name) }}
                    </VAvatar>
                    <div>
                      <h6 class="text-base">
                        {{ item.product_name }}
                      </h6>
                      <div
                        v-if="item.category_name.toUpperCase() === 'WL'"
                        class="d-flex flex-column"
                      >
                        <span class="d-block text-body-2">
                          {{ item.strength }} {{ item.strength_unit }} / weekly
                        </span>
                      </div>
                      <div
                        v-else
                        class="d-flex flex-column"
                      >
                        <span class="d-block text-body-2">
                          {{ item.strength }} {{ item.strength_unit }} x {{ item.qty * item.subscription_interval }} units
                        </span>
                        <span class="d-block text-body-2">({{ item.subscription_interval * 30 }}-day supply)</span>
                      </div>
                      <div
                        v-if="item.pharmacy_name"
                        class="text-body-2"
                      >
                        {{ item.pharmacy_name }}
                      </div>
                    </div>
                  </div>
                </td>

                <!-- 👉 User -->
                <td class="title">
                  <RouterLink
                    :to="{
                      name: 'admin-user-details',
                      params: { userId: item.user_id },
                    }"
                    class="d-flex align-center py-1"
                  >
                    <div>
                      <h6 class="text-base">
                        {{ item.full_name }}
                      </h6>
                      <div class="d-flex flex-column">
                        <span class="text-body-2">{{ item.email }}</span>
                        <span class="text-body-2">{{ formattedPhoneNumber(item.phone_number) }}</span>
                      </div>
                    </div>
                  </RouterLink>
                </td>

                <!-- 👉 Category -->
                <td>{{ item.category_name }}</td>

                <!-- 👉 Affiliate by -->
                <td class="title">
                  <RouterLink
                    v-if="!isEmpty(item.affiliate_user_details)"
                    :to="{
                      name: 'admin-affiliate-user-details',
                      params: { userId: item.affiliate_user_details.id },
                    }"
                    class="d-block"
                  >
                    <div class="d-flex align-center py-1">
                      <VAvatar
                        v-if="item.affiliate_user_details.profile_picture"
                        variant="tonal"
                        size="40"
                        class="me-3"
                        :image="item.affiliate_user_details.profile_picture"
                        rounded
                      />
                      <VAvatar
                        v-else
                        variant="tonal"
                        size="40"
                        class="me-3"
                        rounded
                      >
                        {{ resolveInitials(item.affiliate_user_details.first_name + ' ' + item.affiliate_user_details.last_name) }}
                      </VAvatar>
                      <div>
                        <h6 class="text-base">
                          {{ item.affiliate_user_details.first_name }} {{ item.affiliate_user_details.last_name }}
                        </h6>
                        <div class="d-flex flex-column">
                          <span class="text-body-2">{{ item.affiliate_user_details.email }}</span>
                          <span class="text-body-2">{{ formattedPhoneNumber(item.affiliate_user_details.phone_number) }}</span>
                        </div>
                      </div>
                    </div>
                  </RouterLink>
                  <span v-else> - </span>
                </td>

                <!-- 👉 Subscription Status -->
                <td>
                  <VChip
                    label
                    :color="resolveSubscriptionStatusColor(item.status)"
                    size="small"
                    class="text-capitalize"
                  >
                    {{ item.subscription_status }}
                  </VChip>
                </td>

                <!-- 👉 Prescription Validity -->
                <td class="pt-1">
                  <VChip
                    v-if="item.is_prescription_validity_status === 1 || item.is_prescription_validity_status === 2"
                    label
                    :color="resolvePrescriptionStatus(item.is_prescription_validity_status).color"
                    size="small"
                    class="text-capitalize"
                  >
                    {{ resolvePrescriptionStatus(item.is_prescription_validity_status).label }}
                  </VChip>
                  <span v-else> {{ item.category_name === 'WL' ? 'N/A' : '-' }} </span>
                  <div
                    v-if="item.is_prescription_validity_status === 1"
                    class="text-body-2 mt-1"
                  >
                    {{ item.remaining_prescription_validity_days }} days left
                  </div>
                </td>

                <!-- 👉 Created at -->
                <td>
                  <div class="d-flex flex-column">
                    <span>{{ item.created_date }}</span>
                    <span>{{ item.created_time }}</span>
                  </div>
                </td>

                <!-- 👉 Updated at -->
                <td>
                  <div
                    v-if="item.updated_date !== item.created_date || item.updated_time !== item.created_time"
                    class="d-flex flex-column"
                  >
                    <span>{{ item.updated_date }}</span>
                    <span>{{ item.updated_time }}</span>
                  </div>
                  <div v-else>
                    -
                  </div>
                </td>

                <!-- 👉 Actions -->
                <td
                  class="text-center"
                  style="width: 5rem"
                >
                  <VBtn
                    v-tooltip.top="'View Details'"
                    icon
                    size="x-small"
                    color="default"
                    variant="text"
                    :to="{
                      name: `admin-${item.category_name.toLowerCase()}-manage-subscription`,
                      params: { subscriptionId: item.id },
                      query: { ...route.query },
                    }"
                  >
                    <VIcon
                      size="22"
                      icon="tabler-eye"
                    />
                  </VBtn>
                </td>
              </tr>
            </tbody>

            <!-- 👉 table footer  -->
            <tfoot v-show="!skeletonLoading && items.length === 0">
              <tr>
                <td
                  colspan="10"
                  class="text-center"
                >
                  No data available
                </td>
              </tr>
            </tfoot>
          </VTable>

          <VDivider />

          <VCardText class="d-flex align-center flex-wrap justify-space-between gap-4 py-3 px-5">
            <span class="text-sm text-disabled">
              {{ paginationData }}
            </span>
            <VPagination
              v-model="currentPage"
              size="small"
              :total-visible="5"
              :length="totalPage"
            />
          </VCardText>
        </VCard>
      </VCol>
    </VRow>
  </section>
</template>

<style lang="scss">
.v-table__wrapper td.title {
  min-width: 200px;
  h6 {
    a {
      white-space: pre-line;
    }
  }
}
</style>
