<script setup>
import { requiredValidator } from '@validators'
import { useGlobalData } from '@/store/global'
import { useRouter } from 'vue-router'
import { scrollToTop, toUpperCaseValue, isNumber, removeKeyFromObject } from '@/utils/helpers'
import ApiService from '@/services/ApiService'
import { processErrors } from '@/utils/errorHandler'
import { categoryList } from '@/utils/admin'
import { isEmpty, isEmptyObject, isNullOrUndefined } from '@/@core/utils'
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useAllProductsStore } from '@/store/admin/allProducts'
import { storeToRefs } from 'pinia'

const router = useRouter()
const globalData = useGlobalData()
const { showSnackbar } = globalData
const productsStore =  useAllProductsStore()
const { fetchProducts, fetchCategories } = productsStore

const {
  products,
  otcCategories,
  errors: filterErrors,
} = storeToRefs(productsStore)

const loading = ref(false)
const serverErrors = ref([])
const inputErrors = ref({})
const refForm = ref()
const discountScope = ref('all')

const formData = reactive({
  promocode: null,
  categories: [],
  otc_category_ids: [], // only when categories contains OTC
  product_ids: [],
  promocode_type: 'fixed', // fixed, percentage
  promocode_value: null,
  promocode_valid_from: null, // format m/d/Y H:i , pass null to start from today else any future date
  promocode_valid_until: null, // format m/d/Y H:i , pass null for infinity else any future date
  max_redemptions: -1, // pass -1 for unlimited
  max_redemptions_per_user: -1, // pass -1 for unlimited
  description: null, // optional
  is_active: 1,
})

const promoType = ref('fixed')

const categoryNames = {
  'ED': 'Erectile Dysfunction',
  'HL': 'Hair Loss',
  'WL': 'Weight Loss',
  'OTC': 'Over The Counter',
}

const flattenedProducts = computed(() => {
  if (!products.value || !Array.isArray(products.value)) return []

  const result = []

  products.value.forEach(categoryGroup => {
    const categoryCode = Object.keys(categoryGroup)[0]
    const categoryName = categoryNames[categoryCode] || categoryCode
    const categoryProducts = categoryGroup[categoryCode]

    if (Array.isArray(categoryProducts)) {
      categoryProducts.forEach(product => {
        result.push({
          ...product,
          category_code: categoryCode,
          category_name: categoryName,
        })
      })
    }
  })

  return result
})

watch(promoType, (newVal, oldVal) => {
  formData.promocode_type = newVal

  // if (newVal !== oldVal) {
  //   formData.promocode_value = null
  // }
})

onMounted(async () => {
  if (isEmpty(products.value) || isEmpty(otcCategories.value)) {
    await Promise.all([fetchProducts(), fetchCategories()])
    if (!isEmpty(filterErrors.value)) {
      showSnackbar(processErrors(filterErrors.value)[0], 'error')
    }
  }
})

watch(formData, () => {
  if (!isEmpty(formData.promocode)) {
    formData.promocode = toUpperCaseValue(formData.promocode)
  }
})

const onFormSubmit = () => {
  refForm.value?.validate().then(async ({ valid }) => {
    if (valid) {
      serverErrors.value = []
      loading.value = true

      const postData = {
        ...formData,
        categories: isEmpty(formData.categories) ? null : formData.categories,
        otc_category_ids: isEmpty(formData.otc_category_ids) ? null : formData.otc_category_ids,
        product_ids: isEmpty(formData.product_ids) ? null : formData.product_ids,
      }

      if (discountScope.value === 'all') {
        postData.categories = null
        postData.otc_category_ids = null
        postData.product_ids = null
      } else if (discountScope.value === 'category_specific') {
        postData.product_ids = null
      } else if (discountScope.value === 'product_specific') {
        postData.categories = null
        postData.otc_category_ids = null
      }

      try {
        const { data } = await ApiService.post('/admin/add-promo-code', postData)

        if (data.status === 200) {
          await router.push({ name: 'admin-promo-codes' })
          showSnackbar(data.message)
          refForm.value?.reset()
          refForm.value?.resetValidation()
        } else {
          if (data.errors) {
            inputErrors.value = data.errors
          } else {
            serverErrors.value = processErrors(data)
          }
        }
      } catch (error) {
        console.error(error)
        serverErrors.value = processErrors(error)
      } finally {
        loading.value = false
      }
    } else {
      scrollToTop()
    }
  })
}

function removeKeyFromInputErrors(key) {
  inputErrors.value = removeKeyFromObject(inputErrors.value, key)
}
</script>

<template>
  <VRow>
    <VCol cols="12">
      <VForm
        ref="refForm"
        @submit.prevent="onFormSubmit"
      >
        <VCard title="Add Promo Code">
          <VCardText>
            <!-- 👉 Alert -->
            <VAlert
              v-if="isEmptyObject(inputErrors) && serverErrors.length > 0"
              type="error"
              variant="tonal"
              title="Validation failed!"
              class="mb-6"
            >
              <ul class="mb-0">
                <li
                  v-for="error in serverErrors"
                  :key="error"
                  class="mb-0"
                >
                  {{ error }}
                </li>
              </ul>
            </VAlert>

            <VRow>
              <VCol cols="12">
                <AppTextField
                  v-model="formData.promocode"
                  label="Promo Code Name *"
                  :rules="[requiredValidator]"
                  required
                  @keyup="removeKeyFromInputErrors('promocode')"
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['promocode'])"
                  class="text-sm text-error mb-0"
                >
                  {{ inputErrors['promocode'][0] }}
                </p>
              </VCol>

              <VCol cols="12">
                <AppTextarea
                  v-model="formData.description"
                  label="Description (optional)"
                  rows="2"
                  @keyup="removeKeyFromInputErrors('description')"
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['description'])"
                  class="text-sm text-error mb-0"
                >
                  {{ inputErrors['description'][0] }}
                </p>
              </VCol>

              <VCol cols="12">
                <VRadioGroup
                  v-model="promoType"
                  label="Discount Type *"
                  :rules="[requiredValidator]"
                  required
                  class="d-flex"
                  @change="removeKeyFromInputErrors('promocode_type')"
                >
                  <VRadio
                    label="Fixed Amount"
                    value="fixed"
                    class="ms-2"
                  />
                  <VRadio
                    label="Percentage (of the total amount)"
                    value="percentage"
                    class="ms-2"
                  />
                </VRadioGroup>
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['promocode_type'])"
                  class="text-sm text-error mb-0"
                >
                  {{ inputErrors['promocode_type'][0] }}
                </p>
              </VCol>

              <VCol
                cols="12"
                md="6"
              >
                <AppTextField
                  v-model="formData.promocode_value"
                  label="Discount Value *"
                  :rules="[requiredValidator]"
                  :maxlength="formData.promocode_type === 'fixed' ? 5 : 2"
                  :prefix="formData.promocode_type === 'fixed' ? '$' : ''"
                  :suffix="formData.promocode_type === 'percentage' ? '%' : ''"
                  required
                  hint="If you choose percentage, enter a number from 1 to 99. For example, enter 50 for 50%. For a fixed amount, enter the amount in dollars. For example, enter 10 for $10.00"
                  persistent-hint
                  @keydown="isNumber($event)"
                  @keyup="removeKeyFromInputErrors('promocode_value')"
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['promocode_value'])"
                  class="text-sm text-error mb-0"
                >
                  {{ inputErrors['promocode_value'][0] }}
                </p>
              </VCol>

              <VCol
                cols="12"
                md="3"
              >
                <AppDateTimePicker
                  v-model="formData.promocode_valid_from"
                  label="Valid From"
                  placeholder="Today"
                  :config="{
                    enableTime: true,
                    time_24hr: true,
                    dateFormat: 'm/d/Y H:i',
                    onChange: () => {
                      removeKeyFromInputErrors('promocode_valid_from')
                    },
                    // minDate: new Date(),
                  }"
                  hint="Format: (mm/dd/yyyy hh:mm)"
                  persistent-hint
                  clearable
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['promocode_valid_from'])"
                  class="text-sm text-error mb-0"
                >
                  {{ inputErrors['promocode_valid_from'][0] }}
                </p>
              </VCol>

              <VCol
                cols="12"
                md="3"
              >
                <AppDateTimePicker
                  v-model="formData.promocode_valid_until"
                  label="Valid Until"
                  placeholder="Infinity"
                  :config="{
                    enableTime: true,
                    time_24hr: true,
                    dateFormat: 'm/d/Y H:i',
                    onChange: () => {
                      removeKeyFromInputErrors('promocode_valid_until')
                    },
                    minDate: new Date(),
                  }"
                  hint="Format: (mm/dd/yyyy hh:mm)"
                  persistent-hint
                  clearable
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['promocode_valid_until'])"
                  class="text-sm text-error mb-0"
                >
                  {{ inputErrors['promocode_valid_until'][0] }}
                </p>
              </VCol>

              <VCol
                cols="12"
                md="6"
              >
                <AppTextField
                  v-model="formData.max_redemptions"
                  type="number"
                  label="Max Redemptions"
                  placeholder="Unlimited"
                  hint="Enter -1 for unlimited redemptions (total)."
                  persistent-hint
                  @keyup="removeKeyFromInputErrors('max_redemptions')"
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['max_redemptions'])"
                  class="text-sm text-error mb-0"
                >
                  {{ inputErrors['max_redemptions'][0] }}
                </p>
              </VCol>

              <VCol
                cols="12"
                md="6"
              >
                <AppTextField
                  v-model="formData.max_redemptions_per_user"
                  type="number"
                  label="Max Redemptions Per User"
                  placeholder="Unlimited"
                  hint="Enter -1 for unlimited redemptions per user."
                  persistent-hint
                  @keyup="removeKeyFromInputErrors('max_redemptions_per_user')"
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['max_redemptions_per_user'])"
                  class="text-sm text-error mb-0"
                >
                  {{ inputErrors['max_redemptions_per_user'][0] }}
                </p>
              </VCol>

              <VCol
                cols="12"
                class="mt-6"
              >
                <VRadioGroup
                  v-model="discountScope"
                  class="d-flex"
                  label="Discount Scope"
                >
                  <VRadio
                    label="Store wide: Applies to all products"
                    value="all"
                    class="ms-2"
                  />
                  <VRadio
                    label="Category Specific: Applies to selected categories only"
                    value="category_specific"
                    class="ms-2"
                  />
                  <VRadio
                    label="Product Specific: Applies to selected products only"
                    value="product_specific"
                    class="ms-2"
                  />
                </VRadioGroup>
              </VCol>

              <!-- Category Specific -->
              <VCol
                v-if="discountScope === 'category_specific'"
                cols="12"
                md="6"
              >
                <AppSelect
                  v-model="formData.categories"
                  label="Select Categories (Choose one or more)"
                  :items="categoryList"
                  placeholder="All Categories"
                  hint="Select the categories that this discount will be applied to. If you leave empty, discount will be applied to all categories."
                  persistent-hint
                  clearable
                  clear-icon="tabler-x"
                  multiple
                  chips
                  @keyup="removeKeyFromInputErrors('categories')"
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['categories'])"
                  class="text-sm text-error mb-0"
                >
                  {{ inputErrors['categories'][0] }}
                </p>
              </VCol>
              <VCol
                v-if="discountScope === 'category_specific' && formData.categories.includes('OTC')"
                cols="12"
                md="6"
              >
                <AppSelect
                  v-model="formData.otc_category_ids"
                  label="Select OTC Categories (Choose one or more)"
                  :items="otcCategories"
                  placeholder="All OTC Categories"
                  hint="Select the otc categories that this discount will be applied to. If you leave empty, discount will be applied to all the otc categories."
                  persistent-hint
                  item-title="name"
                  item-value="id"
                  clearable
                  clear-icon="tabler-x"
                  multiple
                  chips
                  @keyup="removeKeyFromInputErrors('otc_category_ids')"
                >
                  <template #item="{ props, item }">
                    <VListItem
                      v-bind="props"
                      class="py-2"
                      :title="item?.raw?.name"
                    >
                      <div class="text-xs">
                        {{ item?.raw?.parent_category_name }}
                      </div>
                    </VListItem>
                  </template>
                </AppSelect>
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['otc_category_ids'])"
                  class="text-sm text-error mb-0"
                >
                  {{ inputErrors['otc_category_ids'][0] }}
                </p>
              </VCol>

              <!-- Product Specific -->
              <VCol
                v-if="discountScope === 'product_specific'"
                cols="12"
              >
                <AppSelect
                  v-model="formData.product_ids"
                  label="Select Products (Choose one or more)"
                  :items="flattenedProducts"
                  item-title="product_name"
                  item-value="id"
                  placeholder="All Products"
                  clearable
                  clear-icon="tabler-x"
                  multiple
                  chips
                  hint="Select the products that this discount will be applied to. If you leave empty, discount will be applied to all products."
                  persistent-hint
                  @keyup="removeKeyFromInputErrors('product_ids')"
                >
                  <template #item="{ props, item }">
                    <VListItem
                      v-bind="props"
                      :title="item.raw.product_name"
                    >
                      <template #subtitle>
                        <span class="text-xs">{{ item.raw.category_name }}</span>
                      </template>
                    </VListItem>
                  </template>
                </AppSelect>
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['product_ids'])"
                  class="text-sm text-error mb-0"
                >
                  {{ inputErrors['product_ids'][0] }}
                </p>
              </VCol>
            </VRow>
            <VRow class="mt-3">
              <VCol cols="12">
                <VBtn
                  type="submit"
                  :loading="loading"
                  @click="refForm?.validate()"
                >
                  Submit
                </VBtn>
                <VBtn
                  type="button"
                  variant="outlined"
                  class="ms-2"
                  :disabled="loading"
                  :to="{ name: 'admin-promo-codes' }"
                >
                  Back
                </VBtn>
              </VCol>
            </VRow>
          </VCardText>
        </VCard>
      </VForm>
    </VCol>
  </VRow>
</template>
