<script setup>
import { isEmpty } from '@/@core/utils'
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { processErrors } from '@/utils/errorHandler'
import { calculateStartIndex, formatCurrency } from '@/utils/helpers'
import { refDebounced } from '@vueuse/core'
import { computed, onMounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

// import { useAllProductsStore } from '@/store/admin/allProducts'
// import { storeToRefs } from 'pinia'

const globalData = useGlobalData()
const { showSnackbar } = globalData
const route = useRoute()
const router = useRouter()

// const productsStore =  useAllProductsStore()
// const { fetchProducts } = productsStore
// const {
//   products,
//   loading: productsLoading,
//   errors: productsErrors,
// } = storeToRefs(productsStore)

const searchQuery = ref(route.query.search ?? null)
const debouncedSearchQuery = refDebounced(searchQuery, 300)
const selectedStatus = ref(!isNaN(parseInt(route.query.status)) ? parseInt(route.query.status) : null)
const selectedCategory = ref(route.query.category ?? null)
const selectedProducts = ref([])
const rowPerPage = ref(route.query.per_page ?? 10)
const currentPage = ref(!isNaN(parseInt(route.query.page)) ? parseInt(route.query.page) : 1)
const totalPage = ref(1)
const totalItems = ref(0)
const items = ref([])
const sortByColumnName = ref(route.query.sort_by ?? 'created_at')
const sortDirection = ref(route.query.sort_dir ?? 'desc')
const isChangeAccountStatusDialogVisible = ref(false)
const isDeleteDialogVisible = ref(false)
const itemId = ref('')
const itemStatus = ref(false)
const skeletonLoading = ref(true)
const changeStatusLoading = ref(false)
const deleteItemLoading = ref(false)

onMounted(async () => {
  fetchItems()

  // await fetchProducts()
  // if (!isEmpty(productsErrors.value)) {
  //   showSnackbar(processErrors(productsErrors.value)[0], 'error')
  // }
})

const updateRoute = () => {
  router.replace({
    query: {
      page: currentPage.value,
      per_page: rowPerPage.value,
      status: selectedStatus.value,
      category: selectedCategory.value,
      search: searchQuery.value,
      sort_by: sortByColumnName.value,
      sort_dir: sortDirection.value,
    },
  })
}

watch([debouncedSearchQuery, selectedStatus, selectedCategory, rowPerPage, selectedProducts], () => {
  currentPage.value = 1
  fetchItems()
})

watch([currentPage, sortByColumnName, sortDirection], () => {
  fetchItems()
})

const fetchItems = async () => {
  skeletonLoading.value = true

  try {
    const { data } = await ApiService.post('/admin/list-promo-code', {
      is_active: selectedStatus.value,
      searchQuery: searchQuery.value,
      perPage: rowPerPage.value,
      page: currentPage.value,
      sortByColumnName: sortByColumnName.value,
      isSortDirDesc: sortDirection.value,
      category: selectedCategory.value,
      product_ids: selectedProducts.value,
    })

    if (data.status === 200) {
      const pagedData = data.promocodeLists

      if (isEmpty(pagedData.records) && currentPage.value !== 1) {
        currentPage.value = currentPage.value - 1

        return
      }

      let startIndex = calculateStartIndex(pagedData.current_page, pagedData.per_page)
      if (currentPage.value > 1) {
        startIndex = (currentPage.value - 1) * rowPerPage.value + 1
      }

      items.value = pagedData.records.map((d, index) => ({
        ...d,
        sno: index + startIndex,
      }))
      totalPage.value = pagedData.totalPage == 0 ? 1 : pagedData.totalPage
      totalItems.value = pagedData.totalRecords
      updateRoute()
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    console.error(error)
    if (error.response.data.message) {
      showSnackbar(error.response.data.message, 'error')
    } else if (error.response.message) {
      showSnackbar(error.response.message, 'error')
    }
  } finally {
    skeletonLoading.value = false
  }
}

const sortColumn = columnName => {
  if (sortByColumnName.value !== '' && sortByColumnName.value === columnName && sortDirection.value === 'asc') {
    sortDirection.value = 'desc'
  } else {
    sortDirection.value = 'asc'
  }

  sortByColumnName.value = columnName
}

const status = [
  {
    title: 'Active',
    value: 1,
  },
  {
    title: 'Inactive',
    value: 0,
  },
]

const categoryFilterOptions = [
  { title: 'All', value: 'all' },
  { title: 'ED', value: 'ED' },
  { title: 'HL', value: 'HL' },
  { title: 'WL', value: 'WL' },
  { title: 'OTC', value: 'OTC' },
]

const resolveItemStatus = stat => {
  if (stat === 1) {
    return { color: 'success', status: true, label: 'Active' }
  } else {
    return { color: 'secondary', status: false, label: 'Inactive' }
  }
}

const onAccountStatusChange = (id, status) => {
  isChangeAccountStatusDialogVisible.value = true
  itemId.value = id
  itemStatus.value = status
}

async function handleStatusChangeConfirmation(isConfirmed) {
  if (isConfirmed) {
    await changeItemStatus()
  }
}

const changeItemStatus = async () => {
  changeStatusLoading.value = true

  let itemsValue = items.value
  let itemIndex = itemsValue.findIndex(item => item.id === itemId.value)

  if (itemIndex === -1) {
    console.error('Item not found')
    changeStatusLoading.value = false

    return
  }

  // let newStatus = itemsValue[itemIndex].is_active === 1 ? 0 : 1

  ApiService
    .get(`/admin/update-promo-code-status/${itemId.value}`)
    .then(async response => {
      if (response.data.status === 200) {
        await fetchItems()

        // itemsValue[itemIndex].is_active = newStatus
        // items.value = itemsValue
        itemId.value = ''
        itemStatus.value = false
        showSnackbar(response.data.message)
      } else {
        showSnackbar(response.data.message, 'error')
      }
    })
    .catch(error => {
      console.error(error)
      showSnackbar(processErrors(error)[0], 'error')
    })
    .finally(() => {
      changeStatusLoading.value = false
      isChangeAccountStatusDialogVisible.value = false
    })
}

const onDelete = id => {
  isDeleteDialogVisible.value = true
  itemId.value = id
}

async function handleDeleteConfirmation(isConfirmed) {
  if (isConfirmed) {
    await deleteItem()
  }
}

const deleteItem = async () => {
  deleteItemLoading.value = true

  ApiService.delete(`/admin/delete-promo-code/${itemId.value}`)
    .then(async response => {
      if (response.data.status) {
        await fetchItems()
        itemId.value = ''
        showSnackbar(response.data.message)
      } else {
        showSnackbar(response.data.message, 'error')
      }
    })
    .catch(error => {
      console.error(error)
      showSnackbar(processErrors(error)[0], 'error')
    })
    .finally(() => {
      deleteItemLoading.value = false
      isDeleteDialogVisible.value = false
    })
}

const paginationData = computed(() => {
  const currentPageValue = currentPage.value <= 0 ? 1 : currentPage.value

  const firstIndex = items.value.length
    ? (currentPageValue - 1) * rowPerPage.value + 1
    : 0

  const lastIndex = items.value.length + (currentPageValue - 1) * rowPerPage.value

  return `Showing ${firstIndex} to ${lastIndex} of ${totalItems.value} entries`
})
</script>

<template>
  <section>
    <VRow class="match-height">
      <VCol cols="12">
        <VCard title="Filter">
          <!-- 👉 Filters -->
          <VCardText>
            <VRow>
              <VCol
                cols="12"
                sm="4"
              >
                <AppSelect
                  v-model="selectedStatus"
                  label="Promo Status"
                  :items="status"
                  clearable
                  clear-icon="tabler-x"
                  placeholder="Select"
                />
              </VCol>
              <VCol
                cols="12"
                sm="4"
              >
                <AppSelect
                  v-model="selectedCategory"
                  label="Category"
                  :items="categoryFilterOptions"
                  clearable
                  clear-icon="tabler-x"
                  placeholder="Select"
                />
              </VCol>
              <!--
                <VCol
                cols="12"
                sm="4"
                >
                <AppSelect
                v-model="selectedProducts"
                label="Product"
                :items="products"
                item-title="product_name"
                item-value="id"
                clearable
                multiple
                clear-icon="tabler-x"
                placeholder="Select"
                />
                </VCol>
              -->
            </VRow>
          </VCardText>

          <VDivider />

          <VCardText class="d-flex flex-wrap py-4 gap-4">
            <div
              class="me-3"
              style="width: 80px"
            >
              <VSelect
                v-model="rowPerPage"
                density="compact"
                variant="outlined"
                :items="[10, 20, 30, 50]"
              />
            </div>

            <VSpacer />

            <div class="d-flex align-center flex-wrap gap-4">
              <!-- 👉 Search  -->
              <div style="width: 23rem">
                <VTextField
                  v-model="searchQuery"
                  placeholder="Search"
                  density="compact"
                  clearable
                  clear-icon="tabler-x"
                />
              </div>
              <!-- 👉 Add button -->
              <VBtn
                variant="tonal"
                color="primary"
                :to="{ name: 'admin-promo-add' }"
              >
                Add New Promo Code
              </VBtn>
            </div>
          </VCardText>

          <VDivider />

          <VTable class="text-no-wrap">
            <!-- 👉 table head -->
            <thead>
              <tr>
                <th scope="col">
                  #
                </th>
                <th scope="col">
                  PROMO
                </th>
                <th scope="col">
                  PROMO DISCOUNT
                </th>
                <th scope="col">
                  CATEGORIES
                </th>
                <!--
                  <th scope="col">
                  PRODUCTS
                  </th>
                -->
                <th scope="col">
                  TOTAL REDEMPTIONS
                </th>
                <th scope="col">
                  STATUS
                </th>
                <th
                  scope="col"
                  class="cursor-pointer"
                  @click="sortColumn('created_at')"
                >
                  <div class="w-125px">
                    CREATED AT
                    <span class="d-grid float-right">
                      <VIcon
                        size="10"
                        icon="tabler-chevron-up"
                      />
                      <VIcon
                        size="10"
                        icon="tabler-chevron-down"
                      />
                    </span>
                  </div>
                </th>
                <th
                  scope="col"
                  class="cursor-pointer"
                  @click="sortColumn('updated_at')"
                >
                  <div class="w-125px">
                    UPDATED AT
                    <span class="d-grid float-right">
                      <VIcon
                        size="10"
                        icon="tabler-chevron-up"
                      />
                      <VIcon
                        size="10"
                        icon="tabler-chevron-down"
                      />
                    </span>
                  </div>
                </th>
                <th scope="col">
                  ACTIONS
                </th>
              </tr>
            </thead>

            <!-- 👉 table body -->
            <tbody v-if="skeletonLoading">
              <tr
                v-for="n in 10"
                :key="n"
              >
                <td
                  v-for="s in 9"
                  :key="s"
                >
                  <Skeleton
                    width="6rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
              </tr>
            </tbody>
            <tbody v-else>
              <tr
                v-for="item in items"
                :key="item.id"
                style="height: 3.75rem"
              >
                <!-- 👉 NO -->
                <td>
                  {{ item.sno }}
                </td>

                <!-- 👉 PROMO -->
                <td>
                  <RouterLink
                    :to="{
                      name: 'admin-promo-edit',
                      params: { id: item.id },
                      query: { ...route.query },
                    }"
                    class="dt-link"
                  >
                    {{ item.promocode }}
                  </RouterLink>
                </td>

                <!-- 👉 PROMO DISCOUNT -->
                <td>
                  <span v-if="item.promocode_type === 'fixed'">{{ formatCurrency(item.promocode_value) }}</span>
                  <span v-else-if="item.promocode_type === 'percentage'">{{ item.promocode_value }}%</span>
                  <span v-else> - </span>
                </td>

                <!-- 👉 Categories -->
                <td>
                  {{ isEmpty(item.categories) ? 'All' : item.categories.join(', ') }}
                </td>

                <!-- 👉 Products -->
                <!--
                  <td>
                  <div
                  style="min-width: 225px;"
                  class="text-wrap"
                  >
                  {{ isEmpty(item.product_names) ? 'All' : item.product_names }}
                  </div>
                  </td>
                -->

                <!-- 👉 Total Redemptions Count -->
                <td>
                  {{ item.total_redemption_count ?? 'Unlimited' }}
                </td>
                <!-- 👉 Status -->
                <td>
                  <VSwitch
                    v-model="resolveItemStatus(item.is_active).status"
                    :label="resolveItemStatus(item.is_active).label"
                    inset
                    @change="onAccountStatusChange(item.id, resolveItemStatus(item.is_active).status)"
                  />
                </td>

                <!-- 👉 Created At -->
                <td>
                  <div class="d-flex flex-column">
                    <span>{{ item.created_date }}</span>
                    <span>{{ item.created_time }}</span>
                  </div>
                </td>

                <!-- 👉 Updated At -->
                <td>
                  <div
                    v-if="item.updated_date !== item.created_date || item.updated_time !== item.created_time"
                    class="d-flex flex-column"
                  >
                    <span>{{ item.updated_date }}</span>
                    <span>{{ item.updated_time }}</span>
                  </div>
                  <div v-else>
                    -
                  </div>
                </td>

                <!-- 👉 Actions -->
                <td
                  class="text-center"
                  style="width: 5rem"
                >
                  <VBtn
                    icon
                    size="x-small"
                    color="default"
                    variant="text"
                    :to="{
                      name: 'admin-promo-edit',
                      params: { id: item.id },
                      query: { ...route.query },
                    }"
                  >
                    <VIcon
                      size="22"
                      icon="tabler-edit"
                    />
                  </VBtn>
                  <VBtn
                    icon
                    size="x-small"
                    color="default"
                    variant="text"
                    @click="onDelete(item.id)"
                  >
                    <VIcon
                      size="22"
                      icon="tabler-trash"
                    />
                  </VBtn>
                </td>
              </tr>
            </tbody>

            <!-- 👉 table footer  -->
            <tfoot v-show="!skeletonLoading && items.length === 0">
              <tr>
                <td
                  colspan="10"
                  class="text-center"
                >
                  No data available
                </td>
              </tr>
            </tfoot>
          </VTable>

          <VDivider />

          <VCardText class="d-flex align-center flex-wrap justify-space-between gap-4 py-3 px-5">
            <span class="text-sm text-disabled">
              {{ paginationData }}
            </span>
            <VPagination
              v-model="currentPage"
              size="small"
              :total-visible="5"
              :length="totalPage"
            />
          </VCardText>
        </VCard>
      </VCol>
    </VRow>

    <!-- 👉 Change Status confirm dialog -->
    <ConfirmDialog
      v-model:isDialogVisible="isChangeAccountStatusDialogVisible"
      confirmation-question="Are you sure you want to change status for this promo code?"
      :loading="changeStatusLoading"
      @confirm="handleStatusChangeConfirmation"
    />

    <!-- 👉 Delete confirm dialog -->
    <ConfirmDialog
      v-model:isDialogVisible="isDeleteDialogVisible"
      confirmation-question="Are you sure you want to delete this promo code?"
      :loading="deleteItemLoading"
      @confirm="handleDeleteConfirmation"
    />
  </section>
</template>

<style lang="scss">
.v-table__wrapper td.title {
  min-width: 200px;
  h6 {
    a {
      white-space: pre-line;
    }
  }
}
</style>
