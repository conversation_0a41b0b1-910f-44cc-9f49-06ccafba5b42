<script setup>
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { processErrors } from '@/utils/errorHandler'
import { watch } from 'vue'
import { isEmpty } from '@/@core/utils'
import { formattedPhoneNumber, formatCurrency, resolveInitials } from '@/utils/helpers'
import { useRoute } from 'vue-router'

const props = defineProps({
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
  reportId: {
    type: String,
    required: false,
  },
})

const emit = defineEmits([
  'update:isDialogVisible',
  'updated',
  'approve',
])

const globalStore = useGlobalData()
const { showSnackbar } = globalStore
const route = useRoute()

const isLoading = ref(true)
const details = ref([])

watch(props, () => {
  if (props.isDialogVisible && !isEmpty(props.reportId)) {
    fetchDetails()
  }
})

const fetchDetails = async () => {
  try {
    isLoading.value = true

    const { data } = await ApiService.get(`/admin/affiliate-transaction-details/${props.reportId}`)

    if (data.status === 200) {
      details.value = data.transactionDetails
    } else {
      showSnackbar(data.message, 'error')
      emit('update:isDialogVisible', false)
    }
  } catch (error) {
    console.error(error)
    showSnackbar(processErrors(error)[0], 'error')
    emit('update:isDialogVisible', false)
  } finally {
    isLoading.value = false
  }
}

function resolveStatus(status) {
  if (status === 0) {
    return { label: 'Pending', color: 'warning' }
  } else if (status === 1) {
    return { label: 'Approved', color: 'success' }
  } else if (status === 2) {
    return { label: 'Rejected', color: 'error' }
  }
}
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 610 "
    :model-value="props.isDialogVisible"
    @update:model-value="val => $emit('update:isDialogVisible', val)"
  >
    <!-- 👉 Dialog close btn -->
    <DialogCloseBtn @click="$emit('update:isDialogVisible', false)" />

    <VCard class="pa-sm-8 pa-5">
      <!-- 👉 Title -->

      <VCardItem>
        <VCardTitle class="text-h4 text-center">
          Transaction Details
        </VCardTitle>
      </VCardItem>

      <VCardText v-if="isLoading">
        <VRow class="mb-6">
          <VCol
            cols="6"
            lg="4"
          >
            <Skeleton
              width="5rem"
              height="1rem"
              class="mb-2"
            ></Skeleton>
            <Skeleton
              width="7rem"
              height="1rem"
            ></Skeleton>
          </VCol>
          <VCol
            cols="6"
            lg="4"
          >
            <Skeleton
              width="5rem"
              height="1rem"
              class="mb-2"
            ></Skeleton>
            <Skeleton
              width="7rem"
              height="1rem"
            ></Skeleton>
          </VCol>
          <VCol
            cols="6"
            lg="4"
          >
            <Skeleton
              width="5rem"
              height="1rem"
              class="mb-2"
            ></Skeleton>
            <Skeleton
              width="7rem"
              height="1rem"
            ></Skeleton>
          </VCol>
          <VCol
            cols="6"
            lg="4"
          >
            <Skeleton
              width="5rem"
              height="1rem"
              class="mb-2"
            ></Skeleton>
            <Skeleton
              width="7rem"
              height="1rem"
            ></Skeleton>
          </VCol>
          <VCol
            cols="6"
            lg="4"
          >
            <Skeleton
              width="5rem"
              height="1rem"
              class="mb-2"
            ></Skeleton>
            <Skeleton
              width="7rem"
              height="1rem"
            ></Skeleton>
          </VCol>
          <VCol
            cols="6"
            lg="4"
          >
            <Skeleton
              width="5rem"
              height="1rem"
              class="mb-2"
            ></Skeleton>
            <Skeleton
              width="7rem"
              height="1rem"
            ></Skeleton>
          </VCol>
        </VRow>
      </VCardText>

      <VCardText v-else>
        <VRow>
          <VCol cols="6">
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Affiliate User
            </div>
            <div class="font-weight-medium text-body-2">
              <div class="d-flex align-center py-1">
                <VAvatar
                  v-if="details.affiliate_user?.selfie"
                  variant="tonal"
                  size="40"
                  class="me-3"
                  :image="details.affiliate_user?.selfie"
                  rounded
                />
                <VAvatar
                  v-else
                  variant="tonal"
                  size="40"
                  class="me-3"
                  rounded
                >
                  {{ resolveInitials(details.affiliate_user?.first_name + ' ' + details.affiliate_user?.last_name) }}
                </VAvatar>
                <div>
                  <h6 class="text-base">
                    {{ details.affiliate_user?.first_name + ' ' + details.affiliate_user?.last_name }}
                  </h6>
                  <div class="d-flex flex-column">
                    <span class="text-body-2">{{ details.affiliate_user?.email }}</span>
                    <span class="text-body-2">{{ formattedPhoneNumber(details.affiliate_user?.phone_number) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </VCol>

          <VCol cols="6">
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Subscriber
            </div>
            <div class="font-weight-medium text-body-2">
              <div class="d-flex align-center py-1">
                <VAvatar
                  v-if="details.user_details?.selfie"
                  variant="tonal"
                  size="40"
                  class="me-3"
                  :image="details.user_details?.selfie"
                  rounded
                />
                <VAvatar
                  v-else
                  variant="tonal"
                  size="40"
                  class="me-3"
                  rounded
                >
                  {{ resolveInitials(details.user_details?.name) }}
                </VAvatar>
                <div>
                  <h6 class="text-base">
                    {{ details.user_details?.name }}
                  </h6>
                  <div class="d-flex flex-column">
                    <span class="text-body-2">{{ details.user_details?.email }}</span>
                    <span class="text-body-2">{{ formattedPhoneNumber(details.user_details?.phone_number) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </VCol>

          <VCol cols="6">
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Product Purchased
            </div>
            <div class="font-weight-medium text-body-2">
              <div class="d-flex align-center py-1">
                <VAvatar
                  v-if="details.product_img"
                  variant="tonal"
                  size="40"
                  class="me-3"
                  :image="details.product_img"
                  rounded
                />
                <VAvatar
                  v-else
                  variant="tonal"
                  size="40"
                  class="me-3"
                  rounded
                >
                  {{ resolveInitials(details.product_name) }}
                </VAvatar>
                <div>
                  <h6 class="text-base">
                    {{ details.product_name }}
                  </h6>
                  <div
                    v-if="details.category?.toLowerCase() === 'wl'"
                    class="d-flex flex-column"
                  >
                    <span class="d-block text-body-2">
                      {{ details.strength }} {{ details.strength_unit }} / weekly
                    </span>
                  </div>
                  <div
                    v-else
                    class="d-flex flex-column"
                  >
                    <span class="d-block text-body-2">
                      {{ details.strength }} {{ details.strength_unit }} x {{ details.qty * details.subscription_interval }} units
                    </span>
                    <span
                      v-if="details.is_medicine_pickup_at_local_pharmacy !== 1"
                      class="d-block text-body-2"
                    >({{ details.subscription_interval }} month supply)</span>
                  </div>
                </div>
              </div>
            </div>
          </VCol>

          <VCol
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Order ID
            </div>
            <div class="font-weight-medium text-body-2">
              <RouterLink
                :to="{
                  name: `admin-${details.category.toLowerCase()}-order-details`,
                  params: { orderId: details.subscription_refill_id },
                  query: { ...route.query },
                }"
                class="dt-link"
              >
                {{ details.order_no }}
              </RouterLink>
            </div>
          </VCol>

          <VCol
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Category
            </div>
            <div class="font-weight-medium text-body-2">
              {{ details.category }}
            </div>
          </VCol>

          <VCol
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Status
            </div>
            <div class="font-weight-medium text-body-2">
              <VChip
                label
                :color="resolveStatus(details.status).color"
                size="small"
                class="text-capitalize"
              >
                {{ resolveStatus(details.status).label }}
              </VChip>
            </div>
          </VCol>

          <VCol
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Order Amount
            </div>
            <div class="font-weight-medium text-body-2">
              {{ formatCurrency(details.order_amount) }}
            </div>
          </VCol>

          <VCol
            v-if="details.product_cost"
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Product Cost
            </div>
            <div class="font-weight-medium text-body-2">
              {{ formatCurrency(details.product_cost) }}
            </div>
          </VCol>

          <VCol
            v-if="details.net_revenue"
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Net Revenue
            </div>
            <div class="font-weight-medium text-body-2">
              {{ formatCurrency(details.net_revenue) }}
            </div>
          </VCol>

          <VCol
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Commission
            </div>
            <div class="font-weight-medium text-body-2">
              <div class="font-weight-bold">
                {{ formatCurrency(details.amount) }}
              </div>
              <div v-if="details.commission_type === 'percentage'">
                {{ details.commission_value }}%
              </div>
            </div>
          </VCol>

          <VCol
            v-if="details.request_date && details.request_time"
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Generated At
            </div>
            <div class="font-weight-medium text-body-2">
              <div>{{ details.request_date }}</div>
              <div>{{ details.request_time }}</div>
            </div>
          </VCol>

          <VCol
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Approved At
            </div>
            <div class="font-weight-medium text-body-2">
              <div>{{ details.created_date }}</div>
              <div>{{ details.created_time }}</div>
            </div>
          </VCol>

          <VCol
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Transaction Type
            </div>
            <div class="font-weight-medium text-body-2">
              <VChip
                label
                :color="details.transaction_type === 'credit' ? 'success' : 'warning'"
                size="small"
                class="text-capitalize"
              >
                {{ details.transaction_type }}
              </VChip>
            </div>
          </VCol>

          <VCol
            v-if="details.transaction_type === 'credit'"
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Credited At
            </div>
            <div class="font-weight-medium text-body-2">
              <div>{{ details.created_date }}</div>
              <div>{{ details.created_time }}</div>
            </div>
          </VCol>

          <VCol
            v-if="details.action_performed_date && details.action_performed_time"
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Action At
            </div>
            <div class="font-weight-medium text-body-2">
              <div>{{ details.action_performed_date }}</div>
              <div>{{ details.action_performed_time }}</div>
            </div>
          </VCol>

          <VCol
            v-if="!isEmpty(details.action_by_user_name) || !isEmpty(details.action_by_user_role)"
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Action By
            </div>
            <div class="font-weight-medium text-body-2">
              <span v-if="!isEmpty(details.action_by_user_name) && !isEmpty(details.action_by_user_role)">
                {{ details.action_by_user_name }} <br>
                {{ details.action_by_user_role }}
              </span>
              <span v-else-if="!isEmpty(details.action_by_user_name) && isEmpty(details.action_by_user_role)">
                {{ details.action_by_user_name }}
              </span>
              <span v-else-if="isEmpty(details.action_by_user_name) && !isEmpty(details.action_by_user_role)">
                {{ details.action_by_user_role }}
              </span>
            </div>
          </VCol>
          <VCol
            v-if="!isEmpty(details.cancel_reason)"
            cols="12"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Reason for Rejection
            </div>
            <div class="font-weight-medium text-body-2">
              {{ details.cancel_reason }}
            </div>
          </VCol>

          <VCol
            v-if="details.status === 2 && details.IsShowApproveBtn === 1"
            cols="12"
            class="pa-0"
          >
            <div class="d-flex justify-center gap-3 mt-4">
              <VBtn
                color="success"
                variant="tonal"
                @click="emit('approve', details.id)"
              >
                Approve Transaction
              </VBtn>
            </div>
          </VCol>
        </VRow>
      </VCardText>
    </VCard>
  </VDialog>
</template>
