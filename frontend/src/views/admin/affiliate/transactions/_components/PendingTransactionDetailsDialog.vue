<script setup>
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { processErrors } from '@/utils/errorHandler'
import { watch } from 'vue'
import { isEmpty } from '@/@core/utils'
import { formattedPhoneNumber, formatCurrency, resolveInitials } from '@/utils/helpers'
import { useRoute } from 'vue-router'

const props = defineProps({
  isDialogVisible: {
    type: Boolean,
    required: true,
  },
  recordId: {
    type: String,
    required: false,
  },
})

const emit = defineEmits([
  'update:isDialogVisible',
  'updated',
  'approve',
  'decline',
])

const globalStore = useGlobalData()
const { showSnackbar } = globalStore
const route = useRoute()

const isLoading = ref(true)
const details = ref([])

watch(props, () => {
  if (props.isDialogVisible && !isEmpty(props.recordId)) {
    fetchDetails()
  }
})

const fetchDetails = async () => {
  try {
    isLoading.value = true

    const { data } = await ApiService.get(`/admin/view-affiliate-pending-transaction/${props.recordId}`)

    if (data.status === 200) {
      details.value = data.earningDetails
    } else {
      showSnackbar(data.message, 'error')
      emit('update:isDialogVisible', false)
    }
  } catch (error) {
    console.error(error)
    showSnackbar(processErrors(error)[0], 'error')
    emit('update:isDialogVisible', false)
  } finally {
    isLoading.value = false
  }
}
</script>

<template>
  <VDialog
    :width="$vuetify.display.smAndDown ? 'auto' : 610 "
    :model-value="props.isDialogVisible"
    @update:model-value="val => $emit('update:isDialogVisible', val)"
  >
    <!-- 👉 Dialog close btn -->
    <DialogCloseBtn @click="$emit('update:isDialogVisible', false)" />

    <VCard class="pa-sm-8 pa-5">
      <!-- 👉 Title -->

      <VCardItem>
        <VCardTitle class="text-h4 text-center">
          Pending Transaction Details
        </VCardTitle>
      </VCardItem>

      <VCardText v-if="isLoading">
        <VRow class="mb-6">
          <VCol
            cols="6"
            lg="4"
          >
            <Skeleton
              width="5rem"
              height="1rem"
              class="mb-2"
            ></Skeleton>
            <Skeleton
              width="7rem"
              height="1rem"
            ></Skeleton>
          </VCol>
          <VCol
            cols="6"
            lg="4"
          >
            <Skeleton
              width="5rem"
              height="1rem"
              class="mb-2"
            ></Skeleton>
            <Skeleton
              width="7rem"
              height="1rem"
            ></Skeleton>
          </VCol>
          <VCol
            cols="6"
            lg="4"
          >
            <Skeleton
              width="5rem"
              height="1rem"
              class="mb-2"
            ></Skeleton>
            <Skeleton
              width="7rem"
              height="1rem"
            ></Skeleton>
          </VCol>
          <VCol
            cols="6"
            lg="4"
          >
            <Skeleton
              width="5rem"
              height="1rem"
              class="mb-2"
            ></Skeleton>
            <Skeleton
              width="7rem"
              height="1rem"
            ></Skeleton>
          </VCol>
          <VCol
            cols="6"
            lg="4"
          >
            <Skeleton
              width="5rem"
              height="1rem"
              class="mb-2"
            ></Skeleton>
            <Skeleton
              width="7rem"
              height="1rem"
            ></Skeleton>
          </VCol>
          <VCol
            cols="6"
            lg="4"
          >
            <Skeleton
              width="5rem"
              height="1rem"
              class="mb-2"
            ></Skeleton>
            <Skeleton
              width="7rem"
              height="1rem"
            ></Skeleton>
          </VCol>
        </VRow>
      </VCardText>

      <VCardText v-else>
        <VRow>
          <VCol cols="6">
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Affiliate User
            </div>
            <div class="font-weight-medium text-body-2">
              <div class="d-flex align-center py-1">
                <VAvatar
                  v-if="details.affiliate_user?.selfie"
                  variant="tonal"
                  size="40"
                  class="me-3"
                  :image="details.affiliate_user?.selfie"
                  rounded
                />
                <VAvatar
                  v-else
                  variant="tonal"
                  size="40"
                  class="me-3"
                  rounded
                >
                  {{ resolveInitials(details.affiliate_user?.first_name + ' ' + details.affiliate_user?.last_name) }}
                </VAvatar>
                <div>
                  <h6 class="text-base">
                    {{ details.affiliate_user?.first_name + ' ' + details.affiliate_user?.last_name }}
                  </h6>
                  <div class="d-flex flex-column">
                    <span class="text-body-2">{{ details.affiliate_user?.email }}</span>
                    <span class="text-body-2">{{ formattedPhoneNumber(details.affiliate_user?.phone_number) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </VCol>

          <VCol cols="6">
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Subscriber
            </div>
            <div class="font-weight-medium text-body-2">
              <div class="d-flex align-center py-1">
                <VAvatar
                  v-if="details.subscribed_by?.selfie"
                  variant="tonal"
                  size="40"
                  class="me-3"
                  :image="details.subscribed_by?.selfie"
                  rounded
                />
                <VAvatar
                  v-else
                  variant="tonal"
                  size="40"
                  class="me-3"
                  rounded
                >
                  {{ resolveInitials(details.subscribed_by?.name) }}
                </VAvatar>
                <div>
                  <h6 class="text-base">
                    {{ details.subscribed_by?.name }}
                  </h6>
                  <div class="d-flex flex-column">
                    <span class="text-body-2">{{ details.subscribed_by?.email }}</span>
                    <span class="text-body-2">{{ formattedPhoneNumber(details.subscribed_by?.phone_number) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </VCol>

          <VCol cols="6">
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Product Purchased
            </div>
            <div class="font-weight-medium text-body-2">
              <div class="d-flex align-center py-1">
                <VAvatar
                  v-if="details.product_img"
                  variant="tonal"
                  size="40"
                  class="me-3"
                  :image="details.product_img"
                  rounded
                />
                <VAvatar
                  v-else
                  variant="tonal"
                  size="40"
                  class="me-3"
                  rounded
                >
                  {{ resolveInitials(details.product_name) }}
                </VAvatar>
                <div>
                  <h6 class="text-base">
                    {{ details.product_name }}
                  </h6>
                  <div
                    v-if="details.category?.toLowerCase() === 'wl'"
                    class="d-flex flex-column"
                  >
                    <span class="d-block text-body-2">
                      {{ details.strength }} {{ details.strength_unit }} / weekly
                    </span>
                  </div>
                  <div
                    v-else
                    class="d-flex flex-column"
                  >
                    <span class="d-block text-body-2">
                      {{ details.strength }} {{ details.strength_unit }} x {{ details.qty * details.subscription_interval }} units
                    </span>
                    <span
                      v-if="details.is_medicine_pickup_at_local_pharmacy !== 1"
                      class="d-block text-body-2"
                    >({{ details.subscription_interval }} month supply)</span>
                  </div>
                </div>
              </div>
            </div>
          </VCol>

          <VCol
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Order ID
            </div>
            <div class="font-weight-medium text-body-2">
              <RouterLink
                :to="{
                  name: `admin-${details.category.toLowerCase()}-order-details`,
                  params: { orderId: details.subscription_refill_id },
                  query: { ...route.query },
                }"
                class="dt-link"
              >
                {{ details.order_no }}
              </RouterLink>
            </div>
          </VCol>

          <VCol
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Category
            </div>
            <div class="font-weight-medium text-body-2">
              {{ details.category }}
            </div>
          </VCol>

          <VCol
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Order Amount
            </div>
            <div class="font-weight-medium text-body-2">
              {{ formatCurrency(details.order_amount) }}
            </div>
          </VCol>

          <VCol
            v-if="details.product_cost"
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Product Cost
            </div>
            <div class="font-weight-medium text-body-2">
              {{ formatCurrency(details.product_cost) }}
            </div>
          </VCol>

          <VCol
            v-if="details.net_revenue"
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Net Revenue
            </div>
            <div class="font-weight-medium text-body-2">
              {{ formatCurrency(details.net_revenue) }}
            </div>
          </VCol>

          <VCol
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Commission
            </div>
            <div class="font-weight-medium text-body-2">
              <div class="font-weight-bold">
                {{ formatCurrency(details.commission_amount) }}
              </div>
              <div v-if="details.commission_type === 'percentage'">
                {{ details.commission_value }}%
              </div>
            </div>
          </VCol>

          <VCol
            v-if="details.created_date && details.created_time"
            cols="6"
            lg="4"
          >
            <div class="text-xs text-uppercase font-weight-bold text-high-emphasis mb-1">
              Created At
            </div>
            <div class="font-weight-medium text-body-2">
              <div>{{ details.created_date }}</div>
              <div>{{ details.created_time }}</div>
            </div>
          </VCol>

          <VCol
            cols="12"
            class="pa-0"
          >
            <div class="d-flex justify-center gap-3 mt-4">
              <VBtn
                color="success"
                variant="tonal"
                @click="emit('approve', details.id)"
              >
                Approve Transaction
              </VBtn>
              <VBtn
                color="error"
                variant="tonal"
                @click="emit('decline', details.id)"
              >
                Reject Transaction
              </VBtn>
            </div>
          </VCol>
        </VRow>
      </VCardText>
    </VCard>
  </VDialog>
</template>
