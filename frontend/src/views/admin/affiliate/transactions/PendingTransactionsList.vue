<script setup>
import { isEmpty } from '@/@core/utils'
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { processErrors } from '@/utils/errorHandler'
import { calculateStartIndex, formattedPhoneNumber, formatCurrency, resolveInitials } from '@/utils/helpers'
import { refDebounced } from '@vueuse/core'
import { computed, onMounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import PendingTransactionDetailsDialog from './_components/PendingTransactionDetailsDialog.vue'
import PendingTransactionDeclineDialog from './_components/PendingTransactionDeclineDialog.vue'

const globalData = useGlobalData()
const { showSnackbar } = globalData
const route = useRoute()
const router = useRouter()
const searchQuery = ref(route.query.search ?? null)
const debouncedSearchQuery = refDebounced(searchQuery, 300)
const dateRange = ref(route.query.from_date && route.query.to_date ? route.query.from_date + ' to ' + route.query.to_date : null)
const fromDate = ref(null)
const toDate = ref(null)
const rowPerPage = ref(10)
const currentPage = ref(!isNaN(parseInt(route.query.page)) ? parseInt(route.query.page) : 1)
const totalPage = ref(1)
const totalItems = ref(0)
const items = ref([])
const sortByColumnName = ref(route.query.sort_by ?? 'created_at')
const sortDirection = ref(route.query.sort_dir ?? 'desc')
const skeletonLoading = ref(true)
const isTransactionDetailsDialogVisible = ref(false)
const selectedTransactionId = ref('')
const isTransactionApproveDialogVisible = ref(false)
const isTransactionDeclineDialogVisible = ref(false)
const transactionActionLoading = ref(false)

onMounted(() => {
  fetchItems()
})

const updateRoute = () => {
  router.replace({
    query: {
      page: currentPage.value,
      per_page: rowPerPage.value,
      search: searchQuery.value,
      sort_by: sortByColumnName.value,
      sort_dir: sortDirection.value,
      from_date: fromDate.value,
      to_date: toDate.value,
    },
  })
}

watch(dateRange, () => {
  if (isEmpty(dateRange.value)) {
    fromDate.value = null
    toDate.value = null

    return
  }

  const dateArr = dateRange.value.split(' to ')

  if (dateArr.length === 1) {
    fromDate.value = dateArr[0] || null
    toDate.value = dateArr[0] || null
  } else if (dateArr.length === 2) {
    fromDate.value = dateArr[0] || null
    toDate.value = dateArr[1] || null
  } else {
    fromDate.value = null
    toDate.value = null
  }
})

watch([debouncedSearchQuery, rowPerPage, fromDate, toDate], () => {
  currentPage.value = 1
  fetchItems()
})

watch([currentPage, sortByColumnName, sortDirection], () => {
  fetchItems()
})

const fetchItems = async () => {
  skeletonLoading.value = true

  try {
    const { data } = await ApiService.post('/admin/affiliate-pending-transaction-list', {
      searchQuery: searchQuery.value,
      perPage: rowPerPage.value,
      page: currentPage.value,
      sortByColumnName: sortByColumnName.value,
      isSortDirDesc: sortDirection.value,
      from_date: fromDate.value,
      to_date: toDate.value,
    })

    if (data.status === 200) {
      const pagedData = data.users

      if (isEmpty(pagedData.records) && currentPage.value !== 1) {
        currentPage.value = currentPage.value - 1

        return
      }

      let startIndex = calculateStartIndex(pagedData.current_page, pagedData.per_page)
      if (currentPage.value > 1) {
        startIndex = (currentPage.value - 1) * rowPerPage.value + 1
      }

      items.value = pagedData.records.map((d, index) => ({
        ...d,
        sno: index + startIndex,
      }))
      totalPage.value = pagedData.totalPage == 0 ? 1 : pagedData.totalPage
      totalItems.value = pagedData.totalRecords
      updateRoute()
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    console.error(error)
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    skeletonLoading.value = false
  }
}

const sortColumn = columnName => {
  if (sortByColumnName.value !== '' && sortByColumnName.value === columnName && sortDirection.value === 'asc') {
    sortDirection.value = 'desc'
  } else {
    sortDirection.value = 'asc'
  }

  sortByColumnName.value = columnName
}

async function handleRecordApproveConfirmation(isConfirmed) {
  if (isConfirmed) {
    await approveRecord()
  }
}

async function approveRecord() {
  try {
    transactionActionLoading.value = true

    const { data } = await ApiService.get(`/admin/approve-affiliate-pending-transaction/${selectedTransactionId.value}`)

    if (data.status === 200) {
      selectedTransactionId.value = ''
      showSnackbar(data.message)
      fetchItems()
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    isTransactionApproveDialogVisible.value = false
    isTransactionDeclineDialogVisible.value = false
    transactionActionLoading.value = false
  }
}

async function handleRecordDeclineConfirmation(cancelReason) {
  if (cancelReason) {
    await declineRecord({
      id: selectedTransactionId.value,
      cancel_reason: cancelReason,
    })
  }
}

async function declineRecord(requestBody) {
  try {
    transactionActionLoading.value = true

    const { data } = await ApiService.post('/admin/reject-affiliate-pending-transaction', requestBody)

    if (data.status === 200) {
      selectedTransactionId.value = ''
      showSnackbar(data.message)
      fetchItems()
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    isTransactionApproveDialogVisible.value = false
    isTransactionDeclineDialogVisible.value = false
    transactionActionLoading.value = false
  }
}

// 👉 Computing pagination data
const paginationData = computed(() => {
  const currentPageValue = currentPage.value <= 0 ? 1 : currentPage.value

  const firstIndex = items.value.length
    ? (currentPageValue - 1) * rowPerPage.value + 1
    : 0

  const lastIndex = items.value.length + (currentPageValue - 1) * rowPerPage.value

  return `Showing ${firstIndex} to ${lastIndex} of ${totalItems.value} entries`
})
</script>

<template>
  <section>
    <VRow class="match-height">
      <VCol cols="12">
        <VCard>
          <!-- 👉 Filters -->
          <VCardText>
            <VRow>
              <VCol cols="12">
                <VAlert variant="tonal">
                  <VIcon
                    icon="tabler-info-circle"
                    size="20"
                    class="me-2"
                  />
                  <span>Request will be auto approved once the order is delivered, if not approved manually.</span>
                </VAlert>
              </VCol>
              <VCol
                cols="12"
                sm="4"
              >
                <AppDateTimePicker
                  v-model="dateRange"
                  label="Created Date"
                  placeholder="Select Date Range"
                  variant="outlined"
                  :config="{
                    mode: 'range',
                    dateFormat: 'm/d/Y',
                    onClose: function (selectedDates, dateStr, instance) {
                      if (selectedDates.length === 1) {
                        instance.setDate([selectedDates[0], selectedDates[0]], true)
                      }
                    },
                    maxDate: new Date(),
                    minDate: (new Date()).setFullYear((new Date()).getFullYear() - 120),
                  }"
                  clearable
                  clear-icon="tabler-x"
                />
              </VCol>
            </VRow>
          </VCardText>

          <VDivider />

          <VCardText class="d-flex flex-wrap py-4 gap-4">
            <div
              class="me-3"
              style="width: 80px"
            >
              <VSelect
                v-model="rowPerPage"
                density="compact"
                variant="outlined"
                :items="[10, 20, 30, 50]"
              />
            </div>

            <VSpacer />

            <div class="d-flex align-center flex-wrap gap-4">
              <!-- 👉 Search  -->
              <div style="width: 23rem">
                <VTextField
                  v-model="searchQuery"
                  placeholder="Search"
                  density="compact"
                  clearable
                  clear-icon="tabler-x"
                />
              </div>
            </div>
          </VCardText>

          <VDivider />

          <VTable class="text-no-wrap">
            <!-- 👉 table head -->
            <thead>
              <tr>
                <th scope="col">
                  #
                </th>
                <th scope="col">
                  ORDER ID
                </th>
                <th scope="col">
                  AFFILIATE USER
                </th>
                <th scope="col">
                  Product Purchased
                </th>
                <th scope="col">
                  Commission
                </th>
                <th
                  scope="col"
                  class="cursor-pointer"
                  @click="sortColumn('created_at')"
                >
                  <div class="w-125px">
                    CREATED AT
                    <span class="d-grid float-right">
                      <VIcon
                        size="10"
                        icon="tabler-chevron-up"
                      />
                      <VIcon
                        size="10"
                        icon="tabler-chevron-down"
                      />
                    </span>
                  </div>
                </th>
                <th scope="col">
                  ACTIONS
                </th>
              </tr>
            </thead>

            <!-- 👉 table body -->
            <tbody v-if="skeletonLoading">
              <tr
                v-for="n in 10"
                :key="n"
              >
                <td>
                  <Skeleton
                    width="2rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
                <td>
                  <Skeleton
                    width="8rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
                <td>
                  <Skeleton
                    width="8rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
                <td>
                  <Skeleton
                    width="8rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
                <td>
                  <Skeleton
                    width="8rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
                <td>
                  <Skeleton
                    width="8rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
                <td>
                  <Skeleton
                    width="8rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
              </tr>
            </tbody>
            <tbody v-else>
              <tr
                v-for="item in items"
                :key="item.id"
                style="height: 3.75rem"
              >
                <!-- 👉 NO -->
                <td>
                  {{ item.sno }}
                </td>

                <!-- 👉 Order ID -->
                <td>
                  <!--
                    <RouterLink
                    :to="{
                    name: `admin-${item.category.toLowerCase()}-order-details`,
                    params: { orderId: item.order_id },
                    query: { ...route.query },
                    }"
                    class="dt-link"
                    >
                    {{ item.order_no }}
                    </RouterLink>
                  -->
                  <div
                    class="dt-link"
                    @click="() => {
                      selectedTransactionId = item.id
                      isTransactionDetailsDialogVisible = true
                    }"
                  >
                    {{ item.order_no }}
                  </div>
                </td>

                <!-- 👉 affiliate User -->
                <td class="title">
                  <RouterLink
                    :to="{
                      name: 'admin-affiliate-user-details',
                      params: { userId: item.affiliate_user?.id },
                    }"
                  >
                    <div class="d-flex align-center py-1">
                      <VAvatar
                        v-if="item.affiliate_user?.selfie"
                        variant="tonal"
                        size="40"
                        class="me-3"
                        :image="item.affiliate_user?.selfie"
                        rounded
                      />
                      <VAvatar
                        v-else
                        variant="tonal"
                        size="40"
                        class="me-3"
                        rounded
                      >
                        {{ resolveInitials(item.affiliate_user?.first_name + ' ' + item.affiliate_user?.last_name) }}
                      </VAvatar>
                      <div>
                        <h6 class="text-base">
                          {{ item.affiliate_user?.first_name + ' ' + item.affiliate_user?.last_name }}
                        </h6>
                        <div class="d-flex flex-column">
                          <span class="text-body-2">{{ item.affiliate_user?.email }}</span>
                          <span class="text-body-2">{{ formattedPhoneNumber(item.affiliate_user?.phone_number) }}</span>
                        </div>
                      </div>
                    </div>
                  </RouterLink>
                </td>

                <!-- 👉 Product Purchased -->
                <td class="title">
                  <div class="d-flex align-center py-1">
                    <VAvatar
                      v-if="item.product_img"
                      variant="tonal"
                      size="40"
                      class="me-3"
                      :image="item.product_img"
                      rounded
                    />
                    <VAvatar
                      v-else
                      variant="tonal"
                      size="40"
                      class="me-3"
                      rounded
                    >
                      {{ resolveInitials(item.product_name) }}
                    </VAvatar>
                    <div>
                      <h6 class="text-base">
                        {{ item.product_name }}
                      </h6>
                      <div
                        v-if="item.category?.toLowerCase() === 'wl'"
                        class="d-flex flex-column"
                      >
                        <span class="d-block text-body-2">
                          {{ item.strength }} {{ item.strength_unit }} / weekly
                        </span>
                      </div>
                      <div
                        v-else
                        class="d-flex flex-column"
                      >
                        <span class="d-block text-body-2">
                          {{ item.strength }} {{ item.strength_unit }} x {{ item.qty * item.subscription_interval }} units
                        </span>
                        <span
                          v-if="item.is_medicine_pickup_at_local_pharmacy !== 1"
                          class="d-block text-body-2"
                        >({{ item.subscription_interval }} month supply)</span>
                      </div>
                    </div>
                  </div>
                </td>

                <!-- 👉 Commission -->
                <td>
                  <div class="font-weight-semibold">
                    {{ formatCurrency(item.commission_amount) }}
                  </div>
                  <div
                    v-if="item.commission_type === 'percentage'"
                    class="text-body-2"
                  >
                    {{ item.commission_value }}%
                  </div>
                </td>

                <!-- 👉 Created at -->
                <td>
                  <div class="d-flex flex-column">
                    <span>{{ item.created_date }}</span>
                    <span>{{ item.created_time }}</span>
                  </div>
                </td>

                <!-- 👉 Actions -->
                <td
                  class="text-center"
                  style="width: 5rem"
                >
                  <VBtn
                    icon
                    size="x-small"
                    color="default"
                    variant="text"
                    @click="() => {
                      selectedTransactionId = item.id
                      isTransactionDetailsDialogVisible = true
                    }"
                  >
                    <VIcon
                      size="22"
                      icon="tabler-eye"
                    />
                  </VBtn>
                </td>
              </tr>
            </tbody>

            <!-- 👉 table footer  -->
            <tfoot v-show="!skeletonLoading && items.length === 0">
              <tr>
                <td
                  colspan="11"
                  class="text-center"
                >
                  No data available
                </td>
              </tr>
            </tfoot>
          </VTable>

          <VDivider />

          <VCardText class="d-flex align-center flex-wrap justify-space-between gap-4 py-3 px-5">
            <span class="text-sm text-disabled">
              {{ paginationData }}
            </span>
            <VPagination
              v-model="currentPage"
              size="small"
              :total-visible="5"
              :length="totalPage"
            />
          </VCardText>
        </VCard>
      </VCol>
    </VRow>

    <!-- 👉 View Details -->
    <PendingTransactionDetailsDialog
      v-model:isDialogVisible="isTransactionDetailsDialogVisible"
      :record-id="selectedTransactionId"
      @approve="(recordId) => {
        selectedTransactionId = recordId
        isTransactionDetailsDialogVisible = false
        isTransactionApproveDialogVisible = true
      }"
      @decline="(recordId) => {
        selectedTransactionId = recordId
        isTransactionDetailsDialogVisible = false
        isTransactionDeclineDialogVisible = true
      }"
    />

    <!-- 👉 Confirm Approve -->
    <ConfirmDialog
      v-model:isDialogVisible="isTransactionApproveDialogVisible"
      confirmation-question="Are you sure you want to approve this transaction?"
      :loading="transactionActionLoading"
      @confirm="handleRecordApproveConfirmation"
    />

    <!-- 👉 Decline Dialog -->
    <PendingTransactionDeclineDialog
      v-model:isDialogVisible="isTransactionDeclineDialogVisible"
      @submit="handleRecordDeclineConfirmation"
    />
  </section>
</template>

<style lang="scss">
.v-table__wrapper td.title {
  min-width: 200px;
  h6 {
    a {
      white-space: pre-line;
    }
  }
}
</style>
