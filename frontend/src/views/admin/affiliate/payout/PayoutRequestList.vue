<script setup>
import { isEmpty } from '@/@core/utils'
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { processErrors } from '@/utils/errorHandler'
import { calculateStartIndex, formattedPhoneNumber, formatCurrency, resolveInitials } from '@/utils/helpers'
import { refDebounced } from '@vueuse/core'
import { computed, onMounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const globalData = useGlobalData()
const { showSnackbar } = globalData
const route = useRoute()
const router = useRouter()
const searchQuery = ref(route.query.search ?? null)
const debouncedSearchQuery = refDebounced(searchQuery, 300)
const selectedStatus = ref(!isNaN(parseInt(route.query.status)) ? parseInt(route.query.status) : null)
const rowPerPage = ref(10)
const currentPage = ref(!isNaN(parseInt(route.query.page)) ? parseInt(route.query.page) : 1)
const totalPage = ref(1)
const totalItems = ref(0)
const items = ref([])
const sortByColumnName = ref(route.query.sort_by ?? 'created_at')
const sortDirection = ref(route.query.sort_dir ?? 'desc')
const skeletonLoading = ref(true)

const status = [
  {
    title: 'Pending',
    value: 0,
  },
  {
    title: 'Approved',
    value: 1,
  },
  {
    title: 'Rejected',
    value: 2,
  },
]

const payoutMethods = ref(['PayPal', 'Direct Bank Transfer'])
const filterPayoutMethod = ref([])
const isPayoutRequestDialogVisible = ref(false)
const selectedPayoutRequestId = ref('')
const isPayoutApproveDialogVisible = ref(false)
const isPayoutDeclineDialogVisible = ref(false)
const payoutActionLoading = ref(false)

onMounted(() => {
  fetchItems()
})

const updateRoute = () => {
  router.replace({
    query: {
      page: currentPage.value,
      per_page: rowPerPage.value,
      status: selectedStatus.value,
      search: searchQuery.value,
      sort_by: sortByColumnName.value,
      sort_dir: sortDirection.value,
      payout_method: filterPayoutMethod.value,
    },
  })
}

watch([debouncedSearchQuery, selectedStatus, rowPerPage, filterPayoutMethod], () => {
  currentPage.value = 1
  fetchItems()
})

watch([currentPage, sortByColumnName, sortDirection], () => {
  fetchItems()
})

const fetchItems = async () => {
  skeletonLoading.value = true

  try {
    const { data } = await ApiService.post('/admin/payout-list', {
      searchQuery: searchQuery.value,
      perPage: rowPerPage.value,
      page: currentPage.value,
      sortByColumnName: sortByColumnName.value,
      isSortDirDesc: sortDirection.value,
      status: selectedStatus.value,
      payout_method: filterPayoutMethod.value,
    })

    if (data.status === 200) {
      const pagedData = data.users

      if (isEmpty(pagedData.records) && currentPage.value !== 1) {
        currentPage.value = currentPage.value - 1

        return
      }

      let startIndex = calculateStartIndex(pagedData.current_page, pagedData.per_page)
      if (currentPage.value > 1) {
        startIndex = (currentPage.value - 1) * rowPerPage.value + 1
      }

      items.value = pagedData.records.map((d, index) => ({
        ...d,
        sno: index + startIndex,
      }))
      totalPage.value = pagedData.totalPage == 0 ? 1 : pagedData.totalPage
      totalItems.value = pagedData.totalRecords
      updateRoute()
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    console.error(error)
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    skeletonLoading.value = false
  }
}

const sortColumn = columnName => {
  if (sortByColumnName.value !== '' && sortByColumnName.value === columnName && sortDirection.value === 'asc') {
    sortDirection.value = 'desc'
  } else {
    sortDirection.value = 'asc'
  }

  sortByColumnName.value = columnName
}

async function handlePayoutApproveConfirmation(isConfirmed) {
  if (isConfirmed) {
    await approvePayout()
  }
}

async function approvePayout() {
  try {
    payoutActionLoading.value = true

    const { data } = await ApiService.get(`/admin/payout-request-approve/${selectedPayoutRequestId.value}`)

    if (data.status === 200) {
      selectedPayoutRequestId.value = ''
      showSnackbar(data.message)
      fetchItems()
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    isPayoutApproveDialogVisible.value = false
    isPayoutDeclineDialogVisible.value = false
    payoutActionLoading.value = false
  }
}

async function handlePayoutDeclineConfirmation(cancelReason) {
  if (cancelReason) {
    await declinePayout({
      id: selectedPayoutRequestId.value,
      cancel_reason: cancelReason,
    })
  }
}

async function declinePayout(requestBody) {
  try {
    payoutActionLoading.value = true

    const { data } = await ApiService.post('/admin/payout-request-reject', requestBody)

    if (data.status === 200) {
      selectedPayoutRequestId.value = ''
      showSnackbar(data.message)
      fetchItems()
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    isPayoutApproveDialogVisible.value = false
    isPayoutDeclineDialogVisible.value = false
    payoutActionLoading.value = false
  }
}

// 👉 Computing pagination data
const paginationData = computed(() => {
  const currentPageValue = currentPage.value <= 0 ? 1 : currentPage.value

  const firstIndex = items.value.length
    ? (currentPageValue - 1) * rowPerPage.value + 1
    : 0

  const lastIndex = items.value.length + (currentPageValue - 1) * rowPerPage.value

  return `Showing ${firstIndex} to ${lastIndex} of ${totalItems.value} entries`
})

function resolveStatus(status) {
  if (status === 0) {
    return { label: 'Pending', color: 'warning' }
  } else if (status === 1) {
    return { label: 'Approved', color: 'success' }
  } else if (status === 2) {
    return { label: 'Rejected', color: 'error' }
  }
}
</script>

<template>
  <section>
    <VRow class="match-height">
      <VCol cols="12">
        <VCard title="Filter">
          <!-- 👉 Filters -->
          <VCardText>
            <VRow>
              <VCol
                cols="12"
                sm="4"
              >
                <AppSelect
                  v-model="selectedStatus"
                  label="Payout Status"
                  :items="status"
                  clearable
                  clear-icon="tabler-x"
                  placeholder="Select"
                />
              </VCol>
              <VCol
                cols="12"
                sm="4"
              >
                <AppSelect
                  v-model="filterPayoutMethod"
                  label="Payout Method"
                  :items="payoutMethods"
                  clearable
                  clear-icon="tabler-x"
                  placeholder="Select"
                />
              </VCol>
            </VRow>
          </VCardText>

          <VDivider />

          <VCardText class="d-flex flex-wrap py-4 gap-4">
            <div
              class="me-3"
              style="width: 80px"
            >
              <VSelect
                v-model="rowPerPage"
                density="compact"
                variant="outlined"
                :items="[10, 20, 30, 50]"
              />
            </div>

            <VSpacer />

            <div class="d-flex align-center flex-wrap gap-4">
              <!-- 👉 Search  -->
              <div style="width: 23rem">
                <VTextField
                  v-model="searchQuery"
                  placeholder="Search"
                  density="compact"
                  clearable
                  clear-icon="tabler-x"
                />
              </div>
            </div>
          </VCardText>

          <VDivider />

          <VTable class="text-no-wrap">
            <!-- 👉 table head -->
            <thead>
              <tr>
                <th scope="col">
                  #
                </th>
                <th scope="col">
                  AFFILIATE USER
                </th>
                <th scope="col">
                  AMOUNT
                </th>
                <th scope="col">
                  PAYOUT METHOD
                </th>
                <th scope="col">
                  STATUS
                </th>
                <th
                  scope="col"
                  class="cursor-pointer"
                  @click="sortColumn('created_at')"
                >
                  <div class="w-125px">
                    CREATED AT
                    <span class="d-grid float-right">
                      <VIcon
                        size="10"
                        icon="tabler-chevron-up"
                      />
                      <VIcon
                        size="10"
                        icon="tabler-chevron-down"
                      />
                    </span>
                  </div>
                </th>
                <th
                  scope="col"
                  class="cursor-pointer"
                  @click="sortColumn('updated_at')"
                >
                  <div class="w-125px">
                    UPDATED AT
                    <span class="d-grid float-right">
                      <VIcon
                        size="10"
                        icon="tabler-chevron-up"
                      />
                      <VIcon
                        size="10"
                        icon="tabler-chevron-down"
                      />
                    </span>
                  </div>
                </th>
                <th scope="col">
                  ACTIONS
                </th>
              </tr>
            </thead>

            <!-- 👉 table body -->
            <tbody v-if="skeletonLoading">
              <tr
                v-for="n in 10"
                :key="n"
              >
                <td>
                  <Skeleton
                    width="2rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
                <td>
                  <Skeleton
                    width="12rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
                <td>
                  <Skeleton
                    width="8rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
                <td>
                  <Skeleton
                    width="8rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
                <td>
                  <Skeleton
                    width="8rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
                <td>
                  <Skeleton
                    width="8rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
                <td>
                  <Skeleton
                    width="8rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
                <td>
                  <Skeleton
                    width="8rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
              </tr>
            </tbody>
            <tbody v-else>
              <tr
                v-for="item in items"
                :key="item.id"
                style="height: 3.75rem"
              >
                <!-- 👉 NO -->
                <td>
                  {{ item.sno }}
                </td>

                <!-- 👉 User -->
                <td class="title">
                  <div class="d-flex align-center py-1">
                    <VAvatar
                      v-if="item.user?.profile_picture"
                      variant="tonal"
                      size="40"
                      class="me-3"
                      :image="item.user?.profile_picture"
                      rounded
                    />
                    <VAvatar
                      v-else
                      variant="tonal"
                      size="40"
                      class="me-3"
                      rounded
                    >
                      {{ resolveInitials(item.user?.first_name + ' ' + item.user?.last_name) }}
                    </VAvatar>
                    <div>
                      <div>
                        <h6 class="text-base">
                          {{ item.user?.first_name }} {{ item.user?.last_name }}
                        </h6>
                        <div class="d-flex flex-column">
                          <span class="text-body-2">{{ item.user?.email }}</span>
                          <span class="text-body-2">{{ formattedPhoneNumber(item.user?.phone_number) }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </td>

                <!-- 👉 Amount -->
                <td>
                  {{ formatCurrency(item.amount) }}
                </td>

                <!-- 👉 Payout Method -->
                <td>
                  {{ item.payout_method }}
                </td>

                <!-- 👉 Payout Status -->
                <td>
                  <VChip
                    label
                    :color="resolveStatus(item.status).color"
                    size="small"
                    class="text-capitalize"
                  >
                    {{ resolveStatus(item.status).label }}
                  </VChip>
                </td>

                <!-- 👉 Created at -->
                <td>
                  <div class="d-flex flex-column">
                    <span>{{ item.created_date }}</span>
                    <span>{{ item.created_time }}</span>
                  </div>
                </td>

                <!-- 👉 Updated at -->
                <td>
                  <div
                    v-if="item.updated_date !== item.created_date || item.updated_time !== item.created_time"
                    class="d-flex flex-column"
                  >
                    <span>{{ item.updated_date }}</span>
                    <span>{{ item.updated_time }}</span>
                  </div>
                  <div v-else>
                    -
                  </div>
                </td>

                <!-- 👉 Actions -->
                <td
                  class="text-center"
                  style="width: 5rem"
                >
                  <VBtn
                    v-tooltip.top="'View Details'"
                    icon
                    size="x-small"
                    color="default"
                    variant="text"
                    @click="() => {
                      selectedPayoutRequestId = item.id
                      isPayoutRequestDialogVisible = true
                    }"
                  >
                    <VIcon
                      size="22"
                      icon="tabler-eye"
                    />
                  </VBtn>

                  <!--
                    <VMenu>
                    <template #activator="{ props }">
                    <VBtn
                    v-bind="props"
                    variant="tonal"
                    color="secondary"
                    rounded="lg"
                    size="x-small"
                    icon
                    >
                    <VIcon
                    icon="mdi-dots-horizontal"
                    size="22"
                    />
                    </VBtn>
                    </template>

                    <VList>
                    <VListItem
                    @click="() => {
                    selectedPayoutRequestId = item.id
                    isPayoutRequestDialogVisible = true
                    }"
                    >
                    <VListItemTitle>
                    <div class="font-weight-medium">
                    View
                    </div>
                    </VListItemTitle>
                    </VListItem>

                    <VListItem
                    v-if="item.status === 0"
                    @click="() => {
                    selectedPayoutRequestId = item.id
                    isPayoutApproveDialogVisible = true
                    }"
                    >
                    <VListItemTitle>
                    <div class="font-weight-medium">
                    Approve
                    </div>
                    </VListItemTitle>
                    </VListItem>

                    <VListItem
                    v-if="item.status === 0"
                    @click="() => {
                    selectedPayoutRequestId = item.id
                    isPayoutDeclineDialogVisible = true
                    }"
                    >
                    <VListItemTitle>
                    <div class="font-weight-medium text-error">
                    Reject
                    </div>
                    </VListItemTitle>
                    </VListItem>
                    </VList>
                    </VMenu>
                  -->
                </td>
              </tr>
            </tbody>

            <!-- 👉 table footer  -->
            <tfoot v-show="!skeletonLoading && items.length === 0">
              <tr>
                <td
                  colspan="8"
                  class="text-center"
                >
                  No data available
                </td>
              </tr>
            </tfoot>
          </VTable>

          <VDivider />

          <VCardText class="d-flex align-center flex-wrap justify-space-between gap-4 py-3 px-5">
            <span class="text-sm text-disabled">
              {{ paginationData }}
            </span>
            <VPagination
              v-model="currentPage"
              size="small"
              :total-visible="5"
              :length="totalPage"
            />
          </VCardText>
        </VCard>
      </VCol>
    </VRow>

    <!-- 👉 View Payout Request -->
    <PayoutRequestDetailsDialog
      v-model:isDialogVisible="isPayoutRequestDialogVisible"
      :payout-request-id="selectedPayoutRequestId"
      @approve="(payoutId) => {
        selectedPayoutRequestId = payoutId
        isPayoutRequestDialogVisible = false
        isPayoutApproveDialogVisible = true
      }"
      @decline="(payoutId) => {
        selectedPayoutRequestId = payoutId
        isPayoutRequestDialogVisible = false
        isPayoutDeclineDialogVisible = true
      }"
    />

    <!-- 👉 Confirm Payout Approve -->
    <ConfirmDialog
      v-model:isDialogVisible="isPayoutApproveDialogVisible"
      confirmation-question="Are you sure you want to approve this payout request?"
      :loading="payoutActionLoading"
      @confirm="handlePayoutApproveConfirmation"
    />

    <!-- 👉 Payout Decline Dialog -->
    <PayoutRequestDeclineDialog
      v-model:isDialogVisible="isPayoutDeclineDialogVisible"
      @submit="handlePayoutDeclineConfirmation"
    />
  </section>
</template>

<style lang="scss">
.v-table__wrapper td.title {
  min-width: 200px;
  h6 {
    a {
      white-space: pre-line;
    }
  }
}
</style>
