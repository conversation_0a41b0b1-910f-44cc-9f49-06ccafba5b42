<script setup>
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { processErrors } from '@/utils/errorHandler'
import { formatCurrency } from '@/utils/helpers'
import { IconChartBar } from '@tabler/icons-vue'
import { onMounted } from 'vue'

const { showSnackbar } = useGlobalData()
const commissionTiers = ref([])
const skeletonLoading = ref(false)
const serverErrors = ref([])
const itemId = ref(null)
const isLoading = ref(false)
const isDeleteDialogVisible = ref(false)
const isStatusDialogVisible = ref(false)

onMounted(async () => {
  await getTiers()
})

async function getTiers() {
  try {
    skeletonLoading.value = true
    serverErrors.value = []

    const { data } = await ApiService.get('/admin/region-affiliate-commission-tier-list')

    if (data.status === 200) {
      commissionTiers.value = data.stateWiseTiers
    } else {
      showSnackbar(processErrors(data)[0], 'error')
    }
  } catch (error) {
    console.error(error)
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    skeletonLoading.value = false
  }
}

async function handleDeleteConfirmation(isConfirmed) {
  if (isConfirmed) {
    await deleteTier()
  }
}

async function deleteTier() {
  try {
    isLoading.value = true

    const { data } = await ApiService.get(`/admin/delete-region-affiliate-commission-tier/${itemId.value}`)

    if (data.status === 200) {
      showSnackbar(data.message)
      itemId.value = null
      getTiers()
    } else {
      showSnackbar(processErrors(data)[0], 'error')
    }
  } catch (error) {
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    isLoading.value = false
    isDeleteDialogVisible.value = false
  }
}

const resolveItemStatus = stat => {
  if (stat === 1) {
    return { color: 'primary', status: true }
  } else {
    return { color: 'secondary', status: false }
  }
}

async function handleToggleStatusConfirmation(isConfirmed) {
  if (isConfirmed) {
    await toggleStatus()
  }
}

async function toggleStatus() {
  try {
    isLoading.value = true

    const { data } = await ApiService.get(`/admin/update-affiliate-commission-tier-status/${itemId.value}`)

    if (data.status === 200) {
      showSnackbar(data.message)
      itemId.value = null
      getTiers()
    } else {
      showSnackbar(processErrors(data)[0], 'error')
    }
  } catch (error) {
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    isLoading.value = false
    isStatusDialogVisible.value = false
  }
}
</script>

<template>
  <div>
    <VCard title="Commission Tiers">
      <template #append>
        <VBtn :to="{ name: 'admin-affiliate-commission-tier-add' }">
          Add Commission Tier
        </VBtn>
      </template>

      <!-- Loading -->
      <VCardText v-if="skeletonLoading">
        <div
          class="d-flex flex-column align-center justify-center"
          style="height: 200px;"
        >
          <VProgressCircular
            size="42"
            indeterminate
          />
        </div>
      </VCardText>

      <!-- Empty state -->
      <VCardText v-if="!skeletonLoading && commissionTiers.length === 0">
        <div class="d-flex flex-column align-center gap-2">
          <IconChartBar
            size="80"
            stroke-width="1.5"
          />
          <div class="text-lg">
            No Commission Tiers
          </div>
          <div class="mt-6">
            <VBtn :to="{ name: 'admin-affiliate-commission-tier-add' }">
              Add Commission Tier
            </VBtn>
          </div>
        </div>
      </VCardText>

      <!-- Commission Tiers -->
      <VCardText v-if="!skeletonLoading && commissionTiers.length > 0">
        <VCard
          v-for="commissionTier in commissionTiers"
          :key="commissionTier.id"
          variant="outlined"
          class="pa-3 px-5 mb-6"
        >
          <div class="text-lg mb-2">
            {{ commissionTier.tier_name }}
          </div>
          <div class="d-flex flex-wrap gap-3">
            <div
              v-for="(tierItem, index) in commissionTier.tiers"
              :key="index"
              class="border rounded-lg pa-3"
              style="flex: 1; min-width: 280px;"
            >
              <div class="text-caption text-uppercase mb-2">
                Tier {{ index + 1 }}
              </div>
              <div v-if="tierItem.max === '-1'">
                Range: Above {{ formatCurrency(tierItem.min) }}
              </div>
              <div v-else>
                Range: {{ formatCurrency(tierItem.min) }} to {{ formatCurrency(tierItem.max) }}
              </div>
              <div>Commission: {{ tierItem.commission_type === 'percentage' ? `${tierItem.commission_value}%` : formatCurrency(tierItem.commission_value) }}</div>
            </div>
          </div>
          <div class="mt-4 d-flex justify-space-between align-center gap-4">
            <div>
              <VSwitch
                v-model="resolveItemStatus(commissionTier.is_active).status"
                :label="resolveItemStatus(commissionTier.is_active).status ? 'Active' : 'Inactive'"
                inset
                @click="() => {
                  itemId = commissionTier.id
                  isStatusDialogVisible = true
                }"
              />
            </div>
            <div
              v-if="commissionTier.last_updated_at"
              class="text-xs text-end"
            >
              <div>
                Last updated: {{ commissionTier.last_updated_at }}
              </div>
              <div v-if="commissionTier.action_by_name && commissionTier.action_by_role">
                By: {{ commissionTier.action_by_name }} ({{ commissionTier.action_by_role }})
              </div>
            </div>
          </div>
          <div class="d-flex gap-4 mt-3 justify-end">
            <VBtn
              size="small"
              variant="tonal"
              :to="{ name: 'admin-affiliate-commission-tier-edit', params: { id: commissionTier.id } }"
            >
              Edit
            </VBtn>
            <VBtn
              color="error"
              size="small"
              variant="tonal"
              @click="() => {
                itemId = commissionTier.id
                isDeleteDialogVisible = true
              }"
            >
              Delete
            </VBtn>
          </div>
        </VCard>
      </VCardText>
    </VCard>

    <!-- 👉 Delete confirm dialog -->
    <ConfirmDialog
      v-model:isDialogVisible="isDeleteDialogVisible"
      confirmation-question="Are you sure you want to delete this tier?"
      :loading="isLoading"
      @confirm="handleDeleteConfirmation"
    />

    <!-- 👉 Toggle Status dialog -->
    <ConfirmDialog
      v-model:isDialogVisible="isStatusDialogVisible"
      confirmation-question="Are you sure you want to change status for this tier?"
      :loading="isLoading"
      @confirm="handleToggleStatusConfirmation"
    />
  </div>
</template>
