<script setup>
import { useGlobalData } from '@/store/global'
import { useRoute } from 'vue-router'
import { requiredValidator } from '@/@core/utils/validators'
import ApiService from '@/services/ApiService'
import { processErrors } from '@/utils/errorHandler'
import { onMounted, ref } from 'vue'
import { isEmptyObject, isNullOrUndefined } from '@/@core/utils'
import router from '@/router'
import { isNumber } from '@/utils/helpers'
import { v4 as uuidv4 } from 'uuid'

const route = useRoute()
const { showSnackbar } = useGlobalData()
const formRef = ref(null)
const skeletonLoading = ref(false)
const isLoading = ref(false)
const serverErrors = ref([])
const inputErrors = ref({})
const tierName = ref(null)

const tiers = ref([
  {
    id: uuidv4(),
    min: null,
    max: null,
    commission_type: 'percentage',
    commission_value: null,
  },
])

function addTier() {
  tiers.value.push({
    id: uuidv4(),
    min: null,
    max: null,
    commission_type: 'percentage',
    commission_value: null,
  })
}

function removeTier(id) {
  tiers.value = tiers.value.filter(tier => tier.id !== id)
}

onMounted(async () => {
  if (route.fullPath.includes('edit') && route.params.id) {
    await getTierDetails()
  }
})

async function getTierDetails() {
  try {
    skeletonLoading.value = true

    const { data } = await ApiService.get(`/admin/view-region-affiliate-commission-tier/${route.params.id}`)

    if (data.status === 200) {
      tierName.value = data.tierDetails.tier_name
      tiers.value = data.tierDetails.tiers
    } else {
      showSnackbar(processErrors(data)[0], 'error')
      router.go(-1)
    }
  } catch (error) {
    console.error(error)
    showSnackbar(processErrors(error)[0], 'error')
    router.go(-1)
  } finally {
    skeletonLoading.value = false
  }
}

const onFormSubmit = async () => {
  formRef.value?.validate().then(async valid => {
    if (valid.valid) {
      const formData = {
        tier_name: tierName.value,
        tiers: tiers.value.map(tier => {
          return {
            min: tier.min,
            max: tier.max,
            commission_type: tier.commission_type,
            commission_value: tier.commission_value,
          }
        }),
        is_active: 1,
      }

      if (route.fullPath.includes('edit') && route.params.id) {
        formData['id'] = route.params.id
        await updateTier(formData)
      } else {
        await addNewTier(formData)
      }
    }
  })
}

async function addNewTier(formData) {
  try {
    isLoading.value = true

    const { data } = await ApiService.post('/admin/add-region-affiliate-commission-tier', formData)

    if (data.status === 200) {
      showSnackbar(data.message)
      router.push({ name: 'admin-affiliate-commission-tiers' })
    } else {
      if (data.errors) {
        inputErrors.value = data.errors
      } else {
        serverErrors.value = processErrors(data)
      }
    }
  } catch (error) {
    console.error(error)
    serverErrors.value = processErrors(error)
  } finally {
    isLoading.value = false
  }
}

async function updateTier(formData) {
  try {
    isLoading.value = true

    const { data } = await ApiService.post('/admin/update-region-affiliate-commission-tier', formData)

    if (data.status === 200) {
      showSnackbar(data.message)
      router.push({ name: 'admin-affiliate-commission-tiers' })
    } else {
      if (data.errors) {
        inputErrors.value = data.errors
      } else {
        serverErrors.value = processErrors(data)
      }
    }
  } catch (error) {
    console.error(error)
    serverErrors.value = processErrors(error)
  } finally {
    isLoading.value = false
  }
}
</script>

<template>
  <div>
    <VCard :title="route.fullPath.includes('edit') ? 'Edit Commission Tier' : 'Add Commission Tier'">
      <VCardText>
        <!-- 👉 Alert -->
        <VAlert
          v-if="isEmptyObject(inputErrors) && serverErrors.length > 0"
          type="error"
          variant="tonal"
          title="Error!"
          class="mb-6"
        >
          <ul class="mb-0">
            <li
              v-for="error in serverErrors"
              :key="error"
              class="mb-0"
            >
              {{ error }}
            </li>
          </ul>
        </VAlert>

        <!-- 👉 Form -->
        <VForm
          ref="formRef"
          class="pa-0 ma-0"
          @submit.prevent="onFormSubmit"
        >
          <VCol
            col="12"
            md="4"
            class="pa-0 ma-0 mb-5"
          >
            <Skeleton
              v-if="skeletonLoading"
              height="2.5rem"
            />
            <AppTextField
              v-else
              v-model="tierName"
              label="Name of Tier"
              :rules="[requiredValidator]"
            />
            <p
              v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['tier_name'])"
              class="text-sm text-error mb-0 ms-3"
            >
              {{ inputErrors['tier_name'][0] }}
            </p>
          </VCol>

          <VRow
            v-for="(tier, index) in tiers"
            :key="tier.id"
            class="border rounded-lg pa-3 ma-0 mb-4"
          >
            <VCol
              col="12"
              md="4"
            >
              <Skeleton
                v-if="skeletonLoading"
                height="2.5rem"
              />
              <AppTextField
                v-else
                v-model="tier.min"
                label="Minimum Gross Sales"
                :rules="[requiredValidator]"
                prefix="$"
                @keypress="isNumber($event)"
              />
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors[`tiers.${index}.min`])"
                class="text-sm text-error mb-0 ms-3"
              >
                {{ inputErrors[`tiers.${index}.min`][0] }}
              </p>
            </VCol>

            <VCol
              col="12"
              md="4"
            >
              <Skeleton
                v-if="skeletonLoading"
                height="2.5rem"
              />
              <AppTextField
                v-else
                v-model="tier.max"
                label="Maximum Gross Sales"
                :rules="[requiredValidator]"
                prefix="$"
                :hint="index === tiers.length - 1 ? 'Enter -1 if there is no maximum' : ''"
                persistent-hint
                @keypress="isNumber($event, index === tiers.length - 1)"
              />
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors[`tiers.${index}.max`])"
                class="text-sm text-error mb-0 ms-3"
              >
                {{ inputErrors[`tiers.${index}.max`][0] }}
              </p>
            </VCol>

            <VCol cols="12">
              <Skeleton
                v-if="skeletonLoading"
                height="2.5rem"
              />
              <VRadioGroup
                v-else
                v-model="tier.commission_type"
                label="Commission Type"
                :rules="[requiredValidator]"
                inline
              >
                <VRadio
                  label="Percentage"
                  value="percentage"
                  class="ms-2"
                />
                <VRadio
                  label="Fixed Amount"
                  value="fixed"
                  class="ms-2"
                />
              </VRadioGroup>
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors[`tiers.${index}.commission_type`])"
                class="text-sm text-error mb-0 ms-3"
              >
                {{ inputErrors[`tiers.${index}.commission_type`][0] }}
              </p>
            </VCol>

            <VCol
              cols="12"
              md="4"
            >
              <Skeleton
                v-if="skeletonLoading"
                height="2.5rem"
              />
              <AppTextField
                v-else
                v-model="tier.commission_value"
                label="Commission Value"
                :rules="[requiredValidator]"
                :maxlength="tier.commission_value === 'fixed' ? 10 : 2"
                :prefix="tier.commission_value === 'fixed' ? '$' : ''"
                :suffix="tier.commission_value === 'percentage' ? '%' : ''"
                @keypress="isNumber($event)"
              />
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors[`tiers.${index}.commission_value`])"
                class="text-sm text-error mb-0 ms-3"
              >
                {{ inputErrors[`tiers.${index}.commission_value`][0] }}
              </p>
            </VCol>

            <VCol
              v-if="index !== 0"
              cols="12"
              class="text-end"
            >
              <VBtn
                variant="tonal"
                color="error"
                class="me-3"
                @click="removeTier(tier.id)"
              >
                Remove
              </VBtn>
            </VCol>
          </VRow>

          <VBtn
            variant="tonal"
            color="secondary"
            block
            @click="addTier"
          >
            Add New Range
          </VBtn>

          <VDivider class="mt-14" />

          <!-- 👉 Submit and Cancel button -->
          <VCol
            cols="12"
            class="text-end mt-4"
          >
            <VBtn
              variant="tonal"
              color="secondary"
              class="me-3"
              :disabled="isLoading"
              @click="() => {
                router.go(-1)
              }"
            >
              Cancel
            </VBtn>
            <VBtn
              type="submit"

              :loading="isLoading"
            >
              Submit
            </VBtn>
          </VCol>
        </VForm>
      </VCardText>
    </VCard>
  </div>
</template>
