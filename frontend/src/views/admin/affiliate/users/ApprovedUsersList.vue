<script setup>
import { isEmpty } from '@/@core/utils'
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { processErrors } from '@/utils/errorHandler'
import { calculateStartIndex, formattedPhoneNumber, resolveInitials } from '@/utils/helpers'
import { refDebounced } from '@vueuse/core'
import { computed, onMounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const globalData = useGlobalData()
const { showSnackbar } = globalData
const route = useRoute()
const router = useRouter()

const searchQuery = ref(route.query.search ?? null)
const debouncedSearchQuery = refDebounced(searchQuery, 300)
const filterAccountStatus = ref(!isNaN(parseInt(route.query.status)) ? parseInt(route.query.status) : null)
const rowPerPage = ref(10)
const currentPage = ref(!isNaN(parseInt(route.query.page)) ? parseInt(route.query.page) : 1)
const totalPage = ref(1)
const totalItems = ref(0)
const items = ref([])
const sortByColumnName = ref(route.query.sort_by ?? 'created_at')
const sortDirection = ref(route.query.sort_dir ?? 'desc')
const skeletonLoading = ref(true)
const isChangeAccountStatusDialogVisible = ref(false)
const itemId = ref('')
const itemStatus = ref(false)
const changeStatusLoading = ref(false)

onMounted(() => {
  fetchItems()
})

const updateRoute = () => {
  router.replace({
    query: {
      page: currentPage.value,
      per_page: rowPerPage.value,
      search: searchQuery.value,
      sort_by: sortByColumnName.value,
      sort_dir: sortDirection.value,
      status: filterAccountStatus.value,
    },
  })
}

watch([debouncedSearchQuery, rowPerPage, filterAccountStatus], () => {
  currentPage.value = 1
  fetchItems()
})

watch([currentPage, sortByColumnName, sortDirection], () => {
  fetchItems()
})

const fetchItems = async () => {
  skeletonLoading.value = true

  try {
    const { data } = await ApiService.post('/admin/affiliate-users-list', {
      searchQuery: searchQuery.value,
      perPage: rowPerPage.value,
      page: currentPage.value,
      sortByColumnName: sortByColumnName.value,
      isSortDirDesc: sortDirection.value,
      account_status: filterAccountStatus.value,
    })

    if (data.status === 200) {
      const pagedData = data.users
      let startIndex = calculateStartIndex(pagedData.current_page, pagedData.per_page)
      if (currentPage.value > 1) {
        startIndex = (currentPage.value - 1) * rowPerPage.value + 1
      }

      items.value = pagedData.records.map((d, index) => ({
        ...d,
        sno: index + startIndex,
      }))
      totalPage.value = pagedData.totalPage == 0 ? 1 : pagedData.totalPage
      totalItems.value = pagedData.totalRecords
      updateRoute()
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    console.error(error)
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    skeletonLoading.value = false
  }
}

const sortColumn = columnName => {
  if (sortByColumnName.value !== '' && sortByColumnName.value === columnName && sortDirection.value === 'asc') {
    sortDirection.value = 'desc'
  } else {
    sortDirection.value = 'asc'
  }

  sortByColumnName.value = columnName
}

const onAccountStatusChange = (id, status) => {
  isChangeAccountStatusDialogVisible.value = true
  itemId.value = id
  itemStatus.value = status
}

async function handleStatusChangeConfirmation(isConfirmed) {
  if (isConfirmed) {
    await changeItemStatus()
  }
}

const changeItemStatus = async () => {
  changeStatusLoading.value = true

  let itemsValue = items.value
  let itemIndex = itemsValue.findIndex(item => item.id === itemId.value)

  if (itemIndex === -1) {
    console.error('Item not found')
    changeStatusLoading.value = false

    return
  }

  let newStatus = itemsValue[itemIndex].account_status === 1 ? 0 : 1

  ApiService
    .get(`/admin/affiliate-user-account-status/${itemId.value}`)
    .then(response => {
      if (response.data.status === 200) {
        itemsValue[itemIndex].account_status = newStatus
        items.value = itemsValue
        itemId.value = ''
        itemStatus.value = false
        showSnackbar(response.data.message)

        fetchItems()
      } else {
        showSnackbar(response.data.message, 'error')
      }
    })
    .catch(error => {
      console.error(error)
    })
    .finally(() => {
      changeStatusLoading.value = false
      isChangeAccountStatusDialogVisible.value = false
    })
}

// 👉 Computing pagination data
const paginationData = computed(() => {
  const currentPageValue = currentPage.value <= 0 ? 1 : currentPage.value

  const firstIndex = items.value.length
    ? (currentPageValue - 1) * rowPerPage.value + 1
    : 0

  const lastIndex = items.value.length + (currentPageValue - 1) * rowPerPage.value

  return `Showing ${firstIndex} to ${lastIndex} of ${totalItems.value} entries`
})

function resolveProfileStatus(isProfileCompleted) {
  if (isProfileCompleted === 1) {
    return {
      label: 'Active',
      color: 'success',
      status: true,
    }
  } else if (isProfileCompleted === 0) {
    return {
      label: 'Inactive',
      color: 'default',
      status: false,
    }
  } else {
    return {
      label: 'Unknown',
      color: 'default',
      status: false,
    }
  }
}
</script>

<template>
  <section>
    <VRow class="match-height">
      <VCol cols="12">
        <VCard title="Filter">
          <!-- 👉 Filters -->
          <VCardText>
            <VRow>
              <VCol
                cols="12"
                sm="4"
              >
                <AppSelect
                  v-model="filterAccountStatus"
                  label="Account Status"
                  :items="[{ title: 'Active', value: 1 }, { title: 'Inactive', value: 0 }]"
                  clearable
                  clear-icon="tabler-x"
                  placeholder="Select"
                />
              </VCol>
            </VRow>
          </VCardText>

          <VDivider />

          <VCardText class="d-flex flex-wrap py-4 gap-4">
            <div
              class="me-3"
              style="width: 80px"
            >
              <VSelect
                v-model="rowPerPage"
                density="compact"
                variant="outlined"
                :items="[10, 20, 30, 50]"
              />
            </div>

            <VSpacer />

            <div class="d-flex align-center flex-wrap gap-4">
              <!-- 👉 Search  -->
              <div style="width: 23rem">
                <VTextField
                  v-model="searchQuery"
                  placeholder="Search"
                  density="compact"
                  clearable
                  clear-icon="tabler-x"
                />
              </div>
            </div>
          </VCardText>

          <VDivider />

          <VTable class="text-no-wrap">
            <!-- 👉 table head -->
            <thead>
              <tr>
                <th scope="col">
                  #
                </th>
                <th scope="col">
                  USER
                </th>
                <th scope="col">
                  TREATMENTS TARGETED
                </th>
                <th scope="col">
                  ACCOUNT STATUS
                </th>
                <th scope="col">
                  ACTIVE/INACTIVE
                </th>
                <th
                  scope="col"
                  class="cursor-pointer"
                  @click="sortColumn('created_at')"
                >
                  <div class="w-125px">
                    CREATED AT
                    <span class="d-grid float-right">
                      <VIcon
                        size="10"
                        icon="tabler-chevron-up"
                      />
                      <VIcon
                        size="10"
                        icon="tabler-chevron-down"
                      />
                    </span>
                  </div>
                </th>
                <th
                  scope="col"
                  class="cursor-pointer"
                  @click="sortColumn('last_login_at')"
                >
                  <div class="w-125px">
                    LAST ACTIVE
                    <span class="d-grid float-right">
                      <VIcon
                        size="10"
                        icon="tabler-chevron-up"
                      />
                      <VIcon
                        size="10"
                        icon="tabler-chevron-down"
                      />
                    </span>
                  </div>
                </th>
                <th scope="col">
                  ACTIONS
                </th>
              </tr>
            </thead>

            <!-- 👉 table body -->
            <tbody v-if="skeletonLoading">
              <tr
                v-for="n in 10"
                :key="n"
              >
                <td>
                  <Skeleton
                    width="2rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
                <td>
                  <Skeleton
                    width="12rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
                <td>
                  <Skeleton
                    width="8rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
                <td>
                  <Skeleton
                    width="8rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
                <td>
                  <Skeleton
                    width="8rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
                <td>
                  <Skeleton
                    width="8rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
                <td>
                  <Skeleton
                    width="8rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
                <td>
                  <Skeleton
                    width="8rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
              </tr>
            </tbody>
            <tbody v-else>
              <tr
                v-for="item in items"
                :key="item.id"
                style="height: 3.75rem"
              >
                <!-- 👉 NO -->
                <td>
                  {{ item.sno }}
                </td>

                <!-- 👉 User -->
                <td class="title">
                  <RouterLink
                    :to="{
                      name: 'admin-affiliate-user-details',
                      params: { userId: item.id },
                    }"
                    class="d-block"
                  >
                    <div class="d-flex align-center py-1">
                      <VAvatar
                        v-if="item.profile_picture"
                        variant="tonal"
                        size="40"
                        class="me-3"
                        :image="item.profile_picture"
                        rounded
                      />
                      <VAvatar
                        v-else
                        variant="tonal"
                        size="40"
                        class="me-3"
                        rounded
                      >
                        {{ resolveInitials(item.first_name + ' ' + item.last_name) }}
                      </VAvatar>
                      <div>
                        <h6 class="text-base">
                          {{ item.first_name }} {{ item.last_name }}
                        </h6>
                        <div class="d-flex flex-column">
                          <span class="text-body-2">{{ item.email }}</span>
                          <span class="text-body-2">{{ formattedPhoneNumber(item.phone_number) }}</span>
                        </div>
                      </div>
                    </div>
                  </RouterLink>
                </td>

                <!-- 👉 Treatments Targeted -->
                <td>
                  {{ !isEmpty(item.targeted_treatments_to_promote) ? item.targeted_treatments_to_promote.join(', ') : '-' }}
                </td>

                <!-- 👉 Profile Status -->
                <td>
                  <VChip
                    label
                    :color="resolveProfileStatus(item.account_status).color"
                    size="small"
                    class="text-capitalize"
                  >
                    {{ resolveProfileStatus(item.account_status).label }}
                  </VChip>
                </td>

                <!-- 👉 Status Change -->
                <td>
                  <VSwitch
                    v-model="resolveProfileStatus(item.account_status).status"
                    inset
                    @change="onAccountStatusChange(item.id, resolveProfileStatus(item.account_status).status)"
                  />
                </td>

                <!-- 👉 Created at -->
                <td>
                  <div class="d-flex flex-column">
                    <span>{{ item.created_date }}</span>
                    <span>{{ item.created_time }}</span>
                  </div>
                </td>

                <!-- 👉 Updated at -->
                <td>
                  <div
                    v-if="item.last_login_date && item.last_login_time"
                    class="d-flex flex-column"
                  >
                    <span>{{ item.last_login_date }}</span>
                    <span>{{ item.last_login_time }}</span>
                  </div>
                  <div v-else>
                    -
                  </div>
                </td>

                <!-- 👉 Actions -->
                <td
                  class="text-center"
                  style="width: 5rem"
                >
                  <VBtn
                    icon
                    size="x-small"
                    color="default"
                    variant="text"
                    :to="{
                      name: 'admin-affiliate-user-details',
                      params: { userId: item.id },
                    }"
                  >
                    <VIcon
                      size="22"
                      icon="tabler-eye"
                    />
                  </VBtn>
                </td>
              </tr>
            </tbody>

            <!-- 👉 table footer  -->
            <tfoot v-show="!skeletonLoading && items.length === 0">
              <tr>
                <td
                  colspan="8"
                  class="text-center"
                >
                  No data available
                </td>
              </tr>
            </tfoot>
          </VTable>

          <VDivider />

          <VCardText class="d-flex align-center flex-wrap justify-space-between gap-4 py-3 px-5">
            <span class="text-sm text-disabled">
              {{ paginationData }}
            </span>
            <VPagination
              v-model="currentPage"
              size="small"
              :total-visible="5"
              :length="totalPage"
            />
          </VCardText>
        </VCard>
      </VCol>
    </VRow>

    <!-- 👉 Change account Status confirm dialog -->
    <ConfirmDialog
      v-model:isDialogVisible="isChangeAccountStatusDialogVisible"
      confirmation-question="Are you sure you want to change account status for this user?"
      :loading="changeStatusLoading"
      @confirm="handleStatusChangeConfirmation"
    />
  </section>
</template>

<style lang="scss">
.v-table__wrapper td.title {
  min-width: 200px;
  h6 {
    a {
      white-space: pre-line;
    }
  }
}
</style>
