<script setup>
import { isEmpty } from '@/@core/utils'
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { processErrors } from '@/utils/errorHandler'
import { computed, onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import EditUserDetailsDrawer from './interested/EditUserDetailsDrawer.vue'
import EditAddressDrawer from './interested/EditAddressDrawer.vue'
import EditAdditionalDrawer from './interested/EditAdditionalDrawer.vue'
import EditPayoutMethodDrawer from './interested/EditPayoutMethodDrawer.vue'

const route = useRoute()
const userId = computed(() => route.params.userId)
const { showSnackbar } = useGlobalData()
const skeletonLoading = ref(true)
const userDetails = ref(undefined)
const isApproveDialogVisible = ref(false)
const isRejectDialogVisible = ref(false)
const isEditUserDetailsDrawerVisible = ref(false)
const isEditAddressDrawerVisible = ref(false)
const isEditAdditionalDrawerVisible = ref(false)
const isEditPayoutMethodDrawerVisible = ref(false)

const userData = computed(() => {
  return {
    first_name: userDetails.value?.first_name ?? null,
    last_name: userDetails.value?.last_name ?? null,
    email: userDetails.value?.email ?? null,
    phone_number: userDetails.value?.phone_number ?? null,
    website: userDetails.value?.website ?? null,
    company_name: userDetails.value?.company_name ?? null,
  }
})

const currentAddress = computed(() => {
  return {
    address_1: userDetails.value.address_1 ?? null,
    address_2: userDetails.value?.address_2 ?? null,
    city: userDetails.value?.city ?? null,
    state: userDetails.value?.state ?? null,
    zipcode: userDetails.value?.zipcode ?? null,
    country: userDetails.value?.country ?? null,
  }
})

const additionalData = computed(() => {
  return {
    advertising_channel_methods: userDetails.value?.advertising_channel_methods ?? null,
    advertising_channel_method_urls: userDetails.value?.advertising_channel_method_urls ?? null,
    targeted_treatments_to_promote: userDetails.value?.targeted_treatments_to_promote ?? null,
    avg_visitors_per_month: userDetails.value?.avg_visitors_per_month ?? null,
  }
})

const payoutMethod = computed(() => {
  return {
    payout_method: userDetails.value?.payout_method ?? null,
    payout_paypal_email: userDetails.value?.payout_paypal_email ?? null,
    account_holder_first_name: userDetails.value?.account_holder_first_name ?? null,
    account_holder_last_name: userDetails.value?.account_holder_last_name ?? null,
    bank_name: userDetails.value?.bank_name ?? null,
    account_number: userDetails.value?.account_number ?? null,
    routing_number: userDetails.value?.routing_number ?? null,
  }
})

onMounted(async () => {
  await fetchUserDetails()
})

async function fetchUserDetails() {
  try {
    skeletonLoading.value = true

    const { data } = await ApiService.get(`/admin/view-interested-affiliate-user-profile/${userId.value}`)

    if (data.status === 200) {
      userDetails.value = data.userDetails
    } else {
      showSnackbar(processErrors(data)[0], 'error')
    }
  } catch (error) {
    showSnackbar(processErrors(error.response.data)[0], 'error')
  } finally {
    skeletonLoading.value = false
  }
}

function resolveProfileStatus(isProfileCompleted) {
  if (isProfileCompleted === 1) {
    return {
      label: 'Completed',
      color: 'success',
    }
  } else if (isProfileCompleted === 0) {
    return {
      label: 'Incomplete',
      color: 'warning',
    }
  } else {
    return {
      label: 'Unknown',
      color: 'default',
    }
  }
}

function resolveApprovalStatus(status) {
  if (status === 0) {
    return {
      label: 'Pending',
      color: 'warning',
    }
  } else if (status === 1) {
    return {
      label: 'Rejected',
      color: 'error',
    }
  } else {
    return {
      label: 'Unknown',
      color: 'default',
    }
  }
}
</script>

<template>
  <div v-if="skeletonLoading">
    <div
      class="d-flex justify-center align-center"
      style="height: calc(100vh - 14rem)"
    >
      <VProgressCircular
        indeterminate
        size="48"
      />
    </div>
  </div>
  <div v-else>
    <VRow>
      <VCol
        cols="12"
        md="6"
      >
        <VCard
          title="User Details"
          class="h-100"
        >
          <template #append>
            <VBtn
              size="small"
              @click="isEditUserDetailsDrawerVisible = true"
            >
              Edit
            </VBtn>
          </template>
          <VCardText>
            <VTable class="text-no-wrap admin-table">
              <tr v-if="userDetails.first_name">
                <td>First Name</td>
                <td class="text-right list-text-fix">
                  <span>{{ userDetails.first_name }}</span>
                </td>
              </tr>
              <tr v-if="userDetails.last_name">
                <td>Last Name</td>
                <td class="text-right list-text-fix">
                  <span>{{ userDetails.last_name }}</span>
                </td>
              </tr>
              <tr v-if="userDetails.email">
                <td>Email</td>
                <td class="text-right">
                  <span>{{ userDetails.email }}</span>
                </td>
              </tr>
              <tr v-if="userDetails.phone_number">
                <td>Phone Number</td>
                <td class="text-right">
                  <span>{{ userDetails.phone_number }}</span>
                </td>
              </tr>
              <tr v-if="userDetails.company_name">
                <td>Company Name</td>
                <td class="text-right list-text-fix">
                  <span>{{ userDetails.company_name }}</span>
                </td>
              </tr>
              <tr v-if="userDetails.website">
                <td>Website</td>
                <td class="text-right list-text-fix">
                  <span>{{ userDetails.website }}</span>
                </td>
              </tr>
              <tr v-if="userDetails.is_profile_completed">
                <td>Profile Status</td>
                <td class="text-right">
                  <VChip
                    label
                    :color="resolveProfileStatus(userDetails.is_profile_completed).color"
                    size="small"
                    class="text-capitalize"
                  >
                    {{ resolveProfileStatus(userDetails.is_profile_completed).label }}
                  </VChip>
                </td>
              </tr>
              <tr v-if="userDetails.is_profile_reviewed">
                <td>Review Status</td>
                <td class="text-right">
                  <VChip
                    v-if="userDetails.is_profile_completed === 1"
                    label
                    :color="resolveApprovalStatus(userDetails.is_profile_reviewed).color"
                    size="small"
                    class="text-capitalize"
                  >
                    {{ resolveApprovalStatus(userDetails.is_profile_reviewed).label }}
                  </VChip>
                  <span v-else> - </span>
                </td>
              </tr>
              <tr v-if="userDetails.cancel_reason">
                <td>Reject Reason</td>
                <td class="text-right list-text-fix">
                  <span>{{ userDetails.cancel_reason }}</span>
                </td>
              </tr>
              <tr v-if="userDetails.profileDeclinedByName && userDetails.profileDeclinedByRole">
                <td>Rejected by</td>
                <td class="text-right list-text-fix">
                  <span>{{ userDetails.profileDeclinedByName }} ({{ userDetails.profileDeclinedByRole }})</span>
                </td>
              </tr>
            </VTable>
          </VCardText>
        </VCard>
      </VCol>

      <VCol
        cols="12"
        md="6"
      >
        <VCard
          title="Address Details"
          class="h-100"
        >
          <template #append>
            <VBtn
              size="small"
              @click="isEditAddressDrawerVisible = true"
            >
              Edit
            </VBtn>
          </template>

          <VCardText>
            <VTable class="text-no-wrap admin-table">
              <tr v-if="userDetails.address_1">
                <td>Address</td>
                <td class="text-right list-text-fix">
                  <span>{{ userDetails.address_1 }}</span> <br>
                  <span v-if="userDetails.address_2">{{ userDetails.address_2 }}</span>
                </td>
              </tr>
              <tr v-if="userDetails.city">
                <td>City</td>
                <td class="text-right list-text-fix">
                  <span>{{ userDetails.city }}</span>
                </td>
              </tr>
              <tr v-if="userDetails.state">
                <td>State / Territory</td>
                <td class="text-right">
                  <span>{{ userDetails.state }}</span>
                </td>
              </tr>
              <tr v-if="userDetails.zipcode">
                <td>Zipcode</td>
                <td class="text-right">
                  <span>{{ userDetails.zipcode }}</span>
                </td>
              </tr>
              <tr v-if="userDetails.country">
                <td>Country</td>
                <td class="text-right">
                  <span>{{ userDetails.country }}</span>
                </td>
              </tr>
            </VTable>
          </VCardText>
        </VCard>
      </VCol>

      <VCol
        cols="12"
        md="6"
      >
        <VCard
          title="Additional Details"
          class="h-100"
        >
          <template #append>
            <VBtn
              size="small"
              @click="isEditAdditionalDrawerVisible = true"
            >
              Edit
            </VBtn>
          </template>

          <VCardText>
            <div class="mb-3">
              <div class="text-body-1 text-medium-emphasis">
                Advertising Channels
              </div>
              <div class="text-body-2 text-high-emphasis">
                {{ !isEmpty(userDetails.advertising_channel_methods) ? userDetails.advertising_channel_methods.join(', ') : '-' }}
              </div>
            </div>

            <div class="mb-3">
              <div class="text-body-1 text-medium-emphasis">
                Advertising Channel URLs
              </div>
              <div class="text-body-2 text-high-emphasis">
                {{ userDetails.advertising_channel_method_urls ?? '-' }}
              </div>
            </div>

            <div class="mb-3">
              <div class="text-body-1 text-medium-emphasis">
                Targeted Treatments
              </div>
              <div class="text-body-2 text-high-emphasis">
                {{ !isEmpty(userDetails.targeted_treatments_to_promote) ? userDetails.targeted_treatments_to_promote.join(', ') : '-' }}
              </div>
            </div>

            <div class="mb-3">
              <div class="text-body-1 text-medium-emphasis">
                Average Visitors per Month
              </div>
              <div class="text-body-2 text-high-emphasis">
                {{ userDetails.avg_visitors_per_month ?? '-' }}
              </div>
            </div>
          </VCardText>
        </VCard>
      </VCol>

      <VCol
        cols="12"
        md="6"
      >
        <VCard
          title="Payout Methods"
          class="h-100"
        >
          <template #append>
            <VBtn
              size="small"
              @click="isEditPayoutMethodDrawerVisible = true"
            >
              Edit
            </VBtn>
          </template>

          <VCardText>
            <div class="mb-3">
              <div class="text-body-1 text-medium-emphasis">
                Payout Method
              </div>
              <div class="text-body-2 text-high-emphasis">
                {{ userDetails.payout_method ?? '-' }}
              </div>
            </div>

            <div
              v-if="userDetails.payout_paypal_email"
              class="mb-3"
            >
              <div class="text-body-1 text-medium-emphasis">
                PayPal Email Address
              </div>
              <div class="text-body-2 text-high-emphasis">
                {{ userDetails.payout_paypal_email ?? '-' }}
              </div>
            </div>

            <div
              v-if="userDetails.account_holder_first_name && userDetails.account_holder_last_name"
              class="mb-3"
            >
              <div class="text-body-1 text-medium-emphasis">
                Account Holder Name
              </div>
              <div class="text-body-2 text-high-emphasis">
                {{ userDetails.account_holder_first_name }} {{ userDetails.account_holder_last_name }}
              </div>
            </div>

            <div
              v-if="userDetails.bank_name"
              class="mb-3"
            >
              <div class="text-body-1 text-medium-emphasis">
                Bank Name
              </div>
              <div class="text-body-2 text-high-emphasis">
                {{ userDetails.bank_name ?? '-' }}
              </div>
            </div>

            <div
              v-if="userDetails.account_number"
              class="mb-3"
            >
              <div class="text-body-1 text-medium-emphasis">
                Account Number
              </div>
              <div class="text-body-2 text-high-emphasis">
                {{ userDetails.account_number ?? '-' }}
              </div>
            </div>

            <div
              v-if="userDetails.routing_number"
              class="mb-3"
            >
              <div class="text-body-1 text-medium-emphasis">
                Routing Number
              </div>
              <div class="text-body-2 text-high-emphasis">
                {{ userDetails.routing_number ?? '-' }}
              </div>
            </div>
          </VCardText>
        </VCard>
      </VCol>
    </VRow>

    <div class="d-flex justify-center align-center flex-wrap gap-y-4 mt-10">
      <div class="d-flex flex-wrap gap-3">
        <VBtn
          v-if="userDetails.is_show_approve_btn === 1"
          color="primary"
          class="w-200px"
          size="large"
          @click="isApproveDialogVisible = true"
        >
          Approve
        </VBtn>
        <VBtn
          v-if="userDetails.is_show_approve_btn === 1 && userDetails.is_profile_reviewed === 0"
          color="error"
          class="w-200px"
          size="large"
          @click="isRejectDialogVisible = true"
        >
          Reject
        </VBtn>
      </div>
    </div>

    <AffiliateApproveProfileDrawer
      v-model:isDrawerOpen="isApproveDialogVisible"
      :user-id="userId"
    />

    <AffiliateRejectProfileDialog
      v-model:isDialogVisible="isRejectDialogVisible"
      :user-id="userId"
      @canceled="fetchUserDetails"
    />

    <!-- 👉 Edit user details dialog -->
    <EditUserDetailsDrawer
      v-model:isDrawerOpen="isEditUserDetailsDrawerVisible"
      :user-data="userData"
      @updated="() => {
        isEditUserDetailsDrawerVisible = false
        fetchUserDetails()
      }"
    />

    <!-- 👉 Edit Address drawer -->
    <EditAddressDrawer
      v-model:isDrawerOpen="isEditAddressDrawerVisible"
      :address="currentAddress"
      @updated="() => {
        isEditAddressDrawerVisible = false
        fetchUserDetails()
      }"
    />

    <!-- 👉 Edit Additional drawer -->
    <EditAdditionalDrawer
      v-model:isDrawerOpen="isEditAdditionalDrawerVisible"
      :additional-data="additionalData"
      @updated="() => {
        isEditAdditionalDrawerVisible = false
        fetchUserDetails()
      }"
    />

    <!-- 👉 Edit Payout method drawer -->
    <EditPayoutMethodDrawer
      v-model:isDrawerOpen="isEditPayoutMethodDrawerVisible"
      :payout-method="payoutMethod"
      @updated="() => {
        isEditPayoutMethodDrawerVisible = false
        fetchUserDetails()
      }"
    />
  </div>
</template>
