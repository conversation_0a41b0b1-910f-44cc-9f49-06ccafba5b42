<script setup>
import { useGlobalData } from '@/store/global'
import { computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import AffiliateUserBioPanel from './view/AffiliateUserBioPanel.vue'
import AffiliateUserTabAnalytics from './view/AffiliateUserTabAnalytics.vue'
import AffiliateUserTabAccount from './view/AffiliateUserTabAccount.vue'
import AffiliateUserTabSecurity from './view/AffiliateUserTabSecurity.vue'
import AffiliateUserTabLoginActivity from './view/AffiliateUserTabLoginActivity.vue'
import AffiliateUserTabTransactions from './view/AffiliateUserTabTransactions.vue'
import AffiliateUserTabAccountLogs from './view/AffiliateUserTabAccountLogs.vue'
import { useAffiliateUserDetailsStore } from '@/store/admin/affiliateUserDetails'
import { storeToRefs } from 'pinia'

const route = useRoute()
const userId = computed(() => route.params.userId)
const { showSnackbar } = useGlobalData()
const userDetailsStore = useAffiliateUserDetailsStore()
const { errors } = storeToRefs(userDetailsStore)

const userTab = ref(null)

const tabs = [
  {
    icon: 'tabler-timeline',
    title: 'Analytics',
  },
  {
    icon: 'tabler-wallet',
    title: 'Transactions',
  },
  {
    icon: 'tabler-user-circle',
    title: 'Account',
  },

  // {
  //   icon: 'tabler-packages',
  //   title: 'Orders',
  // },
  {
    icon: 'tabler-lock',
    title: 'Security',
  },
  {
    icon: 'tabler-logs',
    title: 'Account Logs',
  },
  {
    icon: 'tabler-logs',
    title: 'Login Activity',
  },
]

onMounted(async () => {
  await userDetailsStore.fetchUserDetails(userId.value)
  if (errors.value.length > 0) {
    showSnackbar(errors.value[0].message, 'error')
  }
})
</script>

<template>
  <VRow>
    <VCol
      cols="12"
      md="5"
      lg="4"
    >
      <AffiliateUserBioPanel />
    </VCol>

    <VCol
      cols="12"
      md="7"
      lg="8"
    >
      <VTabs
        v-model="userTab"
        class="v-tabs-pill"
      >
        <VTab
          v-for="tab in tabs"
          :key="tab.icon"
        >
          <VIcon
            :size="18"
            :icon="tab.icon"
            class="me-1"
          />
          <span>{{ tab.title }}</span>
        </VTab>
      </VTabs>

      <VWindow
        v-model="userTab"
        class="mt-6 disable-tab-transition"
        :touch="false"
      >
        <VWindowItem>
          <AffiliateUserTabAnalytics />
        </VWindowItem>

        <VWindowItem>
          <AffiliateUserTabTransactions />
        </VWindowItem>

        <VWindowItem>
          <AffiliateUserTabAccount />
        </VWindowItem>

        <VWindowItem>
          <AffiliateUserTabSecurity />
        </VWindowItem>

        <VWindowItem>
          <AffiliateUserTabAccountLogs />
        </VWindowItem>

        <VWindowItem>
          <AffiliateUserTabLoginActivity />
        </VWindowItem>
      </VWindow>
    </VCol>
  </VRow>
</template>
