<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { useAffiliateUserDetailsStore } from '@/store/admin/affiliateUserDetails'
import { storeToRefs } from 'pinia'
import { isEmpty } from '@/@core/utils'

const route = useRoute()
const userId = computed(() => route.params.userId)
const userDetailsStore = useAffiliateUserDetailsStore()
const { userData, loading: userDataLoading } = storeToRefs(userDetailsStore)
const isEditAddressDrawerVisible = ref(false)
const isEditAdditionalDrawerVisible = ref(false)
const isEditPayoutMethodDrawerVisible = ref(false)

const currentAddress = computed(() => {
  return {
    address_1: userData.value?.user_additional_information?.address_1 ?? null,
    address_2: userData.value?.user_additional_information?.address_2 ?? null,
    city: userData.value?.user_additional_information?.city ?? null,
    state: userData.value?.user_additional_information?.state ?? null,
    zipcode: userData.value?.user_additional_information?.zipcode ?? null,
    country: userData.value?.user_additional_information?.country ?? null,
  }
})

const additionalData = computed(() => {
  return {
    advertising_channel_methods: userData.value?.user_additional_information?.advertising_channel_methods ?? null,
    advertising_channel_method_urls: userData.value?.user_additional_information?.advertising_channel_method_urls ?? null,
    targeted_treatments_to_promote: userData.value?.user_additional_information?.targeted_treatments_to_promote ?? null,
    avg_visitors_per_month: userData.value?.user_additional_information?.avg_visitors_per_month ?? null,
  }
})

const payoutMethod = computed(() => {
  return {
    payout_method: userData.value?.payout_method ?? null,
    payout_paypal_email: userData.value?.payout_paypal_email ?? null,
    account_holder_first_name: userData.value?.account_holder_first_name ?? null,
    account_holder_last_name: userData.value?.account_holder_last_name ?? null,
    bank_name: userData.value?.bank_name ?? null,
    account_number: userData.value?.account_number ?? null,
    routing_number: userData.value?.routing_number ?? null,
  }
})
</script>

<template>
  <VRow>
    <!-- 👉 Address -->
    <VCol cols="12">
      <VCard title="Address">
        <template #append>
          <VBtn
            size="small"
            @click="isEditAddressDrawerVisible = true"
          >
            Edit
          </VBtn>
        </template>

        <VCardText>
          <VRow>
            <VCol cols="12">
              <div v-if="userDataLoading">
                <Skeleton
                  width="240px"
                  height="20px"
                  class="mb-2"
                ></Skeleton>
                <Skeleton
                  width="240px"
                  height="20px"
                  class="mb-2"
                ></Skeleton>
                <Skeleton
                  width="240px"
                  height="20px"
                  class="mb-2"
                ></Skeleton>
                <Skeleton
                  width="240px"
                  height="20px"
                  class="mb-2"
                ></Skeleton>
              </div>
              <VTable
                v-else
                class="billing-address-table"
              >
                <tr>
                  <td class="w-125px">
                    <h6 class="text-h6 text-no-wrap mb-2 w-100px">
                      Address:
                    </h6>
                  </td>
                  <td>
                    <p class="text-body-1 mb-0">
                      <span>{{ currentAddress.address_1 ?? '-' }}</span>
                      <span v-if="currentAddress.address_2">, {{ currentAddress.address_2 }}</span>
                    </p>
                  </td>
                </tr>
                <tr>
                  <td class="w-125px">
                    <h6 class="text-h6 text-no-wrap mb-2">
                      City:
                    </h6>
                  </td>
                  <td>
                    <p class="text-body-1 mb-0">
                      {{ currentAddress.city ?? '-' }}
                    </p>
                  </td>
                </tr>
                <tr>
                  <td class="w-125px">
                    <h6 class="text-h6 text-no-wrap mb-2">
                      State / Territory:
                    </h6>
                  </td>
                  <td>
                    <p class="text-body-1 mb-0">
                      {{ currentAddress.state ?? '-' }}
                    </p>
                  </td>
                </tr>
                <tr>
                  <td class="w-125px">
                    <h6 class="text-h6 text-no-wrap">
                      Zipcode:
                    </h6>
                  </td>
                  <td>
                    <p class="text-body-1 mb-0">
                      {{ currentAddress.zipcode ?? '-' }}
                    </p>
                  </td>
                </tr>
                <tr>
                  <td class="w-125px">
                    <h6 class="text-h6 text-no-wrap">
                      Country:
                    </h6>
                  </td>
                  <td>
                    <p class="text-body-1 mb-0">
                      {{ currentAddress.country ?? '-' }}
                    </p>
                  </td>
                </tr>
              </VTable>
            </VCol>
          </VRow>
        </VCardText>
      </VCard>
    </VCol>

    <VCol cols="12">
      <VCard
        title="Additional Details"
        class="h-100"
      >
        <template #append>
          <VBtn
            size="small"
            @click="isEditAdditionalDrawerVisible = true"
          >
            Edit
          </VBtn>
        </template>

        <VCardText v-if="userDataLoading">
          <Skeleton
            width="240px"
            height="20px"
            class="mb-2"
          ></Skeleton>
          <Skeleton
            width="240px"
            height="20px"
            class="mb-2"
          ></Skeleton>
          <Skeleton
            width="240px"
            height="20px"
            class="mb-2"
          ></Skeleton>
          <Skeleton
            width="240px"
            height="20px"
            class="mb-2"
          ></Skeleton>
        </VCardText>

        <VCardText v-else>
          <div class="mb-3">
            <div class="text-body-1 text-medium-emphasis">
              Advertising Channels
            </div>
            <div class="text-body-2 text-high-emphasis">
              {{ !isEmpty(additionalData.advertising_channel_methods) ? additionalData.advertising_channel_methods.join(', ') : '-' }}
            </div>
          </div>

          <div class="mb-3">
            <div class="text-body-1 text-medium-emphasis">
              Advertising Channel URLs
            </div>
            <div class="text-body-2 text-high-emphasis">
              {{ additionalData.advertising_channel_method_urls ?? '-' }}
            </div>
          </div>

          <div class="mb-3">
            <div class="text-body-1 text-medium-emphasis">
              Targeted Treatments
            </div>
            <div class="text-body-2 text-high-emphasis">
              {{ !isEmpty(additionalData.targeted_treatments_to_promote) ? additionalData.targeted_treatments_to_promote.join(", ") : '-' }}
            </div>
          </div>

          <div class="mb-3">
            <div class="text-body-1 text-medium-emphasis">
              Average Visitors per Month
            </div>
            <div class="text-body-2 text-high-emphasis">
              {{ additionalData.avg_visitors_per_month ?? '-' }}
            </div>
          </div>
        </VCardText>
      </VCard>
    </VCol>

    <!-- 👉 Payment Methods -->
    <VCol cols="12">
      <VCard title="Payment Methods">
        <template #append>
          <VBtn
            size="small"
            @click="isEditPayoutMethodDrawerVisible = true"
          >
            Edit
          </VBtn>
        </template>

        <VCardText>
          <!-- skeleton loading Payment methods -->
          <div v-if="userDataLoading">
            <Skeleton
              width="240px"
              height="20px"
              class="mb-2"
            ></Skeleton>
            <Skeleton
              width="240px"
              height="20px"
              class="mb-2"
            ></Skeleton>
            <Skeleton
              width="240px"
              height="20px"
              class="mb-2"
            ></Skeleton>
            <Skeleton
              width="240px"
              height="20px"
              class="mb-2"
            ></Skeleton>
          </div>

          <!-- Payout method -->
          <div
            v-else
            class="d-flex flex-column gap-y-4"
          >
            <div class="mb-3">
              <div class="text-body-1 text-medium-emphasis">
                Payout Method
              </div>
              <div class="text-body-2 text-high-emphasis">
                {{ payoutMethod.payout_method ?? '-' }}
              </div>
            </div>

            <div
              v-if="payoutMethod.payout_paypal_email"
              class="mb-3"
            >
              <div class="text-body-1 text-medium-emphasis">
                PayPal Email Address
              </div>
              <div class="text-body-2 text-high-emphasis">
                {{ payoutMethod.payout_paypal_email ?? '-' }}
              </div>
            </div>

            <div
              v-if="payoutMethod.account_holder_first_name && payoutMethod.account_holder_last_name"
              class="mb-3"
            >
              <div class="text-body-1 text-medium-emphasis">
                Account Holder Name
              </div>
              <div class="text-body-2 text-high-emphasis">
                {{ payoutMethod.account_holder_first_name }} {{ payoutMethod.account_holder_last_name }}
              </div>
            </div>

            <div
              v-if="payoutMethod.bank_name"
              class="mb-3"
            >
              <div class="text-body-1 text-medium-emphasis">
                Bank Name
              </div>
              <div class="text-body-2 text-high-emphasis">
                {{ payoutMethod.bank_name ?? '-' }}
              </div>
            </div>

            <div
              v-if="payoutMethod.account_number"
              class="mb-3"
            >
              <div class="text-body-1 text-medium-emphasis">
                Account Number
              </div>
              <div class="text-body-2 text-high-emphasis">
                {{ payoutMethod.account_number ?? '-' }}
              </div>
            </div>

            <div
              v-if="payoutMethod.routing_number"
              class="mb-3"
            >
              <div class="text-body-1 text-medium-emphasis">
                Routing Number
              </div>
              <div class="text-body-2 text-high-emphasis">
                {{ payoutMethod.routing_number ?? '-' }}
              </div>
            </div>
          </div>
        </VCardText>
      </VCard>
    </VCol>
  </VRow>

  <!-- 👉 Edit Address drawer -->
  <AffiliateEditAddressDrawer
    v-model:isDrawerOpen="isEditAddressDrawerVisible"
    :address="currentAddress"
    @updated="() => {
      isEditAddressDrawerVisible = false
      userDetailsStore.fetchUserDetails(userId)
    }"
  />

  <!-- 👉 Edit Additional drawer -->
  <AffiliateEditAdditionalDrawer
    v-model:isDrawerOpen="isEditAdditionalDrawerVisible"
    :additional-data="additionalData"
    @updated="() => {
      isEditAdditionalDrawerVisible = false
      userDetailsStore.fetchUserDetails(userId)
    }"
  />

  <!-- 👉 Edit Payout method drawer -->
  <AffiliateEditPayoutMethodDrawer
    v-model:isDrawerOpen="isEditPayoutMethodDrawerVisible"
    :payout-method="payoutMethod"
    @updated="() => {
      isEditPayoutMethodDrawerVisible = false
      userDetailsStore.fetchUserDetails(userId)
    }"
  />
</template>
