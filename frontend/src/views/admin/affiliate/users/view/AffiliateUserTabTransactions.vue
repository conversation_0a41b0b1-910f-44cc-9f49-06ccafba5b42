<script setup>
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { calculateStartIndex, formatCurrency } from '@/utils/helpers'
import { refDebounced } from '@vueuse/core'
import { computed, onMounted, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { processErrors } from '@/utils/errorHandler'
import { isEmpty } from '@/@core/utils'
import { useAffiliateUserDetailsStore } from '@/store/admin/affiliateUserDetails'

const globalData = useGlobalData()
const { showSnackbar } = globalData
const route = useRoute()
const userId = computed(() => route.params.userId)
const userDetailsStore = useAffiliateUserDetailsStore()

const searchQuery = ref(null)
const debouncedSearchQuery = refDebounced(searchQuery, 300)
const filterStatus = ref(null)
const filterTransactionType = ref('')
const filterCategory = ref('')
const dateRange = ref(null)
const fromDate = ref(null)
const toDate = ref(null)
const rowPerPage = ref(10)
const currentPage = ref(1)
const totalPage = ref(1)
const totalItems = ref(0)
const items = ref([])
const skeletonLoading = ref(true)

const overview = ref({
  total_earnings: 0,
  available_balance: 0,
})

const statusOptions = [
  { title: 'Pending', value: 0 },
  { title: 'Approved', value: 1 },
  { title: 'Rejected', value: 2 },
]

const transactionTypeOptions = [
  { title: 'All Transactions', value: '' },
  { title: 'Earned Only', value: 'credit' },
  { title: 'Withdrawn Only', value: 'debit' },
]

const categoryOptions = [
  { title: 'ED', value: 'ED' },
  { title: 'HL', value: 'HL' },
  { title: 'WL', value: 'WL' },
  { title: 'PayoutRequest', value: 'PayoutRequest' },
  { title: 'ReversalPayoutRequest', value: 'ReversalPayoutRequest' },
]

onMounted(() => {
  fetchItems()
})

watch(() => userDetailsStore.profileUpdated, newValue => {
  if (newValue) {
    currentPage.value = 1
    items.value = []
    fetchItems()
  }
})

watch(dateRange, () => {
  if (isEmpty(dateRange.value)) {
    fromDate.value = null
    toDate.value = null

    return
  }

  const dateArr = dateRange.value?.split(' to ')

  if (dateArr.length === 1) {
    fromDate.value = dateArr[0] || null
    toDate.value = dateArr[0] || null
  } else if (dateArr.length === 2) {
    fromDate.value = dateArr[0] || null
    toDate.value = dateArr[1] || null
  } else {
    fromDate.value = null
    toDate.value = null
  }
})

watch([debouncedSearchQuery, fromDate, toDate, rowPerPage, filterStatus, filterTransactionType, filterCategory], () => {
  currentPage.value = 1
  fetchItems()
})

watch([currentPage], () => {
  fetchItems()
})

const fetchItems = async () => {
  skeletonLoading.value = true

  try {
    const { data } = await ApiService.post('/admin/get-affiliate-user-transaction-reports', {
      id: userId.value,
      perPage: rowPerPage.value,
      page: currentPage.value,
      transaction_type: filterTransactionType.value,
      status: filterStatus.value,
      from_date: fromDate.value,
      to_date: toDate.value,
      category: filterCategory.value,
    })

    if (data.status === 200) {
      const pagedData = data.transactionHistories
      let startIndex = calculateStartIndex(pagedData.current_page, pagedData.per_page)
      if (currentPage.value > 1) {
        startIndex = (currentPage.value - 1) * rowPerPage.value + 1
      }

      items.value = pagedData.records.map((d, index) => ({
        ...d,
        sno: index + startIndex,
      }))
      totalPage.value = pagedData.totalPage == 0 ? 1 : pagedData.totalPage
      totalItems.value = pagedData.totalRecords

      overview.value = data.overview
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    console.error(error)
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    skeletonLoading.value = false
  }
}

// 👉 Computing pagination data
const paginationData = computed(() => {
  const currentPageValue = currentPage.value <= 0 ? 1 : currentPage.value

  const firstIndex = items.value.length
    ? (currentPageValue - 1) * rowPerPage.value + 1
    : 0

  const lastIndex = items.value.length + (currentPageValue - 1) * rowPerPage.value

  return `Showing ${firstIndex} to ${lastIndex} of ${totalItems.value} entries`
})

function resolveStatus(status) {
  if (status === 0) {
    return { label: 'Pending', color: 'warning' }
  } else if (status === 1) {
    return { label: 'Approved', color: 'success' }
  } else if (status === 2) {
    return { label: 'Rejected', color: 'error' }
  } else {
    return { label: 'Unknown', color: 'default' }
  }
}
</script>

<template>
  <section>
    <VRow v-if="skeletonLoading">
      <VCol
        v-for="n in 2"
        :key="n"
        cols="12"
        sm="6"
      >
        <Skeleton
          class="pa-6"
          height="5rem"
        />
      </VCol>
    </VRow>

    <VRow v-else>
      <VCol
        cols="12"
        sm="6"
      >
        <v-card class="pa-4">
          <v-card-subtitle class="text-medium-emphasis font-weight-medium text-h6">
            Total Earnings
          </v-card-subtitle>
          <v-card-title class="text-h5">
            {{ overview.total_earnings ? formatCurrency(overview.total_earnings) : '-' }}
          </v-card-title>
        </v-card>
      </VCol>
      <VCol
        cols="12"
        sm="6"
      >
        <v-card class="pa-4">
          <v-card-subtitle class="text-medium-emphasis font-weight-medium text-h6">
            Available Balance
          </v-card-subtitle>
          <v-card-title class="text-h5">
            {{ overview.available_balance ? formatCurrency(overview.available_balance) : '-' }}
          </v-card-title>
        </v-card>
      </VCol>
    </VRow>

    <VRow class="match-height">
      <VCol cols="12">
        <VCard title="Transactions">
          <!-- 👉 Filters -->
          <VCardText>
            <VRow>
              <VCol
                cols="12"
                sm="6"
              >
                <AppSelect
                  v-model="filterTransactionType"
                  label="Transaction Type"
                  :items="transactionTypeOptions"
                  placeholder="Select"
                />
              </VCol>

              <VCol
                cols="12"
                sm="6"
              >
                <AppSelect
                  v-model="filterStatus"
                  label="Status"
                  :items="statusOptions"
                  clearable
                  clear-icon="tabler-x"
                  placeholder="Select"
                />
              </VCol>

              <VCol
                cols="12"
                sm="6"
              >
                <AppSelect
                  v-model="filterCategory"
                  label="Category"
                  :items="categoryOptions"
                  clearable
                  clear-icon="tabler-x"
                  placeholder="Select"
                />
              </VCol>

              <VCol
                cols="12"
                sm="6"
              >
                <AppDateTimePicker
                  v-model="dateRange"
                  label="Created Date"
                  placeholder="Select Date Range"
                  variant="outlined"
                  :config="{
                    mode: 'range',
                    dateFormat: 'm/d/Y',
                    onClose: function (selectedDates, dateStr, instance) {
                      if (selectedDates.length === 1) {
                        instance.setDate([selectedDates[0], selectedDates[0]], true)
                      }
                    },
                    maxDate: new Date(),
                    minDate: (new Date()).setFullYear((new Date()).getFullYear() - 120),
                  }"
                  clearable
                  clear-icon="tabler-x"
                />
              </VCol>
            </VRow>
          </VCardText>

          <VDivider />

          <VCardText class="d-flex flex-wrap py-4 gap-4">
            <div
              class="me-3"
              style="width: 80px"
            >
              <VSelect
                v-model="rowPerPage"
                density="compact"
                variant="outlined"
                :items="[10, 20, 30, 50]"
              />
            </div>

            <VSpacer />

            <div class="d-flex align-center flex-wrap gap-4">
              <!-- 👉 Search  -->
              <!--
                <div style="width: 23rem">
                <VTextField
                v-model="searchQuery"
                placeholder="Search"
                density="compact"
                clearable
                clear-icon="tabler-x"
                />
                </div>
              -->
            </div>
          </VCardText>

          <VDivider />

          <VTable class="text-no-wrap">
            <!-- 👉 table head -->
            <thead>
              <tr>
                <th scope="col">
                  #
                </th>
                <th scope="col">
                  Reference/Order ID
                </th>
                <th scope="col">
                  PRODUCT
                </th>
                <th scope="col">
                  Status
                </th>
                <th scope="col">
                  Category
                </th>
                <th scope="col">
                  Amount
                </th>
                <th scope="col">
                  Balance
                </th>
                <th scope="col">
                  Transaction Type
                </th>
                <th scope="col">
                  Transaction at
                </th>
              </tr>
            </thead>

            <!-- 👉 table body -->
            <tbody v-if="skeletonLoading">
              <tr
                v-for="n in 10"
                :key="n"
              >
                <td>
                  <Skeleton
                    width="2rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
                <td>
                  <Skeleton
                    width="8rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
                <td>
                  <Skeleton
                    width="8rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
                <td>
                  <Skeleton
                    width="8rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
                <td>
                  <Skeleton
                    width="8rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
                <td>
                  <Skeleton
                    width="8rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
                <td>
                  <Skeleton
                    width="8rem"
                    height="1.2rem"
                  ></Skeleton>
                </td>
              </tr>
            </tbody>
            <tbody v-else>
              <tr
                v-for="item in items"
                :key="item.id"
                style="height: 3.75rem"
              >
                <!-- 👉 SNO -->
                <td>
                  {{ item.sno }}
                </td>

                <!-- 👉 Reference Id -->
                <td>
                  <RouterLink
                    v-if="!isEmpty(item.order_id)"
                    :to="{
                      name: `admin-${item.category.toLowerCase()}-order-details`,
                      params: { orderId: item.order_id },
                      query: { ...route.query },
                    }"
                    class="dt-link"
                  >
                    {{ item.order_no }}
                  </RouterLink>
                  <span v-else-if="item.reference_id">#{{ item.reference_id }}</span>
                  <span v-else>-</span>
                </td>

                <!-- 👉 Product -->
                <td class="title">
                  <div
                    v-if="!isEmpty(item.order_id)"
                    class="d-flex align-center py-1"
                  >
                    <VAvatar
                      v-if="item.product_img"
                      variant="tonal"
                      size="40"
                      class="me-3"
                      :image="item.product_img"
                      rounded
                    />
                    <VAvatar
                      v-else
                      variant="tonal"
                      size="40"
                      class="me-3"
                      rounded
                    >
                      {{ resolveInitials(item.product_name) }}
                    </VAvatar>
                    <div>
                      <h6 class="text-base">
                        {{ item.product_name }}
                      </h6>
                      <div
                        v-if="item.category?.toLowerCase() === 'wl'"
                        class="d-flex flex-column"
                      >
                        <span class="d-block text-body-2">
                          {{ item.strength }} {{ item.strength_unit }} / weekly
                        </span>
                      </div>
                      <div
                        v-else
                        class="d-flex flex-column"
                      >
                        <span class="d-block text-body-2">
                          {{ item.strength }} {{ item.strength_unit }} x {{ item.qty * item.subscription_interval }} units
                        </span>
                        <span
                          v-if="item.is_medicine_pickup_at_local_pharmacy !== 1"
                          class="d-block text-body-2"
                        >({{ item.subscription_interval * 30 }}-day supply)</span>
                      </div>
                    </div>
                  </div>
                  <span v-else> - </span>
                </td>

                <!-- 👉 Status -->
                <td>
                  <VChip
                    v-if="item.transaction_type === 'debit' || ['ED', 'HL', 'WL'].includes(item.category)"
                    label
                    :color="resolveStatus(item.status).color"
                    size="small"
                    class="text-capitalize"
                  >
                    {{ resolveStatus(item.status).label }}
                  </VChip>
                  <span v-else>-</span>
                </td>

                <!-- 👉 Category -->
                <td>
                  {{ item.category }}
                </td>

                <!-- 👉 Amount -->
                <td>
                  {{ formatCurrency(item.amount) }}
                </td>

                <!-- 👉 Balance -->
                <td>
                  {{ formatCurrency(item.balance) }}
                </td>

                <!-- 👉 Transaction Type -->
                <td>
                  <VChip
                    label
                    :color="item.transaction_type === 'credit' ? 'success' : 'warning'"
                    size="small"
                    class="text-capitalize"
                  >
                    {{ item.transaction_type }}
                  </VChip>
                </td>

                <!-- 👉 Created at -->
                <td>
                  <div class="d-flex flex-column">
                    <span>{{ item.transaction_at }}</span>
                  </div>
                </td>
              </tr>
            </tbody>

            <!-- 👉 table footer  -->
            <tfoot v-show="!skeletonLoading && items.length === 0">
              <tr>
                <td
                  colspan="10"
                  class="text-center"
                >
                  No data available
                </td>
              </tr>
            </tfoot>
          </VTable>

          <VDivider />

          <VCardText class="d-flex align-center flex-wrap justify-space-between gap-4 py-3 px-5">
            <span class="text-sm text-disabled">
              {{ paginationData }}
            </span>
            <VPagination
              v-model="currentPage"
              size="small"
              :total-visible="5"
              :length="totalPage"
            />
          </VCardText>
        </VCard>
      </VCol>
    </VRow>
  </section>
</template>

<style lang="scss">
.v-table__wrapper td.title {
  min-width: 200px;
  h6 {
    a {
      white-space: pre-line;
    }
  }
}
</style>
