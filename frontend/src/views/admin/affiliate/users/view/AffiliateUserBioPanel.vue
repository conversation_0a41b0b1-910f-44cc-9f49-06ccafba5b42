<script setup>
import { avatarText } from '@/@core/utils/formatters'
import ApiService from '@/services/ApiService'
import { useAffiliateUserDetailsStore } from '@/store/admin/affiliateUserDetails'
import { useGlobalData } from '@/store/global'
import { formattedPhoneNumber, readAsDataURL } from '@/utils/helpers'
import Image from 'primevue/image'
import { computed } from 'vue'
import { useRoute } from 'vue-router'

const globalData = useGlobalData()
const { showSnackbar } = globalData
const userDetailsStore = useAffiliateUserDetailsStore()
const { userData, loading } = storeToRefs(userDetailsStore)

const route = useRoute()
const userId = computed(() => route.params.userId)

const isUserInfoEditDialogVisible = ref(false)
const isChangeAccountStatusDialogVisible = ref(false)
const changeStatusLoading = ref(false)
const profilePictureInput = ref(null)
const profilePictureUploading = ref(false)

const resolveUserStatusVariant = stat => {
  if (stat === 1)
    return { color: 'success', label: 'Active' }
  if (stat === 0)
    return { color: 'secondary', label: 'Inactive' }

  return { color: 'primary', label: 'Unknown' }
}

async function handleStatusChangeConfirmation(isConfirmed) {
  if (isConfirmed) {
    await changeAccountStatus()
  }
}

const changeAccountStatus = async () => {
  try {
    changeStatusLoading.value = true

    const { data } = await ApiService.get(`/admin/affiliate-user-account-status/${userId.value}`)

    if (data.status === 200) {
      isChangeAccountStatusDialogVisible.value = false
      showSnackbar(data.message)
      userDetailsStore.setProfileUpdated()
      await userDetailsStore.fetchUserDetails(userId.value)
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch(error) {
    console.error(error)
  } finally {
    changeStatusLoading.value = false
  }
}

const triggerFileInput = () => {
  if (!profilePictureUploading.value) {
    profilePictureInput.value.click()
  }
}

const uploadProfilePicture = async $event => {
  const { files } = $event.target
  if (!files || files.length === 0) return
  const file = files[0]

  if (file.type.slice(0, 6) !== 'image/') {
    showSnackbar('Only image files are allowed', 'error')

    return false
  }
  if (file.size > 3 * 1024 * 1024) {
    showSnackbar('Profile picture should be less than 3MB', 'error')

    return false
  }

  const url = await readAsDataURL(file)

  try {
    profilePictureUploading.value = true

    const formData = new FormData()

    formData.append('id', userId.value)
    formData.append('profile_picture', url)

    const { data } = await ApiService.post('/admin/update-affiliate-user-profile-picture', formData)

    if (data.status === 200) {
      showSnackbar(data.message)
      userDetailsStore.setProfileUpdated()
      await userDetailsStore.fetchUserDetails(userId.value)
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    console.error('Error uploading profile picture:', error)
    showSnackbar(error.message, 'error')
  } finally {
    profilePictureInput.value.value = ''
    profilePictureUploading.value = false
  }
}
</script>

<template>
  <VRow>
    <!-- SECTION User Details -->
    <VCol cols="12">
      <VCard v-if="userData">
        <VCardText class="text-center pt-15">
          <!-- 👉 Avatar -->
          <Skeleton
            v-if="loading"
            height="140px"
            width="140px"
            class="mx-auto"
          />
          <VAvatar
            v-else
            rounded
            :size="140"
            :color="!userData?.profile_picture ? 'primary' : undefined"
            :variant="!userData?.profile_picture ? 'tonal' : undefined"
            class="avatar-container"
          >
            <!-- Display profile picture -->
            <Image
              v-if="userData?.profile_picture"
              :src="userData?.profile_picture"
              :height="140"
              preview
            />
            <!-- Fallback initials if no profile picture -->
            <span
              v-else
              class="text-h3 font-weight-medium"
            >
              {{ avatarText(userData?.first_name + ' ' + userData?.last_name) }}
            </span>
            <!-- Edit Button Overlay -->
            <button
              v-if="profilePictureUploading"
              class="edit-button"
              aria-label="Edit profile picture"
            >
              <VIcon
                icon="tabler-loader"
                class="animate-spin"
              ></VIcon>
            </button>
            <button
              v-else
              class="edit-button"
              aria-label="Edit profile picture"
              @click="triggerFileInput"
            >
              <VIcon icon="tabler-edit"></VIcon>
            </button>
            <!-- Hidden File Input -->
            <input
              ref="profilePictureInput"
              type="file"
              class="d-none"
              accept="image/*"
              @change="uploadProfilePicture"
            />
          </VAvatar>

          <Skeleton
            v-if="loading"
            height="1.5rem"
            width="12rem"
            class="mt-4 mx-auto"
          />
          <!-- 👉 User fullName -->
          <h6
            v-else
            class="text-h4 mt-4"
          >
            {{ userData?.first_name + ' ' + userData?.last_name }}
          </h6>
        </VCardText>

        <VDivider />

        <!-- 👉 Details -->
        <VCardText>
          <p class="text-sm text-uppercase text-disabled">
            Details
          </p>

          <!-- 👉 User Details list -->
          <VList
            v-if="loading"
            class="card-list mt-2"
          >
            <VListItem
              v-for="i in 7"
              :key="i"
            >
              <VListItemTitle class="mb-1">
                <div class="d-flex justify-space-between">
                  <Skeleton
                    height="1rem"
                    width="6rem"
                    class="d-inline-block"
                  />
                  <Skeleton
                    height="1rem"
                    width="10rem"
                    class="d-inline-block"
                  />
                </div>
              </VListItemTitle>
            </VListItem>
          </VList>
          <VList
            v-else
            class="card-list mt-2"
          >
            <VListItem>
              <VListItemTitle>
                <div class="d-flex justify-space-between">
                  <h6 class="text-h6">
                    Full name:
                  </h6>
                  <div class="text-body-1 list-text-fix">
                    {{ userData?.first_name + ' ' + userData?.last_name }}
                  </div>
                </div>
              </VListItemTitle>
            </VListItem>

            <VListItem>
              <VListItemTitle>
                <div class="d-flex justify-space-between">
                  <h6 class="text-h6">
                    Email:
                  </h6>
                  <div class="text-body-1 list-text-fix">
                    {{ userData?.email }}
                  </div>
                </div>
              </VListItemTitle>
            </VListItem>

            <VListItem>
              <VListItemTitle>
                <div class="d-flex justify-space-between">
                  <h6 class="text-h6">
                    Phone Number:
                  </h6>
                  <div class="text-body-1">
                    {{ formattedPhoneNumber(userData?.phone_number) }}
                  </div>
                </div>
              </VListItemTitle>
            </VListItem>

            <VListItem>
              <VListItemTitle>
                <div class="d-flex justify-space-between">
                  <h6 class="text-h6">
                    Status:
                  </h6>
                  <div class="text-body-1">
                    <VChip
                      label
                      size="small"
                      :color="resolveUserStatusVariant(userData.account_status).color"
                      class="text-capitalize"
                    >
                      {{ resolveUserStatusVariant(userData?.account_status).label }}
                    </VChip>
                  </div>
                </div>
              </VListItemTitle>
            </VListItem>

            <VListItem>
              <VListItemTitle>
                <div class="d-flex justify-space-between">
                  <h6 class="text-h6">
                    Company Name:
                  </h6>
                  <div class="text-body-1 list-text-fix">
                    {{ userData?.user_additional_information?.company_name }}
                  </div>
                </div>
              </VListItemTitle>
            </VListItem>

            <VListItem>
              <VListItemTitle>
                <div class="d-flex justify-space-between">
                  <h6 class="text-h6">
                    Website:
                  </h6>
                  <div class="text-body-1 list-text-fix">
                    {{ userData?.user_additional_information?.website ?? '-' }}
                  </div>
                </div>
              </VListItemTitle>
            </VListItem>
          </VList>
        </VCardText>

        <!-- 👉 Edit and Suspend button -->
        <VCardText
          v-if="!loading"
          class="d-flex justify-center"
        >
          <VBtn
            variant="elevated"
            class="me-4"
            @click="isUserInfoEditDialogVisible = true"
          >
            Edit
          </VBtn>

          <VBtn
            variant="tonal"
            :color="userData.account_status === 1 ? 'error' : 'success'"
            @click="isChangeAccountStatusDialogVisible = true"
          >
            {{ userData.account_status === 1 ? 'Disable Account' : 'Enable Account' }}
          </VBtn>
        </VCardText>
      </VCard>
    </VCol>
    <!-- !SECTION -->
  </VRow>

  <!-- 👉 Edit user info dialog -->
  <AffiliateUserInfoEditDialog v-model:isDialogVisible="isUserInfoEditDialogVisible" />

  <!-- 👉 Change Account status confirm dialog -->
  <ConfirmDialog
    v-model:isDialogVisible="isChangeAccountStatusDialogVisible"
    :confirmation-question="`Are you sure you want to ${userData.account_status === 1 ? 'disable' : 'enable'} this account?`"
    :loading="changeStatusLoading"
    @confirm="handleStatusChangeConfirmation"
  />
</template>

<style lang="scss" scoped>
.card-list {
  --v-card-list-gap: 0.75rem;
}

.text-capitalize {
  text-transform: capitalize !important;
}

.avatar-container {
  position: relative;
}

.edit-button {
  position: absolute;
  bottom: 0;
  right: 0;
  background-color: white;
  color: black;
  padding: 4px;
  border-radius: 50%;
  border: 1px solid #D1D5DB;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.edit-button:hover {
  background-color: #F3F4F6;
}

.animate-spin {
  animation-name: spin;
  animation-duration: 1300ms;
  animation-iteration-count: infinite;
  animation-timing-function: linear;
}

@keyframes spin {
  from {
    transform:rotate(0deg);
  }
  to {
    transform:rotate(360deg);
  }
}
</style>
