<script setup>
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { processErrors } from '@/utils/errorHandler'
import { onMounted, ref, watch } from 'vue'
import { useAffiliateUserDetailsStore } from '@/store/admin/affiliateUserDetails'
import { isEmpty } from '@/@core/utils'

const route = useRoute()
const userId = computed(() => route.params.userId)
const { showSnackbar } = useGlobalData()
const userDetailsStore = useAffiliateUserDetailsStore()

const skeletonLoading = ref(false)
const logs = ref([])
const currentPage = ref(1)
const perPage = ref(10)
const totalPage = ref(0)
const totalRecords = ref(0)
const dateRange = ref(null)
const fromDate = ref(null)
const toDate = ref(null)

onMounted(async () => {
  await fetchLogs()
})

watch(() => userDetailsStore.profileUpdated, newValue => {
  if (newValue) {
    currentPage.value = 1
    logs.value = []
    fetchLogs()
  }
})

watch(dateRange, () => {
  if (isEmpty(dateRange.value)) {
    fromDate.value = null
    toDate.value = null

    return
  }

  const dateArr = dateRange.value.split(' to ')

  if (dateArr.length === 1) {
    fromDate.value = dateArr[0] || null
    toDate.value = dateArr[0] || null
  } else if (dateArr.length === 2) {
    fromDate.value = dateArr[0] || null
    toDate.value = dateArr[1] || null
  } else {
    fromDate.value = null
    toDate.value = null
  }
})

watch([fromDate, toDate], () => {
  currentPage.value = 1
  fetchLogs()
})

async function fetchLogs() {
  try {
    skeletonLoading.value = true

    const postData = {
      id: userId.value,
      page: currentPage.value,
      perPage: perPage.value,
      from_date: fromDate.value,
      to_date: toDate.value,
    }

    const { data } = await ApiService.post('/admin/get-affiliate-user-account-activities', postData)

    if (data.status === 200) {
      if (currentPage.value === 1) {
        logs.value = data.userAuthActivities.records
      } else {
        logs.value = [...logs.value, ...data.userAuthActivities.records]
      }
      totalPage.value = data.userAuthActivities.totalPage
      totalRecords.value = data.userAuthActivities.totalRecords
    } else {
      showSnackbar(data.message, 'error')
    }
  } catch (error) {
    console.error(error)
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    skeletonLoading.value = false
  }
}

async function handlePagination() {
  if (!skeletonLoading.value && currentPage.value < totalPage.value) {
    currentPage.value++
    await fetchLogs()
  }
}
</script>

<template>
  <VCard title="Account Activity Logs">
    <!-- 👉 Filters -->
    <VCardText>
      <VRow>
        <VCol
          cols="12"
          sm="6"
        >
          <AppDateTimePicker
            v-model="dateRange"
            label="Created Date"
            placeholder="Select Date Range"
            variant="outlined"
            :config="{
              mode: 'range',
              dateFormat: 'm/d/Y',
              onClose: function (selectedDates, dateStr, instance) {
                if (selectedDates.length === 1) {
                  instance.setDate([selectedDates[0], selectedDates[0]], true)
                }
              },
              maxDate: new Date(),
              minDate: (new Date()).setFullYear((new Date()).getFullYear() - 120),
            }"
            clearable
            clear-icon="tabler-x"
          />
        </VCol>
      </VRow>
    </VCardText>

    <VDivider />

    <VCardText v-if="logs.length === 0 && !skeletonLoading">
      <div class="d-flex flex-column align-center">
        <VIcon
          icon="tabler-logs"
          size="48"
        />
        <p class="text-body-1 mt-4">
          No activity
        </p>
      </div>
    </VCardText>

    <VCardText>
      <div class="logs-wrapper scroll-container">
        <div v-if="logs.length > 0">
          <div
            v-for="log in logs"
            :key="log"
            class="rounded-lg border border-dashed px-3 py-2 d-flex justify-space-between mb-3"
          >
            <div class="event-content">
              <h6 class="text-h6">
                {{ log.action_message }}
              </h6>
              <div class="text-sm mt-1">
                <address v-if="log.location">
                  {{ log.location }}
                  <span v-if="log.ip_address">
                    ({{ log.ip_address }})
                  </span>
                </address>
              </div>
              <span
                v-if="log.action_by_user_name"
                class="text-xs"
              >
                <span class="text-high-emphasis">Action by: </span>
                <span v-if="log.action_by_user_name">{{ log.action_by_user_name + " " }}</span>
                <span v-if="log.action_by_role">({{ log.action_by_role }})</span>
              </span>
            </div>
            <div>
              <v-tooltip
                :text="log.action_full_date"
                content-class="event-tooltip"
                open-on-click
              >
                <template #activator="{ props: tooltip_props }">
                  <time
                    v-bind="tooltip_props"
                    class="text-xs event-time"
                  >{{ log.action_date }}</time>
                </template>
              </v-tooltip>
            </div>
          </div>
        </div>

        <div v-if="skeletonLoading">
          <div
            v-for="i in 3"
            :key="i"
            class="rounded-lg border border-dashed px-3 py-2 d-flex justify-space-between align-center mb-3"
          >
            <div>
              <Skeleton
                height="1rem"
                width="20rem"
                class="mb-2"
              />
              <Skeleton
                height="0.75rem"
                width="80px"
              />
            </div>
          </div>
        </div>

        <div
          v-if="currentPage < totalPage && !skeletonLoading"
          class="text-center"
        >
          <VBtn
            size="small"
            @click="handlePagination"
          >
            See More
          </VBtn>
        </div>
      </div>
    </VCardText>
  </VCard>
</template>

<style lang="scss">
// .logs-wrapper {
//   max-height: 320px;
//   overflow-y: auto;
// }
.scroll-container::-webkit-scrollbar {
  width: 4px;
}

.scroll-container::-webkit-scrollbar-track {
  background: white;
}

.scroll-container::-webkit-scrollbar-thumb {
  background: #a7a8a9;
}

.scroll-container::-webkit-scrollbar-thumb:hover {
  background: #888;
}
.event-time {
  &:hover {
    text-decoration: underline;
    cursor: pointer;
  }
}
.event-tooltip {
  max-width: 200px;
  font-size: 12px !important;
}
</style>
