<script setup>
import ApiService from '@/services/ApiService'
import { processErrors } from '@/utils/errorHandler'
import { IconClipboardText } from '@tabler/icons-vue'
import { onMounted } from 'vue'
import copy from 'copy-to-clipboard'
import { useRoute } from 'vue-router'
import { useGlobalData } from '@/store/global'
import Skeleton from 'primevue/skeleton'
import { useAffiliateUserDetailsStore } from '@/store/admin/affiliateUserDetails'
import { storeToRefs } from 'pinia'
import { formatCurrency } from '@/utils/helpers'

const route = useRoute()
const userId = computed(() => route.params.userId)
const { showSnackbar } = useGlobalData()
const userDetailsStore = useAffiliateUserDetailsStore()
const { userData } = storeToRefs(userDetailsStore)
const isLoading = ref(false)
const summary = ref({})
const campaigns = ref([])
const isEditCommissionDialogVisible = ref(false)

onMounted(async () => {
  await getAnalyticsData()
})

async function getAnalyticsData() {
  try {
    isLoading.value = true

    const { data } = await ApiService.get(`/admin/get-affiliate-user-summary/${userId.value}`)

    if (data.status === 200) {
      summary.value = data.summary
      campaigns.value = data.campaigns
    } else {
      showSnackbar(processErrors(data)[0], 'error')
    }
  } catch (error) {
    console.error(error)
    showSnackbar(processErrors(error)[0], 'error')
  } finally {
    isLoading.value = false
  }
}

async function copyLinkToClipboard(link) {
  try {
    copy(link)
    showSnackbar('Link copied to clipboard')
  } catch (error) {
    console.error(error)
    showSnackbar('Failed to copy link to clipboard')
  }
}
</script>

<template>
  <div>
    <VRow v-if="isLoading">
      <!-- Skeleton Loading -->
      <VCol
        v-for="n in 3"
        :key="n"
        cols="12"
        sm="4"
      >
        <Skeleton
          class="pa-6"
          height="5rem"
        />
      </VCol>
      <VCol cols="12">
        <Skeleton
          class="pa-6"
          height="10rem"
        />
      </VCol>
    </VRow>

    <div v-else>
      <!-- Summary Data -->
      <VRow class="match-height">
        <VCol
          cols="12"
          sm="4"
        >
          <v-card class="pa-4">
            <v-card-subtitle class="text-medium-emphasis font-weight-medium text-h6">
              Total Sales
            </v-card-subtitle>
            <v-card-title class="text-h5">
              {{ summary.total_sales ?? '-' }}
            </v-card-title>
          </v-card>
        </VCol>
        <VCol
          cols="12"
          sm="4"
        >
          <v-card class="pa-4">
            <v-card-subtitle class="text-medium-emphasis font-weight-medium text-h6">
              Total Earnings
            </v-card-subtitle>
            <v-card-title class="text-h5">
              {{ summary.total_earnings ? formatCurrency(summary.total_earnings) : '-' }}
            </v-card-title>
          </v-card>
        </VCol>
        <VCol
          cols="12"
          sm="4"
        >
          <v-card class="pa-4">
            <v-card-subtitle class="text-medium-emphasis font-weight-medium text-h6 d-flex justify-space-between">
              <span>Commission Rate</span>
              <VBtn
                size="x-small"
                rounded="sm"
                @click="isEditCommissionDialogVisible = true"
              >
                Edit
              </VBtn>
            </v-card-subtitle>
            <v-card-title class="text-h5">
              <div v-if="userData.commission_tier_detail?.current_commission_tier_level">
                <div>
                  {{ userData.commission_tier_detail?.current_commission_tier_level?.commission_type === 'percentage' ? userData.commission_tier_detail?.current_commission_tier_level?.commission_value + '%' : formatCurrency(userData.commission_tier_detail?.current_commission_tier_level?.commission_value) }} / sale
                </div>
                <div class="text-sm mt-1">
                  Level {{ userData.commission_tier_detail?.current_commission_tier_level?.level }} ({{ formatCurrency(userData.commission_tier_detail?.current_commission_tier_level?.min) }} - {{ formatCurrency(userData.commission_tier_detail?.current_commission_tier_level?.max) }})
                </div>
              </div>
              <div v-else>
                -
              </div>
            </v-card-title>
          </v-card>
        </VCol>
      </VRow>

      <!-- Campaign Data -->
      <VRow
        v-for="campaign in campaigns"
        :key="campaign.category"
      >
        <VCol cols="12">
          <v-card class="pa-4">
            <v-card-title>
              {{
                campaign.category === 'HL'
                  ? 'Hair Treatment'
                  : campaign.category === 'WL'
                    ? 'Weight Loss'
                    : campaign.category
              }} Campaign
            </v-card-title>
            <VRow class="mt-4 px-4">
              <VCol
                cols="12"
                sm="4"
              >
                <v-card
                  variant="tonal"
                  class="pa-4"
                >
                  <v-card-subtitle class="text-medium-emphasis font-weight-medium text-h6">
                    Total Clicks
                  </v-card-subtitle>
                  <v-card-title>{{ campaign.total_clicks ?? '-' }}</v-card-title>
                </v-card>
              </VCol>
              <VCol
                cols="12"
                sm="4"
              >
                <v-card
                  variant="tonal"
                  class="pa-4"
                >
                  <v-card-subtitle class="text-medium-emphasis font-weight-medium text-h6">
                    Total Sales
                  </v-card-subtitle>
                  <v-card-title>{{ campaign.total_sales ?? '-' }}</v-card-title>
                </v-card>
              </VCol>
              <VCol
                cols="12"
                sm="4"
              >
                <v-card
                  variant="tonal"
                  class="pa-4"
                >
                  <v-card-subtitle class="text-medium-emphasis font-weight-medium text-h6">
                    Total Earnings
                  </v-card-subtitle>
                  <v-card-title>{{ campaign.total_earnings ? formatCurrency(campaign.total_earnings) : '-' }}</v-card-title>
                </v-card>
              </VCol>
            </VRow>

            <VRow
              v-if="campaign.links"
              class="mt-6 px-4 pb-4"
            >
              <VCol cols="12">
                <h5 class="text-h6 mb-2">
                  Affiliate Link
                </h5>
                <v-card
                  variant="tonal"
                  class="d-flex flex-column flex-sm-row justify-space-between align-center pa-2 px-4"
                >
                  <span class="text-body-2 text-break">{{ campaign.links }}</span>
                  <v-btn
                    class="ms-sm-4 mt-2 mt-sm-0"
                    color="primary"
                    size="small"
                    @click="copyLinkToClipboard(campaign.links)"
                  >
                    <IconClipboardText class="me-1" />
                    <span>Copy</span>
                  </v-btn>
                </v-card>
              </VCol>
            </VRow>
          </v-card>
        </VCol>
      </VRow>
    </div>

    <AffiliateEditCommissionDrawer
      v-model:isDrawerOpen="isEditCommissionDialogVisible"
      :user-id="userId"
      @updated="() => {
        userDetailsStore.fetchUserDetails(userId)
      }"
    />
  </div>
</template>
