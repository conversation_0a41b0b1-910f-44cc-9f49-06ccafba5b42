<script setup>
import { requiredValidator } from '@/@core/utils/validators'
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { processErrors } from '@/utils/errorHandler'
import { isNumber, plainPhoneNumber } from '@/utils/helpers'
import { vMaska } from 'maska'
import { computed, watch } from 'vue'
import { useRoute } from 'vue-router'
import { isEmptyObject, isNullOrUndefined } from '@/@core/utils'
import { PerfectScrollbar } from 'vue3-perfect-scrollbar'

const props = defineProps({
  userData: {
    type: Object,
    required: false,
    default: () => ({
      first_name: '',
      last_name: '',
      email: '',
      phone_number: '',
      website: '',
      company_name: '',
    }),
  },
  isDrawerOpen: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits([
  'update:isDrawerOpen',
  'updated',
])

const route = useRoute()
const globalStore = useGlobalData()
const { showSnackbar } = globalStore
const userId = computed(() => route.params.userId)
const formRef = ref(null)
const isLoading = ref(false)
const serverErrors = ref([])
const inputErrors = ref({})
const formData = ref(structuredClone(toRaw(props.userData)))

watch(props, () => {
  formData.value = structuredClone(toRaw(props.userData))
})

const onFormSubmit = async () => {
  formRef.value?.validate().then(async valid => {
    if (valid.valid) {
      await updateUserDetails()
    }
  })
}

async function updateUserDetails() {
  try {
    isLoading.value = true

    const postData = {
      ...formData.value,
      phone_number: plainPhoneNumber(formData.value.phone_number),
      id: userId.value,
    }

    const { data } = await ApiService.post('/admin/update-interested-affiliate-user-personal-information', postData)

    if (data.status === 200) {
      emit('update:isDrawerOpen', false)
      emit('updated', true)
      showSnackbar(data.message)
    } else {
      if (data.errors) {
        inputErrors.value = data.errors
      } else {
        showSnackbar(data.message)
      }
    }
  } catch (error) {
    serverErrors.value = processErrors(error)
    console.error(error)
  } finally {
    isLoading.value = false
  }
}

const onFormReset = () => {
  emit('update:isDrawerOpen', false)
  formData.value = structuredClone(toRaw(props.payoutMethod))
}

const handleDrawerModelValueUpdate = val => {
  emit('update:isDrawerOpen', val)
}
</script>

<template>
  <VNavigationDrawer
    :model-value="props.isDrawerOpen"
    temporary
    location="end"
    width="400"
    class="category-navigation-drawer scrollable-content"
    @update:model-value="handleDrawerModelValueUpdate"
  >
    <!-- 👉 Header -->
    <AppDrawerHeaderSection
      title="Edit User Details"
      @cancel="$emit('update:isDrawerOpen', false)"
    />

    <VDivider />

    <PerfectScrollbar :options="{ wheelPropagation: false }">
      <VCard
        v-if="props.userData"
        flat
      >
        <VCardText>
          <!-- 👉 Alert -->
          <VAlert
            v-if="isEmptyObject(inputErrors) && serverErrors.length > 0"
            type="error"
            variant="tonal"
            title="Validation failed!"
            class="mb-6"
          >
            <ul class="mb-0">
              <li
                v-for="error in serverErrors"
                :key="error"
                class="mb-0"
              >
                {{ error }}
              </li>
            </ul>
          </VAlert>

          <!-- 👉 Form -->
          <VForm
            ref="formRef"
            @submit.prevent="onFormSubmit"
          >
            <VRow>
              <!-- 👉 First Name -->
              <VCol cols="12">
                <AppTextField
                  v-model="formData.first_name"
                  label="First Name"
                  :rules="[requiredValidator]"
                  required
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['first_name'])"
                  class="text-sm text-error mb-0 ms-3"
                >
                  {{ inputErrors['first_name'][0] }}
                </p>
              </VCol>

              <!-- 👉 Last Name -->
              <VCol cols="12">
                <AppTextField
                  v-model="formData.last_name"
                  label="Last Name"
                  :rules="[requiredValidator]"
                  required
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['last_name'])"
                  class="text-sm text-error mb-0 ms-3"
                >
                  {{ inputErrors['last_name'][0] }}
                </p>
              </VCol>

              <!-- 👉 Email -->
              <VCol cols="12">
                <AppTextField
                  v-model="formData.email"
                  label="Email"
                  :rules="[requiredValidator]"
                  required
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['email'])"
                  class="text-sm text-error mb-0 ms-3"
                >
                  {{ inputErrors['email'][0] }}
                </p>
              </VCol>

              <!-- 👉 Status -->
              <VCol cols="12">
                <AppTextField
                  v-model="formData.phone_number"
                  label="Phone Number"
                  :rules="[requiredValidator]"
                  required
                  @keydown="isNumber"
                />
                <input
                  v-model="formData.phone_number"
                  v-maska
                  type="hidden"
                  data-maska="(###) ###-####"
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['phone_number'])"
                  class="text-sm text-error mb-0 ms-3"
                >
                  {{ inputErrors['phone_number'][0] }}
                </p>
              </VCol>

              <!-- 👉 Company Name -->
              <VCol cols="12">
                <AppTextField
                  v-model="formData.company_name"
                  label="Company Name"
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['company_name'])"
                  class="text-sm text-error mb-0 ms-3"
                >
                  {{ inputErrors['company_name'][0] }}
                </p>
              </VCol>

              <!-- 👉 Website -->
              <VCol cols="12">
                <AppTextField
                  v-model="formData.website"
                  label="Website"
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['website'])"
                  class="text-sm text-error mb-0 ms-3"
                >
                  {{ inputErrors['website'][0] }}
                </p>
              </VCol>

              <!-- 👉 Submit and Cancel -->
              <VCol
                cols="12"
                class="d-flex flex-wrap justify-center gap-4"
              >
                <VBtn
                  type="submit"
                  :loading="isLoading"
                >
                  Submit
                </VBtn>

                <VBtn
                  color="secondary"
                  variant="tonal"
                  :disabled="isLoading"
                  @click="onFormReset"
                >
                  Cancel
                </VBtn>
              </VCol>
            </VRow>
          </VForm>
        </VCardText>
      </VCard>
    </PerfectScrollbar>
  </VNavigationDrawer>
</template>
