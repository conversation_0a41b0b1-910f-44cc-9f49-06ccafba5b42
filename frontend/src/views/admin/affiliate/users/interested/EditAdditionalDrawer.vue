<script setup>
import { requiredValidator } from '@/@core/utils/validators'
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { processErrors } from '@/utils/errorHandler'
import { computed, watch } from 'vue'
import { useRoute } from 'vue-router'
import { isEmptyObject, isNullOrUndefined } from '@/@core/utils'
import { removeKeyFromObject } from '@/utils/helpers'
import { PerfectScrollbar } from 'vue3-perfect-scrollbar'

const props = defineProps({
  additionalData: {
    type: Object,
    required: false,
    default: () => ({
      advertising_channel_methods: [],
      advertising_channel_method_urls: '',
      targeted_treatments_to_promote: [],
      avg_visitors_per_month: '',
    }),
  },
  isDrawerOpen: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits([
  'update:isDrawerOpen',
  'updated',
])

const route = useRoute()
const globalStore = useGlobalData()
const { showSnackbar } = globalStore
const userId = computed(() => route.params.userId)
const formRef = ref(null)
const isLoading = ref(false)
const serverErrors = ref([])
const inputErrors = ref({})

const advertisingChannels = [
  'Blog/Site',
  'YouTube',
  'Social Media',
  'Marketing Agency',
  'B2B',
  'Other',
]

const treatments = [
  { title: 'Erectile Dysfunction (ED)', value: 'ED' },
  { title: 'Hair Treatment', value: 'HL' },
  { title: 'Weight Loss', value: 'WL' },
]

const formData = ref(JSON.parse(JSON.stringify(props.additionalData)))

watch(props, () => {
  formData.value = JSON.parse(JSON.stringify(props.additionalData))
})

const resetForm = () => {
  emit('update:isDrawerOpen', false)
  formData.value = JSON.parse(JSON.stringify(props.additionalData))
}

const onFormSubmit = async () => {
  formRef.value?.validate().then(async valid => {
    if (valid.valid) {
      await update()
    }
  })
}

async function update() {
  try {
    isLoading.value = true

    const postData = {
      id: userId.value,
      ...formData.value,
    }

    const { data } = await ApiService.post('/admin/update-interested-affiliate-user-additional-info', postData)

    if (data.status === 200) {
      emit('update:isDrawerOpen', false)
      emit('updated', true)
      showSnackbar(data.message)
    } else {
      if (data.errors) {
        inputErrors.value = data.errors
      } else {
        serverErrors.value = processErrors(data)
      }
    }
  } catch (error) {
    serverErrors.value = processErrors(error)
    console.error(error)
  } finally {
    isLoading.value = false
  }
}

function removeKeyFromInputErrors(key) {
  inputErrors.value = removeKeyFromObject(inputErrors.value, key)
}

const handleDrawerModelValueUpdate = val => {
  emit('update:isDrawerOpen', val)
}
</script>

<template>
  <VNavigationDrawer
    :model-value="props.isDrawerOpen"
    temporary
    location="end"
    width="400"
    class="category-navigation-drawer scrollable-content"
    @update:model-value="handleDrawerModelValueUpdate"
  >
    <!-- 👉 Header -->
    <AppDrawerHeaderSection
      title="Edit Additional Info"
      @cancel="$emit('update:isDrawerOpen', false)"
    />

    <VDivider />

    <PerfectScrollbar :options="{ wheelPropagation: false }">
      <VCard
        v-if="props.additionalData"
        flat
      >
        <VCardText>
          <!-- 👉 Alert -->
          <VAlert
            v-if="isEmptyObject(inputErrors) && serverErrors.length > 0"
            type="error"
            variant="tonal"
            title="Validation failed!"
            class="mb-6"
          >
            <ul class="mb-0">
              <li
                v-for="error in serverErrors"
                :key="error"
                class="mb-0"
              >
                {{ error }}
              </li>
            </ul>
          </VAlert>

          <!-- 👉 Form -->
          <VForm
            ref="formRef"
            @submit.prevent="onFormSubmit"
          >
            <VRow>
              <!-- 👉 Advertising channels -->
              <VCol cols="12">
                <AppSelect
                  v-model="formData.advertising_channel_methods"
                  label="Advertising Channel(s)"
                  :items="advertisingChannels"
                  :rules="[requiredValidator]"
                  multiple
                  chips
                  required
                  @keyup="removeKeyFromInputErrors('advertising_channel_methods')"
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['advertising_channel_methods'])"
                  class="text-sm text-error mb-0 ms-3"
                >
                  {{ inputErrors['advertising_channel_methods'][0] }}
                </p>
              </VCol>

              <VCol cols="12">
                <AppTextarea
                  v-model="formData.advertising_channel_method_urls"
                  label="Advertising Channel URLs"
                  rows="3"
                  @keyup="removeKeyFromInputErrors('advertising_channel_method_urls')"
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['advertising_channel_method_urls'])"
                  class="text-sm text-error mb-0 ms-3"
                >
                  {{ inputErrors['advertising_channel_method_urls'][0] }}
                </p>
              </VCol>

              <VCol cols="12">
                <AppSelect
                  v-model="formData.targeted_treatments_to_promote"
                  label="Targeted Treatments To Promote"
                  :items="treatments"
                  :rules="[requiredValidator]"
                  multiple
                  chips
                  required
                  @keyup="removeKeyFromInputErrors('targeted_treatments_to_promote')"
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['targeted_treatments_to_promote'])"
                  class="text-sm text-error mb-0 ms-3"
                >
                  {{ inputErrors['targeted_treatments_to_promote'][0] }}
                </p>
              </VCol>

              <VCol cols="12">
                <AppTextarea
                  v-model="formData.avg_visitors_per_month"
                  label="Average Visitors Per Month"
                  rows="3"
                  @keyup="removeKeyFromInputErrors('avg_visitors_per_month')"
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['avg_visitors_per_month'])"
                  class="text-sm text-error mb-0 ms-3"
                >
                  {{ inputErrors['avg_visitors_per_month'][0] }}
                </p>
              </VCol>

              <!-- 👉 Submit and Cancel button -->
              <VCol
                cols="12"
                class="text-center"
              >
                <VBtn
                  type="submit"
                  class="me-3"
                  :loading="isLoading"
                >
                  submit
                </VBtn>

                <VBtn
                  variant="tonal"
                  color="secondary"
                  :disabled="isLoading"
                  @click="resetForm"
                >
                  Cancel
                </VBtn>
              </VCol>
            </VRow>
          </VForm>
        </VCardText>
      </VCard>
    </PerfectScrollbar>
  </VNavigationDrawer>
</template>
