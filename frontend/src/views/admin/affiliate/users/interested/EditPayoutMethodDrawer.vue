<script setup>
import { requiredValidator } from '@/@core/utils/validators'
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { processErrors } from '@/utils/errorHandler'
import { computed, watch } from 'vue'
import { useRoute } from 'vue-router'
import { isEmptyObject, isNullOrUndefined } from '@/@core/utils'
import { removeKeyFromObject } from '@/utils/helpers'
import { PerfectScrollbar } from 'vue3-perfect-scrollbar'

const props = defineProps({
  payoutMethod: {
    type: Object,
    required: false,
    default: () => ({
      payout_method: '', // PayPal, Direct Bank Transfer

      // required only if payout_method is PayPal
      payout_paypal_email: '',

      // required only if payout_method is Direct Bank Transfer
      account_holder_first_name: '',
      account_holder_last_name: '',
      bank_name: '',
      account_number: '',
      routing_number: '',
    }),
  },
  isDrawerOpen: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits([
  'update:isDrawerOpen',
  'updated',
])

const route = useRoute()
const globalStore = useGlobalData()
const { showSnackbar } = globalStore
const userId = computed(() => route.params.userId)
const formRef = ref(null)
const isLoading = ref(false)
const serverErrors = ref([])
const inputErrors = ref({})
const formData = ref(structuredClone(toRaw(props.payoutMethod)))

watch(props, () => {
  formData.value = structuredClone(toRaw(props.payoutMethod))
})

const resetForm = () => {
  emit('update:isDrawerOpen', false)
  formData.value = structuredClone(toRaw(props.payoutMethod))
}

const onFormSubmit = async () => {
  formRef.value?.validate().then(async valid => {
    if (valid.valid) {
      await update()
    }
  })
}

async function update() {
  try {
    isLoading.value = true

    const postData = {
      id: userId.value,
      payout_method: formData.value.payout_method,
    }

    if (formData.value.payout_method === 'PayPal') {
      postData['payout_paypal_email'] = formData.value.payout_paypal_email
    } else if (formData.value.payout_method === 'Direct Bank Transfer') {
      postData['account_holder_first_name'] = formData.value.account_holder_first_name
      postData['account_holder_last_name'] = formData.value.account_holder_last_name
      postData['bank_name'] = formData.value.bank_name
      postData['account_number'] = formData.value.account_number
      postData['routing_number'] = formData.value.routing_number
    }

    const { data } = await ApiService.post('/admin/update-interested-affiliate-user-payment-method', postData)

    if (data.status === 200) {
      emit('update:isDrawerOpen', false)
      emit('updated', true)
      showSnackbar(data.message)
    } else {
      if (data.errors) {
        inputErrors.value = data.errors
      } else {
        serverErrors.value = processErrors(data)
      }
    }
  } catch (error) {
    serverErrors.value = processErrors(error)
    console.error(error)
  } finally {
    isLoading.value = false
  }
}

function removeKeyFromInputErrors(key) {
  inputErrors.value = removeKeyFromObject(inputErrors.value, key)
}

const handleDrawerModelValueUpdate = val => {
  emit('update:isDrawerOpen', val)
}
</script>

<template>
  <VNavigationDrawer
    :model-value="props.isDrawerOpen"
    temporary
    location="end"
    width="400"
    class="category-navigation-drawer scrollable-content"
    @update:model-value="handleDrawerModelValueUpdate"
  >
    <!-- 👉 Header -->
    <AppDrawerHeaderSection
      title="Edit Payout Method"
      @cancel="$emit('update:isDrawerOpen', false)"
    />

    <VDivider />

    <PerfectScrollbar :options="{ wheelPropagation: false }">
      <VCard
        v-if="props.payoutMethod"
        flat
      >
        <VCardText>
          <!-- 👉 Alert -->
          <VAlert
            v-if="isEmptyObject(inputErrors) && serverErrors.length > 0"
            type="error"
            variant="tonal"
            title="Validation failed!"
            class="mb-6"
          >
            <ul class="mb-0">
              <li
                v-for="error in serverErrors"
                :key="error"
                class="mb-0"
              >
                {{ error }}
              </li>
            </ul>
          </VAlert>

          <!-- 👉 Form -->
          <VForm
            ref="formRef"
            @submit.prevent="onFormSubmit"
          >
            <VRow>
              <!-- 👉 Payout Method -->
              <VCol cols="12">
                <AppSelect
                  v-model="formData.payout_method"
                  label="Payout Method"
                  :items="['PayPal', 'Direct Bank Transfer']"
                  :rules="[requiredValidator]"
                  required
                  @keyup="removeKeyFromInputErrors('payout_method')"
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['payout_method'])"
                  class="text-sm text-error mb-0 ms-3"
                >
                  {{ inputErrors['payout_method'][0] }}
                </p>
              </VCol>

              <!-- 👉 When payout method is PayPal -->
              <VCol
                v-if="formData.payout_method === 'PayPal'"
                cols="12"
              >
                <AppTextField
                  v-model="formData.payout_paypal_email"
                  label="PayPal Email Address"
                  :rules="[requiredValidator]"
                  required
                  @keyup="removeKeyFromInputErrors('payout_paypal_email')"
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['payout_paypal_email'])"
                  class="text-sm text-error mb-0 ms-3"
                >
                  {{ inputErrors['payout_paypal_email'][0] }}
                </p>
              </VCol>

              <!-- 👉 When payout method is Direct Bank Transfer -->
              <VRow
                v-if="formData.payout_method === 'Direct Bank Transfer'"
                class="px-3"
              >
                <VCol cols="12">
                  <AppTextField
                    v-model="formData.account_holder_first_name"
                    label="Account Holder First Name"
                    :rules="[requiredValidator]"
                    required
                    @keyup="removeKeyFromInputErrors('account_holder_first_name')"
                  />
                  <p
                    v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['account_holder_first_name'])"
                    class="text-sm text-error mb-0 ms-3"
                  >
                    {{ inputErrors['account_holder_first_name'][0] }}
                  </p>
                </VCol>

                <VCol cols="12">
                  <AppTextField
                    v-model="formData.account_holder_last_name"
                    label="Account Holder Last Name"
                    :rules="[requiredValidator]"
                    required
                    @keyup="removeKeyFromInputErrors('account_holder_last_name')"
                  />
                  <p
                    v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['account_holder_last_name'])"
                    class="text-sm text-error mb-0 ms-3"
                  >
                    {{ inputErrors['account_holder_last_name'][0] }}
                  </p>
                </VCol>

                <VCol cols="12">
                  <AppTextField
                    v-model="formData.bank_name"
                    label="Bank Name"
                    :rules="[requiredValidator]"
                    required
                    @keyup="removeKeyFromInputErrors('bank_name')"
                  />
                  <p
                    v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['bank_name'])"
                    class="text-sm text-error mb-0 ms-3"
                  >
                    {{ inputErrors['bank_name'][0] }}
                  </p>
                </VCol>

                <VCol cols="12">
                  <AppTextField
                    v-model="formData.account_number"
                    label="Account Number"
                    :rules="[requiredValidator]"
                    required
                    @keyup="removeKeyFromInputErrors('account_number')"
                  />
                  <p
                    v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['account_number'])"
                    class="text-sm text-error mb-0 ms-3"
                  >
                    {{ inputErrors['account_number'][0] }}
                  </p>
                </VCol>

                <VCol cols="12">
                  <AppTextField
                    v-model="formData.routing_number"
                    label="Routing Number"
                    :rules="[requiredValidator]"
                    required
                    @keyup="removeKeyFromInputErrors('routing_number')"
                  />
                  <p
                    v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors['routing_number'])"
                    class="text-sm text-error mb-0 ms-3"
                  >
                    {{ inputErrors['routing_number'][0] }}
                  </p>
                </VCol>
              </VRow>

              <!-- 👉 Submit and Cancel button -->
              <VCol
                cols="12"
                class="text-center"
              >
                <VBtn
                  type="submit"
                  class="me-3"
                  :loading="isLoading"
                >
                  submit
                </VBtn>

                <VBtn
                  variant="tonal"
                  color="secondary"
                  :disabled="isLoading"
                  @click="resetForm"
                >
                  Cancel
                </VBtn>
              </VCol>
            </VRow>
          </VForm>
        </VCardText>
      </VCard>
    </PerfectScrollbar>
  </VNavigationDrawer>
</template>
