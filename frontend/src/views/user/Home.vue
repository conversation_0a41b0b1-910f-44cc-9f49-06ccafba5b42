<script setup>
import SignupOverlay from '@/views/user/components/SignupOverlay.vue'

import { ref } from 'vue'

const visible = ref(false)
</script>

<template>
  <div>
    <div class="pt-10">
      <div class="text-center">
        <h5 class="text-2xl text-black mb-4">
          Links
        </h5>
        <div class="mb-3">
          <RouterLink
            :to="{ name: 'ed-start-consult' }"
            class="underline inline-flex items-center gap-x-2 font-semibold text-black"
          >
            <span>Start ED Flow</span>
          </RouterLink>
        </div>
        <!--
          <div class="mb-3">
          <button
          class="underline inline-flex items-center gap-x-2 font-semibold text-black"
          @click="visible = true"
          >
          Start Signup Here
          </button>
          </div>
        -->
        <!--
          <div class="mb-3">
          <RouterLink
          :to="{ name: 'ed-questions', params: { slug: 'start' } }"
          class="underline inline-flex items-center gap-x-2 font-semibold text-black"
          >
          <span>Questions page</span>
          </RouterLink>
          </div>
        -->
        <div class="mb-3">
          <RouterLink
            :to="{ name: 'user-login' }"
            class="underline inline-flex items-center gap-x-2 font-semibold text-black"
          >
            <span>Login page</span>
          </RouterLink>
        </div>
      </div>
    </div>
    <SignupOverlay
      :show="visible"
      @close="visible = false"
    />
  </div>
</template>
