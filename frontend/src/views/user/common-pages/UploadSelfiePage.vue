<script setup>
import { isEmpty } from '@/@core/utils'
import useLogOrderSession from '@/composables/useLogOrderSession'
import router from '@/router'
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { processErrors } from '@/utils/errorHandler'
import { createFileFromUrl, readAsDataURL, scrollToTop } from '@/utils/helpers'
import { resolveVisitSessionKey } from '@/utils/sessionHelpers'
import AlertError from '@/views/user/components/AlertError.vue'
import CameraModal from '@/views/user/components/CameraModal.vue'
import Header from '@/views/user/components/Header.vue'
import { IconCheck, IconLoader2, IconUser } from '@tabler/icons-vue'
import { useDropZone, useSessionStorage, useTimeoutFn } from '@vueuse/core'
import { onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import { toast } from 'vue-sonner'
import '@mediapipe/face_detection'
import '@tensorflow/tfjs-core'
import '@tensorflow/tfjs-backend-webgl'
import * as faceDetection from '@tensorflow-models/face-detection'

const route = useRoute()
const { toggleBackBtnDisabledState } = useGlobalData()
const { logSession } = useLogOrderSession()

const skeletonLoading = ref(false)
const isLoading = ref(false)
const serverErrors = ref([])
const selfieFile = ref({ file: null, url: null })
const selfieDropZoneRef = ref()
const selfieInputRef = ref()
const selfieModalRef = ref()
const selfieImgRef = ref()
const testImgRef = ref()
const isSelfieCaptured = ref(false)
const isUploaded = ref(false)
const imgProcessing = ref(false)

onMounted(async () => {
  await fetchUploadedDocuments()
})

async function fetchUploadedDocuments() {
  try {
    skeletonLoading.value = true

    const { data } = await ApiService.get('/fetch-identity-document')

    if (data.status === 200) {
      if (!isEmpty(data.IdentityDocument) && !isEmpty(data.IdentityDocument.selfie)) {
        selfieFile.value = {
          file: await createFileFromUrl(data.IdentityDocument.selfie),
          url: data.IdentityDocument.selfie,
        }
        isUploaded.value = true
      }
    } else {
      console.error(data)
    }
  } catch (error) {
    console.error(error)
  } finally {
    skeletonLoading.value = false
  }
}

function onSelfieDrop(files) {
  handleSelectedSelfieFiles(files)
}

function handleSelfieSelection() {
  selfieInputRef.value.click()
}

function onChangeSelfieFile() {
  handleSelectedSelfieFiles(selfieInputRef.value.files)
}

async function handleSelectedSelfieFiles(files) {
  if (files.length > 0) {
    const file = files[0]

    if (file.size > 3 * 1024 * 1024) {
      toast.error('File size should be less than 3MB.')

      return
    }
    if (!['image/png', 'image/jpeg', 'image/jpg'].includes(file.type)) {
      toast.error('File type should be jpeg or png.')

      return
    }

    const url = await readAsDataURL(file)

    selfieFile.value = { file, url }

    detectSelfieImage()
  } else {
    selfieFile.value = { file: null, url: null }
    toast.error('Please select an image.')
  }

  isSelfieCaptured.value = false
}

useDropZone(selfieDropZoneRef, onSelfieDrop)

function handleCapturedSelfie(data) {
  selfieFile.value = { file: data.file, url: data.url }
  isSelfieCaptured.value = true
  detectSelfieImage()
}

async function detectSingleFace(input) {
  if (!input) return null

  try {
    const model = faceDetection.SupportedModels.MediaPipeFaceDetector

    const detectorConfig = {
      runtime: 'tfjs',
      maxFaces: 2, // to prevent more than one face being detected
      // modelType: 'full',
    }

    const detector = await faceDetection.createDetector(model, detectorConfig)
    const faces = await detector.estimateFaces(input)

    return faces.length === 1
  } catch (error) {
    console.error('Error detecting face:', error)

    return false
  }
}

async function detectSelfieImage() {
  imgProcessing.value = true
  toggleBackBtnDisabledState(true)

  useTimeoutFn(async () => {
    const input = selfieImgRef.value

    const faceDetected = await detectSingleFace(input)

    if (!faceDetected) {
      if (isSelfieCaptured.value) {
        toast.error('No faces detected. Please try again.')
      } else {
        toast.error('Please select a selfie that shows your face.')
      }
      selfieFile.value = { file: null, url: null }
    }
    imgProcessing.value = false
    toggleBackBtnDisabledState(false)
  }, 500)
}

async function handleSelfieSubmit() {
  await uploadSelfie()
}

function retake() {
  selfieFile.value = { file: null, url: null }
  isUploaded.value = false
  imgProcessing.value = false
}

function nextPage() {
  const visitType = route.params.visitType?.toLowerCase()

  if (visitType === 'ed' || visitType === 'hl') {
    const visitSession = useSessionStorage(resolveVisitSessionKey(visitType), {})

    logSession({
      id: visitSession.value['questionSessionId'],
      category: visitType.toUpperCase(),
      is_selfie_uploaded: 1,
    })

    return router.push({ name: `${visitType}-visit-checkout`, query: { ...route.query } })
  }

  return router.push({ name: 'error-something-wrong' })
}

async function uploadSelfie() {
  try {
    isLoading.value = true
    serverErrors.value = []
    toggleBackBtnDisabledState(true)

    if (isUploaded.value) {
      return nextPage()
    }

    const formData = new FormData()

    formData.append('document_type', 'selfie')
    formData.append('document', selfieFile.value.url)

    const { data } = await ApiService.post('/upload-identity-document', formData)

    if (data.status === 200) {
      return nextPage()
    } else {
      isLoading.value = false
      serverErrors.value = processErrors(data)
      scrollToTop()
    }
  } catch (error) {
    isLoading.value = false
    serverErrors.value = processErrors(error)
    scrollToTop()
  } finally {
    toggleBackBtnDisabledState(false)
  }
}
</script>

<template>
  <div>
    <Header show-back-button />
    <div class="flex items-center justify-center py-10 sm:px-16">
      <div
        v-if="skeletonLoading"
        class="px-4 w-full sm:max-w-[480px]"
      >
        <div class="text-center text-xl md:text-3xl font-bold leading-tight text-black mb-3 h-10 bg-gray-200 rounded animate-pulse"></div>
        <div class="mt-2 text-center text-gray-500 max-w-[300px] mx-auto font-medium h-6 bg-gray-200 rounded animate-pulse"></div>

        <div class="flex flex-col items-center">
          <div class="flex flex-col p-5 mt-6 w-full bg-white rounded-3xl border border-solid !border-zinc-300">
            <div class="justify-between py-1">
              <div class="flex gap-5 max-md:flex-col max-md:gap-0">
                <div class="flex flex-col w-full max-md:ml-0 max-md:w-full">
                  <div class="flex grow justify-between items-top text-sm leading-5 text-black">
                    <div class="grow">
                      <div class="text-xl font-semibold tracking-tight leading-5 mb-4 text-center uppercase h-6 bg-gray-200 rounded animate-pulse"></div>
                      <div class="border-2 border-dashed !border-gray-300 p-4 rounded-lg mb-4 h-64 bg-gray-200 animate-pulse"></div>
                      <div class="text-gray-600 text-center text-sm h-5 bg-gray-200 rounded animate-pulse"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="inline-flex items-center justify-center w-full mt-5">
            <div class="w-64 h-px bg-gray-200 rounded animate-pulse"></div>
            <span class="absolute px-3 font-medium text-gray-900 -translate-x-1/2 bg-white left-1/2"></span>
          </div>

          <button
            type="button"
            class="inline-flex w-full items-center justify-center rounded-full bg-gray-200 px-3.5 py-2.5 font-semibold leading-7 text-white hover:bg-gray-900 text-sm disabled:bg-gray-800 mt-5 uppercase h-12 animate-pulse"
          ></button>
        </div>
      </div>

      <div
        v-else
        class="px-4 w-full sm:max-w-[480px]"
      >
        <h2 class="text-xl md:text-3xl font-bold leading-tight text-black mb-3">
          Upload a photo of yourself
        </h2>
        <p class="mt-2 text-sm text-gray-500 font-medium">
          To verify your identity, we need a clear photo of your face.
        </p>

        <AlertError
          v-if="serverErrors.length > 0"
          title="Error!"
          :errors="serverErrors"
        />

        <div class="flex flex-col items-center">
          <!-- 👉 Selfie -->
          <div class="flex flex-col p-5 mt-6 w-full bg-white rounded-3xl border border-solid !border-zinc-300 relative">
            <div
              v-if="imgProcessing"
              class="absolute bg-gray-600/40 w-full h-full left-0 top-0 rounded-[23px]"
            >
              <div class="flex flex-col items-center justify-center h-full">
                <div>
                  <IconLoader2 class="w-10 h-10 m-auto text-white animate-spin mb-4" />
                  <span class="text-white">Processing...</span>
                </div>
              </div>
            </div>
            <div v-if="!selfieFile.file || !selfieFile.url">
              <div class="justify-between py-1">
                <div class="flex gap-5 max-md:flex-col max-md:gap-0">
                  <div class="flex flex-col w-full max-md:ml-0 max-md:w-full">
                    <div class="flex grow justify-between items-top text-sm leading-5 text-black">
                      <div class="grow">
                        <div class="text-xl font-semibold tracking-tight leading-5 mb-4 text-center uppercase">
                          SELFIE
                        </div>
                        <div
                          ref="selfieDropZoneRef"
                          class="border-2 border-dashed !border-gray-300 p-4 rounded-lg mb-4 cursor-pointer"
                          @click="handleSelfieSelection"
                        >
                          <div class="flex flex-col items-center">
                            <div class="bg-gray-100 w-14 h-14 rounded-full flex items-center justify-center p-2">
                              <IconUser class="w-10 h-10" />
                            </div>
                            <div class="font-medium mt-3">
                              Click to Upload or drag and drop
                            </div>
                            <div class="text-sm">
                              (Max. File size: 3 MB)
                            </div>
                          </div>
                        </div>
                        <input
                          ref="selfieInputRef"
                          type="file"
                          name="selfie_image"
                          class="hidden h-0 w-0"
                          @change="onChangeSelfieFile"
                        >
                        <p class="text-gray-600 text-center text-sm">
                          Selfie should be in (.jpg, .jpeg, .png format only)
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="inline-flex items-center justify-center w-full mt-5">
                <hr class="w-64 h-px bg-gray-200 border-0">
                <span class="absolute px-3 font-medium text-gray-900 -translate-x-1/2 bg-white left-1/2">or</span>
              </div>

              <TwButton
                type="button"
                class="mt-5 uppercase w-full"
                @click="selfieModalRef.open()"
              >
                Capture
              </TwButton>
            </div>

            <div v-if="selfieFile.file && selfieFile.url">
              <div class="flex flex-col">
                <img
                  ref="selfieImgRef"
                  :src="selfieFile.url"
                  alt="selfie preview"
                  class="object-contain w-full min-h-52 max-h-80 rounded-lg bg-gray-50"
                >
                <hr class="my-4">
                <div class="grid grid-cols-4 place-items-center gap-2">
                  <div class="w-full col-span-3">
                    <p class="text-gray-700 w-100 truncate">
                      {{ selfieFile.file.name }}
                    </p>
                    <p class="text-gray-500 text-sm">
                      {{ Math.round(selfieFile.file.size / 1024) }}KB
                    </p>
                  </div>
                  <div class="w-full text-end cols-span-1">
                    <!--
                      <TwButton
                      v-if="isSelfieCaptured"
                      class="!p-1 w-full"
                      @click="selfieModalRef.open()"
                      >
                      Retake
                      </TwButton>
                    -->
                    <TwButton
                      class="!p-1 w-full"
                      @click="retake"
                    >
                      Retake
                    </TwButton>
                  </div>
                </div>
                <div
                  v-if="!isUploaded"
                  class="mt-4"
                >
                  <div class="text-lg font-medium text-gray-700 mb-1">
                    Make sure your photo
                  </div>
                  <div class="flex gap-2">
                    <IconCheck class="w-5 h-5 text-gray-600" />
                    <span>Is not blurry or dark</span>
                  </div>
                  <div class="flex gap-2">
                    <IconCheck class="w-5 h-5 text-gray-600" />
                    <span>Only has you in the photo</span>
                  </div>
                  <div class="flex gap-2">
                    <IconCheck class="w-5 h-5 text-gray-600" />
                    <span>Has not been edited or filtered</span>
                  </div>
                  <div class="flex gap-2">
                    <IconCheck class="w-5 h-5 text-gray-600" />
                    <span>Was taken within the past 30 days</span>
                  </div>
                  <div class="flex gap-2">
                    <IconCheck class="w-5 h-5 text-gray-600" />
                    <span>Shows your full face</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div
            v-if="selfieFile.file && selfieFile.url && !imgProcessing"
            class="mt-8 w-full"
          >
            <TwButton
              type="button"
              class="uppercase w-full"
              :loading="isLoading"
              @click="handleSelfieSubmit"
            >
              Save and continue
            </TwButton>
          </div>
        </div>
      </div>
    </div>

    <CameraModal
      ref="selfieModalRef"
      title="Take a Selfie"
      camera-facing-mode="user"
      :close-on-click-outside="false"
      @image-captured="handleCapturedSelfie"
    />

    <!-- testing image to first load the tf model -->
    <img
      ref="testImgRef"
      src="@images/avatars/avatar-1.png"
      class="hidden h-0 w-0"
    >
  </div>
</template>
