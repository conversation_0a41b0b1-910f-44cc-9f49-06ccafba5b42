<script setup>
import Header from '../components/Header.vue'
import TwButton from '@/components/ui/TwButton.vue'
import { computed, onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import ApiService from '@/services/ApiService'
import { processErrors } from '@/utils/errorHandler'
import router from '@/router'
import { toast } from 'vue-sonner'
import { IconArrowRight, IconCalendarCheck } from '@tabler/icons-vue'

const route = useRoute()
const visitOverview = ref(null)
const isLoading = ref(true)
const btnLoading = ref(false)
const errors = ref([])

const categoryName = computed(() => {
  if (route.params.category === 'ed') {
    return 'Erectile Dysfunction'
  } else if (route.params.category === 'hl') {
    return 'Hair Loss'
  } else if (route.params.category === 'wl') {
    return 'Weight Loss'
  } else {
    return 'Unknown'
  }
})

const startFollowUp = () => {
  btnLoading.value = true

  const category = route.params.category.toLowerCase()

  router.push({ name: `${category}-followup-questions`, params: { slug: 'start' } })
}

async function fetchVisitOverview() {
  try {
    isLoading.value = true
    errors.value = []

    const visitId = route.params.visitId
    const category = route.params.category.toUpperCase()

    const { data } = await ApiService.get(`/followup-visit-overview/${visitId}/${category}`)

    if (data.status === 200) {
      visitOverview.value = data.visitOverview
    } else if (data.status === 400) {
      // start new visit
      router.push({ name: `${route.params.category}-questions` })
    } else {
      toast.error(processErrors(data)[0])
      router.push({ name: 'user-subscription' })
    }
  } catch (error) {
    console.error('Error fetching visit overview:', error)
  } finally {
    isLoading.value = false
  }
}

onMounted(async () => {
  await fetchVisitOverview()
})
</script>

<template>
  <div>
    <Header show-back-button />

    <!-- Loading State -->
    <div
      v-if="isLoading"
      class="grid place-items-center"
    >
      <div class="w-full sm:max-w-lg grid place-items-center h-[calc(100vh-15rem)]">
        <div class="px-4 py-12 sm:px-20 flex flex-col items-center">
          <div class="w-28 h-28 bg-gray-300 rounded-full animate-pulse mb-8"></div>
          <div class="mb-6 text-center space-y-4">
            <div class="h-8 bg-gray-300 rounded w-64 animate-pulse"></div>
            <div class="h-4 bg-gray-300 rounded w-80 animate-pulse"></div>
          </div>
          <div class="h-12 bg-gray-300 rounded-full w-32 animate-pulse"></div>
        </div>
      </div>
    </div>

    <!-- Content -->
    <div
      v-else-if="visitOverview"
      class="grid place-items-center"
    >
      <div class="w-full sm:max-w-lg grid place-items-center h-[calc(100vh-15rem)]">
        <div class="px-4 py-12 sm:px-20 flex flex-col items-center">
          <!-- Icon -->
          <div class="w-28 h-28 bg-green-100 rounded-full flex items-center justify-center mb-8">
            <IconCalendarCheck
              class="w-16 h-16 text-green-600"
              stroke-width="1.5"
            />
          </div>

          <!-- Content -->
          <div class="mb-6 text-center">
            <h1 class="text-2xl sm:text-3xl text-gray-800 font-semibold mb-4">
              Follow-Up Visit Available
            </h1>
            <p class="text-base text-gray-600 mb-6">
              You're eligible for a follow-up visit for your {{ categoryName }} treatment.
            </p>

            <!-- Prescription Details -->
            <div class="bg-green-50 border border-green-200 rounded-2xl p-4 mb-6 text-left">
              <div class="text-sm text-green-700 space-y-2">
                <div class="flex justify-between">
                  <span class="font-medium">Medication:</span>
                  <span>{{ visitOverview.product_name }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="font-medium">Dosage:</span>
                  <span>{{ visitOverview.strength }} {{ visitOverview.strength_unit }} x {{ visitOverview.qty }} units</span>
                </div>
                <div class="flex justify-between">
                  <span class="font-medium">Valid Until:</span>
                  <span>{{ visitOverview.visit_untill }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="font-medium">Category:</span>
                  <span>{{ visitOverview.category_name }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Action Button -->
          <TwButton
            class="px-8"
            :loading="btnLoading"
            @click="startFollowUp"
          >
            <span>Start Follow-Up Visit</span>
            <IconArrowRight
              class="h-5 w-5 ms-2"
              stroke-width="2"
            />
          </TwButton>
        </div>
      </div>
    </div>
  </div>
</template>
