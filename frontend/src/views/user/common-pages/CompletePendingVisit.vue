<script setup>
import Header from '@/views/user/components/Header.vue'
import { IconLoader2 } from '@tabler/icons-vue'
</script>

<template>
  <div>
    <Header />
    <div class="grid place-items-center">
      <div class="w-full sm:max-w-lg grid place-items-center h-[calc(100vh-15rem)]">
        <div class="px-4 py-12 sm:px-20 flex flex-col items-center">
          <IconLoader2
            class="w-12 mb-4 text-gray-800 animate-spin"
            stroke-width="1.5"
          />
          <div>Please wait...</div>
        </div>
      </div>
    </div>
  </div>
</template>
