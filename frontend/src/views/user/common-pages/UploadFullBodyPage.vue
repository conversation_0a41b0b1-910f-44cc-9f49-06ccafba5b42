<script setup>
import { isEmpty } from '@/@core/utils'
import useGlobalSettings from '@/composables/useGlobalSettings'
import useLogOrderSession from '@/composables/useLogOrderSession'
import router from '@/router'
import ApiService from '@/services/ApiService'
import { useGlobalData } from '@/store/global'
import { processErrors } from '@/utils/errorHandler'
import { createFileFromUrl, readAsDataURL, scrollToTop } from '@/utils/helpers'
import { resolveVisitSessionKey } from '@/utils/sessionHelpers'
import AlertError from '@/views/user/components/AlertError.vue'
import CameraModal from '@/views/user/components/CameraModal.vue'
import Header from '@/views/user/components/Header.vue'
import { IconCheck, IconUser } from '@tabler/icons-vue'
import { useDropZone, useSessionStorage } from '@vueuse/core'
import { onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import { toast } from 'vue-sonner'

const route = useRoute()
const { toggleBackBtnDisabledState } = useGlobalData()
const { logSession } = useLogOrderSession()
const { documentUploadStage } = useGlobalSettings()

const skeletonLoading = ref(false)
const isLoading = ref(false)
const serverErrors = ref([])
const bodyImgFile = ref({ file: null, url: null })
const bodyImgDropZoneRef = ref()
const bodyImgInputRef = ref()
const bodyImgModalRef = ref()
const bodyImgRef = ref()
const isBodyImgCaptured = ref(false)
const isUploaded = ref(false)
const isFullBodyImgLatest = useSessionStorage('isFullBodyImgLatest', false)
const ifIdExists = ref(false)

onMounted(async () => {
  await fetchUploadedDocuments()
})

async function fetchUploadedDocuments() {
  try {
    skeletonLoading.value = true

    const { data } = await ApiService.get('/fetch-identity-document')

    if (data.status) {
      if (
        !isEmpty(data.IdentityDocument)
        && !isEmpty(data.IdentityDocument.wl_full_body_image)
        && isFullBodyImgLatest.value
      ) {
        bodyImgFile.value = {
          file: await createFileFromUrl(data.IdentityDocument.wl_full_body_image),
          url: data.IdentityDocument.wl_full_body_image,
        }
        isUploaded.value = true
      }
      if (!isEmpty(data.IdentityDocument) && !isEmpty(data.IdentityDocument.government_document)) {
        ifIdExists.value = true
      }
    } else {
      console.error(data)
    }
  } catch (error) {
    console.error(error)
  } finally {
    skeletonLoading.value = false
  }
}

function onBodyImgDrop(files) {
  handleSelectedBodyImgFiles(files)
}

function handleBodyImgSelection() {
  bodyImgInputRef.value.click()
}

function onChangeBodyImgFile() {
  handleSelectedBodyImgFiles(bodyImgInputRef.value.files)
}

async function handleSelectedBodyImgFiles(files) {
  if (files.length > 0) {
    const file = files[0]

    if (file.size > 3 * 1024 * 1024) {
      toast.error('File size should be less than 3MB.')

      return
    }
    if (!['image/png', 'image/jpeg', 'image/jpg'].includes(file.type)) {
      toast.error('File type should be jpeg or png.')

      return
    }

    const url = await readAsDataURL(file)

    bodyImgFile.value = { file, url }
  } else {
    bodyImgFile.value = { file: null, url: null }
    toast.error('Please select an image.')
  }

  isBodyImgCaptured.value = false
}

useDropZone(bodyImgDropZoneRef, onBodyImgDrop)

function handleCapturedBodyImg(data) {
  bodyImgFile.value = { file: data.file, url: data.url }
  isBodyImgCaptured.value = true
}

async function handleBodyImgSubmit() {
  await uploadBodyImg()
}

function retake() {
  bodyImgFile.value = { file: null, url: null }
  isUploaded.value = false
}

function nextPage(isSyncVisit = false) {
  if (!route.params.visitType) {
    return router.push({ name: 'error-something-wrong' })
  }

  if (!route.query.order_id) {
    const visitSession = useSessionStorage(resolveVisitSessionKey(route.params.visitType), {})

    logSession({
      id: visitSession.value['questionSessionId'],
      category: 'WL',
      is_body_uploaded: 1,
    })
  }

  if (documentUploadStage.value === 'before') {
    return router.push({
      name: `${route.params.visitType}-visit-checkout`,
      query: { ...route.query },
    })
  } else {
    if (ifIdExists.value && isEmpty(route.query.order_id)) {
      return router.push({
        name: `${route.params.visitType}-visit-checkout`,
        query: { ...route.query },
      })
    } else {
      if (route.query.is_sync_visit) {
        return router.push({
          name: 'schedule-video-visit',
          params: {
            visitType: route.params.visitType,
            orderId: route.query.order_id,
          },
          query: { ...route.query },
        })
      } else if (isSyncVisit) {
        return router.push({
          name: 'schedule-video-visit',
          params: {
            visitType: route.params.visitType,
            orderId: route.query.order_id,
          },
          query: { is_sync_visit: true, ...route.query },
        })
      } else {
        return router.push({
          name: `${route.params.visitType}-visit-order-success`,
          params: { orderId: route.query.order_id },
          query: { ...route.query, verification: 'complete' },
        })
      }
    }
  }
}

async function uploadBodyImg() {
  try {
    isLoading.value = true
    serverErrors.value = []
    toggleBackBtnDisabledState(true)

    if (isUploaded.value) {
      return nextPage()
    }

    const formData = new FormData()

    formData.append('document_type', 'full_body_image')
    formData.append('document', bodyImgFile.value.url)

    if (documentUploadStage.value === 'after' && route.query.order_id) {
      formData.append('order_id', route.query.order_id)
    }

    const { data } = await ApiService.post('/upload-identity-document', formData)

    if (data.status === 200) {
      isFullBodyImgLatest.value = true

      return nextPage(data.is_sync_visit ?? false)
    } else {
      isLoading.value = false
      serverErrors.value = processErrors(data)
      scrollToTop()
    }
  } catch (error) {
    isLoading.value = false
    serverErrors.value = processErrors(error)
    scrollToTop()
  } finally {
    toggleBackBtnDisabledState(false)
  }
}
</script>

<template>
  <div>
    <Header show-back-button />
    <div class="flex items-center justify-center py-10 sm:px-16">
      <div
        v-if="skeletonLoading"
        class="px-4 w-full sm:max-w-[480px]"
      >
        <div class="text-center text-xl md:text-3xl font-bold leading-tight text-black mb-3 h-10 bg-gray-200 rounded animate-pulse"></div>
        <div class="mt-2 text-center text-gray-500 max-w-[300px] mx-auto font-medium h-6 bg-gray-200 rounded animate-pulse"></div>

        <div class="flex flex-col items-center">
          <div class="flex flex-col p-5 mt-6 w-full bg-white rounded-3xl border border-solid !border-zinc-300">
            <div class="justify-between py-1">
              <div class="flex gap-5 max-md:flex-col max-md:gap-0">
                <div class="flex flex-col w-full max-md:ml-0 max-md:w-full">
                  <div class="flex grow justify-between items-top text-sm leading-5 text-black">
                    <div class="grow">
                      <div class="text-xl font-semibold tracking-tight leading-5 mb-4 text-center uppercase h-6 bg-gray-200 rounded animate-pulse"></div>
                      <div class="border-2 border-dashed !border-gray-300 p-4 rounded-lg mb-4 h-64 bg-gray-200 animate-pulse"></div>
                      <div class="text-gray-600 text-center text-sm h-5 bg-gray-200 rounded animate-pulse"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="inline-flex items-center justify-center w-full mt-5">
            <div class="w-64 h-px bg-gray-200 rounded animate-pulse"></div>
            <span class="absolute px-3 font-medium text-gray-900 -translate-x-1/2 bg-white left-1/2"></span>
          </div>

          <button
            type="button"
            class="inline-flex w-full items-center justify-center rounded-full bg-gray-200 px-3.5 py-2.5 font-semibold leading-7 text-white hover:bg-gray-900 text-sm disabled:bg-gray-800 mt-5 uppercase h-12 animate-pulse"
          ></button>
        </div>
      </div>

      <div
        v-else
        class="px-4 w-full sm:max-w-[480px]"
      >
        <h2 class="text-xl md:text-3xl font-bold leading-tight text-black mb-3">
          Upload a photo of yourself
        </h2>
        <p class="mt-2 text-sm text-gray-500 font-medium">
          To start your weight loss treatment, we need your full body photo.
        </p>

        <AlertError
          v-if="serverErrors.length > 0"
          title="Error!"
          :errors="serverErrors"
        />

        <div class="flex flex-col items-center">
          <!-- 👉 Full Body Image -->
          <div class="flex flex-col p-5 mt-6 w-full bg-white rounded-3xl border border-solid !border-zinc-300 relative">
            <div v-if="!bodyImgFile.file || !bodyImgFile.url">
              <div class="justify-between py-1">
                <div class="flex gap-5 max-md:flex-col max-md:gap-0">
                  <div class="flex flex-col w-full max-md:ml-0 max-md:w-full">
                    <div class="flex grow justify-between items-top text-sm leading-5 text-black">
                      <div class="grow">
                        <div class="text-xl font-semibold tracking-tight leading-5 mb-4 text-center uppercase">
                          FULL BODY PHOTO
                        </div>
                        <div
                          ref="bodyImgDropZoneRef"
                          class="border-2 border-dashed !border-gray-300 p-4 rounded-lg mb-4 cursor-pointer"
                          @click="handleBodyImgSelection"
                        >
                          <div class="flex flex-col items-center">
                            <div class="bg-gray-100 w-14 h-14 rounded-full flex items-center justify-center p-2">
                              <IconUser class="w-10 h-10" />
                            </div>
                            <div class="font-medium mt-3">
                              Click to Upload or drag and drop
                            </div>
                            <div class="text-sm">
                              (Max. File size: 3 MB)
                            </div>
                          </div>
                        </div>
                        <input
                          ref="bodyImgInputRef"
                          type="file"
                          name="full_body_image"
                          class="hidden h-0 w-0"
                          @change="onChangeBodyImgFile"
                        >
                        <p class="text-gray-600 text-center text-sm">
                          Photo should be in (.jpg, .jpeg, .png format only)
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="inline-flex items-center justify-center w-full mt-5">
                <hr class="w-64 h-px bg-gray-200 border-0">
                <span class="absolute px-3 font-medium text-gray-900 -translate-x-1/2 bg-white left-1/2">or</span>
              </div>

              <TwButton
                type="button"
                class="mt-5 uppercase w-full"
                @click="bodyImgModalRef.open()"
              >
                Capture
              </TwButton>
            </div>

            <div v-if="bodyImgFile.file && bodyImgFile.url">
              <div class="flex flex-col">
                <img
                  ref="bodyImgRef"
                  :src="bodyImgFile.url"
                  alt="full body image preview"
                  class="object-contain w-full min-h-52 max-h-80 rounded-lg bg-gray-50"
                >
                <hr class="my-4">
                <div class="grid grid-cols-4 place-items-center gap-2">
                  <div class="w-full col-span-3">
                    <p class="text-gray-700 w-100 truncate">
                      {{ bodyImgFile.file.name }}
                    </p>
                    <p class="text-gray-500 text-sm">
                      {{ Math.round(bodyImgFile.file.size / 1024) }}KB
                    </p>
                  </div>
                  <div class="w-full text-end cols-span-1">
                    <TwButton
                      class="!p-1 w-full"
                      @click="retake"
                    >
                      Retake
                    </TwButton>
                  </div>
                </div>
                <div
                  v-if="!isUploaded"
                  class="mt-4"
                >
                  <div class="text-lg font-medium text-gray-700 mb-1">
                    Make sure your photo
                  </div>
                  <div class="flex gap-2">
                    <IconCheck class="w-5 h-5 text-gray-600" />
                    <span>Is not blurry or dark</span>
                  </div>
                  <div class="flex gap-2">
                    <IconCheck class="w-5 h-5 text-gray-600" />
                    <span>Only has you in the photo</span>
                  </div>
                  <div class="flex gap-2">
                    <IconCheck class="w-5 h-5 text-gray-600" />
                    <span>Has not been edited or filtered</span>
                  </div>
                  <div class="flex gap-2">
                    <IconCheck class="w-5 h-5 text-gray-600" />
                    <span>Was taken within the past 7 days</span>
                  </div>
                  <div class="flex gap-2">
                    <IconCheck class="w-5 h-5 text-gray-600" />
                    <span>Shows your full body</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div
            v-if="bodyImgFile.file && bodyImgFile.url"
            class="mt-8 w-full"
          >
            <TwButton
              type="button"
              class="uppercase w-full"
              :loading="isLoading"
              @click="handleBodyImgSubmit"
            >
              Save and continue
            </TwButton>
          </div>
        </div>
      </div>
    </div>

    <CameraModal
      ref="bodyImgModalRef"
      title="Take full body photo"
      camera-facing-mode="user"
      :close-on-click-outside="false"
      @image-captured="handleCapturedBodyImg"
    />
  </div>
</template>
