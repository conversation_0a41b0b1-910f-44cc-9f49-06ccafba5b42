<script setup>
import { isEmpty } from '@/@core/utils'
import router from '@/router'
import ApiService from '@/services/ApiService'
import { clearOrderSession } from '@/utils/sessionHelpers'
import Header from '@/views/user/components/Header.vue'
import { IconBolt, IconMoodSad } from '@tabler/icons-vue'
import { computed } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()

onMounted(async () => {
  clearOrderSession()
  await fetchAvailableTreatments()
})

const btnLoading = ref(false)

const pageContent = computed(() => {
  if (route.fullPath.includes('ed')) {
    return 'We\'re sorry, we don\'t offer ED treatments in your state right now. Please check back later as we\'re working to expand our services.'
  } else if (route.fullPath.includes('hl')) {
    return 'We\'re sorry, we don\'t offer Hair loss treatments in your state right now. Please check back later as we\'re working to expand our services.'
  } else if (route.fullPath.includes('wl')) {
    return 'We\'re sorry, we don\'t offer Weight loss treatments in your state right now. Please check back later as we\'re working to expand our services.'
  } else {
    return 'We\'re sorry, we don\'t offer any treatments in your state right now. Please check back later as we\'re working to expand our services.'
  }
})

const btnAction = () => {
  btnLoading.value = true
  router.push({ name: 'user-subscription' })
}

const availableTreatments = ref({})
const isEDAvailable = computed(() => availableTreatments.value['ED'])
const isHLAvailable = computed(() => availableTreatments.value['HL'])
const isWLAvailable = computed(() => availableTreatments.value['WL'])
const startVisitBtnLoading = ref(null)

async function fetchAvailableTreatments() {
  try {
    const { data } = await ApiService.get('/available-treatments')

    if (data.status === 200) {
      availableTreatments.value = data.treatments
    } else {
      console.error(data)
    }
  } catch (error) {
    console.error(error)
  }
}

function startTreatment(treatment) {
  startVisitBtnLoading.value = treatment
  clearOrderSession()
  router.push({ name: `${treatment}-questions`, params: { slug: 'start' } })
}
</script>

<template>
  <div>
    <Header />
    <div class="grid place-items-center">
      <div class="w-full sm:max-w-lg grid place-items-center h-[calc(100vh-15rem)]">
        <div class="bg-gray-100 rounded-[20px] px-5 py-8 flex flex-col items-center mb-10 mt-6">
          <IconMoodSad
            class="w-28 mb-4 text-gray-800"
            stroke-width="1.5"
          />
          <div class="mb-6 text-center">
            <h1 class="text-2xl sm:text-3xl text-gray-800 font-semibold mb-4">
              Treatment Not Available
            </h1>
            <p class="text-base text-gray-600">
              {{ pageContent }}
            </p>
          </div>
        </div>

        <h3
          v-if="isEDAvailable || isHLAvailable || isWLAvailable"
          class="text-2xl font-semibold text-gray-800 mb-6"
        >
          Explore other treatments
        </h3>

        <!-- Start Subscriptions -->
        <div class="flex flex-wrap gap-5">
          <div
            v-if="isEDAvailable"
            class="flex-1 bg-gradient-to-b from-blue-400 via-blue-300 to-cyan-100 p-5 rounded-[20px] min-h-64 min-w-72"
          >
            <div>
              <h5 class="text-2xl font-semibold text-zinc-900 mb-2">
                Sexual health
              </h5>
              <p class="text-sm text-zinc-700 font-medium max-w-[65%]">
                Regain your confidence and have the sexual life you've always wanted.
              </p>
            </div>
            <div class="flex -mt-6">
              <TwButton
                class="h-fit self-end"
                :loading="startVisitBtnLoading === 'ed'"
                :disabled="!isEmpty(startVisitBtnLoading) || btnLoading"
                loading-text="Starting..."
                @click="startTreatment('ed')"
              >
                <span>Start&nbsp;Now</span>
                <IconBolt
                  class="h-[14px] w-4 ms-1"
                  stroke-width="1"
                  fill="currentColor"
                />
              </TwButton>
              <div class="w-fit ms-auto">
                <img
                  src="@/assets/user/images/ed-pills.png"
                  class="w-full h-36 object-cover -me-8"
                  alt="ED Pills"
                >
              </div>
            </div>
          </div>

          <div
            v-if="isHLAvailable"
            class="flex-1 flex flex-col justify-between bg-gradient-to-b from-green-400 via-green-300 to-lime-100 p-5 rounded-[20px] min-h-64 min-w-72"
          >
            <div>
              <h5 class="text-2xl font-semibold text-zinc-900 mb-2">
                Hair Health
              </h5>
              <p class="text-sm text-zinc-700 font-medium max-w-[65%]">
                Address hair loss at the root with prescription medication.
              </p>
            </div>
            <div class="flex -mt-6">
              <TwButton
                class="h-fit self-end"
                :loading="startVisitBtnLoading === 'hl'"
                :disabled="!isEmpty(startVisitBtnLoading) || btnLoading"
                loading-text="Starting..."
                @click="startTreatment('hl')"
              >
                <span>Start&nbsp;Now</span>
                <IconBolt
                  class="h-[14px] w-4 ms-1"
                  stroke-width="1"
                  fill="currentColor"
                />
              </TwButton>
              <div class="w-fit ms-auto">
                <img
                  src="@/assets/user/images/hl-pills.png"
                  class="w-full h-40 object-cover -me-8 -mb-4"
                  alt="ED Pills"
                >
              </div>
            </div>
          </div>

          <div
            v-if="isWLAvailable"
            class="flex-1 flex flex-col justify-between bg-gradient-to-b from-red-400 via-red-300 to-[#FFE5C7] p-5 rounded-[20px] min-h-64 min-w-72"
          >
            <div>
              <h5 class="text-2xl font-semibold text-zinc-900 mb-2">
                Weight Loss
              </h5>
              <p class="text-sm text-zinc-700 font-medium max-w-[65%]">
                Lose up to 15% of your body weight with cutting-edge treatments.
              </p>
            </div>
            <div class="flex -mt-6">
              <TwButton
                class="h-fit self-end"
                :loading="startVisitBtnLoading === 'wl'"
                :disabled="!isEmpty(startVisitBtnLoading) || btnLoading"
                loading-text="Starting..."
                @click="startTreatment('wl')"
              >
                <span>Start&nbsp;Now</span>
                <IconBolt
                  class="h-[14px] w-4 ms-1"
                  stroke-width="1"
                  fill="currentColor"
                />
              </TwButton>
              <div class="w-fit ms-auto">
                <img
                  src="@/assets/user/images/wl-kit.png"
                  class="w-full h-40 object-cover -me-8 -mb-2"
                  alt="ED Pills"
                >
              </div>
            </div>
          </div>
        </div>

        <div class="py-10 w-full">
          <TwButton
            class="w-full"
            :loading="btnLoading || !isEmpty(startVisitBtnLoading)"
            @click="btnAction"
          >
            <span>Back to Home</span>
          </TwButton>
        </div>
      </div>
    </div>
  </div>
</template>
