<script setup>
import { computed, defineAsyncComponent, ref } from 'vue'
import Header from '@/views/user/components/Header.vue'
import { IconCircleCheck, IconCircleX } from '@tabler/icons-vue'
import { useRoute } from 'vue-router'
import router from '@/router'
import { useUpdatePaymentMethod } from '@/store/updatePaymentMethod'

const SubscriptionUpdatePaymentModal = defineAsyncComponent(() => import('@/views/user/components/SubscriptionUpdatePaymentModal.vue'))
const ModalHelp = defineAsyncComponent(() => import('@/views/user/components/ModalHelp.vue'))

const route = useRoute()
const { setSubscriptionRefillId } = useUpdatePaymentMethod()
const isSuccess = computed(() => route.params.status === 'success')
const subscriptionId = computed(() => route.params.subscriptionId)
const nextRefillDate = computed(() => route.query.next_refill_date)
const refillId = computed(() => route.query.r_id)
const currentPaymentId = computed(() => route.query.p_id)
const updatePaymentModalRef = ref(null)

const modalHelpRef = ref(null)

const viewSubscription = () => {
  router.replace({
    name: 'user-manage-subscription',
    params: { subscriptionId: subscriptionId.value },
  })
}

const updatePaymentMethod = () => {
  setSubscriptionRefillId(refillId.value)
  updatePaymentModalRef.value.openModal(currentPaymentId.value)
}

function handlePaymentUpdated() {
  router.replace({
    name: 'user-subscription-reactivation-status',
    params: {
      status: 'success',
      subscriptionId: subscriptionId.value,
    },
    query: {
      payment_success: true,
    },
  })
}
</script>

<template>
  <div>
    <Header />

    <div class="flex items-center justify-center py-10 sm:px-16">
      <div class="p-4 w-full max-w-md bg-white rounded-[20px] border !border-gray-100 shadow-lg">
        <!-- Success State -->
        <div
          v-if="isSuccess"
          class="p-6 text-center"
        >
          <div class="mb-4 relative">
            <div class="mx-auto h-24 w-24 rounded-full bg-green-100 flex items-center justify-center">
              <IconCircleCheck
                class="text-green-400 h-16 w-16"
                stroke-width="0.75"
              />
            </div>
          </div>
          <h2 class="text-2xl font-bold text-gray-800 mb-2">
            Subscription Activated!
          </h2>
          <p
            v-if="nextRefillDate"
            class="text-gray-600 mb-2"
          >
            Your subscription is activated successfully.
          </p>
          <p class="text-gray-600 mb-6">
            <span v-if="nextRefillDate">Your next refill date is <span class="font-semibold">{{ nextRefillDate }}</span></span>
            <span v-else>Your refill is processed successfully. It will be shipped soon.</span>
          </p>
          <TwButton
            class="w-full"
            @click="viewSubscription"
          >
            View Subscription
          </TwButton>
        </div>

        <!-- Failure State -->
        <div
          v-else
          class="p-6"
        >
          <div class="mb-4">
            <div class="mx-auto h-24 w-24 rounded-full bg-red-100 flex items-center justify-center">
              <IconCircleX
                class="text-red-400 h-16 w-16"
                stroke-width="0.75"
              />
            </div>
          </div>
          <h2 class="text-2xl font-bold text-gray-800 mb-2 text-center">
            Activation Failed
          </h2>
          <p class="text-gray-600 mb-4">
            We couldn't activate your subscription. This could be due to:
          </p>
          <ul class="text-left text-gray-600 mb-6 ps-6 list-disc">
            <li>Payment not processed</li>
            <li>Card is expired</li>
            <li>Insufficient funds available</li>
            <li>Some technical issue</li>
          </ul>
          <p class="text-gray-600 mb-6">
            You can change your payment method and try again.
          </p>
          <TwButton
            class="w-full"
            @click="updatePaymentMethod"
          >
            Update Payment Method
          </TwButton>
          <TwButton
            class="w-full mt-3"
            variant="secondary"
            @click="modalHelpRef.open()"
          >
            Contact Support
          </TwButton>
        </div>
      </div>
    </div>

    <!-- Update Payment Modal -->
    <SubscriptionUpdatePaymentModal
      ref="updatePaymentModalRef"
      @updated="handlePaymentUpdated"
    />

    <!-- Help modal -->
    <ModalHelp ref="modalHelpRef" />
  </div>
</template>
