<script setup>
import { isEmpty, isEmptyObject, isNullOrUndefined } from '@/@core/utils'
import useMapsApi from '@/composables/useMapsApi'
import router from '@/router'
import ApiService from '@/services/ApiService'
import { processErrors } from '@/utils/errorHandler'
import { formatCurrency, scrollToTop } from '@/utils/helpers'
import AlertError from '@/views/user/components/AlertError.vue'
import Header from '@/views/user/components/Header.vue'
import { IconCheck, IconListSearch, IconPhone, IconShieldLock, IconX } from '@tabler/icons-vue'
import { useSessionStorage } from '@vueuse/core'
import { Drawer } from 'flowbite'
import { vMaska } from 'maska'
import AutoComplete from 'primevue/autocomplete'
import Sidebar from 'primevue/sidebar'
import { ErrorMessage, Field, Form as VForm } from 'vee-validate'
import { onMounted, ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import * as yup from 'yup'
import useLogOrderSession from '@/composables/useLogOrderSession'
import { resolveVisitSessionKey } from '@/utils/sessionHelpers'
import useGlobalSettings from '@/composables/useGlobalSettings'
import useDocuments from '@/composables/useDocuments'

const route = useRoute()
const { searchPlaces, autoCompleteOptions } = useMapsApi()
const { logSession } = useLogOrderSession()

const formShippingAddressRef = ref(null)
const isLoading = ref(false)
const skeletonLoading = ref(false)
const inputErrors = ref({})
const serverErrors = ref([])
const states = ref([])
const countries = ['USA']
const showEditShippingForm = ref(true)
const shippingMethods = ref([])
const isLocalPickup = ref(false)
const localPharmacyConfirmSidebar = ref(false)
const selectedShippingMethod = ref('')
const shippingDetailsSession = useSessionStorage('shippingDetailsSession', {})
const hideShipmentMethodsSection = ref(false)
const { isIdUploaded } = useDocuments()
const { documentUploadStage } = useGlobalSettings()

const shippingAddress = ref({
  address_line_1: '',
  address_line_2: '',
  city: '',
  state: '',
  zipcode: '',
  country: 'USA',
})

const selectedPharmacyId = ref(null)
const selectedPharmacy = ref(null)
const pharmacyDrawer = ref(null)
const pharmacies = ref([])
const searchLoading = ref(false)
const errorNoPharmacy = ref('')

const pharmacySearch = ref({
  name: '',
  city: '',
  state: '',
  zip: '',
})

const statesList = computed(() => states.value.filter(item => item.type === 'state'))
const territoriesList = computed(() => states.value.filter(item => item.type === 'territory'))

const validationSchema = yup.object().shape({
  address_line_1: yup.string().required('Street address is required'),
  city: yup.string().required('City is required'),
  state: yup.string().required('State / Territory is required'),
  zipcode: yup.string().required('Zipcode is required'),
  country: yup.string().required('Country is required'),
})

onMounted(async () => {
  skeletonLoading.value = true
  await Promise.all([fetchShippingMethods(), fetchStates(), getShippingAddress()])
  populateShippingInfo()
  skeletonLoading.value = false

  initPharmacyDrawer()
})

function initPharmacyDrawer() {
  const $targetEl = document.getElementById('drawer-search-pharmacy')

  const options = {
    placement: 'right',
    backdrop: true,
    bodyScrolling: false,
    edge: false,
    edgeOffset: '',
    backdropClasses: 'bg-gray-900/50 fixed inset-0 z-30',
    onHide: () => {
      pharmacySearch.value = {
        name: '',
        city: '',
        state: '',
        zip: '',
      }
      errorNoPharmacy.value = ''
    },
    onShow: () => {
      selectedPharmacyId.value = null
      pharmacies.value = []
    },
    onToggle: () => {
      // console.log('drawer has been toggled')
    },
  }

  const instanceOptions = {
    id: 'drawer-search-pharmacy',
    override: true,
  }

  pharmacyDrawer.value = new Drawer($targetEl, options, instanceOptions)
}

async function fetchShippingMethods() {
  if (route.params.visitType === 'wl' || route.params.visitType === 'wl-followup') {
    shippingMethods.value = []

    return
  }

  try {
    hideShipmentMethodsSection.value = false

    const visitSession = useSessionStorage(resolveVisitSessionKey(route.params.visitType), {})

    const { data } = await ApiService.post('/fetch-shipment-methods', {
      product_id: visitSession.value['productId'],
      category: String(route.params.visitType).toUpperCase(),
    })

    if (data.status === 200) {
      // if (isEmpty(data.pharmacyShipmentMethods)) {
      //   return router.push({ name: 'error-something-wrong', query: { visit: route.params.visitType } })
      // }
      shippingMethods.value = data.pharmacyShipmentMethods
      isLocalPickup.value = data.is_local_pickup_enabled ?? false

      if (isEmpty(shippingDetailsSession.value.selectedShippingMethod)) {
        selectedShippingMethod.value = shippingMethods.value[0]?.id
      }

      if (shippingMethods.value.length === 1 && shippingMethods.value[0].cost === 0) {
        hideShipmentMethodsSection.value = true
      }
    } else {
      console.error(data)
      serverErrors.value = processErrors(data)
    }
  } catch (error) {
    console.error(error)
    serverErrors.value = processErrors(error)
  }
}

async function fetchStates() {
  try {
    const { data } = await ApiService.get('/state-list')

    if (data.status === 200) {
      states.value = data.stateData ?? []
    } else {
      console.error(data)
    }
  } catch (error) {
    console.error(error)
  }
}

async function getShippingAddress() {
  try {
    serverErrors.value = []

    const { data } = await ApiService.get('/shipping-address-info')

    if (data.status === 200) {
      shippingAddress.value = data.shippingAddressInfo
      showEditShippingForm.value = false
    } else if (data.status === 204) {
      // no action: shipping address not added
    } else {
      serverErrors.value = processErrors(data)
    }
  } catch (error) {
    serverErrors.value = processErrors(error)
    console.error(error)
  }
}

async function createShippingSession() {
  const freeShippingOption = shippingMethods.value.find(s => s.pharmacy_shipment_method_id === 1)
  const switchToId = freeShippingOption ? freeShippingOption.id : shippingMethods.value[0]?.id

  shippingDetailsSession.value = {
    selectedShippingMethod: selectedShippingMethod.value,
    selectedPharmacy: { ...selectedPharmacy.value },
    switchToId: switchToId,
  }

  const visitSession = useSessionStorage(resolveVisitSessionKey(route.params.visitType), {})

  logSession({
    id: visitSession.value['questionSessionId'],
    category: route.params.visitType?.toUpperCase(),
    shipping: {
      id: selectedShippingMethod.value ?? 'shipment_method_not_selected',
      selected_pharmacy: selectedShippingMethod.value === 'local_pickup' ? { ...selectedPharmacy.value } : undefined,
      switch_to_id: selectedShippingMethod.value === 'local_pickup' ? switchToId : undefined,
    },
  })
}

function populateShippingInfo() {
  const shippingData = shippingDetailsSession.value

  if (isEmptyObject(shippingData)) return

  selectedShippingMethod.value = shippingData.selectedShippingMethod
  selectedPharmacy.value = { ...shippingData.selectedPharmacy }
}

function gotoNextPage() {
  isLoading.value = true

  if (route.params.visitType === 'wl' || route.params.visitType === 'wl-followup') {
    logWlSession()
  } else {
    createShippingSession()
  }

  if (documentUploadStage.value === 'before') {
    router.push({
      name: 'visit-photo-upload-id',
      params: { visitType: route.params.visitType },
      query: { ...route.query },
    })
  } else {
    if (isIdUploaded.value) {
      router.push({
        name: 'visit-photo-upload-id',
        params: { visitType: route.params.visitType },
        query: { ...route.query },
      })
    } else {
      router.push({
        name: `${route.params.visitType}-visit-checkout`,
        query: { ...route.query },
      })
    }
  }
}

function logWlSession() {
  const visitSession = useSessionStorage(resolveVisitSessionKey(route.params.visitType), {})

  logSession({
    id: visitSession.value['questionSessionId'],
    category: 'WL',
    shipping: {
      is_wl_shipping_completed: 1,
    },
  })
}

async function saveShippingAddress(values) {
  try {
    isLoading.value = true
    inputErrors.value = {}
    serverErrors.value = []

    const postData = { ...values }

    const { data } = await ApiService.post('/update-shipping-address', postData)

    if (data.status === 200) {
      gotoNextPage()
    } else {
      isLoading.value = false
      if (data.errors) {
        inputErrors.value = data.errors
      }
      serverErrors.value = processErrors(data)
    }
  } catch (error) {
    isLoading.value = false
    if (error.response.errors) {
      inputErrors.value = error.response.errors
    }
    serverErrors.value = processErrors(error)
    console.error(error)
  }
}

async function handleShippingSubmit() {
  if (selectedShippingMethod.value === 'local_pickup' && selectedPharmacy.value == null) {
    errorNoPharmacy.value = 'Please select a pharmacy!'
    pharmacyDrawer.value.show()

    return
  }

  if (showEditShippingForm.value) {
    const { valid } = await formShippingAddressRef.value.validate()
    if (valid) {
      await saveShippingAddress(shippingAddress.value)
    }
  } else {
    gotoNextPage()
  }
}

const handleShippingAddressSearch = () => {
  if (!isEmpty(shippingAddress.value.address_line_1)) {
    searchPlaces(shippingAddress.value.address_line_1)
  }
}

const handleShippingAddressSelectedItem = $event => {
  const item = $event.value

  shippingAddress.value.address_line_1 = item.streetAddress
  shippingAddress.value.city = item.city

  for (const state of states.value) {
    if (state.code === item.state) {
      shippingAddress.value.state = item.state
    }
  }

  shippingAddress.value.zipcode = item.postalCode
}

const confirmLocalPickup = () => {
  localPharmacyConfirmSidebar.value = false
  selectedShippingMethod.value = 'local_pickup'
  pharmacyDrawer.value.show()
}

const switchToOurPharmacy = () => {
  selectedShippingMethod.value = shippingMethods.value[0]?.id || ''
  scrollToTop()
}

const handlePharmacySearch = () => {
  if (
    !isEmpty(pharmacySearch.value.name) ||
    !isEmpty(pharmacySearch.value.city) ||
    !isEmpty(pharmacySearch.value.state) ||
    !isEmpty(pharmacySearch.value.zip)
  ) {
    searchPharmacy()
  } else {
    errorNoPharmacy.value = 'Please enter at least one field'
  }
}

watch(pharmacySearch.value, newValue => {
  errorNoPharmacy.value = ''
})

async function searchPharmacy() {
  try {
    searchLoading.value = true
    errorNoPharmacy.value = ''

    const postData = { ...pharmacySearch.value }

    const { data } = await ApiService.post('/search-pharmacy', postData)

    if (data.status === 200) {
      pharmacies.value = data.pharmacyList
      if (pharmacies.value.length === 0) {
        errorNoPharmacy.value = 'No pharmacies found!'
      }
    } else {
      errorNoPharmacy.value = processErrors(data)[0]
    }
  } catch (error) {
    console.error(error)
    errorNoPharmacy.value = processErrors(error)[0]
  } finally {
    searchLoading.value = false
  }
}

const confirmPharmacySelection = () => {
  pharmacyDrawer.value.hide()
  selectedPharmacy.value = pharmacies.value.find(pharmacy => pharmacy.PharmacyId === selectedPharmacyId.value)
}
</script>

<template>
  <div class="mx-auto max-w-[1920px]">
    <Header show-back-button />

    <div
      v-if="skeletonLoading"
      class="flex items-center justify-center px-4 py-10 sm:px-6 sm:py-16 lg:px-8 lg:py-24"
    >
      <div class="px-4 w-full sm:max-w-[480px]">
        <div class="h-7 bg-gray-300 rounded mb-3 animate-pulse"></div>
        <div class="h-7 bg-gray-300 rounded mb-2 w-75 mx-auto animate-pulse"></div>
        <div class="h-12 bg-gray-300 rounded mt-6 mb-2 animate-pulse"></div>
        <div class="h-12 bg-gray-300 rounded mt-6 mb-2 animate-pulse"></div>
        <div class="h-12 bg-gray-300 rounded mt-6 mb-2 animate-pulse"></div>
        <div class="h-12 bg-gray-300 rounded mt-6 mb-2 animate-pulse"></div>
        <div class="h-12 bg-gray-300 rounded-xl mt-8 mb-2 animate-pulse"></div>
        <div class="h-5 bg-gray-300 rounded mb-2 mt-5 w-50 mx-auto animate-pulse"></div>
      </div>
    </div>

    <div
      v-else
      class="flex items-center justify-center px-4 py-10 sm:px-6 sm:py-16 lg:px-8 lg:py-24"
    >
      <div class="px-4 w-full sm:max-w-[480px]">
        <h2 class="text-center text-2xl md:text-3xl font-bold leading-tight text-black">
          Where would you like us to ship your medication?
        </h2>
        <!--
          <p class="mt-2 text-center text-gray-500 max-w-[300px] mx-auto font-medium">
          Your privacy is important to us, So shipping is discreet and free.
          </p>
        -->

        <AlertError
          v-if="isEmptyObject(inputErrors) && serverErrors.length > 0"
          title="Error!"
          :errors="serverErrors"
        />

        <div
          v-if="selectedShippingMethod !== 'local_pickup' && !hideShipmentMethodsSection"
          class="mt-7 mb-10"
        >
          <div v-if="!isEmpty(shippingMethods) && route.params.visitType !== 'wl' && route.params.visitType !== 'wl-followup'">
            <h5 class="text-gray-700 text-lg font-medium mb-3">
              Select Shipping Method
            </h5>
          </div>
          <ul
            v-if="!isEmpty(shippingMethods)"
            class="grid w-full space-y-3"
          >
            <li
              v-for="shippingMethod in shippingMethods"
              :key="shippingMethod.id"
            >
              <input
                :id="`shipping_method_${shippingMethod.id}`"
                v-model="selectedShippingMethod"
                type="radio"
                name="shipping_method"
                class="hidden peer"
                :value="shippingMethod.id"
                required
              />
              <label
                :for="`shipping_method_${shippingMethod.id}`"
                class="inline-flex items-center justify-between w-full px-5 py-3 text-gray-700 bg-white border !border-gray-200 rounded-2xl cursor-pointer peer-checked:!border-gray-600 peer-checked:text-black hover:text-gray-800 hover:bg-gray-100"
              >
                <div class="block">
                  <div class="w-full text-lg font-medium">{{ shippingMethod.title }}</div>
                  <div class="w-full text-sm">{{ shippingMethod.description }}</div>
                  <div
                    v-if="shippingMethod.cost > 0"
                    class="w-full text-lg font-semibold"
                  >{{ formatCurrency(shippingMethod.cost) }}</div>
                </div>
                <span class="mx-2 flex items-center justify-center">
                  <span
                    class="w-4 h-4 border-2 rounded-sm flex justify-center items-center"
                    :class="[
                      selectedShippingMethod === shippingMethod.id ? 'border-transparent bg-black' : 'border-gray-400'
                    ]"
                  >
                    <IconCheck
                      class="h-5 w-5 z-50"
                      :stroke-width="selectedShippingMethod === shippingMethod.id ? 4 : 1"
                      :class="[
                        selectedShippingMethod === shippingMethod.id ? 'text-white' : 'text-gray-900'
                      ]"
                    />
                  </span>
                </span>
              </label>
            </li>
          </ul>
          <ul
            v-else-if="route.params.visitType === 'wl' || route.params.visitType === 'wl-followup'"
            class="grid w-full space-y-3"
          >
            <li>
              <input
                id="shipping_method_wl_free_shipping"
                type="radio"
                name="shipping_method"
                class="hidden peer"
                checked
                required
              />
              <label
                for="shipping_method_wl_free_shipping"
                class="inline-flex items-center justify-between w-full px-5 py-3 text-gray-700 bg-white border !border-gray-200 rounded-2xl cursor-pointer peer-checked:!border-gray-600 peer-checked:text-black hover:text-gray-800 hover:bg-gray-100"
              >
                <div class="block">
                  <div class="w-full text-lg font-medium">Free Shipping</div>
                </div>
                <span class="mx-2 flex items-center justify-center">
                  <span class="w-4 h-4 border-2 rounded-sm flex justify-center items-center border-transparent bg-black text-white">
                    <IconCheck
                      class="h-5 w-5 z-50"
                      stroke-width="4"
                    />
                  </span>
                </span>
              </label>
            </li>
          </ul>
        </div>

        <div v-if="selectedShippingMethod === 'local_pickup'">
          <!-- 👉 pharmacy not selected -->
          <div
            v-if="isEmpty(selectedPharmacy) || isEmptyObject(selectedPharmacy)"
            class="p-6 mt-6 w-full bg-white rounded-3xl border border-solid !border-zinc-300"
          >
            <h5 class="text-gray-600 text-base font-medium mb-3">
              No pharmacy selected
            </h5>
            <div class="flex flex-col gap-2 sm:flex-row justify-around">
              <div>
                <button
                  class="text-base font-semibold border-b-2 border-solid !border-black hover:text-gray-600 hover:!border-gray-600"
                  type="button"
                  @click="pharmacyDrawer.show()"
                >
                  Search local pharmacy
                </button>
              </div>
              <div>
                <button
                  class="text-base font-semibold border-b-2 border-solid !border-black hover:text-gray-600 hover:!border-gray-600"
                  type="button"
                  @click="switchToOurPharmacy"
                >
                  Ship with our pharmacy
                </button>
              </div>
            </div>
          </div>

          <!-- 👉 pharmacy selected -->
          <div
            v-if="!isEmpty(selectedPharmacy) && !isEmptyObject(selectedPharmacy)"
            class="p-6 mt-6 w-full bg-white rounded-3xl border border-solid !border-zinc-300"
          >
            <h5 class="text-black text-lg font-medium mb-3">
              Prescription pickup location
            </h5>
            <div class="mb-4">
              <span class="block font-medium">{{ selectedPharmacy?.StoreName }}</span>
              <span class="block">{{
                selectedPharmacy?.Address1 +
                  ", " +
                  (!isEmpty(selectedPharmacy?.Address2) ? selectedPharmacy?.Address2 + ", " : "") +
                  selectedPharmacy?.City +
                  ", " +
                  selectedPharmacy?.State +
                  ", " +
                  selectedPharmacy?.ZipCode
              }}</span>
              <span class="w-full flex">
                <span>
                  <IconPhone
                    class="h-4 w-4 me-1"
                    fill="currentColor"
                  />
                </span>
                <span>{{ selectedPharmacy?.PrimaryPhone }}</span>
              </span>
              <span class="block text-sm italic mt-2">You'll pickup your prescription at this pharmacy.</span>
            </div>
            <div class="flex flex-col gap-2 sm:flex-row justify-around">
              <div>
                <button
                  class="inline-block text-base font-semibold border-b-2 border-solid !border-black hover:text-gray-600 hover:!border-gray-600"
                  type="button"
                  @click="pharmacyDrawer.show()"
                >
                  Change pharmacy
                </button>
              </div>
              <div>
                <button
                  class="text-base font-semibold border-b-2 border-solid !border-black hover:text-gray-600 hover:!border-gray-600"
                  type="button"
                  @click="switchToOurPharmacy"
                >
                  Ship with our pharmacy
                </button>
              </div>
            </div>
          </div>
        </div>

        <div class="mt-8">
          <div
            v-if="selectedShippingMethod === 'local_pickup'"
            class="ms-2 mb-5"
          >
            <span class="text-lg font-medium text-gray-700">Your Home Address</span>
            <span class="block text-sm text-gray-500">
              We also need your home address for your account information.
            </span>
          </div>

          <div v-if="!showEditShippingForm">
            <div class="p-6 w-full bg-white rounded-3xl border border-solid !border-zinc-300">
              <h5 class="text-gray-700 text-base font-medium mb-3">
                Saved address
              </h5>
              <div class="mb-4">
                <span>{{ shippingAddress.address_line_1 }}</span> <br>
                <span v-if="shippingAddress.address_line_2">{{ shippingAddress.address_line_2 }}<br></span>
                <span>{{ shippingAddress.city }}, {{ shippingAddress.state }}, {{ shippingAddress.zipcode }}<br></span>
                <span>{{ shippingAddress.country }}</span>
              </div>
              <button
                class="text-base font-semibold border-b-2 border-solid !border-black hover:text-gray-600 hover:!border-gray-600"
                @click="showEditShippingForm = true"
              >
                Edit
              </button>
            </div>
          </div>

          <VForm
            v-if="showEditShippingForm"
            ref="formShippingAddressRef"
            autocomplete="off"
            :validation-schema="validationSchema"
          >
            <div class="space-y-5">
              <!-- 👉 Street address -->
              <div>
                <div class="relative">
                  <AutoComplete
                    id="street_line_1"
                    v-model="shippingAddress.address_line_1"
                    name="address_line_1"
                    option-label="streetAddress"
                    placeholder="Street Address"
                    class="w-full"
                    input-class="block w-full px-5 py-3.5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:!outline-none focus:!ring-1 focus:!ring-black peer"
                    :suggestions="autoCompleteOptions"
                    :delay="500"
                    @complete="handleShippingAddressSearch"
                    @item-select="handleShippingAddressSelectedItem"
                  >
                    <template #option="slotProps">
                      <div class="">
                        <p class="mb-0 text-sm font-medium">
                          {{ slotProps.option.displayName }}
                        </p>
                        <small>{{ slotProps.option.formattedAddress }}</small>
                      </div>
                    </template>
                  </AutoComplete>
                  <Field
                    v-model="shippingAddress.address_line_1"
                    type="hidden"
                    class="hidden h-0 w-0"
                    name="address_line_1"
                    required
                  />
                </div>
                <ErrorMessage
                  name="address_line_1"
                  class="text-red-500 text-sm ms-5"
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.address_line_1)"
                  class="text-red-500 text-sm ms-5"
                >
                  {{ inputErrors.address_line_1[0] }}
                </p>
              </div>

              <!-- 👉 Street address line 2 -->
              <div>
                <div class="relative">
                  <Field
                    id="address_line_2"
                    v-model="shippingAddress.address_line_2"
                    type="text"
                    name="address_line_2"
                    class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                    placeholder=" "
                    autocomplete="off"
                    required
                  />
                  <label
                    for="address_line_2"
                    class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4 cursor-text"
                  >Apt / Suite (optional)</label>
                </div>
                <ErrorMessage
                  name="address_line_2"
                  class="text-red-500 text-sm ms-5"
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.address_line_2)"
                  class="text-red-500 text-sm ms-5"
                >
                  {{ inputErrors.address_line_2[0] }}
                </p>
              </div>

              <!-- 👉 City -->
              <div>
                <div class="relative">
                  <Field
                    id="city"
                    v-model="shippingAddress.city"
                    type="text"
                    name="city"
                    class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                    placeholder=" "
                    autocomplete="off"
                    required
                  />
                  <label
                    for="city"
                    class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4 cursor-text"
                  >City</label>
                </div>
                <ErrorMessage
                  name="city"
                  class="text-red-500 text-sm ms-5"
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.city)"
                  class="text-red-500 text-sm ms-5"
                >
                  {{ inputErrors.city[0] }}
                </p>
              </div>

              <div class="relative">
                <Field
                  id="state"
                  v-model="shippingAddress.state"
                  as="select"
                  name="state"
                  class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                  required
                >
                  <option
                    value=""
                    disabled
                    selected
                  >
                    Select
                  </option>
                  <optgroup label="States">
                    <option
                      v-for="state in statesList"
                      :key="state.code"
                      :value="state.code"
                    >
                      {{ state.name }}
                    </option>
                  </optgroup>
                  <optgroup label="Territories">
                    <option
                      v-for="territory in territoriesList"
                      :key="territory.code"
                      :value="territory.code"
                    >
                      {{ territory.name }}
                    </option>
                  </optgroup>
                </Field>
                <label
                  for="state"
                  class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-focus:scale-75 peer-focus:-translate-y-4"
                >State / Territory</label>
                <ErrorMessage
                  name="state"
                  class="text-red-500 text-sm ms-5"
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.state)"
                  class="text-red-500 text-sm ms-5"
                >
                  {{ inputErrors.state[0] }}
                </p>
              </div>

              <div>
                <div class="relative">
                  <Field
                    id="zipcode"
                    v-model="shippingAddress.zipcode"
                    v-maska
                    type="text"
                    name="zipcode"
                    class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                    placeholder=" "
                    autocomplete="off"
                    data-maska="#####-####"
                    required
                  />
                  <label
                    for="zipcode"
                    class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4 cursor-text"
                  >Zipcode</label>
                </div>
                <ErrorMessage
                  name="zipcode"
                  class="text-red-500 text-sm ms-5"
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.zipcode)"
                  class="text-red-500 text-sm ms-5"
                >
                  {{ inputErrors.zipcode[0] }}
                </p>
              </div>

              <div class="relative">
                <Field
                  id="country"
                  v-model="shippingAddress.country"
                  as="select"
                  name="country"
                  class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                  required
                >
                  <option
                    value=""
                    disabled
                    selected
                  >
                    Select
                  </option>
                  <option
                    v-for="item in countries"
                    :key="item"
                    :value="item"
                  >
                    {{ item }}
                  </option>
                </Field>
                <label
                  for="country"
                  class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-focus:scale-75 peer-focus:-translate-y-4"
                >Country</label>
                <ErrorMessage
                  name="country"
                  class="text-red-500 text-sm ms-5"
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.country)"
                  class="text-red-500 text-sm ms-5"
                >
                  {{ inputErrors.country[0] }}
                </p>
              </div>
            </div>
          </VForm>
        </div>

        <div class="mt-8">
          <TwButton
            class="w-full"
            :loading="isLoading"
            @click="handleShippingSubmit"
          >
            Save and continue
          </TwButton>
        </div>

        <div
          v-if="isLocalPickup && selectedShippingMethod !== 'local_pickup' && route.params.visitType !== 'wl' && route.params.visitType !== 'wl-followup'"
          class="mt-5 flex justify-center"
          @click="localPharmacyConfirmSidebar = true"
        >
          <button class="text-sm font-medium border-b-2 border-solid !border-black hover:text-gray-600 hover:!border-gray-600">
            Select a local pharmacy
          </button>
        </div>

        <div class="mt-8 text-center">
          <p class="text-xs text-gray-600 flex gap-2 justify-center items-center font-medium">
            <span>
              <IconShieldLock
                class="h-[18px] w-[18px] mb-px"
                stroke-width="2"
              />
            </span>
            <span>256-BIT TLS SECURITY</span>
          </p>
        </div>
      </div>
    </div>

    <!-- 👉 local pharmacy confirmation sidebar -->
    <Sidebar
      v-model:visible="localPharmacyConfirmSidebar"
      position="right"
      show-close-icon
      class="local-pharmacy-sidebar"
      @hide="localPharmacyConfirmSidebar = false"
    >
      <div class="w-full">
        <h2 class="text-2xl font-bold leading-tight text-black mb-3">
          Want a Different Pharmacy?
        </h2>
        <div class="mb-6 text-base leading-[1.7] space-y-3">
          <div>
            If you choose a pharmacy outside our network and qualify for a prescription, we can't promise the same price or free monthly delivery, and we won't be able to create a subscription for your visit.
          </div>
          <div>
            Picking a local pharmacy for insurance might mean your plan won't cover ED medication, causing delays.
          </div>
        </div>
        <TwButton
          class="w-full mb-3"
          @click="localPharmacyConfirmSidebar = false"
        >
          Stick with White Label Rx Pharmacy
        </TwButton>
        <button
          class="w-full items-center justify-center rounded-full bg-white text-black border !border-gray-500 px-3.5 py-2 font-semibold leading-7 hover:bg-gray-200 text-sm"
          @click="confirmLocalPickup"
        >
          Yes, I'm sure
        </button>
      </div>
    </Sidebar>

    <!-- 👉 Pharmacy search drawer -->
    <div
      id="drawer-search-pharmacy"
      class="fixed top-0 right-0 z-[60] h-screen p-4 overflow-y-auto transition-transform translate-x-full bg-white w-100 max-w-lg"
      tabindex="-1"
      aria-labelledby="drawer-search-pharmacy-label"
    >
      <h5
        id="drawer-search-pharmacy-label"
        class="inline-flex items-center mb-4 text-lg font-semibold text-gray-700"
      >
        Search your local pharmacy
      </h5>
      <button
        type="button"
        aria-controls="drawer-search-pharmacy"
        class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 absolute top-2.5 end-2.5 inline-flex items-center justify-center"
        @click="pharmacyDrawer.hide()"
      >
        <IconX
          class="w-5 h-5"
          stroke-width="2"
        />
        <span class="sr-only">Close menu</span>
      </button>

      <div>
        <AlertError
          v-if="errorNoPharmacy"
          :title="errorNoPharmacy"
          :errors="[]"
        />

        <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
          <div class="relative">
            <input
              id="pharmacyName"
              v-model="pharmacySearch.name"
              type="text"
              class="block w-full px-4 pb-1.5 pt-3 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
              placeholder=" "
            />
            <label
              for="pharmacyName"
              class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-[0.8rem] scale-75 top-3 z-10 origin-[0] left-4 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-[0.8rem] cursor-text"
            >Pharmacy Name</label>
          </div>

          <div class="relative">
            <input
              id="pharmacyCity"
              v-model="pharmacySearch.city"
              type="text"
              class="block w-full px-4 pb-1.5 pt-3 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
              placeholder=" "
            />
            <label
              for="pharmacyCity"
              class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-[0.8rem] scale-75 top-3 z-10 origin-[0] left-4 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-[0.8rem] cursor-text"
            >City</label>
          </div>

          <div class="relative">
            <select
              id="state"
              v-model="pharmacySearch.state"
              class="block w-full px-4 pb-1.5 pt-3 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
            >
              <option
                value=""
                disabled
                selected
              >
                Select
              </option>
              <optgroup label="States">
                <option
                  v-for="state in statesList"
                  :key="state.code"
                  :value="state.code"
                >
                  {{ state.name }}
                </option>
              </optgroup>
              <optgroup label="Territories">
                <option
                  v-for="territory in territoriesList"
                  :key="territory.code"
                  :value="territory.code"
                >
                  {{ territory.name }}
                </option>
              </optgroup>
            </select>
            <label
              for="state"
              class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-[0.8rem] scale-75 top-3 z-10 origin-[0] left-4 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-[0.8rem] cursor-text"
            >State / Territory</label>
          </div>

          <div class="relative">
            <input
              id="pharmacyZip"
              v-model="pharmacySearch.zip"
              v-maska
              type="text"
              class="block w-full px-4 pb-1.5 pt-3 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
              placeholder=" "
              data-maska="#####"
            />
            <label
              for="pharmacyZip"
              class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-[0.8rem] scale-75 top-3 z-10 origin-[0] left-4 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-[0.8rem] cursor-text"
            >Zipcode</label>
          </div>
        </div>
        <div>
          <TwButton
            :loading="searchLoading"
            class="w-full h-10 mt-5"
            @click="handlePharmacySearch"
          >
            Search Pharmacy
          </TwButton>
        </div>

        <div class="h-px bg-gray-300 my-5"></div>

        <div class="relative">
          <div v-if="pharmacies.length > 0">
            <h5 class="text-gray-700 text-lg font-medium mb-3">
              Select your pharmacy
            </h5>
          </div>
          <ul
            v-if="pharmacies.length > 0"
            class="grid w-full space-y-3 pb-20"
          >
            <li
              v-for="pharmacy in pharmacies"
              :key="pharmacy.PharmacyId"
            >
              <input
                :id="`pharmacy_${pharmacy.PharmacyId}`"
                v-model="selectedPharmacyId"
                type="radio"
                name="shipping_method"
                class="hidden peer"
                :value="pharmacy.PharmacyId"
                required
              />
              <label
                :for="`pharmacy_${pharmacy.PharmacyId}`"
                class="inline-flex items-center justify-between w-full px-4 py-3 text-gray-700 bg-white border !border-gray-200 rounded-2xl cursor-pointer peer-checked:!border-gray-600 peer-checked:text-black hover:text-gray-800 hover:bg-gray-100"
              >
                <div class="block">
                  <div class="w-full text-base font-medium">{{ pharmacy.StoreName }}</div>
                  <div class="w-full text-sm">{{
                    pharmacy.Address1 +
                      ", " +
                      (!isEmpty(pharmacy?.Address2) ? pharmacy?.Address2 + ", " : "") +
                      pharmacy.City +
                      ", " +
                      pharmacy.State +
                      ", " +
                      pharmacy.ZipCode
                  }}</div>
                  <div class="w-full text-sm flex">
                    <span>
                      <IconPhone
                        class="h-4 w-4 me-1"
                        fill="currentColor"
                      />
                    </span>
                    <span>{{ pharmacy.PrimaryPhone }}</span>
                  </div>
                </div>
                <span class="mx-2 flex items-center justify-center">
                  <span
                    class="w-4 h-4 border-2 rounded-sm flex justify-center items-center"
                    :class="[
                      selectedPharmacyId === pharmacy.PharmacyId ? 'border-transparent bg-black' : 'border-gray-400'
                    ]"
                  >
                    <IconCheck
                      class="h-5 w-5 z-50"
                      :stroke-width="selectedPharmacyId === pharmacy.PharmacyId ? 4 : 1"
                      :class="[
                        selectedPharmacyId === pharmacy.PharmacyId ? 'text-white' : 'text-gray-900'
                      ]"
                    />
                  </span>
                </span>
              </label>
            </li>
          </ul>
          <div
            v-else
            class="p-6 w-full grid place-items-center gap-3"
          >
            <IconListSearch
              class="h-16 w-16 text-gray-500"
              stroke-width="2"
            />
            <div class="font-medium text-gray-500">
              Search to see pharmacies
            </div>
          </div>

          <div class="fixed bottom-5 min-w-[calc(100%-38px)] sm:min-w-[474px]">
            <TwButton
              v-if="selectedPharmacyId"
              class="w-full"
              @click="confirmPharmacySelection"
            >
              Confirm Pharmacy
            </TwButton>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss">
.p-inputtext {
  border: none !important;
  border-color: transparent;
  font-family: "Konnect", system-ui, -apple-system, BlinkMacSystemFont,
    "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue",
    sans-serif;
    font-size: 14px !important;
    font-weight: 500;

  &:enabled:hover {
    border-color: transparent;
  }
  &:enabled:focus {
    outline: none;
    outline-offset: 0;
    box-shadow: none;
    border-color: transparent;
  }
}

.p-autocomplete-panel {
  max-width: 400px;
}
.p-autocomplete-panel .p-autocomplete-items .p-autocomplete-item {
  white-space: pre-line;
}
.p-autocomplete-panel {
  z-index: 2102 !important;
}
.p-sidebar-right .local-pharmacy-sidebar.p-sidebar {
  width: 30rem;
  font-family: "Konnect", system-ui, -apple-system, BlinkMacSystemFont,
    "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue",
    sans-serif;
}
</style>
