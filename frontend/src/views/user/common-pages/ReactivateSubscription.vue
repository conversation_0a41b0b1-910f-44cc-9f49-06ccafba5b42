<script setup>
import { isEmpty } from '@/@core/utils'
import router from '@/router'
import ApiService from '@/services/ApiService'
import { formatCurrency } from '@/utils/helpers'
import Header from '@/views/user/components/Header.vue'
import { useRoute } from 'vue-router'
import { useSubscriptionStore } from '@/store/subscription'
import { storeToRefs } from 'pinia'

const route = useRoute()
const visitType = route.params.visitType
const activePrescriptions = ref([])
const btnStartNewLoading = ref(false)
const isLoading = ref(true)

const subscriptionStore = useSubscriptionStore()
const { btnReactivateLoading } = storeToRefs(subscriptionStore)
const { reactivateSubscription } = subscriptionStore

onMounted(async () => {
  await fetchActivePrescriptions()
  isLoading.value = false
})

async function fetchActivePrescriptions() {
  try {
    const { data } = await ApiService.get(`/active-prescriptions/${visitType}`)

    if (data.status === 200) {
      if (isEmpty(data.active_prescriptions)) {
        startNew()

        return
      }

      activePrescriptions.value = data.active_prescriptions
    }
  } catch (error) {
    console.error(error)
    router.push({ name: 'user-subscription' })
  }
}

function startNew() {
  btnStartNewLoading.value = true
  sessionStorage.setItem(`start_new_${visitType}_visit`, true)
  router.push({ name: `${visitType}-questions`, params: { slug: 'start' } })
}
</script>

<template>
  <div>
    <Header />
    <div class="grid place-items-center">
      <div class="w-full sm:max-w-2xl">
        <div class="px-4 flex flex-col items-center pb-12">
          <div class="mb-6 text-center">
            <h1 class="text-2xl sm:text-3xl text-gray-800 font-semibold mb-1">
              We found an active prescription on file
            </h1>
            <p class="text-base text-gray-600">
              You can either reactivate your previous subscription or start a new one with updated preferences.
            </p>
          </div>

          <div class="w-full sm:max-w-lg">
            <!-- Skeleton Loader -->
            <div
              v-if="isLoading"
              class="space-y-4"
            >
              <div
                v-for="n in 1"
                :key="n"
                class="flex flex-col p-5 w-full bg-white rounded-3xl border border-solid !border-zinc-300 animate-pulse"
              >
                <div class="flex max-md:flex-col max-md:gap-0">
                  <div class="flex flex-col w-full max-md:ml-0 max-md:w-full">
                    <div class="flex flex-col gap-5 grow justify-between items-top text-sm leading-5 text-black">
                      <div class="mb-2 bg-gray-100 rounded-lg h-48 grid place-items-center">
                        <div class="h-36 w-36 bg-gray-300 rounded-lg"></div>
                      </div>
                      <div class="grow space-y-4">
                        <div class="h-6 bg-gray-300 rounded w-3/4"></div>
                        <div class="h-4 bg-gray-300 rounded w-1/2"></div>
                        <div class="space-y-2">
                          <div class="h-4 bg-gray-300 rounded w-full"></div>
                          <div class="h-4 bg-gray-300 rounded w-full"></div>
                          <div class="h-4 bg-gray-300 rounded w-full"></div>
                        </div>
                        <div class="grid gap-4">
                          <div class="h-12 bg-gray-300 rounded"></div>
                          <div class="h-12 bg-gray-300 rounded"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Actual Prescriptions -->
            <div
              v-for="(activePrescription, index) in activePrescriptions"
              v-else-if="activePrescriptions.length > 0"
              :key="index"
              class="flex flex-col p-5 w-full bg-white rounded-3xl border border-solid !border-zinc-300"
            >
              <div class="justify-between py-1">
                <div class="flex max-md:flex-col max-md:gap-0">
                  <div class="flex flex-col w-full max-md:ml-0 max-md:w-full">
                    <div class="flex flex-col gap-5 grow justify-between items-top text-sm leading-5 text-black">
                      <div class="mb-2 bg-gray-100 rounded-lg h-48 grid place-items-center">
                        <img
                          :src="activePrescription.product_img"
                          class="h-36 w-36 object-contain rounded-lg"
                          alt=""
                        >
                      </div>
                      <div class="grow">
                        <div class="text-xl font-semibold tracking-tight leading-5 mb-2">
                          {{ activePrescription.product_name }}
                        </div>
                        <div class="text-zinc-500 text-sm">
                          <span>
                            {{ activePrescription.strength }} {{ activePrescription.strength_unit }} x {{ activePrescription.qty * activePrescription.subscription_interval }} units
                          </span>
                          <span>({{ activePrescription.subscription_interval * 30 }}-day supply)</span>
                        </div>
                        <div class="mt-4 space-y-2 text-sm">
                          <div class="flex justify-between">
                            <span class="text-gray-600">Remaining Refills:</span>
                            <span class="font-semibold">{{ activePrescription.remain_refill }}</span>
                          </div>
                          <div class="flex justify-between">
                            <span class="text-gray-600">Prescription Expiry:</span>
                            <span class="font-semibold">{{ activePrescription.prescription_expired_date }}</span>
                          </div>
                          <div class="flex justify-between">
                            <span class="text-gray-600">Subscription Amount:</span>
                            <span class="font-semibold">{{ formatCurrency(activePrescription.subscription_amount) }}</span>
                          </div>
                        </div>
                      </div>
                      <div class="grid gap-4">
                        <div class="bg-white p-4 rounded-lg border !border-gray-200">
                          <div class="text-xs font-semibold uppercase mb-2">
                            Resume Treatment
                          </div>
                          <p class="text-xs text-gray-600 mb-4">
                            Resume your previous treatment plan using your existing prescription. Fast and easy—no new consult needed.
                          </p>
                          <TwButton
                            class="w-full"
                            :loading="btnReactivateLoading"
                            :disabled="btnStartNewLoading"
                            @click="reactivateSubscription(activePrescription.id)"
                          >
                            Reactivate Subscription
                          </TwButton>
                        </div>
                        <div class="bg-white p-4 rounded-lg border !border-gray-200">
                          <div class="text-xs font-semibold uppercase mb-2">
                            Start New
                          </div>
                          <p class="text-xs text-gray-600 mb-4">
                            Begin a new treatment request. You'll go through a brief medical questionnaire and a provider will review your case.
                          </p>
                          <TwButton
                            variant="secondary"
                            class="w-full"
                            :loading="btnStartNewLoading"
                            :disabled="btnReactivateLoading"
                            @click="startNew"
                          >
                            Start New Visit
                          </TwButton>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- No Prescriptions -->
            <div
              v-else
              class="text-center text-gray-600 py-8"
            >
              No active prescriptions found.
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
