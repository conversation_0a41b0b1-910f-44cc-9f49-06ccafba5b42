<script setup>
import { usePreventNavigation } from '@/composables/usePreventNavigation'
import router from '@/router'
import Header from '@/views/user/components/Header.vue'
import ConfettiExplosion from 'vue-confetti-explosion'
import { useRoute } from 'vue-router'
import { toast } from 'vue-sonner'

const route = useRoute()
const loading = ref(false)

usePreventNavigation(true)

const btnAction = () => {
  loading.value = true

  if (route.fullPath.includes('ed')) {
    router.replace({ name: 'ed-visit-product-recommended', query: { ...route.query } })
  } else if (route.fullPath.includes('hl')) {
    router.replace({ name: 'hl-visit-product-recommended', query: { ...route.query } })
  } else if (route.fullPath.includes('wl')) {
    toast.info('implementation pending')
  }
}
</script>

<template>
  <div>
    <Header />
    <div class="grid place-items-center">
      <div class="w-full sm:max-w-lg grid place-items-center h-[calc(100vh-15rem)]">
        <div class="px-4 py-12 sm:px-20 flex flex-col items-center">
          <ConfettiExplosion />
          <img
            class="w-28 mb-8"
            src="@/assets/user/images/confetti.png"
            alt="Confetti"
          />
          <div class="mb-6 text-center">
            <h1 class="text-2xl sm:text-3xl text-gray-800 font-semibold mb-4">
              The best version of YOU awaits!
            </h1>
            <p class="text-base sm:text-lg text-gray-600">
              You're on your way to a healthier you. Let's pick a plan and move forward.
            </p>
          </div>
          <TwButton
            class="px-8"
            :loading="loading"
            @click="btnAction"
          >
            Continue
          </TwButton>
        </div>
      </div>
    </div>
  </div>
</template>
