<script setup>
import router from '@/router'
import { clearOrderSession } from '@/utils/sessionHelpers'
import Header from '@/views/user/components/Header.vue'
import { IconArrowRight, IconPrescription } from '@tabler/icons-vue'
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { useActiveSubscriptionPlan } from '@/composables/useActiveSubscriptionPlan'

const route = useRoute()
const activeSubscriptionPlan = useActiveSubscriptionPlan()

onMounted(async () => {
  clearOrderSession()
  await validateActiveVisits()
})

const validateActiveVisits = async () => {
  try {
    const data = await activeSubscriptionPlan.validateActiveSubscriptionPlan()

    if (data.status === 200) {
      if (route.fullPath.includes('ed') && activeSubscriptionPlan.isSubscriptionActive('ed')) {
        return
      } else if (route.fullPath.includes('hl') && activeSubscriptionPlan.isSubscriptionActive('hl')) {
        return
      } else if (route.fullPath.includes('wl') && activeSubscriptionPlan.isSubscriptionActive('wl')) {
        return
      } else {
        return router.push({ name: 'user-subscription' })
      }
    }
  } catch (error) {
    console.error(error)
    router.push({ name: 'user-subscription' })
  }
}

const pageContent = computed(() => {
  if (route.fullPath.includes('ed')) {
    return {
      title: 'Active Subscription Found',
      description: 'You currently have an active ED subscription. You will need to complete it before purchasing a new subscription.',
    }
  } else if (route.fullPath.includes('hl')) {
    return {
      title: 'Active Subscription Found',
      description: 'You currently have an active subscription for Hair treatment. You will need to complete it before purchasing a new subscription.',
    }
  } else if (route.fullPath.includes('wl')) {
    return {
      title: 'Active Treatment Found',
      description: 'You currently have an active treatment for Weight loss. You will need to wait before creating a follow-up visit.',
    }
  } else {
    return {
      title: 'Active Subscription Found',
      description: 'You currently have an active subscription. You will need to complete it before purchasing a new subscription.',
    }
  }
})

const btnLoading = ref(false)

const btnAction = () => {
  btnLoading.value = true
  router.push({ name: 'user-subscription' })
}
</script>

<template>
  <div>
    <Header />
    <div class="grid place-items-center">
      <div class="w-full sm:max-w-lg grid place-items-center h-[calc(100vh-15rem)]">
        <div class="px-4 py-12 sm:px-20 flex flex-col items-center">
          <IconPrescription
            class="w-28 mb-4 text-gray-800"
            stroke-width="1.5"
          />
          <div class="mb-6 text-center">
            <h1 class="text-2xl sm:text-3xl text-gray-800 font-semibold mb-4">
              {{ pageContent.title }}
            </h1>
            <p class="text-base text-gray-600">
              {{ pageContent.description }}
            </p>
          </div>
          <TwButton
            class="w-auto px-6"
            :loading="btnLoading"
            @click="btnAction"
          >
            <span>View Active Treatments</span>
            <IconArrowRight
              class="h-5 w-5 ms-2"
              stroke-width="2"
            />
          </TwButton>
        </div>
      </div>
    </div>
  </div>
</template>
