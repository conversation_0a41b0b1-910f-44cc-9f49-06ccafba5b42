<script setup>
import successIcon from '@/assets/user/images/success-icon.svg'
import { usePreventNavigation } from '@/composables/usePreventNavigation'
import { clearOrderSession } from '@/utils/sessionHelpers'
import Header from '@/views/user/components/Header.vue'
import { onMounted, ref } from 'vue'
import { RouterLink, useRoute } from 'vue-router'

usePreventNavigation(true)

const route = useRoute()
const skeletonLoading = ref(false)

onMounted(async () => {
  clearOrderSession()
})
</script>

<template>
  <div>
    <Header />
    <div class="flex items-center justify-center py-6 sm:px-16">
      <div
        v-if="skeletonLoading"
        class="px-4 w-full sm:max-w-[600px]"
      >
        <div class="h-7 bg-gray-300 rounded mb-3 animate-pulse"></div>
        <div class="h-7 bg-gray-300 rounded mb-2 w-75 mx-auto animate-pulse"></div>
        <div class="h-12 bg-gray-300 rounded mt-6 mb-2 animate-pulse"></div>
        <div class="h-12 bg-gray-300 rounded mt-6 mb-2 animate-pulse"></div>
        <div class="h-12 bg-gray-300 rounded mt-6 mb-2 animate-pulse"></div>
        <div class="h-12 bg-gray-300 rounded mt-6 mb-2 animate-pulse"></div>
        <div class="h-12 bg-gray-300 rounded-xl mt-8 mb-2 animate-pulse"></div>
      </div>
      <div
        v-else
        class="px-4 w-full sm:max-w-[600px]"
      >
        <div class="flex flex-col w-full">
          <div class="flex flex-col items-center justify-center">
            <img
              :src="successIcon"
              class="h-20 w-20"
              alt="Success Icon"
            />
            <div class="text-2xl sm:text-3xl text-center font-semibold mt-4">
              Thank you for your purchase.
            </div>
          </div>
        </div>

        <div class="p-6 mt-6 w-full bg-amber-100 rounded-2xl border !border-amber-200 shadow-sm">
          <div class="space-y-3">
            <h2 class="text-2xl font-semibold text-red-500">
              Wait! You are not finished yet.
            </h2>
            <p class="text-base leading-[1.7]">
              <span class="font-semibold">You must upload your government issued ID proof {{ route.params.visitType === 'wl' ? 'and a photo of your full body' : '' }} to receive your treatment.</span> Your treatment cannot be prescribed until this verification is complete. Federal regulations require us to verify your identity before our doctors can prescribe medication. This is standard for all legitimate telehealth services.
            </p>
            <p class="text-base font-semibold text-gray-700 mt-4">
              Your Privacy Is Protected
            </p>
            <ul class="text-sm text-gray-600 list-disc ps-3 mt-1">
              <li>Your ID is encrypted and stored securely using HIPAA-compliant systems</li>
              <li>Only your provider can access your verification documents</li>
              <li>Your information is never shared with marketers or third parties</li>
            </ul>
            <p class="text-base font-semibold text-gray-700 mt-4">
              Upload Requirements
            </p>
            <ul class="text-sm text-gray-600 list-disc ps-3 mt-1">
              <li>Government-issued photo ID (such as driver's license, passport, or state ID)</li>
              <li>All four corners visible in the image</li>
              <li>Information clearly readable</li>
              <li v-if="route.params.visitType === 'wl'">
                Photo of your full body
              </li>
            </ul>
          </div>
        </div>

        <RouterLink
          :to="{
            name: 'visit-photo-upload-id',
            params: { visitType: route.params.visitType },
            query: { ...route.query },
          }"
          class="btn-primary w-full mt-6"
        >
          Continue
        </RouterLink>
      </div>
    </div>
  </div>
</template>
