<script setup>
import Header from '../components/Header.vue'
import { onMounted, onUnmounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import ApiService from '@/services/ApiService'
import router from '@/router'
import { IconArrowRight, IconCalendarCheck, IconVideo } from '@tabler/icons-vue'

const route = useRoute()
const visitData = ref(null)
const pollingInterval = ref(null)
const timeoutId = ref(null)
const timerDisplay = ref('10:00')
const timerInterval = ref(null)
const isProcessing = ref(false)
const isLoading = ref(true)
const errors = ref([])

async function fetchSyncVisitDetail() {
  try {
    errors.value = []

    const { data } = await ApiService.get(`/synchronous-visit-detail/${route.params.orderId}`)

    if (data.status === 200) {
      visitData.value = data.data
      if (data.data.is_slot_booked) {
        clearInterval(pollingInterval.value)
        pollingInterval.value = null
        clearTimeout(timeoutId.value)
        timeoutId.value = null
        clearInterval(timerInterval.value)
        timerInterval.value = null
      } else if (data.data.visit_scheduled_link && !timeoutId.value) {
        startTimer()
      }
    } else {
      console.error(data)
      clearInterval(pollingInterval.value)
      pollingInterval.value = null

      router.push({
        name: 'ed-visit-order-success',
        params: { orderId: route.params.orderId },
        query: { ...route.query },
      })
    }
  } catch (error) {
    console.error('Error fetching visit overview:', error)
    clearInterval(pollingInterval.value)
    pollingInterval.value = null
  } finally {
    if (isLoading.value) {
      isLoading.value = false
    }
  }
}

function handleScheduleClick() {
  if (visitData.value?.visit_scheduled_link) {
    window.open(visitData.value.visit_scheduled_link, '_blank')
    isProcessing.value = true
  }
}

function startTimer() {
  // Set a 10-minute timeout
  timeoutId.value = setTimeout(() => {
    handleContinue()
  }, 600000)

  // Update the display every second
  let duration = 600 // 10 minutes in seconds
  timerInterval.value = setInterval(() => {
    duration -= 1

    const minutes = Math.floor(duration / 60)
    const seconds = duration % 60

    timerDisplay.value = `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`

    if (duration <= 0) {
      clearInterval(timerInterval.value)
    }
  }, 1000)
}

function handleContinue() {
  if (visitData.value.is_medicine_pickup_at_local_pharmacy === 1) {
    // redirect to order details page
    router.push({
      name: 'user-order-details',
      params: { orderId: visitData.value.subscription_refill_id },
    })
  } else {
    // redirect to subscription details page
    router.push({
      name: 'user-manage-subscription',
      params: { subscriptionId: visitData.value.subscription_id },
    })
  }
}

onMounted(async () => {
  await fetchSyncVisitDetail()

  if (!visitData.value?.is_slot_booked) {
    pollingInterval.value = setInterval(fetchSyncVisitDetail, 3000)
  }
})

onUnmounted(() => {
  clearInterval(pollingInterval.value)
  clearTimeout(timeoutId.value)
  clearInterval(timerInterval.value)
})
</script>

<template>
  <div>
    <Header show-back-button />

    <!-- Loading State -->
    <div
      v-if="isLoading"
      class="grid place-items-center"
    >
      <div class="w-full sm:max-w-lg grid place-items-center h-[calc(100vh-15rem)]">
        <div class="px-4 py-12 sm:px-20 flex flex-col items-center">
          <div class="w-28 h-28 bg-gray-300 rounded-full animate-pulse mb-8"></div>
          <div class="mb-6 text-center space-y-4">
            <div class="h-8 bg-gray-300 rounded w-64 animate-pulse"></div>
            <div class="h-4 bg-gray-300 rounded w-80 animate-pulse"></div>
          </div>
          <div class="h-12 bg-gray-300 rounded-full w-32 animate-pulse"></div>
        </div>
      </div>
    </div>

    <!-- Content -->
    <div
      v-else-if="visitData"
      class="grid place-items-center"
    >
      <div class="w-full sm:max-w-lg grid place-items-center h-[calc(100vh-15rem)]">
        <div class="px-4 py-12 sm:px-20 flex flex-col items-center text-center">
          <!-- Slot Booked -->
          <div v-if="visitData.is_slot_booked">
            <IconCalendarCheck class="w-28 h-28 text-green-500 mx-auto mb-8" />
            <h1 class="text-2xl font-bold text-gray-800 mb-4">
              Visit Confirmed
            </h1>
            <div
              v-if="visitData.visit_booking_details"
              class="text-left bg-gray-50 p-4 rounded-lg mb-8 w-full"
            >
              <p class="text-gray-800">
                <strong class="font-semibold">Doctor:</strong>
                {{ visitData.visit_booking_details.docName }}
              </p>
              <p class="text-gray-800 mt-2">
                <strong class="font-semibold">Date & Time:</strong>
                {{ visitData.visit_booking_details.scheduledDate }}
              </p>
              <p class="text-gray-800 mt-2">
                <strong class="font-semibold">Meeting Link:</strong>
                <a
                  :href="visitData.visit_booking_details.location"
                  target="_blank"
                  rel="noopener noreferrer"
                  class="text-blue-600 hover:underline"
                >
                  Join Visit
                </a>
              </p>
            </div>
            <p
              v-else
              class="text-gray-600 mb-8"
            >
              Your video visit has been successfully booked. You will receive further details via
              email.
            </p>
            <TwButton
              class="w-full"
              @click="handleContinue"
            >
              Continue
              <IconArrowRight class="w-5 h-5 ms-2" />
            </TwButton>
          </div>

          <!-- Waiting for link or processing -->
          <div
            v-else-if="visitData.visit_scheduled_link"
            class="flex flex-col items-center"
          >
            <div class="w-28 h-28 bg-blue-100 rounded-full flex items-center justify-center mb-8">
              <IconVideo
                class="w-20 h-20 text-blue-700"
                stroke-width="2"
              />
            </div>
            <div v-if="!isProcessing">
              <h1 class="text-2xl font-bold text-gray-800 mb-4">
                Schedule Your Video Visit
              </h1>
              <p class="text-gray-600 mb-8">
                To comply with your state's laws, a video consultation is required. Please click the
                button below to schedule your appointment.
              </p>
              <TwButton
                class="w-full"
                @click="handleScheduleClick"
              >
                Schedule Video Visit
                <IconArrowRight class="w-5 h-5 ms-2" />
              </TwButton>

              <p class="text-base font-semibold text-gray-800 mt-6">
                Time remaining: {{ timerDisplay }}
              </p>
            </div>
            <div
              v-else
              class="flex flex-col items-center"
            >
              <h1 class="text-2xl font-bold text-gray-800 mb-4">
                Processing Your Request
              </h1>
              <p class="text-gray-600">
                Please do not refresh this page. We are confirming your appointment and will update
                this screen automatically.
              </p>
              <p class="text-lg font-semibold text-gray-800 mt-6">
                Time remaining: {{ timerDisplay }}
              </p>
              <div class="mt-8 flex justify-center items-center space-x-2">
                <div class="w-4 h-4 bg-blue-500 rounded-full animate-pulse"></div>
                <div class="w-4 h-4 bg-blue-500 rounded-full animate-pulse delay-200"></div>
                <div class="w-4 h-4 bg-blue-500 rounded-full animate-pulse delay-400"></div>
              </div>
            </div>
          </div>

          <!-- Waiting for schedule link -->
          <div
            v-else
            class="flex flex-col items-center"
          >
            <div class="w-28 h-28 bg-gray-300 rounded-full animate-pulse mb-8"></div>
            <div class="mb-6 text-center space-y-4">
              <div class="h-8 bg-gray-300 rounded w-64 animate-pulse mx-auto"></div>
              <div class="h-4 bg-gray-300 rounded w-80 animate-pulse mx-auto"></div>
            </div>
            <p class="text-gray-500">
              We are preparing your visit details...
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
