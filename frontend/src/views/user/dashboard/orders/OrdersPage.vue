<script setup>
import { useAuthStore } from '@/store/auth'
import { storeToRefs } from 'pinia'
import { IconPackageOff, IconPhoto } from '@tabler/icons-vue'
import ApiService from '@/services/ApiService'
import { onMounted } from 'vue'
import { processErrors } from '@/utils/errorHandler'
import { toast } from 'vue-sonner'
import { getOrderStatus } from '@/utils/user'
import { formatCurrency } from '@/utils/helpers'
import { RouterLink } from 'vue-router'
import SyncVisitBanner from '@/views/user/components/SyncVisitBanner.vue'

const ModalShippingActivity = defineAsyncComponent(() => import('@/views/user/components/ModalShippingActivity.vue'))

// const OrderRetryPaymentModal = defineAsyncComponent(() => import('@/views/user/components/OrderRetryPaymentModal.vue'))

const authStore = useAuthStore()
const { userData } = storeToRefs(authStore)

const skeletonLoading = ref(false)
const currentPage = ref(1)
const orders = ref([])
const totalPage = ref(0)
const totalRecords = ref(0)
const shippingActivityModalRef = ref(null)

// const updatePaymentModalRef = ref(null)

onMounted(async () => {
  await fetchOrders()
})

async function fetchOrders() {
  try {
    skeletonLoading.value = true

    const { data } = await ApiService.post('/order-list', { page: currentPage.value })

    if (data.status === 200) {
      if (currentPage.value === 1) {
        orders.value = data.orders.records
      } else {
        orders.value = [...orders.value, ...data.orders.records]
      }
      totalPage.value = data.orders.totalPage
      totalRecords.value = data.orders.totalRecords
    } else {
      toast.error(data.message)
    }
  } catch (error) {
    console.error(error)

    const errorMessage = processErrors(error)[0]

    toast.error(errorMessage)
  } finally {
    skeletonLoading.value = false
  }
}

async function handlePagination() {
  if (!skeletonLoading.value && currentPage.value < totalPage.value) {
    currentPage.value++
    await fetchOrders()
  }
}
</script>

<template>
  <div class="flex flex-col items-center">
    <div class="flex flex-col self-center px-4 w-full max-w-[840px] max-md:max-w-full">
      <h1 class="text-3xl md:text-5xl text-black font-semibold py-6 mb-4">
        Welcome, {{ userData?.first_name }}!
      </h1>

      <div
        class="bg-white p-6 rounded-[20px] max-md:px-3 max-md:max-w-full"
        aria-labelledby="orders-title"
      >
        <h2 class="text-2xl font-semibold tracking-tight leading-4 text-zinc-800">
          Orders
        </h2>

        <div
          v-if="orders.length === 0 && !skeletonLoading"
          class="mt-6 space-y-5"
        >
          <div class="grid place-items-center min-h-28">
            <div class="flex flex-col justify-center items-center">
              <IconPackageOff
                class="h-14 text-zinc-600"
                stroke-width="1.5"
              />
              <span class="text-zinc-600 text-sm font-medium mt-2">
                You do not have any orders.
              </span>
            </div>
          </div>
        </div>

        <div
          v-if="orders.length > 0"
          class="mt-6 space-y-5"
        >
          <div
            v-for="order in orders"
            :key="order.id"
            class="rounded-[20px] border !border-gray-300 p-3 md:px-5"
          >
            <!-- OTC Order Design -->
            <template v-if="order.order_type === 'otc'">
              <div class="flex w-full flex-col md:py-2">
                <div class="w-full">
                  <div class="p-2">
                    <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                      <h1 class="text-xl font-semibold text-gray-800">
                        Order #{{ order.order_reference_no }}
                      </h1>
                      <p class="text-sm text-gray-600">
                        {{ order.order_date }}
                      </p>
                    </div>

                    <div class="mt-4 grid gap-4">
                      <div class="flex items-center gap-4 p-2 bg-gray-50 rounded-lg">
                        <div class="h-16 w-16 flex-shrink-0 grid place-items-center bg-white rounded-md">
                          <img
                            v-if="order.order_items[0].product_image"
                            :src="order.order_items[0].product_image"
                            :alt="order.order_items[0].product_name"
                            class="h-12 w-12 object-contain"
                          />
                          <IconPhoto
                            v-else
                            class="h-12 w-12 text-gray-400"
                            stroke-width="1.5"
                          />
                        </div>
                        <div class="flex-grow">
                          <h3 class="font-medium text-gray-800">
                            {{ order.order_items[0].product_name }}
                          </h3>
                          <div class="flex flex-wrap gap-x-4 text-xs text-gray-600">
                            <span>Qty: {{ order.order_items[0].qty }}</span>
                          </div>
                        </div>
                      </div>
                      <RouterLink
                        v-if="order.order_items.length > 1"
                        :to="{ name: 'user-otc-order-details', params: { orderId: order.id } }"
                        class="bg-gray-100 text-sm text-gray-600 font-medium rounded-lg p-1.5 text-center hover:shadow-sm hover:bg-gray-50 transition-colors"
                      >
                        + {{ order.order_items.length - 1 }} more {{ order.order_items.length - 1 === 1 ? 'item' : 'items' }}
                      </RouterLink>
                    </div>

                    <div class="shrink-0 border h-px w-full my-4"></div>

                    <div class="space-y-1">
                      <div class="flex items-start justify-between">
                        <span class="text-gray-700">Payment ID</span>
                        <span class="font-medium">{{ order.payment_transaction_id || 'N/A' }}</span>
                      </div>
                      <div class="flex items-start justify-between">
                        <span class="text-gray-700">Total</span>
                        <span class="font-medium">{{ formatCurrency(order.grand_total) }}</span>
                      </div>
                    </div>

                    <div class="shrink-0 border h-px w-full my-4"></div>

                    <div class="flex flex-col sm:flex-row gap-4">
                      <RouterLink
                        :to="{ name: 'user-otc-order-details', params: { orderId: order.id } }"
                        class="btn-secondary w-full"
                      >
                        View Details
                      </RouterLink>
                    </div>
                  </div>
                </div>
              </div>
            </template>

            <!-- RX Order Design -->
            <template v-else>
              <SyncVisitBanner
                :visit-data="{
                  appointmentScheduledLink: order.appointmentScheduledLink,
                  appointmentUrl: order.appointmentUrl,
                  appointmentDocName: order.appointmentDocName,
                  appointmentDateTime: order.appointmentDateTime,
                  isAppointmentMissed: order.isAppointmentMissed,
                }"
                @scheduled="fetchOrders"
              />
              <div class="flex w-full flex-col md:flex-row md:py-2">
                <div class="h-[200px] w-full md:max-w-[250px] grid place-items-center bg-gray-100 !rounded-[10px]">
                  <img
                    :src="order.product_img"
                    :alt="order.product_name"
                    class="h-32 w-32 rounded-lg object-contain"
                  />
                </div>
                <div class="w-full">
                  <div class="p-2 md:px-8">
                    <h1 class="inline-flex items-center text-xl font-semibold text-gray-800">
                      {{ order.product_name }}
                    </h1>
                    <p
                      v-if="order.category_name === 'WL'"
                      class="mt-1 text-sm text-gray-600"
                    >
                      {{ order.strength }} {{ order.strength_unit }}/weekly
                    </p>
                    <p
                      v-else
                      class="mt-1 text-sm text-gray-600"
                    >
                      <span>
                        {{ order.strength }} {{ order.strength_unit }} x {{ order.qty * order.subscription_interval }} units
                      </span>
                      <span
                        v-if="order.is_medicine_pickup_at_local_pharmacy !== 1"
                        class="block sm:inline"
                      >({{ order.subscription_interval * 30 }}-day supply)</span>
                    </p>
                    <div class="mt-4">
                      <div :class="`flex gap-1 w-fit px-1 pe-3 py-0.5 font-bold whitespace-nowrap rounded-2xl bg-opacity-20 ${getOrderStatus(order.status).bgColor} ${getOrderStatus(order.status).textColor}`">
                        <component
                          :is="getOrderStatus(order.status).icon"
                          :class="`shrink-0 self-center w-[20px] h-[20px] text-white rounded-full p-px ${getOrderStatus(order.status).bgColor}`"
                          stroke-width="3"
                        />
                        <span>{{ order.order_status }}</span>
                      </div>
                    </div>
                    <div class="shrink-0 border h-px w-full my-4"></div>
                    <div class="space-y-1">
                      <div class="flex items-start justify-between">
                        <span class="text-gray-700">Order #</span>
                        <span class="font-medium">{{ order.order_no }}</span>
                      </div>
                      <div class="flex items-start justify-between">
                        <span class="text-gray-700">Order Created</span>
                        <span class="font-medium">{{ order.order_date }}</span>
                      </div>
                      <div class="flex items-start justify-between">
                        <span class="text-gray-700">Total</span>
                        <span class="font-medium">{{ formatCurrency(order.total_refill_amount) }}</span>
                      </div>
                    </div>
                    <div class="shrink-0 border h-px w-full my-4"></div>
                    <div class="flex flex-col sm:flex-row gap-4">
                      <RouterLink
                        :to="{ name: 'user-order-details', params: { orderId: order.id } }"
                        class="btn-secondary w-full"
                      >
                        View Details
                      </RouterLink>
                      <!--
                        <TwButton
                        v-if="order.is_medicine_pickup_at_local_pharmacy === 1 && order.status === 4"
                        class="w-full"
                        @click="updatePaymentModalRef.openModal({
                        orderId: order.order_id,
                        currentPaymentMethodId: order.current_payment_method_id,
                        })"
                        >
                        Retry Payment
                        </TwButton>
                      -->
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </div>
        </div>

        <div
          v-if="skeletonLoading"
          class="mt-6 space-y-5"
        >
          <div
            v-for="index in 2"
            :key="index"
            class="rounded-[20px] border !border-gray-300 p-3 md:px-5 animate-pulse"
          >
            <div class="flex w-full flex-col md:flex-row md:py-2">
              <div class="h-[200px] w-full md:max-w-[250px] grid place-items-center bg-gray-300 !rounded-[10px]">
              </div>
              <div class="w-full">
                <div class="p-2 md:px-8">
                  <div class="h-7 bg-gray-300 rounded w-3/4 mb-2"></div>
                  <div class="h-4 bg-gray-300 rounded w-1/2 mt-1"></div>
                  <div class="h-4 bg-gray-300 rounded w-1/3 mt-1"></div>

                  <div class="mt-4">
                    <div class="h-6 bg-gray-300 rounded w-1/4"></div>
                  </div>

                  <div class="border-t border-gray-200 my-4"></div>

                  <div class="space-y-2">
                    <div class="flex items-start justify-between">
                      <div class="h-4 bg-gray-300 rounded w-1/4"></div>
                      <div class="h-4 bg-gray-300 rounded w-1/4"></div>
                    </div>
                    <div class="flex items-start justify-between">
                      <div class="h-4 bg-gray-300 rounded w-1/3"></div>
                      <div class="h-4 bg-gray-300 rounded w-1/3"></div>
                    </div>
                    <div class="flex items-start justify-between">
                      <div class="h-4 bg-gray-300 rounded w-1/4"></div>
                      <div class="h-4 bg-gray-300 rounded w-1/6"></div>
                    </div>
                  </div>

                  <div class="border-t border-gray-200 my-4"></div>

                  <div class="h-10 bg-gray-300 rounded-full w-full"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div
          v-if="currentPage < totalPage"
          class="text-center mt-4"
        >
          <TwButton
            class="text-xs !py-2"
            @click="handlePagination"
          >
            See More
          </TwButton>
        </div>
      </div>
    </div>

    <ModalShippingActivity ref="shippingActivityModalRef" />

    <!--
      <OrderRetryPaymentModal
      ref="updatePaymentModalRef"
      @updated="fetchOrders"
      />
    -->
  </div>
</template>
