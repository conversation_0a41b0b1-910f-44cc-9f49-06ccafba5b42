<script setup>
import { useAuthStore } from '@/store/auth'
import { storeToRefs } from 'pinia'
import { IconArrowLeft, IconExternalLink, IconPhoto, IconCheck, IconClock, IconX, IconCircleDot, IconRosetteDiscount, IconTruckDelivery } from '@tabler/icons-vue'
import ApiService from '@/services/ApiService'
import { computed, onMounted } from 'vue'
import { processErrors } from '@/utils/errorHandler'
import { toast } from 'vue-sonner'
import { useRoute } from 'vue-router'
import router from '@/router'
import { formatCurrency } from '@/utils/helpers'

const route = useRoute()
const authStore = useAuthStore()
const { userData } = storeToRefs(authStore)
const orderId = computed(() => route.params.orderId)
const skeletonLoading = ref(true)
const order = ref(null)

function getProductStatus(status) {
  let bgColor, textColor, icon, label

  switch (status) {
  case 0: // Processing
    bgColor = 'bg-amber-100'
    textColor = 'text-amber-700'
    icon = IconClock
    label = 'Processing'
    break

  case 1: // Delivered
    bgColor = 'bg-green-100'
    textColor = 'text-green-700'
    icon = IconCheck
    label = 'Delivered'
    break

  case 2: // Canceled
    bgColor = 'bg-red-100'
    textColor = 'text-red-700'
    icon = IconX
    label = 'Canceled'
    break

  case 3: // In Transit
    bgColor = 'bg-blue-100'
    textColor = 'text-blue-700'
    icon = IconTruckDelivery
    label = 'Shipped'
    break

  default:
    bgColor = 'bg-amber-100'
    textColor = 'text-amber-700'
    icon = IconClock
    label = 'Processing'
  }

  return { bgColor, textColor, icon, label }
}

onMounted(async () => {
  await fetchOrderDetails()
})

async function fetchOrderDetails() {
  try {
    skeletonLoading.value = true

    const { data } = await ApiService.get(`otc/order-detail/${orderId.value}`)

    if (data.status === 200) {
      skeletonLoading.value = false
      order.value = data.data
    } else if (data.status === 404) {
      router.push({ name: '404' })
    } else {
      skeletonLoading.value = false
      toast.error(data.message || 'Failed to fetch order details')
    }
  } catch (error) {
    skeletonLoading.value = false

    const errorMessage = processErrors(error)[0]

    toast.error(errorMessage)
  }
}
</script>

<template>
  <div class="flex flex-col items-center">
    <div class="flex flex-col self-center px-4 w-full max-w-[840px] max-md:max-w-full">
      <div class="flex flex-col sm:flex-row gap-4 justify-between mb-2 sm:mb-4 py-6">
        <h1 class="text-3xl md:text-5xl text-black font-semibold">
          Welcome, {{ userData?.first_name }}!
        </h1>
        <div class="flex items-center gap-2">
          <RouterLink
            :to="{name: 'user-orders'}"
            class="w-full inline-flex items-center justify-center text-gray-900 text-sm font-medium border-2 border-solid !border-gray-900 rounded-full px-4 py-1.5 hover:bg-gray-300"
          >
            <IconArrowLeft
              class="h-4 w-4 me-1"
              stroke-width="2"
            /> Back
          </RouterLink>
        </div>
      </div>

      <div
        class="bg-white p-4 sm:p-6 rounded-[20px]"
        aria-labelledby="orders-title"
      >
        <h2 class="text-xl sm:text-2xl font-semibold tracking-tight text-zinc-800">
          OTC Order > Details
        </h2>

        <div
          v-if="skeletonLoading"
          class="mt-4 sm:mt-6"
        >
          <div class="rounded-[20px] border !border-gray-300 bg-card text-card-foreground animate-pulse">
            <div class="space-y-1.5 p-4 sm:p-6 flex flex-col sm:flex-row items-start bg-gray-100 rounded-t-[20px]">
              <div class="grid gap-0.5 w-full">
                <div class="h-5 sm:h-6 bg-gray-300 rounded w-1/2 sm:w-1/3"></div>
                <div class="h-3 sm:h-4 bg-gray-300 rounded w-1/3 sm:w-1/4 mt-2"></div>
              </div>
              <div class="mt-3 sm:mt-auto sm:ms-auto">
                <div class="h-5 sm:h-6 bg-gray-300 rounded w-20 sm:w-24"></div>
              </div>
            </div>
            <div class="p-4 sm:p-6">
              <div class="grid gap-3 sm:gap-4">
                <div class="h-5 sm:h-6 bg-gray-300 rounded w-full sm:w-3/4"></div>
                <div class="h-3 sm:h-4 bg-gray-300 rounded w-2/3 sm:w-1/2"></div>
              </div>
              <div class="border-t border-gray-200 my-4 sm:my-6"></div>
              <div class="grid gap-2">
                <div class="flex items-center justify-between">
                  <div class="h-3 sm:h-4 bg-gray-300 rounded w-1/3 sm:w-1/4"></div>
                  <div class="h-3 sm:h-4 bg-gray-300 rounded w-1/5 sm:w-1/6"></div>
                </div>
                <div class="flex items-center justify-between">
                  <div class="h-3 sm:h-4 bg-gray-300 rounded w-2/5 sm:w-1/3"></div>
                  <div class="h-3 sm:h-4 bg-gray-300 rounded w-1/5 sm:w-1/6"></div>
                </div>
                <div class="flex items-center justify-between">
                  <div class="h-3 sm:h-4 bg-gray-300 rounded w-3/5 sm:w-1/2"></div>
                  <div class="h-3 sm:h-4 bg-gray-300 rounded w-1/5 sm:w-1/6"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div
          v-else-if="order"
          class="mt-4 sm:mt-6 space-y-4 sm:space-y-5"
        >
          <div class="rounded-[20px] border !border-gray-300 bg-card text-card-foreground">
            <div class="p-4 sm:p-6 flex flex-col sm:flex-row items-start bg-gray-100 rounded-t-[20px]">
              <div class="grid gap-0.5 mb-2 sm:mb-0">
                <h3 class="font-semibold tracking-tight group flex items-center gap-2 text-base sm:text-lg text-gray-700 break-words">
                  Order # {{ order.order_reference_no }}
                </h3>
              </div>
              <div class="sm:mt-auto sm:ms-auto flex items-center">
                <p class="text-xs sm:text-sm text-gray-700">
                  Created on {{ order.order_date }}
                </p>
              </div>
            </div>
            <div class="p-4 sm:p-6 text-sm">
              <div class="grid">
                <!-- Order Items -->
                <div class="mt-2 sm:mt-4 grid gap-3 sm:gap-4">
                  <div
                    v-for="(item, index) in order.order_items"
                    :key="index"
                    class="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4 p-3 sm:p-4 bg-gray-50 rounded-lg"
                  >
                    <div class="h-14 sm:h-16 w-14 sm:w-16 flex-shrink-0 grid place-items-center bg-white rounded-md">
                      <img
                        v-if="item.product_image"
                        :src="item.product_image"
                        :alt="item.product_name"
                        class="h-10 sm:h-12 w-10 sm:w-12 object-contain"
                      />
                      <IconPhoto
                        v-else
                        class="h-10 sm:h-12 w-10 sm:w-12 text-gray-400"
                        stroke-width="1.5"
                      />
                    </div>
                    <div class="flex-grow min-w-0">
                      <div class="flex justify-between">
                        <h3 class="font-medium text-gray-800 break-words">
                          {{ item.product_name }}
                        </h3>
                      </div>
                      <div class="flex flex-wrap gap-2 text-xs sm:text-sm text-gray-600 mt-1">
                        <span>{{ item.purchased_qty }} x {{ formatCurrency(item.product_amount) }}</span>
                      </div>
                      <div
                        class="flex gap-1 w-fit px-2 py-1 text-xs sm:text-sm font-medium rounded-full mt-2"
                        :class="[getProductStatus(item.status).bgColor, getProductStatus(item.status).textColor]"
                      >
                        <component
                          :is="getProductStatus(item.status).icon"
                          class="size-[16px] sm:size-[18px] me-1"
                          stroke-width="2"
                        />
                        <span>{{ getProductStatus(item.status).label }}</span>
                      </div>
                    </div>

                    <div class="flex flex-wrap items-center mt-2 sm:mt-0 sm:ms-3">
                      <div
                        v-if="item.promo_code_name"
                        class="bg-green-100 text-xs px-1.5 pt-[5px] pb-px rounded-full text-green-700 me-2.5 inline-flex items-center font-medium mb-1 sm:mb-0"
                      >
                        <IconRosetteDiscount
                          class="size-4 me-1 -mt-[3px]"
                          stroke-width="2"
                        />
                        <span>{{ item.promo_code_name }}</span>
                        <span v-if="item.promo_code_type === 'percentage'">
                          &nbsp;({{ item.promo_code_value }}%)
                        </span>
                      </div>
                      <div class="flex items-center w-full justify-end sm:w-fit">
                        <span
                          v-if="item.total_product_amount !== item.grand_total_amount"
                          class="line-through text-gray-500 me-2"
                        >{{ formatCurrency(item.total_product_amount) }}</span>
                        <span class="text-gray-700 font-medium">
                          {{ formatCurrency(item.grand_total_amount) }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="shrink-0 border h-px w-full mt-4 sm:mt-6 mb-3 sm:mb-4"></div>

                <!-- Order Summary -->
                <div class="grid gap-2">
                  <!-- Order Amount -->
                  <!--
                    <div class="flex items-center justify-between">
                    <span class="text-gray-700">Order Amount</span>
                    <span>{{ formatCurrency(order.order_amount) }}</span>
                    </div>
                  -->

                  <!-- Promo Code -->
                  <!--
                    <div
                    v-if="order.promo_code_name"
                    class="flex items-center justify-between"
                    >
                    <span class="text-gray-700 grid">
                    <span>Promotion applied</span>
                    <span class="font-semibold">
                    {{ order.promo_code_name }}
                    <span v-if="order.promo_code_type === 'percentage'">({{ order.promo_code_value }}%)</span>
                    </span>
                    </span>
                    <span class="text-green-600">-{{ formatCurrency(order.promo_code_discount_amount) }}</span>
                    </div>
                  -->

                  <!-- Subtotal -->
                  <div class="flex items-center justify-between">
                    <span class="text-gray-700 text-xs sm:text-sm">Subtotal</span>
                    <span class="text-xs sm:text-sm">{{ formatCurrency(order.sub_total) }}</span>
                  </div>

                  <!-- Shipping Cost -->
                  <div class="flex items-center justify-between">
                    <span class="text-gray-700 text-xs sm:text-sm">Shipping & handling fee</span>
                    <span
                      v-if="order.shipping_cost > 0"
                      class="text-xs sm:text-sm"
                    >
                      {{ formatCurrency(order.shipping_cost) }}
                    </span>
                    <span
                      v-else
                      class="text-green-600 text-xs sm:text-sm"
                    >FREE</span>
                  </div>

                  <!-- Total -->
                  <div class="flex items-center justify-between font-semibold text-sm sm:text-base mt-1">
                    <span class="text-gray-700">Total</span>
                    <span>{{ formatCurrency(order.grand_total) }}</span>
                  </div>

                  <!-- Invoice Link -->
                  <div
                    v-if="order.invoice_url"
                    class="text-end mt-2"
                  >
                    <a
                      :href="order.invoice_url"
                      class="text-xs sm:text-sm font-medium text-black underline inline-flex items-center"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      View Invoice
                      <IconExternalLink
                        class="h-3 w-3 ms-1"
                        stroke-width="2"
                      />
                    </a>
                  </div>
                </div>

                <div class="shrink-0 border h-px w-full my-3 sm:my-4"></div>

                <!-- Shipping Address -->
                <div class="grid sm:grid-cols-2 gap-4 sm:gap-6">
                  <div class="col-span-1">
                    <div>
                      <div class="font-semibold text-sm sm:text-base">
                        Shipping Address
                      </div>
                      <address class="grid not-italic text-gray-700 mt-1.5 text-xs sm:text-sm break-words">
                        <span v-if="order.shipping_details.address_line_2">
                          {{ order.shipping_details.address_line_1 }}, {{ order.shipping_details.address_line_2 }}
                        </span>
                        <span v-else>{{ order.shipping_details.address_line_1 }}</span>
                        <span>{{ order.shipping_details.city }}</span>
                        <span>{{ order.shipping_details.state }}-{{ order.shipping_details.zipcode }}</span>
                        <span>{{ order.shipping_details.country }}</span>
                      </address>
                    </div>
                  </div>

                  <!-- Payment Info -->
                  <div class="col-span-1">
                    <div class="font-semibold text-sm sm:text-base">
                      Payment Information
                    </div>
                    <div class="grid not-italic text-gray-600 mt-1.5 text-xs sm:text-sm">
                      <span class="break-words">Transaction ID: <span class="text-gray-700">{{ order.payment_transaction_id || '-' }}</span></span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
