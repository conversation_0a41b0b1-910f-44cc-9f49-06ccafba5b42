<script setup>
import { useAuthStore } from '@/store/auth'
import { storeToRefs } from 'pinia'
import { IconCheck, IconClock, IconX, IconCircleDot, IconArrowLeft, IconExternalLink } from '@tabler/icons-vue'
import ApiService from '@/services/ApiService'
import { computed, onMounted } from 'vue'
import { processErrors } from '@/utils/errorHandler'
import { toast } from 'vue-sonner'
import { isEmpty } from '@/@core/utils'
import { useRoute } from 'vue-router'
import router from '@/router'
import { formatCurrency } from '@/utils/helpers'
import { useUpdatePaymentMethod } from '@/store/updatePaymentMethod'
import { getOrderStatus } from '@/utils/user'
import SyncVisitBanner from '@/views/user/components/SyncVisitBanner.vue'

const ModalShippingActivity = defineAsyncComponent(() => import('@/views/user/components/ModalShippingActivity.vue'))
const ModalOrderCancel = defineAsyncComponent(() => import('@/views/user/components/ModalOrderCancel.vue'))
const ModalOrderCancelReason = defineAsyncComponent(() => import('@/views/user/components/ModalOrderCancelReason.vue'))
const SubscriptionUpdatePaymentModal = defineAsyncComponent(() => import('@/views/user/components/SubscriptionUpdatePaymentModal.vue'))

const route = useRoute()
const authStore = useAuthStore()
const { userData } = storeToRefs(authStore)
const { setSubscriptionRefillId } = useUpdatePaymentMethod()
const orderId = computed(() => route.params.orderId)
const skeletonLoading = ref(true)
const order = ref(null)
const shippingActivityModalRef = ref(null)
const modalOrderCancelRef = ref(null)
const modalOrderCancelReasonRef = ref(null)
const updatePaymentModalRef = ref(null)

onMounted(async () => {
  await fetchOrderDetails()
})

async function fetchOrderDetails() {
  try {
    skeletonLoading.value = true

    const { data } = await ApiService.get(`/order-detail/${orderId.value}`)

    if (data.status === 200) {
      skeletonLoading.value = false
      order.value = data.orderDetail
    } else if (data.status === 404) {
      router.push({ name: '404' })
    } else {
      skeletonLoading.value = false
      toast.error(data.message)
    }
  } catch (error) {
    skeletonLoading.value = false

    const errorMessage = processErrors(error)[0]

    toast.error(errorMessage)
  }
}

function getRefundStatus(status) {
  let bgColor, textColor, icon, label

  switch (status) {
  case 0: // Pending
  case 3: // Pending
    bgColor = 'bg-amber-500'
    textColor = 'text-amber-500'
    icon = IconClock
    label = 'Pending'
    break

  case 1: // Approved
    bgColor = 'bg-green-600'
    textColor = 'text-green-600'
    icon = IconCheck
    label = 'Approved'
    break

  case 2: // Decline
    bgColor = 'bg-red-600'
    textColor = 'text-red-600'
    icon = IconX
    label = 'Decline'
    break

  default:
    bgColor = 'bg-gray-600'
    textColor = 'text-gray-600'
    icon = IconCircleDot
    label = 'Unknown'
  }

  return { bgColor, textColor, icon, label }
}

function handleOrderRetry(order) {
  if (order.is_refill_order) {
    setSubscriptionRefillId(orderId.value)
    updatePaymentModalRef.value.openModal(order.value.user_payment_card_id)
  } else {
    if (order.category_name === 'ED') {
      sessionStorage.setItem('retryOrderId', order.order_id)
      router.push({ name: 'ed-visit-checkout' })
    } else if (order.category_name === 'HL') {
      sessionStorage.setItem('hlRetryOrderId', order.order_id)
      router.push({ name: 'hl-visit-checkout' })
    } else if (order.category_name === 'WL') {
      sessionStorage.setItem('wlRetryOrderId', order.order_id)
      router.push({
        name: order.is_followup_order
          ? 'wl-followup-visit-checkout'
          : 'wl-visit-checkout',
      })
    }
  }
}
</script>

<template>
  <div class="flex flex-col items-center">
    <div class="flex flex-col self-center px-4 w-full max-w-[840px] max-md:max-w-full">
      <div class="flex flex-col sm:flex-row gap-4 justify-between mb-2 sm:mb-4 py-6">
        <h1 class="text-3xl md:text-5xl text-black font-semibold">
          Welcome, {{ userData?.first_name }}!
        </h1>
        <div class="flex items-center gap-2">
          <RouterLink
            :to="{name: 'user-orders'}"
            class="w-full inline-flex items-center justify-center text-gray-900 text-sm font-medium border-2 border-solid !border-gray-900 rounded-full px-4 py-1.5 hover:bg-gray-300"
          >
            <IconArrowLeft
              class="h-4 w-4 me-1"
              stroke-width="2"
            /> Back
          </RouterLink>
        </div>
      </div>

      <div
        class="bg-white p-6 rounded-[20px] max-md:px-3 max-md:max-w-full"
        aria-labelledby="orders-title"
      >
        <h2 class="text-2xl font-semibold tracking-tight leading-4 text-zinc-800">
          Order > Details
        </h2>

        <div
          v-if="skeletonLoading"
          class="mt-6"
        >
          <div class="rounded-[20px] border !border-gray-300 bg-card text-card-foreground animate-pulse">
            <div class="space-y-1.5 p-6 flex flex-col sm:flex-row items-start bg-gray-100 rounded-t-[20px]">
              <div class="grid gap-0.5 w-full">
                <div class="h-6 bg-gray-300 rounded w-1/3"></div>
                <div class="h-4 bg-gray-300 rounded w-1/4 mt-2"></div>
              </div>
              <div class="mt-3 sm:mt-auto sm:ms-auto">
                <div class="h-6 bg-gray-300 rounded w-24"></div>
              </div>
            </div>
            <div class="p-6">
              <div class="grid md:grid-cols-2 gap-6">
                <div class="flex items-center gap-3">
                  <div class="w-20 h-20 bg-gray-300 rounded-md"></div>
                  <div class="w-full">
                    <div class="h-6 bg-gray-300 rounded w-3/4"></div>
                    <div class="h-4 bg-gray-300 rounded w-1/2 mt-2"></div>
                    <div class="h-4 bg-gray-300 rounded w-1/3 mt-1"></div>
                  </div>
                </div>
                <div class="grid gap-2">
                  <div class="flex items-start justify-between">
                    <div class="h-4 bg-gray-300 rounded w-1/4"></div>
                    <div class="h-4 bg-gray-300 rounded w-1/4"></div>
                  </div>
                  <div class="flex items-start justify-between">
                    <div class="h-4 bg-gray-300 rounded w-1/3"></div>
                    <div class="h-4 bg-gray-300 rounded w-1/3"></div>
                  </div>
                  <div class="flex items-start justify-between">
                    <div class="h-4 bg-gray-300 rounded w-1/4"></div>
                    <div class="h-4 bg-gray-300 rounded w-1/4"></div>
                  </div>
                </div>
              </div>
              <div class="border-t border-gray-200 my-6"></div>
              <div class="grid gap-2">
                <div class="flex items-center justify-between">
                  <div class="h-4 bg-gray-300 rounded w-1/4"></div>
                  <div class="h-4 bg-gray-300 rounded w-1/6"></div>
                </div>
                <div class="flex items-center justify-between">
                  <div class="h-4 bg-gray-300 rounded w-1/3"></div>
                  <div class="h-4 bg-gray-300 rounded w-1/6"></div>
                </div>
                <div class="flex items-center justify-between">
                  <div class="h-4 bg-gray-300 rounded w-1/2"></div>
                  <div class="h-4 bg-gray-300 rounded w-1/6"></div>
                </div>
              </div>
              <div class="border-t border-gray-200 my-6"></div>
              <div>
                <div class="h-5 bg-gray-300 rounded w-1/4 mb-3"></div>
                <div class="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
                <div class="h-4 bg-gray-300 rounded w-1/2 mb-2"></div>
                <div class="h-4 bg-gray-300 rounded w-2/3"></div>
              </div>
            </div>
          </div>
        </div>

        <div
          v-else-if="order"
          class="mt-6 space-y-5"
        >
          <!-- status banner -->
          <div
            v-if="Boolean(order.is_payment_retry_option)"
            class="w-full bg-red-100 rounded-lg px-4 py-2 text-sm font-medium text-red-700 mb-4 flex justify-between align-center gap-4"
          >
            <span>Your order is on hold due to a payment failure.</span>
            <TwButton
              class="!py-1.5"
              @click="handleOrderRetry(order)"
            >
              Retry
            </TwButton>
          </div>

          <SyncVisitBanner
            :visit-data="{
              appointmentScheduledLink: order.appointmentScheduledLink,
              appointmentUrl: order.appointmentUrl,
              appointmentDocName: order.appointmentDocName,
              appointmentDateTime: order.appointmentDateTime,
              isAppointmentMissed: order.isAppointmentMissed,
            }"
            @scheduled="fetchOrderDetails"
          />

          <div class="rounded-[20px] border !border-gray-300 bg-card text-card-foreground">
            <div class="space-y-1.5 p-6 flex flex-col sm:flex-row items-start bg-gray-100 rounded-t-[20px]">
              <div class="grid gap-0.5">
                <h3 class="whitespace-nowrap font-semibold tracking-tight group flex items-center gap-2 text-lg text-gray-700">
                  Order # {{ order.order_no }}
                </h3>
                <p class="text-sm text-gray-700">
                  Created on {{ order.order_date }}
                </p>
              </div>
              <div class="mt-3 sm:mt-auto sm:ms-auto flex items-center gap-1">
                <div :class="`flex gap-1 w-fit px-1 pe-3 py-0.5 font-bold whitespace-nowrap rounded-2xl bg-opacity-20 ${getOrderStatus(order.status).bgColor} ${getOrderStatus(order.status).textColor}`">
                  <component
                    :is="getOrderStatus(order.status).icon"
                    :class="`shrink-0 self-center w-[20px] h-[20px] text-white rounded-full p-px ${getOrderStatus(order.status).bgColor}`"
                    stroke-width="3"
                  />
                  <span>{{ order.order_status }}</span>
                </div>
              </div>
            </div>
            <div class="p-6 text-sm">
              <div class="grid">
                <div class="grid md:grid-cols-2 gap-6">
                  <div class="grid gap-3">
                    <div class="flex items-center gap-3">
                      <img
                        :src="order.product_img"
                        alt="Product Image"
                        width="80"
                        height="80"
                        class="rounded-md object-cover"
                        style="aspect-ratio: 80 / 80; object-fit: cover;"
                      />
                      <div>
                        <h3 class="text-gray-800 font-medium text-lg">
                          {{ order.product_name }}
                        </h3>
                        <p
                          v-if="order.category_name === 'WL'"
                          class="text-gray-700"
                        >
                          {{ order.strength }} {{ order.strength_unit }}/weekly
                        </p>
                        <p
                          v-else
                          class="text-gray-700"
                        >
                          <span class="block">
                            {{ order.strength }} {{ order.strength_unit }} x {{ order.qty * order.subscription_interval }} units
                          </span>
                          <span
                            v-if="order.is_medicine_pickup_at_local_pharmacy !== 1"
                            class="block"
                          >({{ order.subscription_interval * 30 }}-day supply)</span>
                        </p>
                      </div>
                    </div>
                  </div>
                  <div class="grid gap-1">
                    <div class="flex items-start justify-between">
                      <span class="text-gray-700">Order #</span>
                      <span>{{ order.order_no }}</span>
                    </div>
                    <div class="flex items-start justify-between">
                      <span class="text-gray-700">Transaction ID</span>
                      <span v-if="order.paypal_transaction_id">{{ order.paypal_transaction_id }}</span>
                      <span v-else> - </span>
                    </div>
                    <div
                      v-if="!isEmpty(order.refund_status)"
                      class="flex items-start justify-between"
                    >
                      <span class="text-gray-700">Refund Status</span>
                      <div :class="`flex gap-1 w-fit px-1 pe-3 py-0.5 font-bold whitespace-nowrap rounded-2xl bg-opacity-20 ${getRefundStatus(order.refund_status).bgColor} ${getRefundStatus(order.refund_status).textColor}`">
                        <component
                          :is="getRefundStatus(order.refund_status).icon"
                          :class="`shrink-0 self-center w-[20px] h-[20px] text-white rounded-full p-px ${getRefundStatus(order.refund_status).bgColor}`"
                          stroke-width="3"
                        />
                        <span>{{ getRefundStatus(order.refund_status).label }}</span>
                      </div>
                    </div>
                    <div
                      v-if="order.is_medicine_pickup_at_local_pharmacy !== 1"
                      class="flex items-start justify-between"
                    >
                      <span class="text-gray-700">Tracking #</span>
                      <span>{{ !isEmpty(order.tracking_no) ? order.tracking_no : '-' }}</span>
                    </div>
                    <div
                      v-if="order.is_medicine_pickup_at_local_pharmacy !== 1"
                      class="text-end"
                    >
                      <button
                        v-if="!isEmpty(order.is_tracking_details_available) && order.is_tracking_details_available === 1"
                        class="text-sm font-medium text-black underline"
                        @click="shippingActivityModalRef.open(order.id)"
                      >
                        <span>Track Shipment</span>
                      </button>
                    </div>
                  </div>
                </div>
                <div
                  v-if="!isEmpty(order.cancel_reason)"
                  class="bg-red-200 rounded-lg px-5 py-2 w-full mt-5"
                >
                  <div class="text-xs uppercase font-medium text-red-900">
                    Cancelation reason
                  </div>
                  <div class="text-base text-red-800">
                    {{ order.cancel_reason }}
                  </div>
                </div>
                <div
                  v-if="!isEmpty(order.refund_cancel_reason)"
                  class="bg-red-200 rounded-lg px-5 py-2 w-full mt-3"
                >
                  <div class="text-xs uppercase font-medium text-red-900">
                    Refund decline reason
                  </div>
                  <div class="text-base text-red-800">
                    {{ order.refund_cancel_reason }}
                  </div>
                </div>
                <div class="shrink-0 border h-px w-full mt-6 mb-4"></div>
                <div class="grid gap-2">
                  <!-- Medication -->
                  <div
                    v-if="order.orderSummary?.medicine_amount"
                    class="flex items-center justify-between"
                  >
                    <span class="text-gray-700">Medication</span>
                    <span>{{ formatCurrency(order.orderSummary?.medicine_amount) }}</span>
                  </div>

                  <!-- First Order discount -->
                  <div
                    v-if="order.orderSummary?.first_time_order_discount_amount"
                    class="flex items-center justify-between"
                  >
                    <span class="text-gray-700">First Order Discount</span>
                    <span>-{{ formatCurrency(order.orderSummary?.first_time_order_discount_amount) }}</span>
                  </div>

                  <!-- Promotion applied -->
                  <div
                    v-if="order.orderSummary?.promo_code_name"
                    class="flex items-center justify-between"
                  >
                    <span class="text-gray-700 grid">
                      <span>Promotion applied</span>
                      <span>
                        {{ order.orderSummary.promo_code_name }} <span v-if="order.orderSummary?.promo_code_type === 'percentage'">({{ order.orderSummary.promo_code_value }}%)</span>
                      </span>
                    </span>
                    <span>-{{ formatCurrency(order.orderSummary?.promo_code_discount_amount) }}</span>
                  </div>

                  <!-- Sub Total -->
                  <div
                    v-if="order.orderSummary?.sub_total && order.orderSummary?.sub_total !== order.orderSummary?.medicine_amount"
                    class="flex items-center justify-between"
                  >
                    <span class="text-gray-700">Subtotal</span>
                    <span>{{ formatCurrency(order.orderSummary?.sub_total) }}</span>
                  </div>

                  <!-- Shipping & handling fee -->
                  <div
                    v-if="order.is_medicine_pickup_at_local_pharmacy !== 1"
                    class="flex items-center justify-between"
                  >
                    <span class="text-gray-700">Shipping & handling fee</span>
                    <span v-if="!isEmpty(order.orderSummary?.shipping_cost) && order.orderSummary?.shipping_cost > 0">
                      {{ formatCurrency(order.orderSummary?.shipping_cost) }}
                    </span>
                    <span
                      v-else
                      class="text-green-400"
                    >FREE</span>
                  </div>

                  <!-- Physician & processing fee -->
                  <div
                    v-if="!isEmpty(order.orderSummary?.provider_consultation_fee)"
                    class="flex items-center justify-between"
                  >
                    <span class="text-gray-700">Physician & processing fee</span>
                    <span
                      v-if="order.orderSummary?.provider_consultation_fee === 0"
                      class="text-green-400"
                    >FREE</span>
                    <span v-else>{{ formatCurrency(order.orderSummary?.provider_consultation_fee) }}</span>
                  </div>

                  <!-- Convenience fee -->
                  <div
                    v-if="!isEmpty(order.orderSummary?.convenience_fee)"
                    class="flex items-center justify-between"
                  >
                    <span class="text-gray-700">Convenience fee</span>
                    <span v-if="order.orderSummary?.convenience_fee > 0">
                      {{ formatCurrency(order.orderSummary?.convenience_fee) }}
                    </span>
                    <span
                      v-else
                      class="text-green-400"
                    >FREE</span>
                  </div>

                  <!-- Total -->
                  <div
                    v-if="order.orderSummary?.total_amount"
                    class="flex items-center justify-between font-semibold"
                  >
                    <span class="text-gray-700">Total</span>
                    <span>{{ formatCurrency(order.orderSummary?.total_amount) }}</span>
                  </div>
                  <div
                    v-if="order && order.invoice_url"
                    class="text-end"
                  >
                    <a
                      :href="order.invoice_url"
                      class="text-sm font-medium text-black underline"
                      target="_blank"
                      rel="noopener noreferrer"
                    >View Invoice</a>
                  </div>
                </div>
                <div class="shrink-0 border h-px w-full my-4"></div>
                <div class="grid sm:grid-cols-2 gap-6">
                  <div class="col-span-1">
                    <div v-if="order.is_medicine_pickup_at_local_pharmacy === 1">
                      <div class="font-semibold">
                        Prescription Pickup Location
                      </div>
                      <address class="grid not-italic text-gray-700 mt-1.5">
                        <span class="font-medium">{{ order.pharmacy_name }}</span>
                        <span>{{ order.pharmacy_address }}</span>
                        <span>{{ order.pharmacy_city }}</span>
                        <span>{{ order.pharmacy_state }}-{{ order.pharmacy_zipcode }}</span>
                      </address>
                      <a
                        v-if="order && order.pharmacy_direction"
                        :href="order.pharmacy_direction"
                        class="text-black font-medium inline-flex items-center border-b !border-black mt-2"
                        target="_blank"
                        rel="noreferrer noopener"
                      >
                        <span>Get Directions</span>
                        <IconExternalLink
                          class="h-4 w-4 ms-1"
                          stroke-width="2"
                        />
                      </a>
                    </div>
                    <div v-if="order.is_medicine_pickup_at_local_pharmacy !== 1">
                      <div class="font-semibold">
                        Shipping Address
                      </div>
                      <address class="grid not-italic text-gray-700 mt-1.5">
                        <span v-if="order.address_line_2">{{ order.address_line_1 }}, {{ order.address_line_2 }}</span>
                        <span v-else>{{ order.address_line_1 }}</span>
                        <span>{{ order.city }}</span>
                        <span>{{ order.state }}-{{ order.zipcode }}</span>
                      </address>
                    </div>
                  </div>
                  <div class="col-span-1 place-content-center text-end">
                    <TwButton
                      v-if="Boolean(order.is_order_cancel_btn_enabled)"
                      class="w-full sm:w-auto"
                      variant="secondary"
                      @click="modalOrderCancelRef.open()"
                    >
                      Cancel Order
                    </TwButton>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <ModalShippingActivity ref="shippingActivityModalRef" />

    <ModalOrderCancel
      ref="modalOrderCancelRef"
      :order-id="orderId"
      :is-follow-up-visit="Boolean(order?.is_follow_up_visit_cancel_btn_enabled) || false"
      @canceled="modalOrderCancelReasonRef.open()"
    />

    <ModalOrderCancelReason
      ref="modalOrderCancelReasonRef"
      :order-id="orderId"
      @updated="fetchOrderDetails"
    />

    <SubscriptionUpdatePaymentModal
      ref="updatePaymentModalRef"
      :order-subscription-id="order?.subscription_id || ''"
      @updated="fetchOrderDetails"
    />
  </div>
</template>
