<script setup>
import ProfileDetailsCard from '@/views/user/components/ProfileDetailsCard.vue'
import ProfilePaymentCard from '@/views/user/components/ProfilePaymentCard.vue'
import ProfileShippingCard from '@/views/user/components/ProfileShippingCard.vue'
import ProfilePasswordCard from '@/views/user/components/ProfilePasswordCard.vue'
import { useAuthStore } from '@/store/auth'
import { storeToRefs } from 'pinia'

const authStore = useAuthStore()
const { userData } = storeToRefs(authStore)
</script>

<template>
  <div class="flex flex-col items-center">
    <div class="flex flex-col self-center px-4 w-full max-w-[840px] max-md:max-w-full">
      <h1 class="text-3xl md:text-5xl text-black font-semibold py-6 mb-4">
        Welcome, {{ userData?.first_name }}!
      </h1>

      <div class="space-y-5">
        <ProfileDetailsCard />
        <ProfilePaymentCard />
        <ProfileShippingCard />
        <ProfilePasswordCard />
      </div>
    </div>
  </div>
</template>
