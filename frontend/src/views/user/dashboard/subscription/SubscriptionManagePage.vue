<script setup>
import { isEmpty } from '@/@core/utils'
import router from '@/router'
import ApiService from '@/services/ApiService'
import { useAuthStore } from '@/store/auth'
import { useUpdatePaymentMethod } from '@/store/updatePaymentMethod'
import { processErrors } from '@/utils/errorHandler'
import { formatCurrency, isCardExpired } from '@/utils/helpers'
import { getOrderStatus } from '@/utils/user'
import { IconArrowLeft, IconBolt, IconCalendarRepeat, IconCheck, IconChevronRight, IconCircleDot, IconClockExclamation, IconExternalLink, IconInfoCircle, IconLink, IconPencil, IconPlayerPause, IconRefresh, IconX } from '@tabler/icons-vue'
import { useSessionStorage } from '@vueuse/core'
import { computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { toast } from 'vue-sonner'
import { useWLQuestions } from '@/store/wlQuestions'
import { useSubscriptionStore } from '@/store/subscription'
import { storeToRefs } from 'pinia'
import SyncVisitBanner from '@/views/user/components/SyncVisitBanner.vue'

const SubscriptionNotifications = defineAsyncComponent(() => import('@/views/user/components/SubscriptionNotifications.vue'))
const SubscriptionUpdatePaymentModal = defineAsyncComponent(() => import('@/views/user/components/SubscriptionUpdatePaymentModal.vue'))
const SubscriptionUpdateShipmentMethodModal = defineAsyncComponent(() => import('@/views/user/components/SubscriptionUpdateShipmentMethodModal.vue'))
const SubscriptionUpdateShippingModal = defineAsyncComponent(() => import('@/views/user/components/SubscriptionUpdateShippingModal.vue'))
const ModalOrderCancel = defineAsyncComponent(() => import('@/views/user/components/ModalOrderCancel.vue'))
const ModalOrderCancelReason = defineAsyncComponent(() => import('@/views/user/components/ModalOrderCancelReason.vue'))

const route = useRoute()
const authStore = useAuthStore()
const { userData } = storeToRefs(authStore)
const { setSubscriptionRefillId } = useUpdatePaymentMethod()
const { resetQuestions: resetWLQuestions } = useWLQuestions()

const subscriptionStore = useSubscriptionStore()
const { btnReactivateLoading } = storeToRefs(subscriptionStore)
const { reactivateSubscription } = subscriptionStore

const edVisitSession = useSessionStorage('edVisitSession', {})
const hlVisitSession = useSessionStorage('hlVisitSession', {})

const subscriptionId = computed(() => route.params.subscriptionId)
const skeletonLoading = ref(false)
const subscription = ref({})
const showReactivateCard = ref(false)
const updateShippingModalRef = ref(null)
const updatePaymentModalRef = ref(null)
const showNextRefillCard = ref(false)
const nextRefillDateOptions = ref([])
const selectedNextRefillDate = ref(null)
const btnChangeNextRefillDateLoading = ref(false)
const notificationsRef = ref(null)
const btnRenewPrescriptionLoading = ref(null)
const shippingMethods = ref([])
const updateShipmentMethodModalRef = ref(null)
const showRequestRefillCard = ref(false)
const btnRequestRefillLoading = ref(false)
const modalOrderCancelRef = ref(null)
const modalOrderCancelReasonRef = ref(null)

// const additionalOptionsModalRef = ref(null)

onMounted(async () => {
  await fetchSubscriptionDetails()
})

const showSubscriptionActions = computed(() => {
  return !showReactivateCard.value && !showNextRefillCard.value && !showRequestRefillCard.value
})

async function fetchSubscriptionDetails() {
  try {
    skeletonLoading.value = true

    const { data } = await ApiService.get(`/subscription-details/${subscriptionId.value}`)

    if (data.status === 200) {
      subscription.value = data.subscriptionDetails

      if (!isEmpty(data.subscriptionDetails.adjust_next_refill_dates)) {
        nextRefillDateOptions.value = data.subscriptionDetails.adjust_next_refill_dates
      }

      if (!isEmpty(data.subscriptionDetails.pharmacyShipmentMethods)) {
        shippingMethods.value = data.subscriptionDetails.pharmacyShipmentMethods
      }
    } else {
      toast.error(data.message)
    }
  } catch (error) {
    console.error(error)

    const errorMessage = processErrors(error)[0]

    toast.error(errorMessage)
  } finally {
    skeletonLoading.value = false
  }
}

const isRefillNowDisabled = computed(() => subscription.value?.is_refill_now !== 1)

const isChangeNextRefillDateDisabled = computed(() => {
  const type = subscription.value?.subscription_action_type

  return [0, 1, 3, 4, 5].includes(type) || subscription.value?.status === 0
})

function openUpdateShippingModal() {
  const shippingAddress = JSON.parse(JSON.stringify(subscription.value.shipping_address))

  updateShippingModalRef.value.openModal(shippingAddress)
}

function openUpdateShipmentMethodModal() {
  updateShipmentMethodModalRef.value.openModal(
    shippingMethods.value, // all shipping methods
    subscription.value.current_pharmacy_shipment_id, // current selected shipping method
  )
}

function openUpdatePaymentModal(isPaymentFailed) {
  if (!isPaymentFailed) {
    setSubscriptionRefillId(null)
  }
  updatePaymentModalRef.value.openModal(subscription.value.user_payment_card_id)
}

function getSubscriptionStatus(status) {
  const statusDetails = {
    '0': {
      bgColor: 'bg-amber-500',
      textColor: 'text-amber-500',
      icon: IconPlayerPause,
    },
    '1': {
      bgColor: 'bg-lime-700',
      textColor: 'text-lime-700',
      icon: IconCheck,
    },
    '2': {
      bgColor: 'bg-red-600',
      textColor: 'text-red-600',
      icon: IconX,
    },
    '3': {
      bgColor: 'bg-black',
      textColor: 'text-black',
      icon: IconClockExclamation,
    },
    '4': {
      bgColor: 'bg-green-500',
      textColor: 'text-green-500',
      icon: IconCheck,
    },
  }

  return statusDetails[status] || {
    bgColor: 'bg-gray-200',
    textColor: 'text-gray-200',
    icon: IconCircleDot,
  }
}

async function handleRequestRefill() {
  try {
    btnRequestRefillLoading.value = true

    const { data } = await ApiService.get(`/generate-refill/${subscriptionId.value}`)

    if (data.status === 200) {
      toast.success(data.message)
      showRequestRefillCard.value = false
      fetchSubscriptionDetails()
      notificationsRef.value.reload()
    } else {
      toast.error(data.message)
    }
  } catch (error) {
    toast.error(processErrors(error)[0])
  } finally {
    btnRequestRefillLoading.value = false
  }
}

async function handleNextRefillDateChange() {
  try {
    btnChangeNextRefillDateLoading.value = true

    const postData = {
      id: subscriptionId.value,
      date: selectedNextRefillDate.value,
    }

    const { data } = await ApiService.post('/change-subscription-refill-date', postData)

    if (data.status === 200) {
      toast.success(data.message)
      showNextRefillCard.value = false
      fetchSubscriptionDetails()
      notificationsRef.value.reload()
    } else {
      toast.error(data.message)
    }
  } catch (error) {
    toast.error(processErrors(error)[0])
  } finally {
    btnChangeNextRefillDateLoading.value = false
  }
}

function handleRenewPrescription(subscription) {
  btnRenewPrescriptionLoading.value = subscriptionId.value

  const renewPrescriptionData = subscription.renewedPrescriptionData

  if (!isEmpty(renewPrescriptionData)) {
    if (subscription.category_name.toUpperCase() === 'ED') {
      edVisitSession.value = {
        visitType: 'renew',
        subscriptionId: subscriptionId.value,

        // category: renewPrescriptionData.category,
        // productFreq: renewPrescriptionData.frequency,
        // productId: renewPrescriptionData.ed_product_id,
        // strengthId: renewPrescriptionData.ed_product_strength_id,
        // productQty: renewPrescriptionData.ed_product_qty_id,
        // planId: renewPrescriptionData.ed_product_subscription_plan_id,
        // selectedShippingMethod: renewPrescriptionData.pharmacy_shipping_method_id,
        // productType: renewPrescriptionData.product_type,
      }

      router.push({ name: 'ed-questions-renew', params: { slug: 'start' } })

      return
    } else if (subscription.category_name.toUpperCase() === 'HL') {
      hlVisitSession.value = {
        visitType: 'renew',
        subscriptionId: subscriptionId.value,

        // category: renewPrescriptionData.category,
        // productId: renewPrescriptionData.hl_product_id,
        // strengthId: renewPrescriptionData.hl_product_strength_id,
        // productQty: renewPrescriptionData.hl_product_qty_id,
        // planId: renewPrescriptionData.hl_product_subscription_plan_id,
        // selectedShippingMethod: renewPrescriptionData.pharmacy_shipping_method_id,
      }

      router.push({ name: 'hl-questions-renew', params: { slug: 'start' } })

      return
    }
  }

  toast.error('We are unable to renew your prescription at this time. Please try again later.')
}

const isStartFollowupEnabled = computed(() => {
  return subscription.value?.is_follow_up_visit_enable === 1
})

function startFollowupVisit(subscription) {
  if (subscription.category_name === 'WL') {
    resetWLQuestions()

    const wlVisitSession = useSessionStorage('wlVisitSession', {})

    wlVisitSession.value['subscriptionId'] = subscription.id
    wlVisitSession.value['activeIngredient'] = subscription.product_active_ingredient

    router.push({
      name: 'wl-followup-questions',
      params: { slug: 'start' },
    })
  } else if (subscription.category_name === 'ED') {
    const edVisitSession = useSessionStorage('edVisitSession', {})

    edVisitSession.value['subscriptionId'] = subscription.followup_visit_id

    router.push({
      name: 'ed-followup-questions',
      params: { slug: 'start' },
    })
  } else if (subscription.category_name === 'HL') {
    const hlVisitSession = useSessionStorage('hlVisitSession', {})

    hlVisitSession.value['subscriptionId'] = subscription.followup_visit_id

    router.push({
      name: 'hl-followup-questions',
      params: { slug: 'start' },
    })
  } else {
    console.error('Error: unknown treatment')
  }
}

function handleOrderRetry(subscription) {
  if (subscription.subscription_action_type === 1 && subscription.is_refill_order) {
    setSubscriptionRefillId(subscription?.subscription_refill_id)
    openUpdatePaymentModal(true)
  } else {
    if (subscription.category_name === 'ED') {
      sessionStorage.setItem('retryOrderId', subscription.order_id)
      router.push({ name: 'ed-visit-checkout' })
    } else if (subscription.category_name === 'HL') {
      sessionStorage.setItem('hlRetryOrderId', subscription.order_id)
      router.push({ name: 'hl-visit-checkout' })
    } else if (subscription.category_name === 'WL') {
      sessionStorage.setItem('wlRetryOrderId', subscription.order_id)
      router.push({
        name: subscription.is_followup_order
          ? 'wl-followup-visit-checkout'
          : 'wl-visit-checkout',
      })
    }
  }
}
</script>

<template>
  <div class="flex flex-col items-center">
    <div class="flex flex-col self-center px-4 w-full max-w-[1120px] max-md:max-w-full">
      <div class="flex flex-col sm:flex-row gap-4 justify-between mb-2 sm:mb-4 py-6">
        <h1 class="text-3xl md:text-5xl text-black font-semibold">
          Welcome, {{ userData?.first_name }}!
        </h1>
        <div class="flex items-center gap-2">
          <RouterLink
            :to="{name: 'user-subscription'}"
            class="w-full inline-flex items-center justify-center text-gray-900 text-sm font-medium border-2 border-solid !border-gray-900 rounded-full px-4 py-1.5 hover:bg-gray-300"
          >
            <IconArrowLeft
              class="h-4 w-4 me-1"
              stroke-width="2"
            /> Back
          </RouterLink>
        </div>
      </div>

      <div>
        <div class="grid grid-cols-12 gap-5">
          <div class="col-span-12 lg:col-span-8 space-y-5">
            <!-- Skeleton Loading -->
            <div v-if="skeletonLoading">
              <div class="bg-white p-5 rounded-[20px] mb-5">
                <div class="text-xl font-semibold mb-3">
                  Subscription > Manage
                </div>
                <div class="px-3.5 py-4 rounded-[20px] border !border-gray-300 max-md:max-w-full">
                  <div class="flex flex-col sm:flex-row sm:items-center gap-4">
                    <!-- Image Placeholder -->
                    <div class="flex justify-center rounded-2xl bg-gray-200 animate-pulse w-full h-52 sm:h-28 sm:w-28"></div>
                    <div class="flex-1 sm:px-3">
                      <div class="flex flex-col gap-4">
                        <div class="flex flex-col sm:flex-row gap-4">
                          <div class="flex-1">
                            <!-- Product Name Placeholder -->
                            <div class="font-medium text-xl mb-1 bg-gray-200 animate-pulse h-6 w-28 sm:w-48 rounded"></div>
                            <!-- Product Details Placeholder -->
                            <div class="text-zinc-500">
                              <span class="block bg-gray-200 animate-pulse h-4 w-36 sm:w-72 rounded mb-1"></span>
                            </div>
                          </div>
                          <div>
                            <!-- Subscription Id Placeholder -->
                            <div class="text-xs uppercase font-medium text-zinc-500 mb-1 bg-gray-200 animate-pulse h-4 w-24 rounded"></div>
                            <div class="text-zinc-900 bg-gray-200 animate-pulse h-6 w-32 rounded"></div>
                          </div>
                        </div>
                        <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
                          <div>
                            <!-- Subscribed On Placeholder -->
                            <div class="text-xs uppercase font-medium text-zinc-500 mb-1 bg-gray-200 animate-pulse h-4 w-24 rounded"></div>
                            <div class="text-zinc-900 bg-gray-200 animate-pulse h-6 w-32 rounded"></div>
                          </div>
                          <div>
                            <!-- Next Refill Date Placeholder -->
                            <div class="text-xs uppercase font-medium text-zinc-500 mb-1 bg-gray-200 animate-pulse h-4 w-24 rounded"></div>
                            <div class="text-zinc-900 bg-gray-200 animate-pulse h-6 w-32 rounded"></div>
                          </div>
                          <div>
                            <!-- Status Placeholder -->
                            <div class="flex gap-1 px-1 pe-3 py-0.5 font-bold whitespace-nowrap rounded-2xl bg-gray-200 animate-pulse h-6 w-36"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="grid grid-cols-12 gap-4 mt-4">
                  <div class="col-span-12 sm:col-span-6 px-3.5 py-4 rounded-[20px] border !border-gray-300">
                    <div class="h-8 w-8 bg-gray-300 rounded-full mb-2 animate-pulse"></div>
                    <div class="text-lg font-medium text-zinc-800 mb-3 bg-gray-300 h-6 w-32 rounded-md animate-pulse"></div>
                    <div class="w-full h-10 bg-gray-300 rounded-full animate-pulse"></div>
                  </div>
                  <div class="col-span-12 sm:col-span-6 px-3.5 py-4 rounded-[20px] border !border-gray-300">
                    <div class="h-8 w-8 bg-gray-300 rounded-full mb-2 animate-pulse"></div>
                    <div class="text-lg font-medium text-zinc-800 mb-3 bg-gray-300 h-6 w-32 rounded-md animate-pulse"></div>
                    <div class="w-full h-10 bg-gray-300 rounded-full animate-pulse"></div>
                  </div>
                  <div class="col-span-12 sm:col-span-6 px-3.5 py-4 rounded-[20px] border !border-gray-300">
                    <div class="h-8 w-8 bg-gray-300 rounded-full mb-2 animate-pulse"></div>
                    <div class="text-lg font-medium text-zinc-800 mb-3 bg-gray-300 h-6 w-32 rounded-md animate-pulse"></div>
                    <div class="w-full h-10 bg-gray-300 rounded-full animate-pulse"></div>
                  </div>
                  <div class="col-span-12 sm:col-span-6 px-3.5 py-4 rounded-[20px] border !border-gray-300">
                    <div class="h-8 w-8 bg-gray-300 rounded-full mb-2 animate-pulse"></div>
                    <div class="text-lg font-medium text-zinc-800 mb-3 bg-gray-300 h-6 w-32 rounded-md animate-pulse"></div>
                    <div class="w-full h-10 bg-gray-300 rounded-full animate-pulse"></div>
                  </div>
                </div>
              </div>
              <div class="grid grid-cols-10 gap-5">
                <div class="col-span-10 md:col-span-5 bg-white p-5 rounded-[20px]">
                  <div class="flex justify-between">
                    <div class="text-xl font-semibold mb-3 bg-gray-300 h-6 w-32 rounded-md animate-pulse"></div>
                    <div class="inline-flex items-center gap-1 px-3 h-8 w-16 text-xs font-medium rounded-full bg-gray-300 animate-pulse"></div>
                  </div>
                  <div>
                    <div class="text-xs uppercase font-medium text-zinc-500 mb-1 bg-gray-300 h-4 w-24 rounded-md animate-pulse"></div>
                    <div class="text-zinc-700 bg-gray-300 h-4 w-32 rounded-md animate-pulse"></div>
                  </div>
                </div>
                <div class="col-span-10 md:col-span-5 bg-white p-5 rounded-[20px]">
                  <div class="flex justify-between">
                    <div class="text-xl font-semibold mb-3 bg-gray-300 h-6 w-32 rounded-md animate-pulse"></div>
                    <div class="inline-flex items-center gap-1 px-3 h-8 w-16 text-xs font-medium rounded-full bg-gray-300 animate-pulse"></div>
                  </div>
                  <div>
                    <div class="text-xs uppercase font-medium text-zinc-500 mb-1 bg-gray-300 h-4 w-24 rounded-md animate-pulse"></div>
                    <div class="text-zinc-700 bg-gray-300 h-4 w-32 rounded-md animate-pulse"></div>
                  </div>
                </div>
              </div>
            </div>

            <div v-else>
              <div class="bg-white p-5 rounded-[20px] mb-5">
                <div class="text-xl font-semibold mb-3">
                  Subscription > Manage
                </div>

                <!-- status banner -->
                <div
                  v-if="subscription.subscription_action_type === 1"
                  class="w-full bg-red-100 rounded-lg px-4 py-2 text-sm font-medium text-red-700 mb-4 flex justify-between align-center gap-4"
                >
                  <span>Your subscription is on hold due to a payment failure.</span>
                  <TwButton
                    class="!py-1.5"
                    @click="handleOrderRetry(subscription)"
                  >
                    Retry
                  </TwButton>
                </div>

                <!-- Subscription Details -->
                <div class="px-3.5 py-4 rounded-[20px] border !border-gray-300 max-md:max-w-full">
                  <div class="flex flex-col sm:flex-row sm:items-start gap-4">
                    <div class="flex justify-center rounded-2xl w-full h-52 bg-gray-100 sm:w-auto sm:h-auto">
                      <img
                        loading="lazy"
                        :src="subscription.product_img"
                        class="self-center aspect-square w-28 h-28 rounded-2xl"
                        alt=""
                      />
                    </div>
                    <div class="flex-1 px-3">
                      <div class="flex flex-col gap-5">
                        <div class="flex flex-col align-start sm:align-center sm:flex-row gap-4">
                          <div class="flex-1">
                            <div class="font-medium text-xl mb-1">
                              {{ subscription.product_name }}
                            </div>
                            <div
                              v-if="subscription.category_name === 'WL'"
                              class="text-zinc-500"
                            >
                              <span>
                                {{ subscription.strength }} {{ subscription.strength_unit }}/weekly
                              </span>
                            </div>
                            <div
                              v-else
                              class="text-zinc-500"
                            >
                              <span>
                                {{ subscription.strength }} {{ subscription.strength_unit }} x {{ subscription.qty * subscription.subscription_interval }} units
                              </span>
                              <span class="block"> ({{ subscription.subscription_interval * 30 }}-day supply)</span>
                            </div>
                          </div>
                          <div v-if="!isEmpty(subscription.subscription_reference_id)">
                            <div class="text-xs uppercase font-medium text-zinc-500 mb-1">
                              Subscription ID
                            </div>
                            <div class="text-zinc-900">
                              {{ subscription.subscription_reference_id }}
                            </div>
                          </div>
                          <div v-if="!isEmpty(subscription.sub_total)">
                            <div class="text-xs uppercase font-medium text-zinc-500 mb-1">
                              Plan Amount
                            </div>
                            <div class="text-zinc-900">
                              {{ formatCurrency(subscription.sub_total) }}
                            </div>
                          </div>
                        </div>
                        <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
                          <div v-if="!isEmpty(subscription.subscribed_on)">
                            <div class="text-xs uppercase font-medium text-zinc-500 mb-1">
                              Subscribed On
                            </div>
                            <div class="text-zinc-900">
                              {{ subscription.subscribed_on }}
                            </div>
                          </div>
                          <div v-if="!isEmpty(subscription.current_next_refill_date)">
                            <div class="text-xs uppercase font-medium text-zinc-500 mb-1">
                              Next Refill Date
                            </div>
                            <div class="text-zinc-900">
                              {{ subscription.current_next_refill_date }}
                            </div>
                          </div>
                          <div v-if="!isEmpty(subscription.next_follow_up_visit_date)">
                            <div class="text-xs uppercase font-medium text-zinc-500 mb-1">
                              Next Follow-up Date
                            </div>
                            <div class="text-zinc-900">
                              {{ subscription.next_follow_up_visit_date }}
                            </div>
                          </div>
                          <div v-if="!isEmpty(subscription.canceled_at)">
                            <div class="text-xs uppercase font-medium text-zinc-500 mb-1">
                              Canceled On
                            </div>
                            <div class="text-zinc-900">
                              {{ subscription.canceled_at }}
                            </div>
                          </div>
                          <div v-if="!isEmpty(subscription.expired_at)">
                            <div class="text-xs uppercase font-medium text-zinc-500 mb-1">
                              Expired On
                            </div>
                            <div class="text-zinc-900">
                              {{ subscription.expired_at }}
                            </div>
                          </div>
                          <div class="inline-flex gap-1">
                            <div :class="`flex gap-1 w-fit px-1 pe-3 py-0.5 font-bold whitespace-nowrap rounded-2xl bg-opacity-10 ${getSubscriptionStatus(subscription.status).bgColor} ${getSubscriptionStatus(subscription.status).textColor}`">
                              <component
                                :is="getSubscriptionStatus(subscription.status).icon"
                                :class="`shrink-0 self-center w-[20px] h-[20px] text-white rounded-full p-px ${getSubscriptionStatus(subscription.status).bgColor}`"
                                stroke-width="3"
                              />
                              <span>{{ subscription.order_status }}</span>
                            </div>
                            <button
                              v-if="subscription.subscription_action_type === 0"
                              v-tooltip.focus="'Your subscription is on hold because the provider is reviewing your order. It will automatically become active once the prescription is issued.'"
                              class="text-gray-600"
                            >
                              <IconInfoCircle
                                class="h-4 w-4"
                                stroke-width="2"
                              />
                            </button>
                          </div>
                        </div>
                      </div>
                      <div
                        v-if="
                          subscription.status === 3
                            && !isEmpty(subscription.is_renewed_prescription)
                            && subscription.is_renewed_prescription === 1
                        "
                        class="flex flex-col sm:flex-row gap-4 mt-4 border-t !border-gray-200 pt-4"
                      >
                        <TwButton
                          loading-text="Please wait..."
                          :loading="btnRenewPrescriptionLoading === subscriptionId"
                          :disabled="!isEmpty(btnRenewPrescriptionLoading)"
                          @click="handleRenewPrescription(subscription)"
                        >
                          Renew Prescription
                        </TwButton>
                      </div>

                      <!-- Cancelation reason -->
                      <div
                        v-if="!isEmpty(subscription.subscription_canceled_reason)"
                        class="w-full bg-red-100 text-center rounded-lg p-3 text-sm font-medium text-red-700 mt-3"
                      >
                        {{ subscription.subscription_canceled_reason }}
                      </div>
                    </div>
                  </div>
                </div>

                <div class="mt-4">
                  <SyncVisitBanner
                    :visit-data="{
                      appointmentScheduledLink: subscription.appointmentScheduledLink,
                      appointmentUrl: subscription.appointmentUrl,
                      appointmentDocName: subscription.appointmentDocName,
                      appointmentDateTime: subscription.appointmentDateTime,
                      isAppointmentMissed: subscription.isAppointmentMissed,
                    }"
                    @scheduled="fetchSubscriptionDetails"
                  />

                  <!-- ****Subscription Actions**** -->
                  <div
                    v-if="showSubscriptionActions"
                    class="grid grid-cols-12 gap-4 mt-4"
                  >
                    <!-- For ED and HL treatments -->

                    <!-- Request Refill -->
                    <div
                      v-if="subscription.category_name !== 'WL' && (subscription.status !== 2 || (!isEmpty(subscription.is_reactive_canceled_subscription) && subscription.is_reactive_canceled_subscription === 1))"
                      class="col-span-12 sm:col-span-6 px-3.5 py-4 rounded-[20px] border"
                      :class="[
                        isRefillNowDisabled ? '!border-gray-200 bg-gray-100' : '!border-gray-300'
                      ]"
                    >
                      <div class="flex justify-between items-start">
                        <IconBolt
                          class="h-8 w-8 bg-yellow-200 text-yellow-600 p-1 rounded-full mb-2"
                          stroke-width="2"
                        />
                        <button
                          v-if="!isEmpty(subscription.refill_note)"
                          v-tooltip.focus="subscription.refill_note"
                        >
                          <IconInfoCircle
                            class="h-4 w-4 text-gray-500 cursor-pointer"
                            stroke-width="2"
                          />
                        </button>
                      </div>
                      <div class="text-lg font-medium text-zinc-800 mb-3">
                        {{
                          subscription.remain_refill === 1
                            ? `${subscription.remain_refill} Refill Remaining`
                            : `${subscription.remain_refill} Refills Remaining`
                        }}
                      </div>
                      <TwButton
                        class="w-full h-10"
                        :disabled="isRefillNowDisabled"
                        @click="showRequestRefillCard = true"
                      >
                        Refill Now
                      </TwButton>
                    </div>

                    <!-- Change Next Refill Date -->
                    <div
                      v-if="subscription.category_name !== 'WL' && subscription.status !== 2"
                      class="col-span-12 sm:col-span-6 px-3.5 py-4 rounded-[20px] border"
                      :class="[
                        isChangeNextRefillDateDisabled ? '!border-gray-200 bg-gray-100' : '!border-gray-300'
                      ]"
                    >
                      <div class="flex justify-between items-start">
                        <IconRefresh
                          class="h-8 w-8 bg-cyan-400 text-white p-1 rounded-full mb-2"
                          stroke-width="2"
                        />
                        <button
                          v-if="subscription.subscription_action_type === 0"
                          v-tooltip.focus="'You will be able to change next refill after doctor prescribes.'"
                        >
                          <IconInfoCircle
                            class="h-4 w-4 text-gray-500 cursor-pointer"
                            stroke-width="2"
                          />
                        </button>
                        <button
                          v-else-if="subscription.status === 3"
                          v-tooltip.focus="'Prescription Expired!'"
                        >
                          <IconInfoCircle
                            class="h-4 w-4 text-gray-500 cursor-pointer"
                            stroke-width="2"
                          />
                        </button>
                      </div>
                      <div class="text-lg font-medium text-zinc-800 mb-3">
                        Adjust Next Refill Schedule
                      </div>
                      <TwButton
                        class="w-full h-10"
                        :disabled="isChangeNextRefillDateDisabled"
                        @click="showNextRefillCard = true"
                      >
                        Reschedule Next Refill
                      </TwButton>
                    </div>

                    <!-- Reactivate -->
                    <div
                      v-if="!isEmpty(subscription.is_reactive_canceled_subscription) && subscription.is_reactive_canceled_subscription === 1"
                      class="col-span-12 sm:col-span-6 px-3.5 py-4 rounded-[20px] border !border-gray-300"
                    >
                      <IconRefresh
                        class="h-8 w-8 bg-blue-400 text-white p-1 rounded-full mb-2"
                        stroke-width="2"
                      />
                      <div class="text-lg font-medium text-zinc-800 mb-3">
                        Want to try again?
                      </div>
                      <TwButton
                        class="w-full h-10"
                        @click="showReactivateCard = true"
                      >
                        Reactivate Subscription
                      </TwButton>
                    </div>

                    <!-- Follow up -->
                    <div
                      v-if="subscription.status !== 2"
                      class="col-span-12 sm:col-span-6 px-3.5 py-4 rounded-[20px] border"
                      :class="[
                        isStartFollowupEnabled ? '!border-gray-300' : '!border-gray-200 bg-gray-100'
                      ]"
                    >
                      <div class="flex justify-between items-start">
                        <IconCalendarRepeat
                          class="h-8 w-8 bg-green-500 text-white p-1 rounded-full mb-2"
                          stroke-width="2"
                        />
                        <button
                          v-if="!isStartFollowupEnabled && !isEmpty(subscription.next_follow_up_visit_date)"
                          v-tooltip.focus="`You will be able to start follow-up visit after ${subscription.next_follow_up_visit_date}`"
                        >
                          <IconInfoCircle
                            class="h-4 w-4 text-gray-500 cursor-pointer"
                            stroke-width="2"
                          />
                        </button>
                      </div>
                      <div class="text-lg font-medium text-zinc-800 mb-3">
                        Need a follow-up visit?
                      </div>
                      <TwButton
                        class="w-full h-10"
                        :disabled="!isStartFollowupEnabled"
                        @click="startFollowupVisit(subscription)"
                      >
                        Start Follow-up Visit
                      </TwButton>
                      <p
                        v-if="subscription.last_followup_visit_created_date"
                        class="text-sm text-gray-600 mt-2 mb-0 text-center"
                      >
                        Last follow-up on {{ subscription.last_followup_visit_created_date }}
                      </p>
                    </div>

                    <!-- Follow-up Visit details -->
                    <div
                      v-if="subscription.category_name === 'WL' && subscription.follow_up_visit_details"
                      class="col-span-12 sm:col-span-6 px-3.5 py-4 rounded-[20px] border !border-gray-300"
                    >
                      <div class="mb-1">
                        <p class="text-[11px] uppercase font-semibold text-gray-500">
                          {{
                            subscription.follow_up_visit_details?.is_follow_up_visit
                              ? 'Follow-up Visit'
                              : 'New Visit'
                          }}
                        </p>
                        <p class="text-gray-700 font-medium">
                          Order # <RouterLink
                            :to="{ name: 'user-order-details', params: { orderId: subscription.follow_up_visit_details.refill_id } }"
                            class="underline"
                          >
                            {{ subscription.follow_up_visit_details.order_no }}
                          </RouterLink>
                        </p>
                      </div>
                      <div class="mb-3">
                        <div :class="`flex gap-1 w-fit px-1 pe-3 py-0.5 text-sm font-bold whitespace-nowrap rounded-2xl bg-opacity-20 ${getOrderStatus(subscription.follow_up_visit_details.status).bgColor} ${getOrderStatus(subscription.follow_up_visit_details.status).textColor}`">
                          <component
                            :is="getOrderStatus(subscription.follow_up_visit_details.status).icon"
                            :class="`shrink-0 self-center size-[18px] text-white rounded-full p-px ${getOrderStatus(subscription.follow_up_visit_details.status).bgColor}`"
                            stroke-width="3"
                          />
                          <span class="mt-[1px]">{{ subscription.follow_up_visit_details.order_status }}</span>
                        </div>
                      </div>
                      <TwButton
                        v-if="subscription.follow_up_visit_details.is_follow_up_visit_cancel_btn_enabled === 1"
                        class="w-full h-10"
                        @click="modalOrderCancelRef.open()"
                      >
                        Cancel Visit
                      </TwButton>
                    </div>
                  </div>

                  <!-- Reactivate Section -->
                  <div
                    v-if="showReactivateCard"
                    class="bg-gray-100 p-4 rounded-[20px] mt-5 flex flex-col gap-5"
                  >
                    <div>
                      <div class="text-base font-medium text-zinc-900 mb-1">
                        Considering reactivating your subscription?
                      </div>
                      <div class="text-sm text-zinc-600">
                        It's important to note that your upcoming refill will be processed.
                      </div>
                    </div>
                    <div class="inline-flex flex-col sm:flex-row gap-2">
                      <TwButton
                        class="min-w-32 !bg-green-500 hover:!bg-green-600"
                        :loading="btnReactivateLoading"
                        @click="reactivateSubscription(subscriptionId)"
                      >
                        Yes
                      </TwButton>
                      <TwButton
                        class="!bg-transparent border !border-black !text-black hover:!bg-gray-200  min-w-32"
                        :disabled="btnReactivateLoading"
                        @click="showReactivateCard = false"
                      >
                        No
                      </TwButton>
                    </div>
                  </div>

                  <!-- Request Refill -->
                  <div
                    v-if="showRequestRefillCard"
                    class="bg-gray-100 p-4 rounded-[20px] mt-5 flex flex-col gap-5"
                  >
                    <div>
                      <div class="text-base font-medium text-zinc-900 mb-1">
                        Time for a Refill?
                      </div>
                      <div class="text-sm text-zinc-600">
                        Quickly request a refill for your ongoing medication.
                      </div>
                    </div>
                    <div class="inline-flex flex-col sm:flex-row gap-2">
                      <TwButton
                        class="min-w-32 !bg-green-500 hover:!bg-green-600"
                        :loading="btnRequestRefillLoading"
                        @click="handleRequestRefill"
                      >
                        Yes
                      </TwButton>
                      <TwButton
                        class="!bg-transparent border !border-black !text-black hover:!bg-gray-200  min-w-32"
                        :disabled="btnRequestRefillLoading"
                        @click="showRequestRefillCard = false"
                      >
                        No
                      </TwButton>
                    </div>
                  </div>

                  <!-- Change Refill Date -->
                  <div
                    v-if="showNextRefillCard"
                    class="bg-gray-100 p-4 rounded-[20px] mt-5 flex flex-col gap-5"
                  >
                    <div>
                      <div class="text-base font-medium text-zinc-900 mb-3">
                        Reschedule your next refill date
                      </div>
                      <ul class="grid grid-cols-4 w-full gap-3">
                        <li
                          v-for="(option, index) in nextRefillDateOptions"
                          :key="option.id"
                          class="col-span-4 sm:col-span-2"
                        >
                          <input
                            :id="`next_refill_date_${index}`"
                            v-model="selectedNextRefillDate"
                            type="radio"
                            name="next_refill_date"
                            class="hidden peer"
                            :value="option.date"
                            required
                          />
                          <label
                            :for="`next_refill_date_${index}`"
                            class="inline-flex items-center justify-between w-full px-5 py-3 text-gray-700 bg-white border !border-gray-200 rounded-2xl cursor-pointer peer-checked:!border-gray-600 peer-checked:text-black hover:text-gray-800 hover:bg-gray-50"
                          >
                            <div class="block">
                              <div class="w-full">
                                <span class="block text-base font-medium">{{ option.title }}</span>
                                <span class="block text-base font-medium text-zinc-600">{{ option.sub_title }}</span>
                              </div>
                              <div class="w-full text-sm font-medium text-gray-500">{{ option.formatted_date }}</div>
                            </div>
                            <span class="mx-2 flex items-center justify-center">
                              <span
                                class="w-4 h-4 border-2 rounded-sm flex justify-center items-center"
                                :class="[
                                  selectedNextRefillDate === option.date ? 'border-transparent bg-black' : 'border-gray-300'
                                ]"
                              >
                                <IconCheck
                                  class="h-5 w-5 z-50"
                                  :stroke-width="selectedNextRefillDate === option.date ? 4 : 1"
                                  :class="[
                                    selectedNextRefillDate === option.date ? 'text-white' : 'text-gray-900'
                                  ]"
                                />
                              </span>
                            </span>
                          </label>
                        </li>
                      </ul>
                    </div>
                    <div class="inline-flex flex-col sm:flex-row gap-2">
                      <TwButton
                        class="min-w-32"
                        :loading="btnChangeNextRefillDateLoading"
                        @click="handleNextRefillDateChange"
                      >
                        Confirm
                      </TwButton>
                      <TwButton
                        class="!bg-transparent border !border-black !text-black hover:!bg-gray-200  min-w-32"
                        :disabled="btnChangeNextRefillDateLoading"
                        @click="showNextRefillCard = false"
                      >
                        Cancel
                      </TwButton>
                    </div>
                  </div>
                </div>
              </div>

              <!-- visit history -->
              <div
                v-if="!isEmpty(subscription.visits)"
                class="w-full rounded-[20px] bg-white p-5 my-4"
              >
                <div class="text-lg font-medium text-zinc-800 mb-3">
                  Visit History
                </div>
                <div class="space-y-3 max-h-64 overflow-y-auto px-1">
                  <div
                    v-for="visit in subscription.visits"
                    :key="visit.id"
                    class="px-3.5 py-4 rounded-[20px] border !border-gray-300 max-md:max-w-full"
                  >
                    <div class="flex flex-col sm:flex-row justify-between text-gray-700 mb-2">
                      <p class="text-sm">
                        Order # <RouterLink
                          :to="{ name: 'user-order-details', params: { orderId: visit.id } }"
                          class="underline font-medium"
                        >
                          {{ visit.order_no }}
                        </RouterLink> | <span class="font-medium">{{ visit.refill_type }}</span>
                      </p>
                      <p class="text-sm">
                        {{ visit.created_date }}
                      </p>
                    </div>
                    <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between border-b border-gray-200 last:border-b-0">
                      <div class="flex flex-col items-start sm:flex-row sm:items-center gap-3">
                        <div class="size-14 bg-gray-100 !rounded-xl">
                          <img
                            class="w-full h-full object-contain p-1"
                            :src="visit.product_img"
                            :alt="visit.product_name"
                          />
                        </div>
                        <div v-if="subscription.category_name === 'WL'">
                          <div class="text-base font-medium text-zinc-800">
                            {{ visit.product_name }}
                          </div>
                          <div class="text-sm text-zinc-600">
                            {{ visit.strength }}{{ visit.strength_unit }} / weekly
                          </div>
                        </div>
                        <div v-else>
                          <div class="text-base font-medium text-zinc-800">
                            {{ visit.product_name }}
                          </div>
                          <div class="text-sm text-zinc-600">
                            <span>
                              {{ visit.strength }} {{ visit.strength_unit }} x {{ visit.qty * visit.subscription_interval }} units
                            </span>
                            <span class="block"> ({{ visit.subscription_interval * 30 }}-day supply)</span>
                          </div>
                        </div>
                      </div>
                      <div class="flex flex-col items-end gap-1">
                        <div class="text-sm font-medium text-zinc-800 mt-1">
                          <div :class="`flex gap-1 w-fit px-1 pe-3 py-0.5 text-sm font-bold whitespace-nowrap rounded-2xl bg-opacity-20 ${getOrderStatus(visit.status).bgColor} ${getOrderStatus(visit.status).textColor}`">
                            <component
                              :is="getOrderStatus(visit.status).icon"
                              :class="`shrink-0 self-center size-[18px] text-white rounded-full p-px ${getOrderStatus(visit.status).bgColor}`"
                              stroke-width="3"
                            />
                            <span class="mt-[1px]">{{ visit.order_status }}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- treatment instructions -->
              <div
                v-if="!isEmpty(subscription.dose_instruction)"
                class="w-full rounded-[20px] bg-white p-5 my-4"
              >
                <div class="text-lg font-medium text-zinc-800 mb-1">
                  {{ subscription.category_name?.toLowerCase() === 'wl' ? 'Dose Instruction' : 'How to take' }}
                </div>
                <div class="text-zinc-900 text-base">
                  {{ subscription.dose_instruction }}
                </div>

                <RouterLink
                  v-if="!isEmpty(subscription.product_id) && subscription.category_name?.toLowerCase() !== 'wl'"
                  :to="{ name: 'user-treatment-overview', params: { productId: subscription.product_id, category: String(subscription.category_name)?.toLowerCase()} }"
                  class="flex flex-col sm:flex-row justify-between align-center gap-4 mt-6 p-4 bg-[#fff8f1] border !border-[#f7e3d7] !rounded-xl shadow-sm hover:from-white transition-all cursor-pointer"
                >
                  <div class="flex flex-col sm:flex-row justify-between align-center gap-5">
                    <div class="flex justify-center rounded-2xl w-16 h-16">
                      <img
                        loading="lazy"
                        :src="subscription.product_img"
                        class="self-center aspect-square w-16 h-16 rounded-2xl"
                        alt="product image"
                      />
                    </div>
                    <div class="text-center sm:!text-start">
                      <div class="text-xs font-medium text-gray-600 uppercase mb-1 tracking-wider">
                        {{ subscription.product_name }}
                      </div>
                      <div class="text-lg sm:text-2xl text-gray-800 tracking-tight">
                        Medication Overview
                      </div>
                    </div>
                  </div>
                  <div>
                    <IconChevronRight
                      class="w-6 h-6 text-zinc-500"
                      stroke-width="1.5"
                    />
                  </div>
                </RouterLink>
              </div>

              <div
                v-if="subscription.category_name?.toLowerCase() === 'wl' && !isEmpty(subscription.wl_attachments)"
                class="w-full rounded-[20px] bg-white p-5 my-4"
              >
                <div class="text-lg font-medium text-zinc-800 mb-3">
                  How to take
                </div>
                <div class="flex flex-wrap gap-4">
                  <a
                    v-for="(item, index) in subscription.wl_attachments"
                    :key="index"
                    :href="item.attachment"
                    class="flex-1 border !border-gray-200 !rounded-xl px-4 py-3 flex justify-between align-center hover:bg-gray-50 cursor-pointer"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <span class="text-gray-700 font-semibold">{{ item.title }}</span>
                    <IconExternalLink
                      class="size-5"
                      stroke-width="2"
                    />
                  </a>
                </div>
              </div>

              <div class="grid grid-cols-10 gap-5 mb-5">
                <div class="col-span-10 md:col-span-5 bg-white p-5 rounded-[20px]">
                  <div class="flex justify-between">
                    <div class="text-xl font-semibold mb-3">
                      Payment Method
                    </div>
                    <button
                      v-if="subscription.subscription_action_type !== 3 && subscription.subscription_action_type !== 5"
                      class="inline-flex items-center gap-1 border !border-black px-3 h-8 text-xs font-medium rounded-full hover:bg-gray-100"
                      @click="openUpdatePaymentModal(false)"
                    >
                      <IconPencil
                        class="h-[14px] w-4"
                        stroke-width="2"
                      />
                      <span>Change</span>
                    </button>
                  </div>
                  <div
                    v-if="subscription.last_four_digit_card && subscription.card_expiry_month && subscription.card_expiry_year"
                    class="space-y-1"
                  >
                    <div class="text-sm uppercase font-medium text-zinc-500">
                      {{ subscription.card_brand_type }}
                    </div>
                    <div class="text-zinc-700">
                      xxxx xxxx xxxx {{ subscription.last_four_digit_card }}
                    </div>
                    <div class="w-full text-sm">
                      <span
                        v-if="isCardExpired(subscription.card_expiry_year, subscription.card_expiry_month)"
                        class="bg-red-300 text-red-700 font-medium  px-2 py-0.5 rounded-full"
                      >Expired</span>
                      <span v-else>
                        Expires {{ subscription.card_expiry_month }}/{{ subscription.card_expiry_year }}
                      </span>
                    </div>
                  </div>
                  <div v-else>
                    -
                  </div>
                </div>
                <div class="col-span-10 md:col-span-5 bg-white p-5 rounded-[20px]">
                  <div class="flex justify-between">
                    <div class="text-xl font-semibold mb-3">
                      Shipping Details
                    </div>
                  </div>
                  <div>
                    <div
                      v-if="Boolean(subscription.is_shipment_method_change)"
                      class="mb-3"
                    >
                      <div class="text-xs uppercase font-medium text-zinc-500 mb-1 flex justify-between items-start">
                        <span>Shipping Method</span>
                        <button
                          v-if="subscription.subscription_action_type !== 3 && subscription.subscription_action_type !== 5"
                          class="text-gray-900 underline uppercase font-semibold"
                          @click="openUpdateShipmentMethodModal"
                        >
                          Change
                        </button>
                      </div>
                      <div class="text-zinc-700">
                        {{ subscription.pharmacy_shipment_method_title }}
                      </div>
                    </div>

                    <div>
                      <div class="text-xs uppercase font-medium text-zinc-500 mb-1 flex justify-between items-start">
                        <span>Shipping Address</span>
                        <button
                          v-if="subscription.subscription_action_type !== 3 && subscription.subscription_action_type !== 5"
                          class="text-gray-900 underline uppercase font-semibold"
                          @click="openUpdateShippingModal"
                        >
                          Edit
                        </button>
                      </div>

                      <div class="text-zinc-700">
                        {{ subscription.shipping_address?.address_line_1 }} <br>
                        <span v-if="subscription.shipping_address?.address_line_2">
                          {{ subscription.shipping_address?.address_line_2 }} <br>
                        </span>
                        {{ subscription.shipping_address?.city }}, {{ subscription.shipping_address?.state }}-{{ subscription.shipping_address?.zipcode }} <br>
                        {{ subscription.shipping_address?.country }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="bg-white p-5 rounded-[20px]">
                <div class="text-xl font-semibold mb-3">
                  Get Help
                </div>
                <div class="space-y-3">
                  <RouterLink
                    :to="{name: 'account-support'}"
                    class="border !border-gray-200 !rounded-xl px-4 py-3 flex justify-between align-center hover:bg-gray-50 cursor-pointer"
                  >
                    <div>
                      <h5 class="text-black font-medium text-sm sm:text-base">
                        Account & Order questions
                      </h5>
                      <p class="text-xs sm:text-sm text-gray-700">
                        Contact customer support
                      </p>
                    </div>
                    <div>
                      <IconChevronRight
                        class="h-5 w-5 text-gray-600"
                        stroke-width="2"
                      />
                    </div>
                  </RouterLink>
                  <RouterLink
                    v-if="subscription.subscription_action_type !== 3 && subscription.subscription_action_type !== 5"
                    class="border !border-gray-200 !rounded-xl px-4 py-3 flex justify-between align-center hover:bg-gray-50 cursor-pointer"
                    :to="{ name: 'user-manage-subscription-additional', params: { subscriptionId } }"
                  >
                    <!-- @click="additionalOptionsModalRef.openModal(subscription)" -->
                    <div>
                      <h5 class="text-black font-medium text-sm sm:text-base">
                        Additional Options
                      </h5>
                      <p class="text-xs sm:text-sm text-gray-700">
                        Edit upcoming orders
                      </p>
                    </div>
                    <div>
                      <IconChevronRight
                        class="h-5 w-5 text-gray-600"
                        stroke-width="2"
                      />
                    </div>
                  </RouterLink>
                </div>
              </div>
            </div>
          </div>

          <!-- Notifications View -->
          <div class="col-span-12 lg:col-span-4">
            <SubscriptionNotifications
              ref="notificationsRef"
              class="h-full"
              :subscription-id="subscriptionId"
              @update-payment-method="openUpdatePaymentModal(true)"
            />
          </div>
        </div>
      </div>
    </div>

    <SubscriptionUpdatePaymentModal
      ref="updatePaymentModalRef"
      @updated="fetchSubscriptionDetails"
    />

    <SubscriptionUpdateShippingModal
      ref="updateShippingModalRef"
      @updated="fetchSubscriptionDetails"
    />

    <SubscriptionUpdateShipmentMethodModal
      ref="updateShipmentMethodModalRef"
      @updated="fetchSubscriptionDetails"
    />

    <ModalOrderCancel
      ref="modalOrderCancelRef"
      :order-id="subscription.follow_up_visit_details?.refill_id || ''"
      is-follow-up-visit
      @canceled="modalOrderCancelReasonRef.open()"
    />

    <ModalOrderCancelReason
      ref="modalOrderCancelReasonRef"
      :order-id="subscription.follow_up_visit_details?.refill_id || ''"
      @updated="fetchSubscriptionDetails"
    />
  </div>
</template>
