<script setup>
import ApiService from '@/services/ApiService'
import { processErrors } from '@/utils/errorHandler'
import { toast } from 'vue-sonner'
import { useRoute } from 'vue-router'
import {
  IconCheck,
  IconChevronRight,
  IconHourglass,
  IconLoader2,
  IconWand,
  IconZzz,
} from '@tabler/icons-vue'
import { computed } from 'vue'
import router from '@/router'
import Header from '@/views/user/components/Header.vue'
import { isEmpty } from '@/@core/utils'
import { useSubscriptionStore } from '@/store/subscription'
import { storeToRefs } from 'pinia'

const emit = defineEmits(['updated'])

const route = useRoute()

const subscriptionStore = useSubscriptionStore()
const { btnReactivateLoading } = storeToRefs(subscriptionStore)
const { reactivateSubscription } = subscriptionStore

const SECTION = {
  OPTIONS: 'options',
  SNOOZE: 'snooze',
  PAUSE: 'pause',
  DATE_EXTENDED: 'date_extended',
  CANCEL: 'cancel',
  CANCEL_REASON: 'cancel_reason',
  SUB_CANCELED: 'sub_canceled',
}

const currentSection = ref(SECTION.OPTIONS)
const subscriptionId = computed(() => route.params.subscriptionId)
const skeletonLoading = ref(false)
const subscription = ref({})
const isLoading = ref(false)
const selectedPauseDate = ref(null)
const isExtendLoading = ref(null)
const extendResponse = ref(null)
const selectedCancelReason = ref([])
const isCancelReasonLoading = ref(false)

const cancelReasons = [
  'I have too many extra products',
  'It\'s too hard to manage my subscription',
  'I had side effects using the product',
  'I no longer want a subscription',
  'Took too long to receive package',
  'My subscription costs too much',
  'I don\'t need the product anymore',
  'I didn\'t get the results I wanted',
]

onMounted(async () => {
  setSection(SECTION.OPTIONS)
  await fetchSubscriptionDetails()
})

async function fetchSubscriptionDetails() {
  try {
    skeletonLoading.value = true

    const { data } = await ApiService.get(`/subscription-details/${subscriptionId.value}`)

    if (data.status === 200) {
      subscription.value = data.subscriptionDetails
    } else {
      toast.error(data.message)
    }
  } catch (error) {
    console.error(error)

    const errorMessage = processErrors(error)[0]

    toast.error(errorMessage)
  } finally {
    skeletonLoading.value = false
  }
}

function setSection(section) {
  currentSection.value = section

  // clear loading state
  isLoading.value = false
  isExtendLoading.value = false
  isCancelReasonLoading.value = false
}

async function handleSnoozeNextOrder() {
  try {
    isLoading.value = true

    const { data } = await ApiService.get(`/snooze-subscription/${subscriptionId.value}`)

    if (data.status === 200) {
      toast.success(data.message)
      router.push({ name: 'user-manage-subscription', params: { subscriptionId: subscriptionId.value } })
    } else {
      isLoading.value = false
      if (data.message) {
        toast.error(data.message)
      } else {
        toast.error(processErrors(data))
      }
    }
  } catch (error) {
    isLoading.value = false
    toast.error(processErrors(error))
  }
}

function onClickTreatmentComplete() {
  if (
    !isEmpty(subscription.value?.paused_date_details)
    && !isEmpty(subscription.value?.paused_date_details?.date_list)
  ) {
    setSection(SECTION.PAUSE)
  } else if (
    subscription.value.subscription_action_type !== 3
    && subscription.value.subscription_action_type !== 5
  ) {
    setSection(SECTION.CANCEL)
  }
}

async function handlePauseDateOptionClick() {
  if (selectedPauseDate.value === 'no_thanks') {
    setSection(SECTION.CANCEL)
  } else {
    await extendNextOrderDate()
  }
}

async function extendNextOrderDate() {
  try {
    isExtendLoading.value = true
    extendResponse.value = null

    const { data } = await ApiService.post('/extend-next-refill-date/', {
      subscription_id: subscriptionId.value,
      extend_date: selectedPauseDate.value,
    })

    if (data.status === 200) {
      extendResponse.value = data.subscriptionInfo
      setSection(SECTION.DATE_EXTENDED)
    } else {
      isExtendLoading.value = false
      if (data.message) {
        toast.error(data.message)
      } else {
        toast.error(processErrors(data))
      }
    }
  } catch (error) {
    isExtendLoading.value = false
    toast.error(processErrors(error))
  }
}

async function handleCancelSubscription() {
  try {
    isLoading.value = true

    const { data } = await ApiService.get(`/cancel-subscription/${subscriptionId.value}`)

    if (data.status === 200) {
      // toast.success(data.message)
      fetchSubscriptionDetails()
      setSection(SECTION.CANCEL_REASON)
    } else {
      isLoading.value = false
      if (data.message) {
        toast.error(data.message)
      } else {
        toast.error(processErrors(data))
      }
    }
  } catch (error) {
    isLoading.value = false
    toast.error(processErrors(error))
  }
}

async function handleCancelReasonSubmit() {
  try {
    isCancelReasonLoading.value = true

    const { data } = await ApiService.post('/update-cancel-subscription-reason', {
      subscription_id: subscriptionId.value,
      cancel_reason: selectedCancelReason.value,
    })

    if (data.status === 200) {
      // toast.success(data.message)
      setSection(SECTION.SUB_CANCELED)
    } else {
      isCancelReasonLoading.value = false
      if (data.message) {
        toast.error(data.message)
      } else {
        toast.error(processErrors(data))
      }
    }
  } catch (error) {
    isCancelReasonLoading.value = false
    toast.error(processErrors(error))
  }
}
</script>

<template>
  <div>
    <Header show-back-button />

    <div class="flex items-center justify-center py-10 sm:px-16">
      <div class="px-4 w-full sm:max-w-[480px]">
        <div v-if="skeletonLoading">
          <div class="text-center py-20">
            <IconLoader2 class="inline w-14 h-14 text-gray-800 animate-spin" />
          </div>
        </div>
        <div
          v-else
          class="flex flex-col items-center"
        >
          <div class="w-full">
            <!-- Additional Options -->
            <div v-if="currentSection === SECTION.OPTIONS">
              <div class="text-center mb-10">
                <h2 class="text-xl md:text-3xl font-bold leading-tight text-black mb-3">
                  Manage your Subscription
                </h2>
                <p class="text-sm">
                  Update your plan as per your needs
                </p>
              </div>

              <div class="space-y-3">
                <div
                  v-if="subscription.subscription_action_type === 3 || subscription.subscription_action_type === 5"
                  class="border !rounded-xl px-4 py-2 bg-amber-100 text-amber-700 text-center"
                >
                  <div class="font-medium text-sm">
                    <span v-if="subscription.status === 2">Your subscription is canceled</span>
                    <span v-if="subscription.status === 3">Your Rx is Expired</span>
                  </div>
                </div>
                <div
                  v-if="subscription.category_name !== 'WL' && subscription.status === 1 && subscription.is_subscription_snoozed === 0"
                  class="border !rounded-xl px-4 py-3 flex justify-between align-center !border-gray-200 hover:bg-gray-50 cursor-pointer"
                  @click="setSection(SECTION.SNOOZE)"
                >
                  <div>
                    <h5 class="text-black font-medium text-sm sm:text-base">
                      Take a break
                    </h5>
                    <p class="text-xs sm:text-sm text-gray-700">
                      <span v-if="subscription.is_subscription_snoozed === 1">Subscription already snoozed</span>
                      <span v-else-if="subscription.is_subscription_snoozed === 2">All refills are used</span>
                      <span v-else>Snooze your next order</span>
                    </p>
                  </div>
                  <div>
                    <IconChevronRight
                      class="h-5 w-5 text-gray-600"
                      stroke-width="2"
                    />
                  </div>
                </div>
                <div
                  v-if="subscription.subscription_action_type !== 3 && subscription.subscription_action_type !== 5"
                  class="border !rounded-xl px-4 py-3 flex justify-between align-center"
                  :class="[subscription.status === 2 ? 'bg-gray-100 !border-gray-100 cursor-not-allowed' : '!border-gray-200 hover:bg-gray-50 cursor-pointer']"
                  @click="onClickTreatmentComplete"
                >
                  <div>
                    <h5 class="text-black font-medium text-sm sm:text-base">
                      Treatment complete?
                    </h5>
                    <p class="text-xs sm:text-sm text-gray-700">
                      Cancel your Subscription
                    </p>
                  </div>
                  <div>
                    <IconChevronRight
                      class="h-5 w-5 text-gray-600"
                      stroke-width="2"
                    />
                  </div>
                </div>
              </div>

              <div class="inline-flex items-center justify-center w-full my-14 relative">
                <hr class="w-64 h-px bg-gray-200 border-0">
                <span class="absolute px-3 font-medium text-gray-900 -translate-x-1/2 bg-white left-1/2">or</span>
              </div>

              <RouterLink
                :to="{name: 'account-support'}"
                class="border !border-gray-200 !rounded-xl px-4 py-3 flex justify-between align-center hover:bg-gray-50 cursor-pointer"
              >
                <div>
                  <h5 class="text-black font-medium text-sm sm:text-base">
                    Account & Order questions
                  </h5>
                  <p class="text-xs sm:text-sm text-gray-700">
                    Contact customer support
                  </p>
                </div>
                <div>
                  <IconChevronRight
                    class="h-5 w-5 text-gray-600"
                    stroke-width="2"
                  />
                </div>
              </RouterLink>
            </div>

            <!-- Snooze next order -->
            <div v-if="currentSection === SECTION.SNOOZE">
              <div class="text-center py-6 px-4 border !border-gray-200 rounded-2xl">
                <IconZzz
                  class="h-16 w-16 mx-auto mb-3"
                  stroke-width="2"
                />

                <h5 class="text-xl md:text-3xl font-semibold leading-tight text-black mb-3">
                  Snooze next refill
                </h5>
                <p class="text-base text-gray-700 mb-6">
                  We will send your next refill on {{ subscription.next_refill_date_after_snooze }}.
                </p>
                <div class="flex justify-center gap-3">
                  <TwButton
                    :disabled="isLoading"
                    variant="secondary"
                    class="w-full"
                    @click="setSection(SECTION.OPTIONS)"
                  >
                    Cancel
                  </TwButton>
                  <TwButton
                    :loading="isLoading"
                    class="w-full"
                    @click="handleSnoozeNextOrder"
                  >
                    Confirm
                  </TwButton>
                </div>
              </div>
            </div>

            <!-- Pausing Subscription -->
            <div v-if="currentSection === SECTION.PAUSE">
              <div class="text-center mb-10">
                <h2 class="text-xl md:text-2xl font-bold leading-tight text-black mb-3">
                  Would you like to pause your subscription?
                </h2>
                <p class="text-sm">
                  Pausing your subscription will ensure you're able to receive future refills on your prescription.
                </p>
              </div>

              <ul class="grid w-full space-y-3">
                <li
                  v-for="item in subscription?.paused_date_details?.date_list"
                  :key="item.date"
                >
                  <input
                    :id="`date_option${item.date}`"
                    v-model="selectedPauseDate"
                    type="radio"
                    class="hidden peer"
                    :value="item.date"
                    :disabled="isExtendLoading"
                    required
                  />
                  <label
                    :for="`date_option${item.date}`"
                    class="inline-flex items-center justify-between w-full px-5 py-3 text-gray-700 bg-white border !border-gray-200 rounded-2xl cursor-pointer peer-checked:!border-gray-600 peer-checked:text-black hover:text-gray-800 hover:bg-gray-100"
                  >
                    <div class="block">
                      <div class="w-full text-lg font-medium">
                        Yes, pause for {{ item.days }} days
                      </div>
                      <div class="w-full text-sm">
                        Next order ships {{ item.human_date }}
                      </div>
                    </div>
                    <span class="mx-2 flex items-center justify-center">
                      <span class="w-6 h-6 flex justify-center items-center">
                        <IconChevronRight
                          class="h-5 w-5 z-50"
                          stroke-width="2"
                        />
                      </span>
                    </span>
                  </label>
                </li>

                <!-- No thanks ~ Proceed with cancel -->
                <li>
                  <input
                    id="no_thanks"
                    v-model="selectedPauseDate"
                    type="radio"
                    class="hidden peer"
                    value="no_thanks"
                    required
                  />
                  <label
                    for="no_thanks"
                    class="inline-flex items-center justify-between w-full px-5 py-3 text-gray-700 bg-white border !border-gray-200 rounded-2xl cursor-pointer peer-checked:!border-gray-600 peer-checked:text-black hover:text-gray-800 hover:bg-gray-100"
                    @click="setSection(SECTION.CANCEL)"
                  >
                    <div class="block">
                      <div class="w-full text-lg font-medium">
                        No thanks
                      </div>
                    </div>
                    <span class="mx-2 flex items-center justify-center">
                      <span class="w-6 h-6 flex justify-center items-center">
                        <IconChevronRight
                          class="h-5 w-5 z-50"
                          stroke-width="2"
                        />
                      </span>
                    </span>
                  </label>
                </li>
              </ul>

              <TwButton
                v-if="!isEmpty(selectedPauseDate) && selectedPauseDate !== 'no_thanks'"
                class="w-full mt-6"
                :loading="isExtendLoading"
                @click="handlePauseDateOptionClick"
              >
                Continue
              </TwButton>
            </div>

            <!-- Date Extended Success -->
            <div v-if="currentSection === SECTION.DATE_EXTENDED">
              <h2 class="text-xl md:text-2xl font-bold leading-tight text-black mb-6 text-center">
                Success! Here's your new refill date
              </h2>
              <div class="flex flex-col p-5 mt-6 w-full bg-white rounded-3xl border border-solid !border-zinc-300">
                <div class="justify-between py-1">
                  <div class="flex max-md:flex-col max-md:gap-0">
                    <div class="flex flex-col w-full max-md:ml-0 max-md:w-full">
                      <div class="flex flex-col gap-5 grow justify-between items-top text-sm leading-5 text-black">
                        <div class="mb-2 bg-gray-100 rounded-lg h-48 grid place-items-center">
                          <img
                            :src="extendResponse.product_img"
                            class="h-36 w-36 object-contain rounded-lg"
                            alt=""
                          >
                        </div>
                        <div class="grow">
                          <div class="text-2xl font-semibold tracking-tight leading-5 mb-3 text-center">
                            {{ extendResponse.product_name }}
                          </div>
                          <div class="text-zinc-600 mb-4 text-center">
                            {{ extendResponse.supply_interval }}-month supply of {{ extendResponse.total_qty }}, {{ extendResponse.strength }} doses
                          </div>

                          <hr>

                          <div class="flex justify-between mt-4">
                            <div class="font-medium text-gray-600">
                              Old Refill date
                            </div>
                            <div class="font-medium text-black">
                              {{ extendResponse.old_refill_date }}
                            </div>
                          </div>
                          <div class="flex justify-between mt-2">
                            <div class="font-medium text-gray-600">
                              New Refill date
                            </div>
                            <div class="font-medium text-black">
                              {{ extendResponse.new_refill_date }}
                            </div>
                          </div>
                          <div class="text-center text-[13px] bg-gray-100 mt-5 px-5 py-3 rounded-lg">
                            Any orders that have already been processed will
                            continue to ship. To change your refill date, request
                            it from your Subscriptions page.
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <TwButton
                type="button"
                class="mt-10 w-full"
                @click="() => {
                  router.push({ name: 'user-manage-subscription', params: { subscriptionId } })
                }"
              >
                Back to Subscription
              </TwButton>
            </div>

            <!-- Cancel Subscription -->
            <div v-if="currentSection === SECTION.CANCEL">
              <div class="text-center mb-6">
                <h2 class="text-xl md:text-2xl font-bold leading-tight text-black mb-3">
                  What to expect after canceling your subscription?
                </h2>
                <p class="text-sm">
                  Changed your mind?
                  <RouterLink
                    :to="{ name: 'user-manage-subscription', params: { subscriptionId } }"
                    class="font-medium text-black underline"
                  >
                    Back to Subscription
                  </RouterLink>
                </p>
              </div>

              <div class="flex flex-col p-5 w-full bg-white rounded-3xl border border-solid !border-zinc-300">
                <div class="justify-between py-1">
                  <div class="flex max-md:flex-col max-md:gap-0">
                    <div class="flex flex-col w-full max-md:ml-0 max-md:w-full">
                      <div class="flex flex-col gap-5 grow justify-between items-top text-sm leading-5 text-black">
                        <div class="mb-2 bg-gray-100 rounded-lg h-48 grid place-items-center">
                          <img
                            :src="subscription.product_img"
                            class="h-36 w-36 object-contain rounded-lg"
                            alt=""
                          >
                        </div>
                        <div class="grow">
                          <div class="text-xl font-semibold tracking-tight leading-5 text-center mb-2">
                            {{ subscription.product_name }}
                          </div>
                          <div
                            v-if="subscription.category_name === 'WL'"
                            class="text-zinc-500 text-sm text-center mb-4"
                          >
                            <span>
                              {{ subscription.strength }} {{ subscription.strength_unit }}/weekly
                            </span>
                          </div>
                          <div
                            v-else
                            class="text-zinc-500 text-sm text-center mb-4"
                          >
                            <span>
                              {{ subscription.strength }} {{ subscription.strength_unit }} x {{ subscription.qty * subscription.subscription_interval }} units
                            </span>
                            <span>({{ subscription.subscription_interval * 30 }}-day supply)</span>
                          </div>

                          <hr>

                          <div class="flex gap-4 bg-gray-100 mt-5 p-3 !rounded-xl">
                            <div class="h-8 w-8">
                              <IconHourglass
                                class="h-7 w-7"
                                stroke-width="1.5"
                              />
                            </div>
                            <div class="flex-1">
                              <div class="text-base font-medium text-gray-800">
                                Subscription ends
                              </div>
                              <div class="text-[13px] font-normal text-gray-600">
                                Once you confirm, your plan will end on the last day of your current subscription period.
                              </div>
                            </div>
                          </div>
                          <div class="flex gap-4 bg-gray-100 mt-3 p-3 !rounded-xl">
                            <div class="h-8 w-8">
                              <IconWand
                                class="h-7 w-7"
                                stroke-width="1.5"
                              />
                            </div>
                            <div class="flex-1">
                              <div class="text-base font-medium text-gray-800">
                                Easy reactivation before {{ subscription.prescription_expired_date }}
                              </div>
                              <div class="text-[13px] font-normal text-gray-600">
                                If your prescription's still active, reactive any time from the Subscriptions page.
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="flex justify-center gap-3 py-10">
                <TwButton
                  :disabled="isLoading"
                  variant="secondary"
                  class="w-full"
                  @click="setSection(SECTION.OPTIONS)"
                >
                  Discard
                </TwButton>
                <TwButton
                  :loading="isLoading"
                  class="w-full"
                  @click="handleCancelSubscription"
                >
                  Confirm Cancel
                </TwButton>
              </div>
            </div>

            <!-- Cancelation reason -->
            <div v-if="currentSection === SECTION.CANCEL_REASON">
              <div class="text-center mb-10">
                <h2 class="text-xl md:text-2xl font-bold leading-tight text-black mb-3">
                  Your subscription has been canceled
                </h2>
                <p class="text-sm">
                  Can you tell us why you canceled? Select all that apply
                </p>
              </div>

              <ul class="w-full mt-8 space-y-5">
                <li
                  v-for="(reason, index) in cancelReasons"
                  :key="reason"
                >
                  <input
                    :id="`cancel_reason_${index}`"
                    v-model="selectedCancelReason"
                    name="cancel_reason[]"
                    :value="reason"
                    class="hidden peer"
                    type="checkbox"
                  />
                  <label
                    :for="`cancel_reason_${index}`"
                    class="inline-flex items-center justify-between w-full px-5 py-3 text-gray-700 bg-white border !border-gray-200 rounded-lg cursor-pointer peer-checked:!border-gray-500 peer-checked:text-black hover:bg-gray-100"
                  >
                    <span class="block text-start">
                      <span class="w-full text-base font-medium">{{ reason }}</span>
                    </span>
                    <span class="mx-2 flex items-center justify-center">
                      <span
                        class="w-4 h-4 border-2 rounded-sm flex justify-center items-center"
                        :class="[
                          selectedCancelReason.includes(reason) ? 'border-transparent bg-black' : 'border-gray-400'
                        ]"
                      >
                        <IconCheck
                          class="h-5 w-5 z-50"
                          :stroke-width="selectedCancelReason.includes(reason) ? 4 : 1"
                          :class="[
                            selectedCancelReason.includes(reason) ? 'text-white' : 'text-gray-900'
                          ]"
                        />
                      </span>
                    </span>
                  </label>
                </li>
              </ul>

              <TwButton
                class="w-full mt-6"
                :loading="isCancelReasonLoading"
                :disabled="isEmpty(selectedCancelReason)"
                @click="handleCancelReasonSubmit"
              >
                Continue
              </TwButton>
            </div>

            <!-- Subscription Canceled Success -->
            <div v-if="currentSection === SECTION.SUB_CANCELED">
              <div class="text-center mb-6">
                <h2 class="text-xl md:text-2xl font-bold leading-tight text-black mb-3">
                  Your subscription has been canceled for the following medication
                </h2>
              </div>

              <div class="flex flex-col p-5 w-full bg-white rounded-3xl border border-solid !border-zinc-300">
                <div class="justify-between py-1">
                  <div class="flex max-md:flex-col max-md:gap-0">
                    <div class="flex flex-col w-full max-md:ml-0 max-md:w-full">
                      <div class="flex flex-col gap-5 grow justify-between items-top text-sm leading-5 text-black">
                        <div class="mb-2 bg-gray-100 rounded-lg h-48 grid place-items-center">
                          <img
                            :src="subscription.product_img"
                            class="h-36 w-36 object-contain rounded-lg"
                            alt=""
                          >
                        </div>
                        <div class="grow">
                          <div class="text-xl font-semibold tracking-tight leading-5 text-center mb-2">
                            {{ subscription.product_name }}
                          </div>
                          <div
                            v-if="subscription.category_name === 'WL'"
                            class="text-zinc-500 text-sm text-center"
                          >
                            {{ subscription.strength }} {{ subscription.strength_unit }}/weekly
                          </div>
                          <div
                            v-else
                            class="text-zinc-500 text-sm text-center"
                          >
                            <span>
                              {{ subscription.strength }} {{ subscription.strength_unit }} x {{ subscription.qty * subscription.subscription_interval }} units
                            </span>
                            <span>({{ subscription.subscription_interval * 30 }}-day supply)</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="flex justify-center gap-3 py-10">
                <TwButton
                  class="w-full"
                  variant="secondary"
                  :disabled="btnReactivateLoading"
                  @click="$router.push({ name: 'user-subscription' })"
                >
                  Back to Account
                </TwButton>
                <TwButton
                  v-if="!isEmpty(subscription.is_reactive_canceled_subscription) && subscription.is_reactive_canceled_subscription === 1"
                  :loading="btnReactivateLoading"
                  class="w-full"
                  @click="reactivateSubscription(subscriptionId)"
                >
                  Reactivate Subscription
                </TwButton>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss">
@import '@/assets/user/css/style.css';
</style>

<style lang="scss">
.p-inputtext {
  border: none !important;
  border-color: transparent;
  font-family: "Konnect", system-ui, -apple-system, BlinkMacSystemFont,
    "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue",
    sans-serif;
    font-size: 14px !important;
    font-weight: 500;

  &:enabled:hover {
    border-color: transparent;
  }
  &:enabled:focus {
    outline: none;
    outline-offset: 0;
    box-shadow: none;
    border-color: transparent;
  }
}

.p-autocomplete-panel {
  max-width: 400px;
}
.p-autocomplete-panel .p-autocomplete-items .p-autocomplete-item {
  white-space: pre-line;
}
.p-autocomplete-panel {
  z-index: 2102 !important;
}
</style>
