<script setup>
import { onMounted, ref } from 'vue'
import ApiService from '@/services/ApiService'
import Header from '@/views/user/components/Header.vue'
import { toast } from 'vue-sonner'
import { processErrors } from '@/utils/errorHandler'
import router from '@/router'
import { useRoute } from 'vue-router'

const route = useRoute()
const productId = computed(() => route.params.productId)
const productCategory = computed(() => route.params.category)
const overview = ref(null)
const skeletonLoading = ref(true)

onMounted(async () => {
  await fetchOverview()
})

async function fetchOverview() {
  try {
    skeletonLoading.value = true

    const { data } = await ApiService.get(`/product-instruction/${productId.value}/${productCategory.value}`)

    if (data.status === 200) {
      overview.value = data.productDetail
    } else {
      if (data.message) {
        toast.error(data.message)
      } else {
        toast.error(processErrors(data))
      }
      router.go(-1)
    }
  } catch (error) {
    console.error(error)
    router.go(-1)
  } finally {
    skeletonLoading.value = false
  }
}
</script>

<template>
  <div>
    <Header show-back-button />

    <div class="flex items-center justify-center py-10 sm:px-16">
      <div class="px-4 w-full sm:max-w-screen-md">
        <!-- Loading -->
        <div
          v-if="skeletonLoading"
          class="flex flex-col items-center animate-pulse"
        >
          <div class="w-full bg-gray-200 grid place-items-center rounded-[20px] mb-10 h-[250px]"></div>
          <div class="space-y-3 w-full">
            <div class="bg-gray-300 h-4 w-40 rounded-md"></div>
            <div class="bg-gray-300 h-6 w-full sm:w-60 rounded-md"></div>
            <div class="bg-gray-300 h-4 w-full rounded-md"></div>
            <div class="bg-gray-300 h-4 w-full rounded-md"></div>
            <div class="bg-gray-300 h-4 w-full sm:w-96 rounded-md"></div>
          </div>
        </div>

        <!-- Content -->
        <div
          v-else
          class="flex flex-col items-center"
        >
          <div class="w-full bg-gray-100 grid place-items-center rounded-[20px] mb-10 py-10">
            <img
              :src="overview?.product_image"
              class="h-48 w-48 rounded-lg object-cover"
              alt="product image"
            >
          </div>
          <article
            class="prose md:prose-lg prose-p:mb-2 prose-p:mt-2"
            v-html="(overview?.product_instruction).replace(/<p><\/p>/g, '<br>')"
          ></article>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss">
@import '@/assets/user/css/style.css';
</style>
