<script setup>
import { isEmpty, isEmptyObject } from '@/@core/utils'
import router from '@/router'
import ApiService from '@/services/ApiService'
import { useSessionStorage } from '@vueuse/core'
import { onMounted, onUnmounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import Header from '../components/Header.vue'
import { formatCurrency } from '@/utils/helpers'
import { IconHelpCircle } from '@tabler/icons-vue'
import Sidebar from 'primevue/sidebar'
import { useWLQuestions } from '@/store/wlQuestions'
import useLogOrderSession from '@/composables/useLogOrderSession'

const route = useRoute()
const wlVisitSession = useSessionStorage('wlVisitSession', {})
const { logSession } = useLogOrderSession()
const products = ref([])
const selectedProduct = ref({})
const isLoading = ref(false)
const btnLoading = ref(null)
const productInfoSidebarVisible = ref(false)
const questionsStore = useWLQuestions()
const { isNavigatingBack } = storeToRefs(questionsStore)

const currentProductInfo = ref({
  name: '',
  description: '',
})

onMounted(async () => {
  await fetchRecommendedProducts()

  window.addEventListener('popstate', () => {
    isNavigatingBack.value = true
  })
})

onUnmounted(() => {
  window.removeEventListener('popstate', () => {})
})

async function fetchRecommendedProducts() {
  try {
    isLoading.value = true

    const { data } = await ApiService.post('/wl-follow-up-products', {
      qa_id: wlVisitSession.value['questionSessionId'],
      subscription_id: wlVisitSession.value['subscriptionId'],
    })

    if (data.status === 200) {
      if (isEmpty(data.productData)) {
        return router.push({ name: 'wl-treatment-not-available' })
      }
      products.value = data.productData
      selectedProduct.value = products.value[0]
    } else {
      if (data.status === 400 && data.treatment_not_available) {
        router.push({ name: 'wl-treatment-not-available' })
      } else {
        console.error(data)
        router.replace({ name: 'error-something-wrong', query: { visit: 'wl' } })
      }
    }
  } catch (error) {
    console.log(error)
    router.replace({ name: 'error-something-wrong', query: { visit: 'wl' } })
  } finally {
    isLoading.value = false
  }
}

function showProductInfo(product) {
  currentProductInfo.value = {
    name: product.product_name,
    description: product.product_description,
  }
  productInfoSidebarVisible.value = true
}

async function handleContinue() {
  btnLoading.value = true

  wlVisitSession.value['productId'] = selectedProduct.value?.id
  wlVisitSession.value['strengthId'] = selectedProduct.value?.wl_product_strength?.id
  wlVisitSession.value['planId'] = selectedProduct.value?.wl_product_strength?.wl_product_subscription_plan?.id

  await router.push({
    name: 'visit-shipping',
    params: { visitType: 'wl-followup' },
    query: { ...route.query },
  })

  logSession({
    id: wlVisitSession.value['questionSessionId'],
    category: 'WL',
    product: {
      id: wlVisitSession.value['productId'] ?? null,
      strength_id: wlVisitSession.value['strengthId'] ?? null,
      plan_id: wlVisitSession.value['planId'] ?? null,
    },
  })
}
</script>

<template>
  <div>
    <Header show-back-button />

    <div class="flex items-center justify-center py-10 sm:px-16">
      <div class="px-4 w-full sm:max-w-[480px]">
        <div
          v-if="isLoading"
          class="h-7 bg-gray-300 rounded mb-2 animate-pulse"
        ></div>
        <div
          v-else
          class="mb-3"
        >
          <h2 class="text-xl md:text-3xl font-bold leading-tight text-black">
            Choose your plan
          </h2>
          <p class="font-normal text-gray-700 mt-2">
            <span class="text-red-500 font-medium uppercase">IMPORTANT:</span> Please review and select the plan best for you.
          </p>
          <p class="text-gray-700 mt-4 font-semibold">
            *Any promotional offers will be applied on the final check-out page*
          </p>
        </div>

        <div
          v-if="isLoading"
          class="flex flex-col items-center"
        >
          <div class="flex flex-col p-5 mt-6 w-full bg-white rounded-3xl border border-solid !border-zinc-300">
            <!-- Product Info Skeleton Loader -->
            <div class="animate-pulse">
              <div class="justify-between py-1">
                <div class="flex gap-5 max-md:flex-col max-md:gap-0">
                  <div class="flex flex-col w-full max-md:ml-0 max-md:w-full">
                    <div class="flex flex-col-reverse sm:flex-row gap-5 grow justify-between items-top text-sm leading-5 text-black">
                      <div class="grow">
                        <div class="h-6 w-4/5 bg-gray-300 rounded mb-2"></div> <!-- Title Skeleton Loader -->
                        <div class="h-5 w-36 bg-gray-300 rounded mb-2"></div> <!-- Price Skeleton Loader -->
                        <div class="h-3 w-40 bg-gray-300 rounded mb-2"></div> <!-- Description Skeleton Loader -->
                        <div class="h-3 w-32 bg-gray-300 rounded"></div> <!-- Description Skeleton Loader -->
                      </div>
                      <div>
                        <div class="min-h-24 min-w-24 w-full sm:h-24 sm:w-24 bg-gray-300 rounded-lg"></div> <!-- Image Skeleton Loader -->
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="mt-5 animate-pulse">
              <div class="h-10 w-full bg-gray-300 rounded"></div>
            </div>
          </div>
        </div>

        <div
          v-else
          class="flex flex-col items-center mt-8"
        >
          <ul class="grid w-full space-y-5 mb-10">
            <li
              v-for="product in products"
              :key="product.id"
            >
              <input
                :id="`product_${product.id}`"
                v-model="selectedProduct"
                type="radio"
                name="product"
                class="hidden peer"
                :value="product"
                required
              />
              <label
                :for="`product_${product.id}`"
                class="inline-flex items-center justify-between w-full p-4 text-gray-700 bg-white border !border-gray-200 rounded-2xl cursor-pointer peer-checked:!border-gray-500 peer-checked:bg-gray-50 peer-checked:text-black hover:text-gray-800 hover:bg-gray-100"
              >
                <div class="block w-full">
                  <div>
                    <div class="bg-gray-100 !rounded-xl p-4">
                      <img
                        :src="product.product_image"
                        class="size-40 mx-auto"
                        :alt="product.product_name"
                      >
                    </div>
                    <div class="mt-4">
                      <div class="w-full flex">
                        <div class="text-lg font-medium">{{ product.product_name }}</div>
                        <button
                          type="button"
                          class="ms-2 cursor-pointer"
                          @click="showProductInfo(product)"
                        >
                          <IconHelpCircle
                            class="size-5 cursor-pointer text-gray-600"
                            stroke-width="2"
                          />
                        </button>
                      </div>
                      <div class="w-full flex">
                        <span>{{ product.wl_product_strength?.strength }}{{ product.wl_product_strength?.strength_unit }} / weekly <span v-if="product.wl_product_strength?.drug_month">({{ `Month ${product.wl_product_strength?.drug_month}` }})</span></span>
                      </div>
                      <div
                        v-if="product.wl_product_strength?.wl_product_subscription_plan?.subscription_plan_price_after_discount > 0"
                        class="w-full space-x-1 mt-1"
                      >
                        <span class="text-lg font-semibold">{{ formatCurrency(product.wl_product_strength?.wl_product_subscription_plan?.subscription_plan_price_after_discount) }}</span>
                        <span
                          v-if="product.wl_product_strength?.wl_product_subscription_plan?.plan_interval_price !== product.wl_product_strength?.wl_product_subscription_plan?.subscription_plan_price_after_discount"
                          class="text-sm text-gray-600 line-through"
                        >{{ formatCurrency(product.wl_product_strength?.wl_product_subscription_plan?.plan_interval_price) }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </label>
            </li>
          </ul>

          <TwButton
            v-if="!isEmptyObject(selectedProduct)"
            class="mt-6 w-full"
            :loading="btnLoading"
            @click="handleContinue"
          >
            Continue
          </TwButton>
        </div>
      </div>
    </div>

    <!-- 👉 product description sidebar -->
    <Sidebar
      v-model:visible="productInfoSidebarVisible"
      position="right"
      show-close-icon
      class="product-description"
      @hide="productInfoSidebarVisible = false"
    >
      <div class="w-full">
        <h2 class="text-xl font-semibold leading-tight text-black mb-3">
          {{ currentProductInfo.name }}
        </h2>
        <div
          class="w-full prose mb-10"
          v-html="currentProductInfo.description"
        ></div>
        <TwButton
          class="w-full"
          @click="productInfoSidebarVisible = false"
        >
          Got it
        </TwButton>
      </div>
    </Sidebar>
  </div>
</template>

<style lang="scss">
.p-sidebar-right .product-description.p-sidebar {
  width: 30rem;
  font-family: "Konnect", system-ui, -apple-system, BlinkMacSystemFont,
    "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue",
    sans-serif;
}
</style>
