<script setup>
import router from '@/router'
import { clearOrderSession } from '@/utils/sessionHelpers'
import Header from '@/views/user/components/Header.vue'
import { IconMoodSad } from '@tabler/icons-vue'
import { computed } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()

onMounted(async () => {
  clearOrderSession()
})

const btnLoading = ref(false)

const btnAction = () => {
  btnLoading.value = true
  router.push({ name: 'user-subscription' })
}
</script>

<template>
  <div>
    <Header />
    <div class="grid place-items-center">
      <div class="w-full sm:max-w-lg grid place-items-center h-[calc(100vh-15rem)]">
        <div class="px-4 py-12 sm:px-20 flex flex-col items-center">
          <IconMoodSad
            class="w-28 mb-4 text-gray-800"
            stroke-width="1.5"
          />
          <div class="mb-6 text-center">
            <h1 class="text-2xl sm:text-3xl text-gray-800 font-semibold mb-4">
              Out of Stock
            </h1>
            <p class="text-base text-gray-600">
              Sorry, we don't have any weight loss products available right now. Please try again later.
            </p>
          </div>
          <TwButton
            class="w-auto px-6"
            :loading="btnLoading"
            @click="btnAction"
          >
            <span>Back to Home</span>
          </TwButton>
        </div>
      </div>
    </div>
  </div>
</template>
