<script setup>
import { isEmpty, isEmptyObject } from '@/@core/utils'
import router from '@/router'
import ApiService from '@/services/ApiService'
import { processErrors } from '@/utils/errorHandler'
import { formatCurrency, scrollToTop } from '@/utils/helpers'
import { clearOrderSession } from '@/utils/sessionHelpers'
import AlertError from '@/views/user/components/AlertError.vue'
import Header from '@/views/user/components/Header.vue'
import { loadScript } from '@paypal/paypal-js'
import { IconCheck, IconCreditCardOff, IconInfoCircle, IconShieldLock, IconVideo } from '@tabler/icons-vue'
import { useSessionStorage } from '@vueuse/core'
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { toast } from 'vue-sonner'
import { useWLQuestions } from '@/store/wlQuestions'
import useGlobalSettings from '@/composables/useGlobalSettings'
import ConsultFeeSidebar from '@/views/user/components/ConsultFeeSidebar.vue'
import useDocuments from '@/composables/useDocuments'

// import PaymentRefundSidebar from '@/views/user/components/PaymentRefundSidebar.vue'

const $cookies = inject('$cookies')
const route = useRoute()
const questionsStore = useWLQuestions()
const { documentUploadStage } = useGlobalSettings()
const { isIdUploaded } = useDocuments()

const wlVisitSession = useSessionStorage('wlVisitSession', {})
const isLoading = ref(false)
const skeletonLoading = ref(false)
const planLoading = ref(false)
const serverErrors = ref([])
const isCardFormVisible = ref(false)

// const paymentAndRefundSidebarVisible = ref(false)
const consultFeeSidebarVisible = ref(false)
const checkoutSummary = ref({})
const paymentMethods = ref([])
const selectedPaymentMethod = ref(null)
const promoCode = useSessionStorage('wlCheckoutPromoCode', null)
const showPromoInput = ref(false)
const promoCodeError = ref('')
const isPromoAdded = ref(false)
const promoCodeInputRef = ref(null)
const retryOrderId = useSessionStorage('wlRetryOrderId', null)
const retryOrderSummary = ref(null)
const showPaymentMethods = ref(true)
const showBackButton = ref(true)

onMounted(async () => {
  skeletonLoading.value = true

  if (isEmpty(retryOrderId.value) && isEmptyObject(wlVisitSession.value)) {
    router.replace({ name: 'wl-products', query: { ...route.query } })
  }

  if (retryOrderId.value) {
    preventBackNavigation()
    await getFailedOrderSummary()
  } else {
    await preCheckout()
  }

  skeletonLoading.value = false
})

watch(promoCode, () => {
  promoCode.value = promoCode.value?.toUpperCase()
})

function applyPromoCode() {
  promoCodeError.value = ''

  if (isEmpty(promoCode.value)) {
    promoCodeError.value = 'Please enter the promo code'
  } else {
    preCheckout()
  }
}

function discardPromoApply() {
  showPromoInput.value = false
  promoCode.value = null
  promoCodeError.value = ''
}

function removePromoCode() {
  promoCode.value = null
  isPromoAdded.value = false
  preCheckout()
}

async function preCheckout() {
  try {
    planLoading.value = true
    serverErrors.value = []

    const postData = {
      qa_id: wlVisitSession.value['questionSessionId'],
      wl_product_id: wlVisitSession.value['productId'],
      subscription_plan_id: wlVisitSession.value['planId'],
    }

    if (!isEmpty(promoCode.value)) {
      postData['promocode'] = promoCode.value
    }

    const { data } = await ApiService.post('/wl-pre-checkout', postData)

    if (data.status === 200) {
      checkoutSummary.value = data.planSummary

      if (
        !isEmpty(data.planSummary.is_promocode_invalid)
        && data.planSummary.is_promocode_invalid === 1
      ) {
        promoCode.value = null
        promoCodeError.value = data.planSummary.invalid_promocode_msg
        isPromoAdded.value = false
      } else if (!isEmpty(data.planSummary.promocode)) {
        promoCodeError.value = ''
        showPromoInput.value = false
        isPromoAdded.value = true
      } else {
        promoCode.value = null
        promoCodeError.value = ''
        isPromoAdded.value = false
      }

      if (!isEmpty(data.paymentMethods)) {
        paymentMethods.value = data.paymentMethods

        const defaultPaymentMethod = paymentMethods.value.find(pm => pm.is_default === 1 && !isCardExpired(pm))
        if (defaultPaymentMethod) {
          selectedPaymentMethod.value = defaultPaymentMethod.id
        } else {
          const firstPaymentMethod = paymentMethods.value[0]
          if (!isCardExpired(firstPaymentMethod)) {
            selectedPaymentMethod.value = firstPaymentMethod.id
          }
        }
      }
    } else {
      if (data.status === 400) {
        if (Boolean(data.treatment_not_available)) {
          return router.push({ name: 'wl-treatment-not-available' })
        } else if (Boolean(data.user_has_an_active_plan)) {
          return router.push({ name: 'wl-visit-exists' })
        }
      }
      serverErrors.value = processErrors(data)
    }
  } catch (error) {
    serverErrors.value = processErrors(error)
    console.error(error)
  } finally {
    planLoading.value = false
  }
}

async function getFailedOrderSummary() {
  try {
    showPaymentMethods.value = false
    serverErrors.value = []

    const { data } = await ApiService.get(`/wl-failed-payment-order-summary/${retryOrderId.value}`)

    if (data.status === 200) {
      retryOrderSummary.value = data.orderSummary

      if (!isEmpty(data.paymentMethods)) {
        paymentMethods.value = data.paymentMethods

        const defaultPaymentMethod = paymentMethods.value.find(pm => pm.is_default === 1 && !isCardExpired(pm))
        if (defaultPaymentMethod) {
          selectedPaymentMethod.value = defaultPaymentMethod.id
        } else {
          const firstPaymentMethod = paymentMethods.value[0]
          if (!isCardExpired(firstPaymentMethod)) {
            selectedPaymentMethod.value = firstPaymentMethod.id
          }
        }
      }
    } else {
      serverErrors.value = processErrors(data)
    }
  } catch (error) {
    serverErrors.value = processErrors(error)
    console.error(error)
  }
}

const percentageSaved = computed(() => {
  const totalPrice = checkoutSummary.value?.plan_details?.plan_interval_price
  const discountedPrice = checkoutSummary.value?.subscription_plan_amount

  if (checkoutSummary.value.subscription_plan_amount < totalPrice) {
    const amountSaved = totalPrice - discountedPrice
    const percentage = (amountSaved / totalPrice) * 100

    return `You're saving ${percentage.toFixed(0)}%`
  } else {
    return null
  }
})

const cardFieldErrors = ref([])
const cardFieldsRendered = ref(false)

async function initPaypal() {
  isCardFormVisible.value = true
  selectedPaymentMethod.value = null

  if (cardFieldsRendered.value) return
  isLoading.value = true

  const paypal = await loadScript({
    'client-id': (import.meta.env.VITE_PAYPAL_CLIENT_ID),
    components: 'card-fields',
  }).catch(error => {
    console.error('paypal error :::', error)
  })

  const inputStyles = {
    'input': {
      'padding': '0.75rem 1.25rem',
      'font-size': '1rem',
      'font-weight': '500',
      'font-family': 'Konnect, system-ui, sans-serif',
      'appearance': 'none',
      'outline': 'none',
      'transition': 'border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out',
    },
  }

  const cardFields = paypal.CardFields({
    style: inputStyles,
    createVaultSetupToken: async () => {
      const { data } = await ApiService.get('/paypal/vault/token/create')
      if (data.status === 200) return data.vaultId
      else throw data
    },
    onApprove: async data => {
      const vaultId = data.vaultSetupToken
      const response = await ApiService.get(`/paypal/vault/payment/token/create/${vaultId}`)
      if (response.data.status === 200) {
        selectedPaymentMethod.value = response.data.id
        await placeOrder()
      } else {
        throw response.data
      }
    },
    onError: async error => {
      // console.log(error)
      if (!retryOrderId.value && !retryOrderSummary.value) {
        // when not retry call then calling place order API on card failure
        await placeOrder()
      } else {
        // if card failure is on retry flow then show error message
        if (error.message === 'Internal server error') {
          cardFieldErrors.value.push('Please check your card details and try again.')
        } else {
          cardFieldErrors.value.push(error.message || 'Something went wrong. Please try again.')
        }
      }
    },
  })

  if (cardFields.isEligible()) {
    // cardFields.NameField().render("#card-holder-name")
    cardFields.NumberField().render('#card-number')
    cardFields.ExpiryField().render('#expiration-date')
    cardFields.CVVField().render('#cvv')

    isLoading.value = false
    cardFieldsRendered.value = true
  } else {
    // Handle the workflow when credit and debit cards are not available
    toast.error('Credit and debit cards are not available. Please try again later.')
  }

  const submitButton = document.getElementById('checkoutSubmitBtn')

  submitButton.addEventListener('click', () => {
    isLoading.value = true
    serverErrors.value = []
    cardFieldErrors.value = []

    cardFields
      .submit()
      .then(() => {
        // console.log("submit was successful")
      })
      .catch(error => {
        isLoading.value = false
        if (error.message === 'INVALID_NUMBER') {
          cardFieldErrors.value.push('Please enter a valid card number.')
        }
        if (error.message === 'INVALID_CVV') {
          cardFieldErrors.value.push('Please enter a valid CVV number.')
        }
        if (error.message === 'INVALID_EXPIRY') {
          cardFieldErrors.value.push('Please enter a valid expiration date.')
        }
      })
  })
}

function selectExistingPaymentMethod() {
  isCardFormVisible.value = false
  cardFieldErrors.value = []
  if (paymentMethods.value.length > 0) {
    selectedPaymentMethod.value = paymentMethods.value[0].id
  }
}

async function placeOrder() {
  try {
    isLoading.value = true
    serverErrors.value = []

    const postData = {}
    let placeOrderUrl = '/wl-place-order'

    if (retryOrderId.value) {
      // payment has failed once so only send this data to server
      postData['refill_id'] = retryOrderSummary.value.refill_id
      postData['paypal_card_id'] = selectedPaymentMethod.value

      // change api url on retry failed order
      placeOrderUrl = '/retry-wl-payment-order'
    } else {
      // new order postData
      postData['qa_id'] = wlVisitSession.value['questionSessionId']
      postData['wl_product_id'] = wlVisitSession.value['productId']
      postData['paypal_card_id'] = selectedPaymentMethod.value
      postData['subscription_plan_id'] = wlVisitSession.value['planId']

      if (!isEmpty(promoCode.value)) {
        postData['promocode'] = promoCode.value
      }

      if ($cookies.isKey('wl_affiliate_code')) {
        postData['affiliate_reference_code'] = $cookies.get('wl_affiliate_code')
      }
    }

    const { data } = await ApiService.post(placeOrderUrl, postData)

    if (data.status === 200) {
      questionsStore.resetQuestions()

      if (documentUploadStage.value === 'before') {
        if (data.is_sync_visit) {
          router.push({
            name: 'schedule-video-visit',
            params: { visitType: 'wl', orderId: data.orderId },
            query: { ...route.query },
          })
        } else {
          router.push({
            name: 'wl-visit-order-success',
            params: { orderId: data.orderId },
            query: { ...route.query },
          })
        }
      } else {
        if (isIdUploaded.value) {
          if (data.is_sync_visit) {
            router.push({
              name: 'schedule-video-visit',
              params: { visitType: 'wl', orderId: data.orderId },
              query: { ...route.query },
            })
          } else {
            router.push({
              name: 'wl-visit-order-success',
              params: { orderId: data.orderId },
              query: { ...route.query },
            })
          }
        } else {
          router.push({
            name: 'visit-purchase-success',
            params: { visitType: 'wl' },
            query: { ...route.query, order_id: data.orderId, is_sync_visit: data.is_sync_visit },
          })
        }
      }
    } else {
      if (data.status === 400) {
        if (Boolean(data.treatment_not_available)) {
          return router.push({ name: 'wl-treatment-not-available' })
        } else if (Boolean(data.user_has_an_active_plan)) {
          return router.push({ name: 'wl-visit-exists' })
        }
      }

      if (data.failedPayment === 1 && !isEmpty(data.order_id)) {
        retryOrderId.value = data.order_id
        preventBackNavigation()
        await getFailedOrderSummary()
      } else {
        serverErrors.value = processErrors(data)
        scrollToTop()
      }
      isLoading.value = false
    }
  } catch (error) {
    isLoading.value = false
    console.error(error)
    serverErrors.value = processErrors(error)
    scrollToTop()
  }
}

function preventBackNavigation() {
  showBackButton.value = false
  window.addEventListener('beforeunload', onLoadListener)
  window.addEventListener('popstate', async () => {
    await router.replace({ name: 'user-orders' })
    clearOrderSession()
  })
}

function onLoadListener(e) {
  e.preventDefault()

  const message = 'You have unsaved changes. Are you sure you wish to leave?'

  e.returnValue = message

  return message
}

onUnmounted(() => {
  window.removeEventListener('beforeunload', onLoadListener)
})

function isCardExpired(paymentMethod) {
  const now = new Date()
  const expirationDate = new Date(paymentMethod.card_expiry_year, paymentMethod.card_expiry_month - 1)

  return now > expirationDate
}
</script>

<template>
  <div>
    <Header :show-back-button="showBackButton" />

    <div
      v-if="skeletonLoading"
      class="flex items-center justify-center py-8 sm:px-16"
    >
      <div class="px-4 w-full sm:max-w-[480px]">
        <div class="h-7 bg-gray-300 rounded mb-3 animate-pulse"></div>
        <div class="flex flex-col sm:p-5 mt-6 w-full bg-white rounded-3xl sm:border sm:border-solid sm:!border-gray-300">
          <div class="h-14 bg-gray-300 rounded mb-2 animate-pulse"></div>
          <hr>
          <div class="h-5 bg-gray-300 w-[50%] rounded mt-6 mb-4 animate-pulse"></div>
          <div class="h-12 bg-gray-300 rounded mb-4 animate-pulse"></div>
          <div class="h-12 bg-gray-300 rounded mb-4 animate-pulse"></div>
          <div class="h-12 bg-gray-300 rounded mb-6 animate-pulse"></div>
          <div class="h-10 bg-gray-300 rounded mb-4 animate-pulse"></div>
          <div class="h-10 bg-gray-300 rounded mb-4 animate-pulse"></div>
          <div class="h-12 bg-gray-300 rounded-xl mt-8 mb-2 animate-pulse"></div>
        </div>
      </div>
    </div>

    <div
      v-else
      class="flex items-center justify-center py-6 sm:px-16"
    >
      <div class="px-4 w-full sm:max-w-[480px]">
        <div class="text-center mb-3">
          <h2 class="text-xl md:text-3xl font-bold leading-tight text-black">
            Complete Your Order
          </h2>
          <p class="text-sm text-zinc-700 mt-2">
            Secure checkout for your weight management treatment
          </p>
        </div>

        <AlertError
          v-if="serverErrors.length > 0"
          title="Error!"
          :errors="serverErrors"
        />

        <div class="flex flex-col sm:p-5 mt-6 w-full bg-white rounded-3xl sm:border sm:border-solid sm:!border-gray-300">
          <!-- Retry Order Product Summary -->
          <div
            v-if="retryOrderId && retryOrderSummary"
            class="justify-between py-1 mb-6"
          >
            <div class="flex gap-5 max-md:flex-col max-md:gap-0">
              <div class="flex flex-col w-full max-md:ml-0 max-md:w-full">
                <div class="flex grow gap-3 justify-between items-top text-sm leading-5 text-black">
                  <div class="ms-2 bg-gray-100 size-16 rounded-lg">
                    <img
                      :src="retryOrderSummary.image"
                      class="h-full w-full object-contain rounded-lg"
                      alt=""
                    >
                  </div>
                  <div class="grow">
                    <div class="text-xl font-medium tracking-tight leading-5 mb-2">
                      {{ retryOrderSummary.product_name }}
                    </div>
                    <div class="text-sm">
                      {{ retryOrderSummary.strength }} / weekly
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- New Order Summary -->
          <div
            v-else
            class="justify-between py-1 mb-6"
          >
            <div class="flex gap-5 max-md:flex-col max-md:gap-0">
              <div class="flex flex-col w-full max-md:ml-0 max-md:w-full">
                <div class="flex grow gap-3 justify-between items-top text-sm leading-5 text-black">
                  <div class="ms-2 bg-gray-100 size-16 rounded-lg">
                    <img
                      :src="checkoutSummary?.plan_details?.full_strength_image_path"
                      class="h-full w-full object-contain rounded-lg"
                      alt=""
                    >
                  </div>
                  <div class="grow">
                    <div class="text-xl font-medium tracking-tight leading-5 mb-2">
                      {{ checkoutSummary?.plan_details?.product_name }}
                    </div>
                    <div class="text-sm">
                      {{ checkoutSummary?.plan_details?.strength }}{{ checkoutSummary?.plan_details?.strength_unit }} / weekly
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div
            v-if="planLoading"
            class="grid w-full space-y-3"
          >
            <div
              v-for="i in 4"
              :key="i"
            >
              <div class="px-2 py-3 border-t !border-gray-200">
                <div class="flex justify-between">
                  <div class="h-5 min-w-[180px] bg-gray-300 rounded animate-pulse"></div>
                  <div class="h-5 min-w-[25px] bg-gray-300 rounded animate-pulse"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- Retry Order Payment Summary -->
          <div v-else-if="retryOrderId && retryOrderSummary">
            <div
              v-if="!isEmpty(retryOrderSummary.medicine_amount)"
              class="px-2 py-4 border-t !border-gray-200"
            >
              <div class="flex justify-between">
                <div>Month {{ retryOrderSummary.drug_month }} package</div>
                <div class="ms-1 font-medium">
                  {{ formatCurrency(retryOrderSummary.medicine_amount) }}
                </div>
              </div>
            </div>

            <div
              v-if="!isEmpty(retryOrderSummary.promo_code_name)"
              class="flex justify-between px-2 py-4 border-t !border-gray-200"
            >
              <div>
                <div>Promotion applied</div>
                <div class="text-sm font-semibold">
                  {{ retryOrderSummary.promo_code_name }} <span v-if="retryOrderSummary.promo_code_type === 'percentage'">({{ retryOrderSummary.promo_code_value }}%)</span>
                </div>
              </div>
              <div class="font-medium">
                -{{ formatCurrency(retryOrderSummary.promo_code_discount_amount) }}
              </div>
            </div>

            <div
              v-if="!isEmpty(retryOrderSummary.first_time_order_discount_amount)"
              class="flex justify-between px-2 py-4 border-t !border-gray-200"
            >
              <div>First purchase discount applied</div>
              <div class="font-medium">
                -{{ formatCurrency(retryOrderSummary.first_time_order_discount_amount) }}
              </div>
            </div>

            <div
              v-if="!isEmpty(retryOrderSummary.sub_total) && retryOrderSummary.sub_total > 0"
              class="flex justify-between px-2 py-4 border-t !border-gray-200"
            >
              <div>Subtotal</div>
              <div class="font-medium">
                {{ formatCurrency(retryOrderSummary.sub_total) }}
              </div>
            </div>

            <div
              v-if="!isEmpty(retryOrderSummary.provider_consultation_fee)"
              class="flex flex-col px-2 py-4 border-t !border-gray-200"
            >
              <div class="flex justify-between">
                <div class="flex items-center">
                  <span>Physician and Processing Fee</span>
                  <button
                    v-if="retryOrderSummary.provider_consultation_fee > 0"
                    type="button"
                    class="ms-2 cursor-pointer"
                    @click="consultFeeSidebarVisible = true"
                  >
                    <IconInfoCircle
                      class="w-4 h-4"
                      stroke-width="2"
                    />
                  </button>
                </div>
                <div class="font-medium">
                  <span
                    v-if="retryOrderSummary.provider_consultation_fee === 0"
                    class="text-green-400"
                  >FREE</span>
                  <span v-else>{{ formatCurrency(retryOrderSummary.provider_consultation_fee) }}</span>
                </div>
              </div>
              <div
                v-if="retryOrderSummary.is_synchronous_visit"
                class="inline-flex items-center gap-2 text-sm text-gray-600 mt-1 bg-amber-100 px-2 py-2 rounded-lg"
              >
                <IconVideo
                  class="w-4 h-4"
                  stroke-width="2"
                />
                Video visit is required in {{ retryOrderSummary.state }} by state law.
              </div>
            </div>

            <div
              v-if="!isEmpty(retryOrderSummary.shipping_cost)"
              class="flex justify-between px-2 py-4 border-t !border-gray-200"
            >
              <div>Shipping & handling cost</div>
              <div class="font-medium">
                <span
                  v-if="retryOrderSummary.shipping_cost === 0"
                  class="text-green-400"
                >FREE</span>
                <span v-else>{{ formatCurrency(retryOrderSummary.shipping_cost) }}</span>
              </div>
            </div>

            <div
              v-if="!isEmpty(retryOrderSummary.total_amount)"
              class="flex justify-between px-2 py-4 border-t !border-gray-200"
            >
              <div class="text-base font-bold">
                Total due if prescribed
              </div>
              <div class="text-base font-bold">
                {{ formatCurrency(retryOrderSummary.total_amount) }}
              </div>
            </div>
          </div>

          <!-- Normal Order Payment Summary -->
          <div v-else>
            <div
              v-if="!isEmpty(checkoutSummary.subscription_plan_amount)"
              class="px-2 py-4 border-t !border-gray-200"
            >
              <div class="flex justify-between">
                <div>Month {{ checkoutSummary?.plan_details?.drug_month }} package</div>
                <div>
                  <span
                    v-if="checkoutSummary.subscription_plan_amount !== (checkoutSummary?.plan_details?.plan_interval_price)"
                    class="line-through text-gray-700"
                  >{{ formatCurrency(checkoutSummary?.plan_details?.plan_interval_price) }}</span>
                  <span class="ms-1 font-medium">{{ formatCurrency(checkoutSummary.subscription_plan_amount) }}</span>
                </div>
              </div>
              <div
                v-if="percentageSaved"
                class="text-sm text-green-400 font-medium"
              >
                {{ percentageSaved }}
              </div>
            </div>

            <div
              v-if="!isPromoAdded && checkoutSummary.sub_total !== 0"
              class="py-2 border-t !border-gray-200"
            >
              <div
                v-if="!showPromoInput"
                class="flex justify-between items-center bg-gray-100 px-4 py-2 rounded-lg"
              >
                <div class="text-sm">
                  Have a Promo code?
                </div>
                <button
                  class="text-[13px] font-medium border-b !border-gray-900 text-gray-900 disabled:text-gray-600"
                  :disabled="isLoading"
                  @click="showPromoInput = true"
                >
                  Click to Apply
                </button>
              </div>
              <div v-if="showPromoInput">
                <input
                  ref="promoCodeInputRef"
                  v-model="promoCode"
                  type="text"
                  placeholder="Enter promo code"
                  class="block w-full px-4 py-2 text-md font-medium appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                  autofocus
                  @keyup.enter="applyPromoCode"
                  @keydown="() => {
                    promoCodeError = ''
                  }"
                >
                <div v-if="!isEmpty(promoCodeError)">
                  <span class="text-red-500 text-sm ms-2">{{ promoCodeError }}</span>
                </div>
                <div class="mt-3 flex justify-end">
                  <button
                    class="inline-flex items-center justify-center text-black border !border-black hover:bg-gray-100 focus:outline-none font-medium rounded-full text-sm px-4 py-[5px] text-center disabled:bg-zinc-100 disabled:text-gray-600 disabled:cursor-not-allowed me-2"
                    @click="discardPromoApply"
                  >
                    Discard
                  </button>
                  <TwButton
                    class="!py-1.5"
                    @click="applyPromoCode"
                  >
                    Apply
                  </TwButton>
                </div>
              </div>
            </div>

            <div
              v-if="!isEmpty(checkoutSummary.promocode)"
              class="flex justify-between px-2 py-4 border-t !border-gray-200"
            >
              <div>
                <div>Promotion applied</div>
                <div class="text-sm font-semibold">
                  {{ checkoutSummary.promocode }} <span v-if="checkoutSummary.promocode_type === 'percentage'">({{ checkoutSummary.promocode_value }}%)</span>
                </div>
                <button
                  v-if="isPromoAdded"
                  class="underline text-sm font-medium disabled:text-gray-600"
                  :disabled="isLoading"
                  @click="removePromoCode"
                >
                  Remove
                </button>
              </div>
              <div class="font-medium">
                -{{ formatCurrency(checkoutSummary.promocode_discount_amount) }}
              </div>
            </div>

            <div
              v-if="!isEmpty(checkoutSummary.discount_amount)"
              class="flex justify-between px-2 py-4 border-t !border-gray-200"
            >
              <div>First purchase discount applied</div>
              <div class="font-medium">
                -{{ formatCurrency(checkoutSummary.discount_amount) }}
              </div>
            </div>

            <div
              v-if="!isEmpty(checkoutSummary.sub_total) && checkoutSummary.sub_total > 0"
              class="flex justify-between px-2 py-4 border-t !border-gray-200"
            >
              <div>Subtotal</div>
              <div class="font-medium">
                {{ formatCurrency(checkoutSummary.sub_total) }}
              </div>
            </div>

            <div
              v-if="!isEmpty(checkoutSummary.provider_consultation_fee)"
              class="flex flex-col px-2 py-4 border-t !border-gray-200"
            >
              <div class="flex justify-between">
                <div class="flex items-center">
                  <span>Physician and Processing Fee</span>
                  <button
                    v-if="checkoutSummary.provider_consultation_fee > 0"
                    type="button"
                    class="ms-2 cursor-pointer"
                    @click="consultFeeSidebarVisible = true"
                  >
                    <IconInfoCircle
                      class="w-4 h-4"
                      stroke-width="2"
                    />
                  </button>
                </div>
                <div class="font-medium">
                  <span
                    v-if="checkoutSummary.provider_consultation_fee === 0"
                    class="text-green-400"
                  >FREE</span>
                  <span v-else>{{ formatCurrency(checkoutSummary.provider_consultation_fee) }}</span>
                </div>
              </div>
              <div
                v-if="checkoutSummary.is_synchronous_visit"
                class="inline-flex items-center gap-2 text-sm text-gray-600 mt-1 bg-amber-100 px-2 py-2 rounded-lg"
              >
                <IconVideo
                  class="w-4 h-4"
                  stroke-width="2"
                />
                Video visit is required in {{ checkoutSummary.state }} by state law.
              </div>
            </div>

            <div
              v-if="!isEmpty(checkoutSummary.convenience_fee)"
              class="flex justify-between px-2 py-4 border-t !border-gray-200"
            >
              <div>Convenience fee</div>
              <div class="font-medium">
                {{ formatCurrency(checkoutSummary.convenience_fee) }}
              </div>
            </div>

            <div
              v-if="!isEmpty(checkoutSummary.shipping_handling_cost)"
              class="flex justify-between px-2 py-4 border-t !border-gray-200"
            >
              <div>Shipping & handling cost</div>
              <div class="font-medium">
                <span
                  v-if="checkoutSummary.shipping_handling_cost === 0"
                  class="text-green-400"
                >FREE</span>
                <span v-else>{{ formatCurrency(checkoutSummary.shipping_handling_cost) }}</span>
              </div>
            </div>

            <div
              v-if="!isEmpty(checkoutSummary.grand_total)"
              class="flex justify-between px-2 py-4 border-t !border-gray-200"
            >
              <div class="text-base font-bold">
                Total due if prescribed
              </div>
              <div class="text-base font-bold">
                {{ formatCurrency(checkoutSummary.grand_total) }}
              </div>
            </div>

            <div
              v-if="!isEmpty(checkoutSummary.full_refund_if_not_prescribed)"
              class="flex justify-between px-2 py-1"
            >
              <div>
                <div class="font-semibold mb-1">
                  Due Today
                </div>
                <!--
                  <button
                  class="text-sm font-medium border-b !border-gray-900 text-gray-900"
                  @click="paymentAndRefundSidebarVisible = true"
                  >
                  When am I charged?
                  </button>
                -->
              </div>
              <div class="font-medium">
                {{ formatCurrency(checkoutSummary.full_refund_if_not_prescribed) }}
              </div>
            </div>
            <div class="text-sm text-gray-600 px-2 border-b !border-gray-200 pb-4 mt-2">
              <h5 class="font-semibold text-sm text-gray-700 mb-0.5">
                Only charged if prescribed by a licensed physician
              </h5>
              <p>We'll securely hold your payment method. No charge until prescribed.</p>
            </div>
          </div>

          <!-- Payment Failed Card -->
          <div v-if="retryOrderId && !showPaymentMethods">
            <div class="bg-red-50 p-6 rounded-lg">
              <IconCreditCardOff
                class="size-16 mx-auto mb-3"
                stroke-width="1.5"
              />
              <div class="text-gray-600 text-[15px]">
                <h5 class="mb-2 text-lg font-medium text-gray-900">
                  Oops! Your payment didn't go through.
                </h5>
                <p>
                  This could be because:
                </p>
                <ul class="list-disc ps-5">
                  <li>You have insufficient funds in your account</li>
                  <li>Your payment card has expired</li>
                  <li>There's a problem with your bank</li>
                </ul>
                <p class="mt-2">
                  Please try again using a different payment method.
                </p>
              </div>
              <TwButton
                class="w-full mt-5"
                @click="showPaymentMethods = true"
              >
                Retry Payment
              </TwButton>
            </div>
          </div>

          <!-- Payment methods -->
          <div
            v-if="showPaymentMethods"
            class="mt-4 space-y-6"
          >
            <div v-if="paymentMethods.length > 0 && !isCardFormVisible">
              <div>
                <h5 class="text-gray-700 text-base font-medium mb-3">
                  Select from existing payment methods
                </h5>
              </div>
              <ul class="grid w-full space-y-3 mb-5">
                <li
                  v-for="paymentMethod in paymentMethods"
                  :key="paymentMethod.id"
                >
                  <input
                    :id="`payment_method_${paymentMethod.id}`"
                    v-model="selectedPaymentMethod"
                    type="radio"
                    name="shipping_method"
                    class="hidden peer"
                    :value="paymentMethod.id"
                    :disabled="isCardExpired(paymentMethod)"
                    required
                  />
                  <label
                    :for="`payment_method_${paymentMethod.id}`"
                    class="inline-flex items-center justify-between w-full px-5 py-3 text-gray-700 bg-white border !border-gray-200 rounded-2xl cursor-pointer peer-checked:!border-gray-600 peer-checked:text-black hover:text-gray-800 hover:bg-gray-100 peer-disabled:bg-gray-200 peer-disabled:cursor-not-allowed"
                  >
                    <div class="block">
                      <div class="w-full">
                        <span class="block text-xs uppercase">{{ paymentMethod.card_brand_type }}</span>
                        <span class="block text-base font-medium">xxxx xxxx xxxx {{ paymentMethod.card_last_4_digit }}</span>
                      </div>
                      <div class="w-full text-xs">
                        <span
                          v-if="isCardExpired(paymentMethod)"
                          class="bg-red-300 text-red-700 font-medium  px-2 py-0.5 rounded-full"
                        >Expired</span>
                        <span v-else>
                          Expires {{ paymentMethod.card_expiry_month }}/{{ paymentMethod.card_expiry_year }}
                        </span>
                      </div>
                    </div>
                    <span class="mx-2 flex items-center justify-center">
                      <span
                        class="w-4 h-4 border-2 rounded-sm flex justify-center items-center"
                        :class="[
                          selectedPaymentMethod === paymentMethod.id ? 'border-transparent bg-black' : 'border-gray-400'
                        ]"
                      >
                        <IconCheck
                          class="h-5 w-5 z-50"
                          :stroke-width="selectedPaymentMethod === paymentMethod.id ? 4 : 1"
                          :class="[
                            selectedPaymentMethod === paymentMethod.id ? 'text-white' : 'text-gray-900'
                          ]"
                        />
                      </span>
                    </span>
                  </label>
                </li>
              </ul>

              <TwButton
                class="w-full"
                :loading="isLoading"
                :disabled="planLoading || isEmpty(selectedPaymentMethod)"
                @click="placeOrder"
              >
                <span v-if="retryOrderId && retryOrderSummary">
                  Pay {{ formatCurrency(retryOrderSummary.total_amount) }}
                </span>
                <span v-else>Continue</span>
              </TwButton>
            </div>

            <div
              v-if="paymentMethods.length > 0 && !isCardFormVisible"
              class="inline-flex items-center justify-center w-full py-2"
            >
              <hr class="w-64 h-px bg-gray-200 border-0">
              <span class="absolute px-3 font-medium text-gray-900 -translate-x-1/2 bg-white left-1/2">or</span>
            </div>

            <TwButton
              v-if="!isCardFormVisible"
              class="w-full !bg-[#ffef08] border-2 border-solid !border-amber-300 !text-black hover:!brightness-90 transition-all"
              :disabled="planLoading"
              @click="initPaypal"
            >
              Add Credit or Debit Card
            </TwButton>
          </div>

          <div v-show="isCardFormVisible && showPaymentMethods">
            <AlertError
              v-if="cardFieldErrors.length > 0"
              title="Error!"
              :errors="cardFieldErrors"
            />
            <div>
              <div
                id="card-number"
                class="w-full"
              ></div>
              <div
                id="expiration-date"
                class="w-full"
              ></div>
              <div
                id="cvv"
                class="w-full"
              ></div>
              <TwButton
                id="checkoutSubmitBtn"
                class="w-full mt-2"
                value="submit"
                :loading="isLoading"
                :disabled="planLoading"
              >
                <span v-if="retryOrderId && retryOrderSummary">
                  Pay {{ formatCurrency(retryOrderSummary.total_amount) }}
                </span>
                <span v-else>Continue</span>
              </TwButton>
              <div
                v-if="paymentMethods.length > 0"
                class="text-center mt-5"
              >
                <button
                  class="text-sm font-medium border-b !border-gray-900 text-gray-900 inline-block"
                  @click="selectExistingPaymentMethod"
                >
                  Select existing payment method
                </button>
              </div>
            </div>
          </div>

          <hr
            v-if="!retryOrderId"
            class="w-full h-px bg-gray-200 border-0 mt-8 mb-4"
          >

          <div
            v-if="!retryOrderId"
            class="text-[13px] italic text-gray-600 bg-gray-100 !rounded-xl p-4"
          >
            You won't be charged anything for now. If approved, we will charge the provided card {{ formatCurrency(checkoutSummary.grand_total) }} and discreetly deliver your prescription to your doorstep.
          </div>
        </div>

        <div class="mt-8">
          <div class="grid gap-2 text-xs text-gray-600">
            <div>
              <span class="font-semibold">Payment Authorization:</span> We'll securely pre-authorize your payment method for the amount shown. You'll only be charged if a licensed physician prescribes your medication after reviewing your medical information.
            </div>
            <div>
              <span class="font-semibold">Medical Disclaimer:</span> By submitting this form, I confirm that all information provided is accurate and complete to the best of my knowledge. I understand that providing complete and honest medical information is essential for safe treatment.
            </div>
          </div>
          <p class="text-xs text-gray-600 flex gap-2 justify-center items-center font-medium mt-5">
            <span>
              <IconShieldLock
                class="h-[18px] w-[18px] mb-px"
                stroke-width="2"
              />
            </span>
            <span>256-BIT TLS SECURITY</span>
          </p>
        </div>
      </div>
    </div>
  </div>

  <!-- 👉 payment and refund sidebar -->
  <!--
    <PaymentRefundSidebar
    :visible="paymentAndRefundSidebarVisible"
    @update:visible="paymentAndRefundSidebarVisible = $event"
    />
  -->

  <!-- 👉 consult fee sidebar -->
  <ConsultFeeSidebar
    :visible="consultFeeSidebarVisible"
    @update:visible="consultFeeSidebarVisible = $event"
  />
</template>

<style lang="scss">
.p-sidebar-right .payment-and-refund-sidebar.p-sidebar {
  width: 30rem;
  font-family: "Konnect", system-ui, -apple-system, BlinkMacSystemFont,
    "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue",
    sans-serif;
}
</style>
