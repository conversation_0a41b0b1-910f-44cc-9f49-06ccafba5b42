<script setup>
import { isEmptyObject, isNullOrUndefined } from '@/@core/utils'
import useCaptcha from '@/composables/useCaptcha'
import { useAppAbility } from '@/plugins/casl/useAppAbility'
import { alertWarning } from '@/plugins/sweetalert2'
import router from '@/router'
import ApiService from '@/services/ApiService'
import { useAuthStore } from '@/store/auth'
import { processErrors } from '@/utils/errorHandler'
import { formattedPhoneNumber, plainPhoneNumber } from '@/utils/helpers'
import AlertError from '@/views/user/components/AlertError.vue'
import { IconArrowLeft, IconBolt, IconEdit, IconEye, IconEyeOff, IconMail } from '@tabler/icons-vue'
import { vMaska } from 'maska'
import { ErrorMessage, Field, Form as VForm } from 'vee-validate'
import { computed, onMounted } from 'vue'
import { RouterLink, useRoute } from 'vue-router'
import { toast } from 'vue-sonner'
import * as yup from 'yup'
import Header from '../components/Header.vue'

const $cookies = inject('$cookies')
const authStore = useAuthStore()
const ability = useAppAbility()
const route = useRoute()
const { getRecaptchaToken } = useCaptcha()

const isLoading = ref(false)
const inputErrors = ref({})
const serverErrors = ref([])
const errorTitle = ref('Error!')
const isPasswordVisible = ref(false)
const showPasswordField = ref(false)
const isPhoneLoginActive = ref(false)
const isPhoneOtpSent = ref(false)
const emailRef = ref()
const emailOrPhone = ref(route.query.login_hint ?? '')
const password = ref('')
const verificationCode = ref('')
const passwordInputRef = ref(null)
const otpInputRef = ref(null)
const frontendUrl = computed(() => import.meta.env.VITE_MARKETING_SITE_URL)

onMounted(() => {
  showResetPasswordLinkExpiredPopup()
})

const emailSchema = yup.object().shape({
  email: yup.string()
    .required('Email address or phone number is required')
    .test(
      'is-valid-email-or-phone',
      'Invalid email or phone',
      value => {
        const emailRegex = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
        const phoneRegex = /^\d{10}$/

        return emailRegex.test(value) || phoneRegex.test(value)
      },
    ),
})

const loginSchema = yup.object().shape({
  email: yup.string().required('Email address is required').email('Invalid email address'),
  password: yup.string().required('Password is required'),
})

const validationSchema = computed(() => {
  if (showPasswordField.value) {
    return loginSchema
  } else {
    return emailSchema
  }
})

const isEmailOrPhone = value => {
  const phoneRegex = /^\d{10}$/

  if (phoneRegex.test(value)) {
    return 'phone'
  }

  return 'email'
}

const editEmailInput = () => {
  showPasswordField.value = false
  password.value = ''
  emailRef.value.focus()
}

const resetPhoneLogin = () => {
  isPhoneLoginActive.value = false
  isPhoneOtpSent.value = false
  emailOrPhone.value = ''
}

const handleSubmit = async values => {
  if (showPasswordField.value) {
    loginWithEmailAndPassword(values)
  } else {
    const result = isEmailOrPhone(values.email)

    if (result === 'phone') {
      isPhoneLoginActive.value = true
    } else if (result === 'email') {
      showPasswordField.value = true
      setTimeout(() => {
        passwordInputRef.value?.focus()
      }, 100)
    }
  }
}

const loginWithEmailAndPassword = async values => {
  try {
    isLoading.value = true
    inputErrors.value = {}
    serverErrors.value = []
    errorTitle.value = 'Error!'

    const captchaToken = await getRecaptchaToken('login')

    const postData = {
      ...values,
      'g-recaptcha-response': captchaToken,
    }

    const { data } = await ApiService.post('/user-authentication', postData)

    if (data.status === 200) {
      authStore.setAuth(data)

      const userAbilities = []
      let dashboardRoute = 'user-subscription'

      if (data.userData.role === 'superadmin' || data.userData.role === 'admin') {
        userAbilities.push({ action: 'manage', subject: 'all' })
        dashboardRoute = 'admin-dashboard'
      }

      $cookies.set('userAbilities', userAbilities)
      ability.update(userAbilities)

      if (route.query.redirect_to) {
        router.replace(String(route.query.redirect_to))
      } else {
        router.replace({ name: dashboardRoute })
      }
    } else {
      isLoading.value = false
      if (data.errors) {
        inputErrors.value = data.errors
      }
      errorTitle.value = data.title ?? 'Error!'
      serverErrors.value = processErrors(data)
    }
  } catch (error) {
    isLoading.value = false
    if (error.response.errors) {
      inputErrors.value = error.response.errors
    }
    errorTitle.value = data.title ?? 'Error!'
    serverErrors.value = processErrors(error)
    console.error(error)
  }
}

const timer = ref(null)
let intervalId

const startTimer = () => {
  const countDownDate = new Date(Date.now() + 120 * 1000).getTime()
  let mins = 1
  let secs = 59
  timer.value = `0${mins}:${secs}`

  if (intervalId) {
    clearInterval(intervalId)
  }

  intervalId = setInterval(() => {
    const now = new Date().getTime()
    const distance = countDownDate - now

    if (distance < 0) {
      clearInterval(intervalId)
      timer.value = null

      return
    }

    const minutes = Math.floor((distance / (1000 * 60)) % 60)
    const seconds = Math.floor((distance / 1000) % 60)

    mins = minutes < 10 ? '0' + minutes : minutes
    secs = seconds < 10 ? '0' + seconds : seconds

    timer.value = `${mins}:${secs}`
  }, 1000)
}

const isLoadingSendOtp = ref(false)

const sendPhoneOtp = async () => {
  try {
    isLoadingSendOtp.value = true

    const captchaToken = await getRecaptchaToken('phoneAuthentication')

    const postData = {
      phone_number: plainPhoneNumber(emailOrPhone.value),
      'g-recaptcha-response': captchaToken,
    }

    const { data } = await ApiService.post('/phone-number-authentication', postData)

    if (data.status === 200) {
      startTimer()
      toast.success(data.message)
      isPhoneOtpSent.value = true
      isLoadingSendOtp.value = false
      setTimeout(() => {
        otpInputRef.value?.focus()
      }, 100)
    } else {
      if (data.message) {
        toast.error(data.message)
      }
    }
  } catch (error) {
    if (error.data.message) {
      toast.error(error.data.message)
    } else if (error.message) {
      toast.error(error.message)
    }
    console.error(error)
  } finally {
    isLoadingSendOtp.value = false
  }
}

const isLoadingResendCode = ref(false)

async function resendNewCode() {
  try {
    isLoadingResendCode.value = true

    const postData = {
      phone_number: plainPhoneNumber(emailOrPhone.value),
      type: 'login',
    }

    const { data } = await ApiService.post('/resend-sms-otp', postData)

    if (data.status === 200) {
      toast.success(data.message)
      startTimer()
      setTimeout(() => {
        otpInputRef.value?.focus()
      }, 100)
    } else {
      if (data.message) {
        toast.error(data.message)
      }
    }
  } catch (error) {
    if (error.data.message) {
      toast.error(error.data.message)
    } else if (error.message) {
      toast.error(error.message)
    }
    console.error(error)
  } finally {
    isLoadingResendCode.value = false
  }
}

async function handlePhoneLoginSubmit() {
  try {
    isLoading.value = true
    inputErrors.value = {}

    const postData = {
      otp: verificationCode.value,
      phone_number: plainPhoneNumber(emailOrPhone.value),
      type: 'login',
    }

    const { data } = await ApiService.post('/verify-sms-otp', postData)

    if (data.status === 200) {
      authStore.setAuth(data)

      const userAbility = []

      $cookies.set('userAbilities', userAbility)
      ability.update(userAbility)

      if (route.query.redirect_to) {
        router.replace(String(route.query.redirect_to))
      } else {
        router.replace({ name: 'user-subscription' })
      }
    } else {
      isLoading.value = false
      if (data.errors) {
        if (data.errors) {
          inputErrors.value = data.errors
        }
      } else if (data.message) {
        toast.error(data.message)
      }
    }
  } catch (error) {
    isLoading.value = false
    if (error.response.errors) {
      if (error.response.errors) {
        inputErrors.value = error.response.errors
      }
    } else if (error.data.message) {
      toast.error(error.data.message)
    } else if (error.message) {
      toast.error(error.message)
    }
    console.error(error)
  }
}

const showResetPasswordLinkExpiredPopup = () => {
  if (sessionStorage.getItem('resetLinkExpired') === '1') {
    alertWarning.fire({
      title: 'Link Expired!',
      html: 'We apologize, but it appears that the link to reset your password has expired. Please request a new link to reset your password.',
      confirmButtonColor: '#000',
    })
    sessionStorage.removeItem('resetLinkExpired')
  }
}
</script>

<template>
  <div>
    <Header />

    <div class="flex items-center justify-center py-14 sm:px-16">
      <div class="px-4 w-full sm:max-w-lg">
        <h2 class="text-center text-2xl md:text-5xl font-bold leading-tight text-black">
          {{ isPhoneOtpSent ? 'Check your phone' : 'Welcome Back!' }}
        </h2>
        <p
          v-if="isPhoneOtpSent"
          class="mt-2 text-base font-medium text-center text-gray-600"
        >
          Please enter the 6 digit OTP sent to your phone <br>
          <span class="font-semibold">{{ formattedPhoneNumber(emailOrPhone) }}</span>
        </p>
        <p
          v-else
          class="mt-2 text-lg font-medium text-center text-gray-600"
        >
          Sign in to your account.
        </p>

        <AlertError
          v-if="isEmptyObject(inputErrors) && serverErrors.length > 0"
          :title="errorTitle"
          :errors="serverErrors"
        />

        <div v-if="!isPhoneLoginActive">
          <VForm
            class="mt-8"
            :validation-schema="validationSchema"
            @submit="handleSubmit"
          >
            <div class="space-y-5">
              <div class="mb-4">
                <div class="relative">
                  <Field
                    v-slot="{ field }"
                    v-model="emailOrPhone"
                    name="email"
                  >
                    <input
                      id="email"
                      v-bind="field"
                      ref="emailRef"
                      v-model="emailOrPhone"
                      type="email"
                      name="email"
                      class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                      :readonly="showPasswordField"
                      placeholder=" "
                      autofocus
                      required
                    />
                  </Field>
                  <label
                    for="email"
                    class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4 cursor-text"
                  >Email or Phone Number</label>
                  <div class="absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                    <IconMail
                      v-if="!showPasswordField"
                      class="h-5 w-5"
                      color="#fff"
                      fill="#000"
                    />
                    <IconEdit
                      v-if="showPasswordField"
                      class="h-5 w-5 cursor-pointer"
                      stroke-width="2"
                      @click="editEmailInput"
                    />
                  </div>
                </div>
                <ErrorMessage
                  name="email"
                  class="text-red-500 text-sm ms-5"
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.email)"
                  class="text-red-500 text-sm ms-5"
                >
                  {{ inputErrors.email[0] }}
                </p>
              </div>

              <div
                v-if="showPasswordField"
                class="mb-4"
              >
                <div class="relative">
                  <Field
                    v-slot="{ field }"
                    name="password"
                  >
                    <input
                      v-bind="field"
                      id="password"
                      ref="passwordInputRef"
                      v-model="password"
                      :type="isPasswordVisible ? 'text' : 'password'"
                      class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                      placeholder=" "
                      required
                    />
                  </Field>
                  <label
                    for="password"
                    class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4"
                  >Password</label>
                  <div class="absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                    <IconEye
                      v-if="!isPasswordVisible"
                      class="h-5 w-5"
                      stroke-width="2"
                      @click="isPasswordVisible = !isPasswordVisible"
                    />
                    <IconEyeOff
                      v-else
                      class="h-5 w-5"
                      stroke-width="2"
                      @click="isPasswordVisible = !isPasswordVisible"
                    />
                  </div>
                </div>
                <ErrorMessage
                  name="password"
                  class="text-red-500 text-sm ms-5"
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.password)"
                  class="text-red-500 text-sm ms-5"
                >
                  {{ inputErrors.password[0] }}
                </p>
              </div>

              <div class="mt-8">
                <TwButton
                  type="submit"
                  class="w-full uppercase"
                  :loading="isLoading"
                >
                  <span v-if="showPasswordField">Login</span>
                  <span v-else>Continue to Login</span>
                  <IconBolt
                    v-if="showPasswordField"
                    class="h-4 w-4 ms-1"
                    stroke-width="1"
                    fill="currentColor"
                  />
                </TwButton>
              </div>
            </div>
          </VForm>

          <div class="mt-8 text-center">
            <RouterLink
              :to="{ name: 'user-forgot-password', query: { ...route.query } }"
              class="text-base underline font-semibold hover:no-underline transition-all"
            >
              Forgot password?
            </RouterLink>
          </div>
        </div>

        <div v-if="isPhoneLoginActive">
          <div class="flex justify-center mt-10">
            <TwButton
              v-if="!isPhoneOtpSent"
              type="button"
              class="w-full"
              :loading="isLoadingSendOtp"
              :loading-text="`Sending OTP to ${formattedPhoneNumber(emailOrPhone)}`"
              @click="sendPhoneOtp"
            >
              Text Login code to {{ formattedPhoneNumber(emailOrPhone) }}
            </TwButton>

            <div
              v-if="isPhoneOtpSent"
              class="w-full"
            >
              <form @submit.prevent="handlePhoneLoginSubmit">
                <div class="relative mb-10">
                  <input
                    ref="otpInputRef"
                    v-model="verificationCode"
                    v-maska
                    type="text"
                    class="block w-full px-5 py-3 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black text-center peer"
                    placeholder="######"
                    data-maska="######"
                    required
                  />
                  <p
                    v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.otp)"
                    class="text-red-500 text-sm ms-5"
                  >
                    {{ inputErrors.otp[0] }}
                  </p>
                </div>

                <button
                  type="submit"
                  class="inline-flex w-full items-center justify-center rounded-full bg-black px-3.5 py-2.5 font-semibold leading-[1.7] text-white hover:bg-black/80 text-sm disabled:bg-gray-800 uppercase"
                  :disabled="isLoading"
                >
                  <span
                    v-if="isLoading"
                    role="status"
                    class="me-2"
                  >
                    <svg
                      aria-hidden="true"
                      class="w-6 h-6 text-gray-200 animate-spin fill-yellow-300"
                      viewBox="0 0 100 101"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                        fill="currentColor"
                      />
                      <path
                        d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                        fill="currentFill"
                      />
                    </svg>
                    <span class="sr-only">Loading...</span>
                  </span>
                  <span
                    v-if="!isLoading"
                    class="flex items-center"
                  >
                    <span>Login</span>
                    <IconBolt
                      class="h-4 w-4 ms-1 mb-[2.5px]"
                      stroke-width="1"
                      fill="currentColor"
                    />
                  </span>
                </button>

                <button
                  type="button"
                  class="inline-flex w-full items-center justify-center rounded-full border !border-black px-3.5 py-2.5 font-semibold leading-[1.7] text-black hover:bg-gray-200 text-sm mt-4 disabled:cursor-not-allowed disabled:bg-gray-300"
                  :disabled="isLoadingResendCode || timer"
                  @click="resendNewCode"
                >
                  <span
                    v-if="isLoadingResendCode"
                    role="status"
                    class="me-2"
                  >
                    <svg
                      aria-hidden="true"
                      class="w-5 h-5 text-gray-300 animate-spin fill-black"
                      viewBox="0 0 100 101"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                        fill="currentColor"
                      />
                      <path
                        d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                        fill="currentFill"
                      />
                    </svg>
                    <span class="sr-only">Loading...</span>
                  </span>
                  <span>{{ timer ? `Resend code in ${timer}` : 'Resend code' }}</span>
                </button>
              </form>
            </div>
          </div>
          <div class="flex justify-center gap-4 mt-10">
            <button
              class="text-sm font-semibold border-b-2 border-solid !border-black hover:text-gray-600 hover:!border-gray-600"
              @click="resetPhoneLogin"
            >
              Login with email
            </button>
            <button
              class="text-sm font-semibold border-b-2 border-solid !border-black hover:text-gray-600 hover:!border-gray-600"
              @click="resetPhoneLogin"
            >
              Not you? Login here
            </button>
          </div>
        </div>

        <div class="flex justify-center mt-12">
          <a
            class="text-sm font-semibold border-b-2 border-solid !border-black hover:text-gray-600 hover:!border-gray-600"
            :href="frontendUrl"
          >
            <span class="flex items-center">
              <IconArrowLeft
                stroke-width="2"
                class="h-4 w-4 me-1"
              /> Back to Home
            </span>
          </a>
        </div>
      </div>
    </div>
  </div>
</template>
