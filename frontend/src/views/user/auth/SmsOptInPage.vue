<script setup>
import Header from '../components/Header.vue'
import { onMounted, ref } from 'vue'
import router from '@/router'
import { useRoute } from 'vue-router'
import { IconPackage, IconPrescription, IconTagStarred, IconUsersPlus } from '@tabler/icons-vue'
import ApiService from '@/services/ApiService'
import { toast } from 'vue-sonner'
import { processErrors } from '@/utils/errorHandler'

const route = useRoute()
const btnLoading = ref(false)

async function optIn() {
  try {
    btnLoading.value = true

    const { data } = await ApiService.get('/update-sms-notification')

    if (data.status === 200) {
      skipToNextStep()
    } else {
      btnLoading.value = false
      toast.error(data.message || 'Something went wrong!')
    }
  } catch (error) {
    btnLoading.value = false
    console.log(error)
    toast.error(processErrors(error)[0])
  }
}

function skipToNextStep() {
  if (route.query.redirect_to) {
    router.replace(String(route.query.redirect_to))
  } else {
    router.replace({ name: 'user-subscription' })
  }
}
</script>

<template>
  <div>
    <Header />

    <div class="flex items-center justify-center py-10 sm:px-16">
      <div class="px-4 w-full sm:max-w-[480px]">
        <div class="mb-6 text-center">
          <h2 class="text-center text-xl md:text-3xl font-bold leading-tight text-black">
            Stay in the Loop with SMS&nbsp;Alerts
          </h2>
          <p class="mt-2">
            Never miss a beat—get critical updates from White Label Rx straight to your phone.
          </p>
        </div>

        <div class="w-full flex flex-col items-center space-y-4">
          <div class="w-full flex align-center gap-4 bg-gray-100 p-4 !rounded-xl">
            <IconPrescription
              class="h-6 w-6"
              stroke-width="1.5"
            />
            <div class="flex-1 text-base font-medium text-gray-800">
              Reminders for prescription expiration
            </div>
          </div>
          <div class="w-full flex align-center gap-4 bg-gray-100 p-4 !rounded-xl">
            <IconTagStarred
              class="h-6 w-6"
              stroke-width="1.5"
            />
            <div class="flex-1 text-base font-medium text-gray-800">
              Latest deals and treatments
            </div>
          </div>
          <div class="w-full flex align-center gap-4 bg-gray-100 p-4 !rounded-xl">
            <IconUsersPlus
              class="h-6 w-6"
              stroke-width="1.5"
            />
            <div class="flex-1 text-base font-medium text-gray-800">
              Updates from your healthcare providers
            </div>
          </div>
          <div class="w-full flex align-center gap-4 bg-gray-100 p-4 !rounded-xl">
            <IconPackage
              class="h-6 w-6"
              stroke-width="1.5"
            />
            <div class="flex-1 text-base font-medium text-gray-800">
              Order and shipping updates
            </div>
          </div>
        </div>

        <div class="mt-8 space-y-4">
          <TwButton
            class="w-full"
            :loading="btnLoading"
            loading-text="Please wait..."
            @click="optIn"
          >
            Keep Me Informed
          </TwButton>
          <TwButton
            variant="secondary"
            class="w-full"
            :disabled="btnLoading"
            @click="skipToNextStep"
          >
            Skip for now
          </TwButton>
        </div>
      </div>
    </div>
  </div>
</template>
