<script setup>
import { useAuthStore } from '@/store/auth'
import SignupOverlay from '@/views/user/components/SignupOverlay.vue'
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import Header from '../components/Header.vue'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

const signupPopupVisible = ref(false)

onMounted(() => {
  if ((
    sessionStorage.getItem('edQuestionsSession') || sessionStorage.getItem('hlQuestionsSession') || sessionStorage.getItem('wlQuestionsSession')
  ) && !authStore.isAuthenticated) {
    signupPopupVisible.value = true
  } else {
    if (route.params.visitType) {
      router.push({ name: `${route.params.visitType}-questions`, params: { slug: 'start' } })
    } else {
      window.location.href = import.meta.env.VITE_MARKETING_SITE_URL
    }
  }
})
</script>

<template>
  <div>
    <Header />
    <SignupOverlay :show="signupPopupVisible" />
  </div>
</template>
