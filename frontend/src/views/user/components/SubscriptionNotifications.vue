<script setup>
import { isEmpty } from '@/@core/utils'
import ApiService from '@/services/ApiService'
import { useUpdatePaymentMethod } from '@/store/updatePaymentMethod'
import { processErrors } from '@/utils/errorHandler'
import { IconNotification } from '@tabler/icons-vue'
import { onMounted, ref } from 'vue'
import { toast } from 'vue-sonner'

const props = defineProps({
  subscriptionId: {
    type: String,
    required: true,
  },
})

const emit = defineEmits(['updatePaymentMethod'])

const { setSubscriptionRefillId } = useUpdatePaymentMethod()

const skeletonLoading = ref(false)
const notifications = ref([])
const currentPage = ref(1)
const totalPage = ref(0)
const totalRecords = ref(0)

onMounted(async () => {
  await fetchNotifications()
})

async function fetchNotifications() {
  try {
    skeletonLoading.value = true

    const postData = {
      id: props.subscriptionId,
      page: currentPage.value,
    }

    const { data } = await ApiService.post('/subscription-notifications', postData)

    if (data.status === 200) {
      if (currentPage.value === 1) {
        notifications.value = data.subscriptionsNotifications.records
      } else {
        notifications.value = [...notifications.value, ...data.subscriptionsNotifications.records]
      }
      totalPage.value = data.subscriptionsNotifications.totalPage
      totalRecords.value = data.subscriptionsNotifications.totalRecords
    } else {
      toast.error(data.message)
    }
  } catch (error) {
    console.error(error)
    toast.error(processErrors(error)[0])
  } finally {
    skeletonLoading.value = false
  }
}

async function handlePagination() {
  if (!skeletonLoading.value && currentPage.value < totalPage.value) {
    currentPage.value++
    await fetchNotifications()
  }
}

function actionResolver(notification) {
  const type = notification.notification_type

  if (type === 9) {
    return {
      buttonLabel: 'Retry Payment',
      onClick: () => {
        setSubscriptionRefillId(notification.subscription_refill_id)
        emit('updatePaymentMethod')
      },
    }
  }

  return null
}

function reload() {
  currentPage.value = 1
  notifications.value = []
  fetchNotifications()
}

defineExpose({ reload })
</script>

<template>
  <aside class="bg-white p-5 rounded-[20px]">
    <div class="text-xl font-semibold mb-3">
      Notifications
    </div>

    <div
      v-if="notifications.length === 0 && !skeletonLoading"
      class="grid place-items-center min-h-28"
    >
      <div class="flex flex-col justify-center items-center">
        <IconNotification
          class="h-14 text-zinc-600"
          stroke-width="1.5"
        />
        <span class="text-zinc-600 text-sm font-medium mt-2">
          You're all caught up!
        </span>
      </div>
    </div>

    <div class="space-y-3 h-auto overflow-y-auto pb-6 scroll-container">
      <div
        v-if="notifications.length > 0"
        class="space-y-3"
      >
        <div
          v-for="notification in notifications"
          :key="notification"
          class="bg-gray-100 rounded-2xl border !border-gray-200 px-3 py-3"
        >
          <div class="content">
            <h5 class="text-sm text-gray-800 font-medium">
              {{ notification.title }}
            </h5>
            <time class="text-xs text-gray-500">{{ notification.date }}</time>
          </div>
          <button
            v-if="notification.is_action === 1 && !isEmpty(actionResolver(notification))"
            class="text-sm font-medium border-b !border-black hover:text-gray-600 hover:!border-gray-600 mt-1"
            @click="actionResolver(notification).onClick"
          >
            {{ actionResolver(notification).buttonLabel }}
          </button>
        </div>
      </div>

      <div
        v-if="skeletonLoading"
        class="space-y-3"
      >
        <div
          v-for="i in 3"
          :key="i"
          class="bg-gray-100 rounded-2xl border !border-gray-200 px-3 py-3 animate-pulse"
        >
          <div class="content">
            <h5 class="text-sm text-gray-800 font-medium bg-gray-300 rounded h-4 w-52"></h5>
            <div class="text-xs text-gray-500 bg-gray-300 rounded h-3 w-20 mt-2"></div>
          </div>
        </div>
      </div>

      <div
        v-if="currentPage < totalPage && !skeletonLoading"
        class="text-center"
      >
        <TwButton
          class="text-xs !py-2"
          @click="handlePagination"
        >
          See More
        </TwButton>
      </div>
    </div>
  </aside>
</template>

<style lang="scss">
.scroll-container::-webkit-scrollbar {
  width: 4px;
}

.scroll-container::-webkit-scrollbar-track {
  background: white;
}

.scroll-container::-webkit-scrollbar-thumb {
  background: #a7a8a9;
}

.scroll-container::-webkit-scrollbar-thumb:hover {
  background: #888;
}
</style>
