<script setup>
import { useGlobalData } from '@/store/global'
import { IconChevronLeft, IconHelp, IconShoppingCart } from '@tabler/icons-vue'
import { storeToRefs } from 'pinia'
import ModalHelp from '@/views/user/components/ModalHelp.vue'
import CartSidebar from '@/views/user/components/CartSidebar.vue'
import { useCartStore } from '@/store/cartStore'
import { useRoute } from 'vue-router'

const props = defineProps({
  showBackButton: {
    type: Boolean,
    default: false,
  },
})

const route = useRoute()

const store = useGlobalData()
const cartStore = useCartStore()
const { backBtnDisabled } = storeToRefs(store)
const { totalItems } = storeToRefs(cartStore)
const modalHelpRef = ref(null)

const openCartSidebar = () => {
  cartStore.openCartSidebar()
}
</script>

<template>
  <nav class="flex justify-center items-center px-11 py-3 w-full bg-white max-md:px-5 max-md:max-w-full relative">
    <button
      v-if="showBackButton"
      class="absolute top-0 left-0 cursor-pointer py-7 px-5 sm:!px-10 disabled:cursor-not-allowed disabled:text-gray-500"
      :disabled="backBtnDisabled"
      @click="$router.go(-1)"
    >
      <IconChevronLeft
        stroke-width="2.5"
        class="w-6 h-6"
      />
    </button>
    <AppLogo />
    <div class="absolute top-[10px] right-[10px] flex items-center">
      <button
        v-if="route.name !== 'user-cart'"
        v-tooltip.bottom="'Cart'"
        type="button"
        class="cursor-pointer p-4 px-2 disabled:cursor-not-allowed disabled:text-gray-500 relative"
        @click="openCartSidebar"
      >
        <IconShoppingCart
          class="h-6 w-6 cursor-pointer text-black"
          stroke-width="2"
        />
        <span
          v-if="totalItems > 0"
          class="absolute top-2 right-1 bg-gray-800 text-white text-[11px] font-medium rounded-full h-5 w-5 flex items-center justify-center"
        >
          {{ totalItems > 9 ? '9+' : totalItems }}
        </span>
      </button>

      <button
        v-tooltip.bottom="'Help'"
        type="button"
        class="cursor-pointer p-4 px-3 disabled:cursor-not-allowed disabled:text-gray-500"
        @click="modalHelpRef.open()"
      >
        <IconHelp
          class="h-6 w-6 cursor-pointer text-black"
          stroke-width="2"
        />
      </button>
    </div>
  </nav>

  <!-- Help modal -->
  <ModalHelp ref="modalHelpRef" />

  <!-- Cart sidebar -->
  <CartSidebar />
</template>
