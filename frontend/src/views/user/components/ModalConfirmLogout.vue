<script setup>
import { ref } from 'vue'

const props = defineProps({
  closeOnClickOutside: {
    type: Boolean,
    default: true,
  },
})

const emit = defineEmits(['confirmed'])

const isOpen = ref(false)
const isLoading = ref(false)

function open() {
  isOpen.value = true
}

function close() {
  isOpen.value = false
}

function handleOverlayClick(event) {
  if (props.closeOnClickOutside && event.target === event.currentTarget) {
    close()
  }
}

defineExpose({ open, close })
</script>

<template>
  <div
    v-if="isOpen"
    class="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center overflow-y-auto overflow-x-hidden top-0 right-0 left-0 z-50 w-full md:inset-0 h-full max-h-full"
    @click="handleOverlayClick"
  >
    <div class="relative p-4 w-full max-w-2xl max-h-full">
      <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
        <div class="px-5 py-8 md:p-10">
          <h2 class="text-center text-2xl font-bold leading-tight text-black">
            Are you sure you want to logout?
          </h2>
          <div class="flex justify-center gap-3 mt-6">
            <TwButton
              class="w-48"
              variant="secondary"
              :disabled="isLoading"
              @click="close"
            >
              Cancel
            </TwButton>
            <TwButton
              :loading="isLoading"
              class="w-48"
              @click="() => {
                isLoading = true
                emit('confirmed', true)
              }"
            >
              Logout
            </TwButton>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
