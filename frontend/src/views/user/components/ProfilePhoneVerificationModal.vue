<script setup>
import { ref } from 'vue'
import { vMaska } from 'maska'
import { IconClockHour4, IconX } from '@tabler/icons-vue'
import ApiService from '@/services/ApiService'
import { isEmptyObject, isNullOrUndefined } from '@/@core/utils'
import { plainPhoneNumber } from '@/utils/helpers'
import { toast } from 'vue-sonner'

const props = defineProps({
  closeOnClickOutside: {
    type: Boolean,
    default: true,
  },
})

const emit = defineEmits(['phoneVerified', 'closeModal'])

const changedPhoneNumber = ref('')
const isOpen = ref(false)
const timer = ref(null)
const verificationCode = ref('')
const isLoading = ref(false)
const isLoadingResendCode = ref(false)
const inputErrors = ref({})

function open(phone) {
  isOpen.value = true
  changedPhoneNumber.value = phone
  startTimer()
}

let intervalId

const startTimer = () => {
  const countDownDate = new Date(Date.now() + 120 * 1000).getTime()
  let mins = 1
  let secs = 59
  timer.value = `0${mins}:${secs}`

  if (intervalId) {
    clearInterval(intervalId)
  }

  intervalId = setInterval(() => {
    const now = new Date().getTime()
    const distance = countDownDate - now

    if (distance < 0) {
      clearInterval(intervalId)
      timer.value = null

      return
    }

    const minutes = Math.floor((distance / (1000 * 60)) % 60)
    const seconds = Math.floor((distance / 1000) % 60)

    mins = minutes < 10 ? '0' + minutes : minutes
    secs = seconds < 10 ? '0' + seconds : seconds

    timer.value = `${mins}:${secs}`
  }, 1000)
}

async function resendNewCode() {
  try {
    isLoadingResendCode.value = true

    const postData = {
      phone_number: plainPhoneNumber(changedPhoneNumber.value),
      type: 'change_phone_number',
    }

    const { data } = await ApiService.post('/resend-sms-otp', postData)

    if (data.status === 200) {
      toast.success(data.message)
      startTimer()
    } else {
      if (data.message) {
        toast.error(data.message)
      }
    }
  } catch (error) {
    if (error.data.message) {
      toast.error(error.data.message)
    } else if (error.message) {
      toast.error(error.message)
    }
    console.error(error)
  } finally {
    isLoadingResendCode.value = false
  }
}

async function handleVerifySubmit() {
  try {
    isLoading.value = true
    inputErrors.value = {}

    const postData = {
      otp: verificationCode.value,
      phone_number: plainPhoneNumber(changedPhoneNumber.value),
      type: 'change_phone_number',
    }

    const { data } = await ApiService.post('/verify-sms-otp', postData)

    if (data.status === 200) {
      toast.success(data.message)
      close()
      emit('phoneVerified', true)
    } else {
      isLoading.value = false
      if (data.errors) {
        if (data.errors) {
          inputErrors.value = data.errors
        }
      } else if (data.message) {
        toast.error(data.message)
      }
    }
  } catch (error) {
    isLoading.value = false
    if (error.response.errors) {
      if (error.response.errors) {
        inputErrors.value = error.response.errors
      }
    } else if (error.data.message) {
      toast.error(error.data.message)
    } else if (error.message) {
      toast.error(error.message)
    }
    console.error(error)
  }
}

function close() {
  isOpen.value = false
}

function handleOutsideClick() {
  if (props.closeOnClickOutside) {
    close()
  }
}

function handleClose() {
  close()
  emit('closeModal', true)
}

defineExpose({ open, close })
</script>

<template>
  <div
    v-if="isOpen"
    class="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center overflow-y-auto overflow-x-hidden top-0 right-0 left-0 z-50 w-full md:inset-0 h-full max-h-full"
    @click="handleOutsideClick"
  >
    <div class="relative p-4 w-full max-w-2xl max-h-full">
      <div class="relative inline-block z-[999] top-12 right-[-88%] sm:right-[-94%]">
        <IconX
          class="cursor-pointer h-6 w-6"
          stroke-width="2"
          @click="handleClose"
        />
      </div>
      <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
        <div class="px-5 py-8 md:p-10">
          <div class="xl:mx-auto xl:w-full xl:max-w-sm 2xl:max-w-md">
            <h2 class="text-center text-2xl md:text:3xl font-bold leading-tight text-black mt-3">
              Verifying your identity to ensure it's genuinely you.
            </h2>
            <p class="mt-2 text-center text-gray-600">
              Please enter the code we sent to <span class="font-medium">{{ changedPhoneNumber }}</span>.
            </p>
            <form
              method="POST"
              class="mt-8"
              @submit.prevent="handleVerifySubmit"
            >
              <div class="space-y-5">
                <div class="relative mb-4">
                  <input
                    v-model="verificationCode"
                    v-maska
                    type="text"
                    class="block w-full px-5 py-3 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer text-center"
                    placeholder="######"
                    data-maska="######"
                    required
                  />
                  <p
                    v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.otp)"
                    class="text-red-500 text-sm ms-5"
                  >
                    {{ inputErrors.otp[0] }}
                  </p>
                </div>

                <div class="flex flex-col sm:flex-row justify-center sm:justify-between gap-3">
                  <div class="flex gap-3 justify-center">
                    <button
                      class="underline text-black hover:text-black/80 font-medium disabled:text-gray-400 disabled:cursor-not-allowed"
                      :disabled="timer !== null || isLoadingResendCode"
                      @click="resendNewCode"
                    >
                      Send new code
                    </button>
                    <div
                      v-if="timer"
                      class="flex items-center gap-1 bg-[#FFEF08] px-1.5 py-0.5 text-sm rounded-full"
                    >
                      <span>
                        <IconClockHour4
                          class="w-4 h-4 mb-[2px]"
                          stroke-width="2"
                        />
                      </span>
                      <span>{{ timer }}</span>
                    </div>
                  </div>
                </div>

                <div class="mt-8">
                  <TwButton
                    type="submit"
                    class="w-full"
                    :loading="isLoading"
                  >
                    Submit
                  </TwButton>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
