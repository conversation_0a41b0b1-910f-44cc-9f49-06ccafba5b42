<script setup>
import { ref, computed } from 'vue'
import { isEmpty } from '@/@core/utils'
import router from '@/router'
import { useSessionStorage } from '@vueuse/core'
import { toast } from 'vue-sonner'
import { clearRetrySession } from '@/utils/sessionHelpers'

const props = defineProps({
  visit: {
    type: Object,
    required: true,
  },
})

const edVisitSession = useSessionStorage('edVisitSession', {})
const hlVisitSession = useSessionStorage('hlVisitSession', {})
const wlVisitSession = useSessionStorage('wlVisitSession', {})
const btnLoading = ref('')

const visitCategory = computed(() => {
  if (!props.visit) return null

  return props.visit.category.toUpperCase()
})

const visitTitle = computed(() => {
  if (!props.visit) return null

  let title = ''
  if (visitCategory.value === 'ED') {
    if (!isEmpty(props.visit.productData)) {
      title = 'Resume your ED renewal'
    } else if (props.visit.visit_type === 'followup') {
      title = 'Continue your ED follow-up visit'
    } else {
      title = 'Continue your ED visit'
    }
  } else if (visitCategory.value === 'HL') {
    if (!isEmpty(props.visit.productData)) {
      title = 'Resume your Hair treatment renewal'
    } else if (props.visit.visit_type === 'followup') {
      title = 'Continue your Hair treatment follow-up visit'
    } else {
      title = 'Continue your Hair treatment visit'
    }
  } else if (visitCategory.value === 'WL') {
    if (props.visit.visit_type === 'followup') {
      title = 'Continue your Weight Loss follow-up visit'
    } else {
      title = 'Continue your Weight Loss visit'
    }
  }

  return title
})

const visitDescription = computed(() => {
  if (!props.visit) return null

  let description = ''
  if (visitCategory.value === 'ED') {
    if (!isEmpty(props.visit.productData)) {
      description = 'You recently started renewing your Erectile Dysfunction visit. Please complete the remaining steps to finish your renewal.'
    } else if (props.visit.visit_type === 'followup') {
      description = 'You recently started a follow-up visit for Erectile Dysfunction. Please complete the remaining steps to finish your follow-up.'
    } else {
      description = 'You recently started a visit for Erectile Dysfunction. Please complete the remaining steps to finish your visit.'
    }
  } else if (visitCategory.value === 'HL') {
    if (!isEmpty(props.visit.productData)) {
      description = 'You recently started renewing your Hair Treatment visit. Please complete the remaining steps to finish your renewal.'
    } else if (props.visit.visit_type === 'followup') {
      description = 'You recently started a follow-up visit for Hair Treatment. Please complete the remaining steps to finish your follow-up.'
    } else {
      description = 'You recently started a visit for Hair Treatment. Please complete the remaining steps to finish your visit.'
    }
  } else if (visitCategory.value === 'WL') {
    if (props.visit.visit_type === 'followup') {
      description = 'You recently started a follow-up visit for Weight Loss. Please complete the remaining steps to finish your follow-up.'
    } else {
      description = 'You recently started a visit for Weight Loss. Please complete the remaining steps to finish your visit.'
    }
  }

  return description
})

function handleCompleteVisitClick() {
  clearRetrySession()
  if (visitCategory.value === 'ED') {
    handleCompleteEdVisit(props.visit)
  } else if (visitCategory.value === 'HL') {
    handleCompleteHlVisit(props.visit)
  } else if (visitCategory.value === 'WL') {
    handleCompleteWlVisit(props.visit)
  }
}

function handleCompleteEdVisit(visit) {
  btnLoading.value = visit.id

  if (isEmpty(visit.productData)) {
    edVisitSession.value = {}
    edVisitSession.value['questionSessionId'] = visit.id
    if (visit.visit_type === 'followup') {
      edVisitSession.value['visitType'] = 'followup'
      edVisitSession.value['subscriptionId'] = visit.followup_visit_id
      router.push({ name: 'ed-visit-current-treatment' })
    } else {
      edVisitSession.value['visitType'] = 'new'
      router.push({ name: 'ed-visit-product-recommended' })
    }
  } else {
    const productData = visit.productData

    if (!isEmpty(productData)) {
      edVisitSession.value = {}
      edVisitSession.value = {
        visitType: 'renew',
        questionSessionId: visit.id,
        subscriptionId: productData.previous_subscription_id,

        // category: productData.category,
        // productFreq: productData.frequency,
        // productId: productData.ed_product_id,
        // strengthId: productData.ed_product_strength_id,
        // productQty: productData.ed_product_qty_id,
        // planId: productData.ed_product_subscription_plan_id,
        // selectedShippingMethod: productData.pharmacy_shipping_method_id,
        // productType: productData.product_type,
      }
      router.push({ name: 'ed-visit-current-treatment' })
    } else {
      toast.error('We are unable to complete your pending visit at this time. Please try again later.')
    }
  }
}

function handleCompleteHlVisit(visit) {
  btnLoading.value = visit.id

  if (isEmpty(visit.productData)) {
    hlVisitSession.value = {}
    hlVisitSession.value['questionSessionId'] = visit.id
    if (visit.visit_type === 'followup') {
      hlVisitSession.value['visitType'] = 'followup'
      hlVisitSession.value['subscriptionId'] = visit.followup_visit_id
      router.push({ name: 'hl-visit-current-treatment' })
    } else {
      hlVisitSession.value['visitType'] = 'new'
      router.push({ name: 'hl-visit-product-recommended' })
    }
  } else {
    const productData = visit.productData

    if (!isEmpty(productData)) {
      hlVisitSession.value = {}
      hlVisitSession.value = {
        visitType: 'renew',
        questionSessionId: visit.id,
        subscriptionId: productData.previous_subscription_id,

        // category: productData.category,
        // productId: productData.hl_product_id,
        // strengthId: productData.hl_product_strength_id,
        // productQty: productData.hl_product_qty_id,
        // planId: productData.hl_product_subscription_plan_id,
        // selectedShippingMethod: productData.pharmacy_shipping_method_id,
      }
      router.push({ name: 'hl-checkout' })
    } else {
      toast.error('We are unable to complete your pending visit at this time. Please try again later.')
    }
  }
}

function handleCompleteWlVisit(visit) {
  btnLoading.value = visit.id
  window.sessionStorage.removeItem('isFullBodyImgLatest')
  wlVisitSession.value = {}
  wlVisitSession.value['questionSessionId'] = visit.id

  if (visit?.followup_visit_id) {
    // Follow-up visit
    wlVisitSession.value['subscriptionId'] = visit.followup_visit_id
    router.push({ name: 'wl-followup-products' })
  } else {
    // New visit
    router.push({ name: 'wl-products' })
  }
}

function handleStartNewVisit() {
  edVisitSession.value = {}
  hlVisitSession.value = {}
  wlVisitSession.value = {}

  router.push({ name: `${visitCategory.value.toLowerCase()}-questions`, params: { slug: 'start' } })
}
</script>

<template>
  <div
    v-if="visit"
    class="bg-white rounded-[20px] p-5"
  >
    <h5 class="text-gray-800 text-base font-semibold uppercase mb-2">
      {{ visitTitle }}
    </h5>
    <p class="text-base font-normal mb-5">
      {{ visitDescription }}
    </p>
    <TwButton
      class="w-full mb-3"
      :loading="btnLoading === visit.id"
      :disabled="!isEmpty(btnLoading)"
      @click="handleCompleteVisitClick"
    >
      {{ visit.visit_type === 'new' ? 'Complete Visit' : 'Complete Follow-up Visit' }}
    </TwButton>

    <div
      v-if="visit.visit_type === 'new'"
      class="text-center"
    >
      <button
        class="inline-flex items-center text-sm text-black font-medium border-b !border-black hover:text-gray-700 hover:!border-gray-700 disabled:cursor-not-allowed"
        :disabled="!isEmpty(btnLoading)"
        @click="handleStartNewVisit"
      >
        Start New Visit
      </button>
    </div>
  </div>
</template>
