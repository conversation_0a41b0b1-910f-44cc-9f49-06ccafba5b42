<script setup>
import ApiService from '@/services/ApiService'
import { processErrors } from '@/utils/errorHandler'
import { IconPlus, IconX } from '@tabler/icons-vue'
import { onMounted, ref } from 'vue'
import AlertError from '@/views/user/components/AlertError.vue'
import { loadScript } from '@paypal/paypal-js'
import { toast } from 'vue-sonner'

const skeletonLoading = ref(false)
const isLoading = ref(false)
const serverErrors = ref([])
const scrollToEle = ref(null)
const paymentMethods = ref([])
const editMode = ref(false)

onMounted(async () => {
  skeletonLoading.value = true
  await fetchPaymentMethods()
  skeletonLoading.value = false
})

async function fetchPaymentMethods() {
  try {
    serverErrors.value = []

    const { data } = await ApiService.get('/list-payment-methods')

    if (data.status === 200) {
      paymentMethods.value = data.paymentMethodList
    } else {
      serverErrors.value = processErrors(data)
      scrollToEle.value.scrollIntoView({ behavior: 'smooth' })
    }
  } catch (error) {
    console.error(error)
    serverErrors.value = processErrors(error)
    scrollToEle.value.scrollIntoView({ behavior: 'smooth' })
  }
}

function handleDefaultPaymentMethodChange(paymentMethodId) {
  toast.promise(setAsDefaultPaymentMethod(paymentMethodId), {
    loading: 'Changing your primary payment method...',
    success: data => data,
    error: data => data,
  })
}

async function setAsDefaultPaymentMethod(paymentMethodId) {
  return new Promise(async (resolve, reject) => {
    try {
      isLoading.value = true
      serverErrors.value = []

      const { data } = await ApiService.get(`/default-payment-method/${paymentMethodId}`)

      if (data.status === 200) {
        await fetchPaymentMethods()
        resolve(data.message)

        // toast.success(data.message)
      } else {
        reject(data.message)

        // serverErrors.value = processErrors(data)
        // scrollToEle.value.scrollIntoView({ behavior: 'smooth' })
      }
    } catch (error) {
      reject(error.message)
      console.error(error)

      // serverErrors.value = processErrors(error)
      // scrollToEle.value.scrollIntoView({ behavior: 'smooth' })
    } finally {
      isLoading.value = false
    }
  })
}

async function deletePaymentMethod(paymentMethodId) {
  try {
    isLoading.value = true
    serverErrors.value = []

    const { data } = await ApiService.delete(`/delete-payment-method/${paymentMethodId}`)

    if (data.status === 200) {
      await fetchPaymentMethods()
      toast.success(data.message)
    } else {
      serverErrors.value = processErrors(data)
      scrollToEle.value.scrollIntoView({ behavior: 'smooth' })
    }
  } catch (error) {
    console.error(error)
    serverErrors.value = processErrors(error)
    scrollToEle.value.scrollIntoView({ behavior: 'smooth' })
  } finally {
    isLoading.value = false
  }
}

// add new payment method
const cardFieldErrors = ref([])
const cardFieldsRendered = ref(false)

function addNewPaymentMethod() {
  editMode.value = true
  initPaypal()
}

function discardEdit() {
  editMode.value = false
}

async function initCardForm() {
  // Clear existing content
  const cardFieldsWrapper = document.getElementById('cardFieldsWrapper')

  cardFieldsWrapper.innerHTML = ''

  // Create elements
  const cardNumberDiv = document.createElement('div')

  cardNumberDiv.id = 'card-number'
  cardNumberDiv.classList.add('w-full')

  const expirationDateDiv = document.createElement('div')

  expirationDateDiv.id = 'expiration-date'
  expirationDateDiv.classList.add('w-full')

  const cvvDiv = document.createElement('div')

  cvvDiv.id = 'cvv'
  cvvDiv.classList.add('w-full')

  // Append elements to the parent container
  cardFieldsWrapper.appendChild(cardNumberDiv)
  cardFieldsWrapper.appendChild(expirationDateDiv)
  cardFieldsWrapper.appendChild(cvvDiv)
}

async function initPaypal() {
  isLoading.value = true
  cardFieldsRendered.value = false
  await initCardForm()

  const paypal = await loadScript({
    'client-id': (import.meta.env.VITE_PAYPAL_CLIENT_ID),
    components: 'card-fields',
  })

  const inputStyles = {
    'input': {
      'padding': '0.75rem 1.25rem',
      'font-size': '1rem',
      'font-weight': '500',
      'font-family': 'Konnect',
      'appearance': 'none',
      'outline': 'none',
      'transition': 'border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out',
    },
  }

  const cardFields = paypal.CardFields({
    style: inputStyles,
    createVaultSetupToken: async () => {
      const { data } = await ApiService.get('/paypal/vault/token/create')
      if (data.status === 200) return data.vaultId
      else throw data
    },
    onApprove: async data => {
      const vaultId = data.vaultSetupToken
      const response = await ApiService.get(`/paypal/vault/payment/token/create/${vaultId}`)
      if (response.data.status === 200) {
        toast.success(response.data.message)
        editMode.value = false

        skeletonLoading.value = true
        await fetchPaymentMethods()
        skeletonLoading.value = false
      } else {
        throw response.data
      }
    },
    onError: async error => {
      if (error.message === 'Internal server error') {
        cardFieldErrors.value.push('Please check your card details and try again.')
      } else {
        cardFieldErrors.value.push(error.message || 'Something went wrong. Please try again.')
      }
    },
  })

  if (cardFields.isEligible()) {
    cardFields.NumberField().render('#card-number')
    cardFields.ExpiryField().render('#expiration-date')
    cardFields.CVVField().render('#cvv')

    isLoading.value = false
    cardFieldsRendered.value = true
  } else {
    toast.error('Credit and debit cards are not available. Please try again later.')
  }

  const submitButton = document.getElementById('saveCardBtn')

  submitButton.addEventListener('click', () => {
    isLoading.value = true
    cardFieldErrors.value = []

    cardFields
      .submit()
      .then(() => {
        // console.log("submit was successful")
      })
      .catch(error => {
        isLoading.value = false
        if (error.message === 'INVALID_NUMBER') {
          cardFieldErrors.value.push('Please enter a valid card number.')
        }
        if (error.message === 'INVALID_CVV') {
          cardFieldErrors.value.push('Please enter a valid CVV number.')
        }
        if (error.message === 'INVALID_EXPIRY') {
          cardFieldErrors.value.push('Please enter a valid expiration date.')
        }
      })
  })
}

function isCardExpired(paymentMethod) {
  const now = new Date()
  const expirationDate = new Date(paymentMethod.card_expiry_year, paymentMethod.card_expiry_month - 1)

  return now > expirationDate
}
</script>

<template>
  <div class="bg-white py-5 px-6 rounded-[20px]">
    <div class="flex justify-between mb-3">
      <div class="text-xl font-semibold">
        Payment Method
      </div>
      <div>
        <button
          v-if="!editMode"
          class="text-sm font-medium border !border-zinc-800 px-4 py-1.5 rounded-full hover:bg-gray-100 transition-all duration-300"
          @click="addNewPaymentMethod"
        >
          <span class="hidden sm:inline">Add new payment method</span>
          <span class="inline sm:hidden">Add</span>
        </button>
      </div>
    </div>

    <div
      ref="scrollToEle"
      class="h-0"
    ></div>

    <AlertError
      v-if="serverErrors.length > 0"
      title="Error!"
      :errors="serverErrors"
    />

    <div
      v-if="skeletonLoading"
      class="space-y-3"
    >
      <div class="h-16 bg-gray-300 rounded animate-pulse"></div>
      <div class="h-16 bg-gray-300 rounded animate-pulse"></div>
      <div class="h-16 bg-gray-300 rounded animate-pulse"></div>
    </div>

    <div v-else>
      <div v-if="!editMode">
        <div
          v-if="paymentMethods.length > 0"
          class="space-y-3"
        >
          <div
            v-for="paymentMethod in paymentMethods"
            :key="paymentMethod.id"
            class="card-item flex gap-2 flex-col md:flex-row md:justify-between border !border-gray-300 rounded-lg p-3"
            :class="{ 'bg-gray-100': isCardExpired(paymentMethod) }"
          >
            <div class="card-details flex-1">
              <div class="inline-flex items-center gap-x-1 text-sm font-medium text-zinc-700">
                {{ paymentMethod.card_brand_type }}
              </div>
              <div class="text-sm text-zinc-900 font-medium">
                xxxx xxxx xxxx {{ paymentMethod.card_last_4_digit }}
              </div>
              <div class="w-full text-xs mt-0.5">
                <span
                  v-if="isCardExpired(paymentMethod)"
                  class="bg-red-300 text-red-700 font-medium  px-2 py-0.5 rounded-full"
                >Expired</span>
                <span v-else>
                  Expires {{ paymentMethod.card_expiry_month }}/{{ paymentMethod.card_expiry_year }}
                </span>
              </div>
            </div>
            <div class="flex-1 card-actions flex items-center md:justify-end gap-1">
              <label
                v-if="!isCardExpired(paymentMethod)"
                class="inline-flex items-center me-5 cursor-pointer"
              >
                <span class="me-3 text-sm font-medium text-gray-900">
                  {{ paymentMethod.is_default ? 'Primary' : 'Set as primary' }}
                </span>
                <input
                  name="default_payment_method"
                  type="radio"
                  value=""
                  class="sr-only peer"
                  :checked="paymentMethod.is_default"
                  :disabled="isLoading || paymentMethod.is_default"
                  @change="handleDefaultPaymentMethodChange(paymentMethod.id)"
                >
                <div class="relative w-11 h-6 bg-gray-200 rounded-full peer peer-focus:ring-4 peer-focus:ring-green-300 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-400 peer-disabled:cursor-not-allowed"></div>
              </label>
              <button
                v-tooltip.bottom="{
                  value: paymentMethod.is_default
                    ? 'Primary payment method cannot be deleted'
                    : paymentMethod.is_card_associated_with_active_order
                      ? 'Card associated with an active subscription cannot be deleted'
                      : 'Delete payment method',
                  showDelay: 300,
                }"
                class="bg-red-500 text-xs font-medium p-1 rounded-full hover:bg-red-600 transition-all duration-300 disabled:cursor-not-allowed disabled:bg-red-300"
                :disabled="isLoading || paymentMethod.is_default || paymentMethod.is_card_associated_with_active_order"
                @click="deletePaymentMethod(paymentMethod.id)"
              >
                <IconX
                  class="w-4 h-4 text-white"
                  stroke-width="3"
                />
              </button>
            </div>
          </div>
        </div>
        <div v-else>
          <div class="text-sm font-medium text-zinc-700">
            No payment methods found
          </div>
        </div>
      </div>

      <div v-show="editMode">
        <AlertError
          v-if="cardFieldErrors.length > 0"
          title="Error!"
          :errors="cardFieldErrors"
        />
        <div id="cardFieldsWrapper">
          <!--
            <div
            id="card-number"
            class="w-full"
            ></div>
            <div
            id="expiration-date"
            class="w-full"
            ></div>
            <div
            id="cvv"
            class="w-full"
            ></div>
          -->
        </div>

        <div class="mt-8 flex gap-3">
          <button
            type="button"
            class="inline-flex w-full items-center justify-center rounded-full border !border-black px-3.5 py-2.5 font-semibold leading-7 text-black hover:bg-gray-200 text-sm disabled:cursor-not-allowed disabled:bg-gray-300"
            :disabled="isLoading"
            @click="discardEdit"
          >
            Discard
          </button>
          <TwButton
            id="saveCardBtn"
            value="submit"
            class="w-full"
            :disabled="!cardFieldsRendered"
            :loading="isLoading"
          >
            Save Card
          </TwButton>
        </div>
      </div>
    </div>
  </div>
</template>
