<script setup>
import Sidebar from 'primevue/sidebar'

const props = defineProps({
  visible: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits(['update:visible'])

const handleClose = () => {
  emit('update:visible', false)
}
</script>

<template>
  <Sidebar
    :visible="props.visible"
    position="right"
    show-close-icon
    class="payment-and-refund-sidebar"
    @update:visible="(val) => emit('update:visible', val)"
  >
    <div class="w-full">
      <h2 class="text-2xl font-bold leading-tight text-black mb-3">
        Why am I being charged for a physician fee?
      </h2>
      <div class="mb-6 text-base leading-[1.7] space-y-3">
        <div>
          You will be charged a physician fee because White Label Rx connects you with a physician to do a text-based consult to prescribe your medication when ordering medicine.
        </div>
      </div>
      <TwButton
        class="w-full"
        @click="handleClose"
      >
        Got it
      </TwButton>
    </div>
  </Sidebar>
</template>
