<script setup>
import { ref } from 'vue'
import ModalHelp from '@/views/user/components/ModalHelp.vue'

const modalHelpRef = ref(null)
</script>

<template>
  <div class="bg-white py-5 px-6 rounded-[20px]">
    <div class="text-xl font-semibold mb-3">
      Shipping Address
    </div>
    <div class="space-y-5">
      <div>
        <div class="text-sm text-zinc-700">
          Orders
        </div>
        <div class="text-zinc-900">
          Need to change the address of an order that's in-progress? <span
            type="button"
            class="underline font-medium hover:text-black"
            @click="modalHelpRef.open()"
          >Contact customer support.</span>
        </div>
      </div>
      <div>
        <div class="text-sm text-zinc-700">
          Subscriptions
        </div>
        <div class="text-red-500">
          Update your shipping address in your <RouterLink
            :to="{ name: 'user-subscription' }"
            class="text-red-600 font-medium underline"
          >
            subscriptions page.
          </RouterLink>
        </div>
      </div>
    </div>

    <!-- Help modal -->
    <ModalHelp ref="modalHelpRef" />
  </div>
</template>
