<script setup>
import ApiService from '@/services/ApiService'
import { processErrors } from '@/utils/errorHandler'
import { IconX } from '@tabler/icons-vue'
import { ref } from 'vue'
import { toast } from 'vue-sonner'

const props = defineProps({
  closeOnClickOutside: {
    type: Boolean,
    default: true,
  },
})

const isOpen = ref(false)
const orderId = ref(null)
const skeletonLoading = ref(true)
const trackingDetails = ref([])

async function open(id) {
  isOpen.value = true
  skeletonLoading.value = true
  orderId.value = id

  await fetchTrackingDetails()
}

async function fetchTrackingDetails() {
  try {
    skeletonLoading.value = true

    const { data } = await ApiService.get(`/order-tracking-details/${orderId.value}`)

    if (data.status === 200) {
      trackingDetails.value = data.trackingDetails
    } else {
      toast.error(data.message)
    }
  } catch (error) {
    toast.error(processErrors(error)[0])
  } finally {
    skeletonLoading.value = false
  }
}

function close() {
  isOpen.value = false
}

function handleOutsideClick() {
  if (props.closeOnClickOutside) {
    close()
  }
}

defineExpose({ open, close })
</script>

<template>
  <div
    v-if="isOpen"
    class="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center overflow-y-auto overflow-x-hidden top-0 right-0 left-0 z-50 w-full md:inset-0 h-full max-h-full"
  >
    <div class="relative p-4 w-full max-w-2xl max-h-full">
      <div class="relative inline-block z-[999] top-12 right-[-88%] sm:right-[-94%]">
        <IconX
          class="cursor-pointer h-6 w-6"
          stroke-width="2"
          @click="close"
        />
      </div>
      <div class="relative bg-white rounded-[20px] shadow">
        <div class="px-5 py-8 md:p-10">
          <h2 class="text-center text-2xl md:text:3xl font-bold leading-tight text-black">
            Shipping Activity
          </h2>
          <div class="mt-5">
            <div
              v-if="skeletonLoading"
              class="space-y-4 max-h-[400px] overflow-y-auto pb-10 animate-pulse"
            >
              <div
                v-for="i in 2"
                :key="i"
                class="bg-gray-100 rounded-[20px] p-4 border-s-2 border-black"
              >
                <div class="h-4 bg-gray-200 rounded-full w-1/2"></div>
                <div class="mt-1">
                  <div class="h-4 bg-gray-200 rounded w-full mt-2"></div>
                  <div class="h-4 bg-gray-200 rounded w-2/3 mt-2"></div>
                </div>
                <div class="h-4 bg-gray-200 rounded w-1/2 mt-2"></div>
              </div>
            </div>
            <div
              v-else
              class="space-y-4 max-h-[400px] overflow-y-auto pb-10"
            >
              <div
                v-for="(item, index) in trackingDetails"
                :key="index"
                class="bg-gray-100 rounded-[20px] p-4 border-s-2 !border-black"
              >
                <span
                  v-if="item.EventStatusText"
                  class="bg-blue-200 text-blue-700 px-3 py-1 rounded-lg text-sm font-medium"
                >
                  {{ item.EventStatusText }}
                </span>
                <div class="mt-1">
                  <h5
                    v-if="item.EventDescription"
                    class="text-lg text-gray-900"
                  >
                    {{ item.EventDescription }}
                  </h5>
                  <p
                    v-if="item.EventLocation"
                    class="text-sm text-gray-600"
                  >
                    {{ item.EventLocation }}
                  </p>
                </div>
                <div
                  v-if="item.EventDate"
                  class="text-xs text-amber-500 mt-2"
                >
                  {{ item.EventDate }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
