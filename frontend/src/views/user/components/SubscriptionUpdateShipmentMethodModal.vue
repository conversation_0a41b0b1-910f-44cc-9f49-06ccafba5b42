<script setup>
import ApiService from '@/services/ApiService'
import { processErrors } from '@/utils/errorHandler'
import { formatCurrency } from '@/utils/helpers'
import AlertError from '@/views/user/components/AlertError.vue'
import { IconCheck } from '@tabler/icons-vue'
import Sidebar from 'primevue/sidebar'
import { useRoute } from 'vue-router'
import { toast } from 'vue-sonner'

const emit = defineEmits(['updated'])

const route = useRoute()

const isVisible = ref(false)
const skeletonLoading = ref(false)
const isLoading = ref(false)
const serverErrors = ref([])
const subscriptionId = ref(route.params.subscriptionId)
const shippingMethods = ref([])
const selectedShippingMethod = ref('')

async function updateShippingMethod() {
  try {
    isLoading.value = true
    serverErrors.value = []

    const postData = {
      id: subscriptionId.value,
      pharmacy_shipment_method_id: selectedShippingMethod.value,
    }

    const { data } = await ApiService.post('/update-subscription-pharmacy-shipment-method', postData)

    if (data.status === 200) {
      toast.success(data.message)
      emit('updated')
      closeModal()
    } else {
      isLoading.value = false
      serverErrors.value = processErrors(data)
    }
  } catch (error) {
    isLoading.value = false
    serverErrors.value = processErrors(error)
    console.error(error)
  }
}

function openModal(methods, selected) {
  skeletonLoading.value = true

  shippingMethods.value = methods
  selectedShippingMethod.value = methods.find(m => m.id === selected)?.id
  serverErrors.value = []
  isVisible.value = true
  isLoading.value = false

  skeletonLoading.value = false
}

function closeModal() {
  isVisible.value = false
}

defineExpose({ openModal, closeModal })
</script>

<template>
  <Sidebar
    v-model:visible="isVisible"
    position="right"
    show-close-icon
    class="update-shipping-sidebar"
    @hide="closeModal"
  >
    <div class="w-full">
      <h2 class="text-2xl font-bold leading-tight text-black mb-3">
        Update Shipping Method
      </h2>

      <AlertError
        v-if="serverErrors.length > 0"
        title="Error!"
        :errors="serverErrors"
      />

      <div v-if="skeletonLoading">
        <div class="px-4 w-full">
          <div class="h-12 bg-gray-300 rounded mt-6 mb-2 animate-pulse"></div>
          <div class="h-12 bg-gray-300 rounded mt-6 mb-2 animate-pulse"></div>
          <div class="h-12 bg-gray-300 rounded mt-6 mb-2 animate-pulse"></div>
          <div class="h-12 bg-gray-300 rounded mt-6 mb-2 animate-pulse"></div>
          <div class="h-12 bg-gray-300 rounded mt-6 mb-2 animate-pulse"></div>
          <div class="h-10 bg-gray-300 rounded-xl mt-8 mb-2 animate-pulse"></div>
        </div>
      </div>

      <div v-else>
        <ul class="grid w-full space-y-3">
          <li
            v-for="shippingMethod in shippingMethods"
            :key="shippingMethod.id"
          >
            <input
              :id="`shipping_method_${shippingMethod.id}`"
              v-model="selectedShippingMethod"
              type="radio"
              name="shipping_method"
              class="hidden peer"
              :value="shippingMethod.id"
              required
            />
            <label
              :for="`shipping_method_${shippingMethod.id}`"
              class="inline-flex items-center justify-between w-full px-5 py-3 text-gray-700 bg-white border !border-gray-200 rounded-2xl cursor-pointer peer-checked:!border-gray-600 peer-checked:text-black hover:text-gray-800 hover:bg-gray-100"
            >
              <div class="block">
                <div class="w-full text-lg font-medium">{{ shippingMethod.title }}</div>
                <div class="w-full text-sm">{{ shippingMethod.description }}</div>
                <div
                  v-if="shippingMethod.cost > 0"
                  class="w-full text-lg font-semibold"
                >{{ formatCurrency(shippingMethod.cost) }}</div>
              </div>
              <span class="mx-2 flex items-center justify-center">
                <span
                  class="w-4 h-4 border-2 rounded-sm flex justify-center items-center"
                  :class="[
                    selectedShippingMethod === shippingMethod.id ? 'border-transparent bg-black' : 'border-gray-400'
                  ]"
                >
                  <IconCheck
                    class="h-5 w-5 z-50"
                    :stroke-width="selectedShippingMethod === shippingMethod.id ? 4 : 1"
                    :class="[
                      selectedShippingMethod === shippingMethod.id ? 'text-white' : 'text-gray-900'
                    ]"
                  />
                </span>
              </span>
            </label>
          </li>
        </ul>

        <div>
          <TwButton
            loading-text="Updating..."
            :loading="isLoading"
            class="w-full h-10 mt-5"
            @click="updateShippingMethod"
          >
            Update
          </TwButton>
        </div>
      </div>
    </div>
  </Sidebar>
</template>

<style lang="scss">
.p-inputtext {
  border: none !important;
  border-color: transparent;
  font-family: "Konnect", system-ui, -apple-system, BlinkMacSystemFont,
    "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue",
    sans-serif;
    font-size: 14px !important;
    font-weight: 500;

  &:enabled:hover {
    border-color: transparent;
  }
  &:enabled:focus {
    outline: none;
    outline-offset: 0;
    box-shadow: none;
    border-color: transparent;
  }
}

.p-autocomplete-panel {
  max-width: 400px;
}
.p-autocomplete-panel .p-autocomplete-items .p-autocomplete-item {
  white-space: pre-line;
}
.p-autocomplete-panel {
  z-index: 2102 !important;
}
.p-sidebar-right .update-shipping-sidebar.p-sidebar {
  width: 30rem;
  font-family: "Konnect", system-ui, -apple-system, BlinkMacSystemFont,
    "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue",
    sans-serif;
}
</style>
