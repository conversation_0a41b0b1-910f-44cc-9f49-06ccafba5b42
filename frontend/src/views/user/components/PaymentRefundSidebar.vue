<script setup>
import Sidebar from 'primevue/sidebar'

const props = defineProps({
  visible: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits(['update:visible'])

const handleClose = () => {
  emit('update:visible', false)
}
</script>

<template>
  <Sidebar
    :visible="props.visible"
    position="right"
    show-close-icon
    class="payment-and-refund-sidebar"
    @update:visible="(val) => emit('update:visible', val)"
  >
    <div class="w-full">
      <h2 class="text-2xl font-bold leading-tight text-black mb-3">
        When will I be charged - and are there any hidden fees?
      </h2>
      <div class="mb-6 text-base leading-[1.7] space-y-3">
        <div>
          Rest assured, White Label Rx charges you only after a licensed healthcare provider approves your prescription. Although you might see a temporary hold on your credit card after checkout, it will only be processed once your prescription is authorized.
        </div>
        <div>
          If your provider doesn't approve your request, recommends a different treatment, or your request expires, you won't be charged anything and no shipments will be sent.
        </div>
        <div>
          Once you receive your medication, no refunds or returns are possible. All sales are final. Refer to our Terms and Conditions for more information.
        </div>
      </div>
      <TwButton
        class="w-full"
        @click="handleClose"
      >
        Got it
      </TwButton>
    </div>
  </Sidebar>
</template>
