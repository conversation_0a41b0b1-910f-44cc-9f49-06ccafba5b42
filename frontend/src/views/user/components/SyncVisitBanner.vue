<script setup>
const props = defineProps({
  visitData: {
    type: Object,
    required: true,
  },
})

const emit = defineEmits(['scheduled'])

const data = computed(() => props.visitData)

const isScheduleVideoVisitBtnClicked = ref(false)

function handleScheduleNow() {
  window.open(data.value.appointmentScheduledLink, '_blank')
  setTimeout(() => {
    isScheduleVideoVisitBtnClicked.value = true
  }, 1000)
}

function handleJoinNow() {
  window.open(data.value.appointmentUrl, '_blank')
}
</script>

<template>
  <template v-if="data.appointmentScheduledLink && !data.appointmentUrl">
    <div
      v-if="isScheduleVideoVisitBtnClicked"
      class="w-full bg-amber-100 !rounded-xl px-3 py-3 text-sm font-medium mb-4 flex flex-col gap-3 sm:flex-row sm:justify-between sm:items-center"
    >
      <h5 class="text-gray-900">
        Did you schedule the video visit?
      </h5>

      <div class="flex gap-2">
        <TwButton
          class="!py-1.5"
          variant="secondary"
          @click="isScheduleVideoVisitBtnClicked = false"
        >
          No
        </TwButton>
        <TwButton
          class="!py-1.5"
          @click="() => {
            isScheduleVideoVisitBtnClicked = false
            emit('scheduled')
          }"
        >
          Yes
        </TwButton>
      </div>
    </div>
    <div
      v-else
      class="w-full bg-amber-100 !rounded-xl px-3 py-3 text-sm font-medium mb-4 flex flex-col gap-4 sm:flex-row sm:justify-between sm:items-center"
    >
      <div v-if="data.isAppointmentMissed">
        <h5 class="text-gray-900">
          Looks like you missed your video visit
        </h5>
        <p class="text-xs text-gray-500">
          No worries - you can easily book a new appointment and continue your treatment without delay. Just pick a new time that works for you.
        </p>
      </div>
      <div v-else>
        <h5 class="text-gray-900">
          Action Required
        </h5>
        <p class="text-xs text-gray-500">
          Scheduling this video visit is required by state law to complete your treatment.
        </p>
      </div>
      <TwButton
        class="h-10"
        @click="handleScheduleNow"
      >
        Schedule&nbsp;Appointment
      </TwButton>
    </div>
  </template>
  <template v-if="data.appointmentUrl">
    <div class="w-full bg-amber-100 !rounded-xl px-3 py-3 text-sm font-medium mb-4 flex flex-col gap-3 sm:flex-row sm:justify-between">
      <div>
        <h5 class="text-gray-900">
          Video visit with <span class="font-bold text-black">{{ data.appointmentDocName }}</span>
        </h5>
        <p class="text-xs text-gray-500">
          Date & Time: {{ data.appointmentDateTime }}
        </p>
      </div>
      <TwButton
        class="!py-1.5"
        @click="handleJoinNow"
      >
        Join Now
      </TwButton>
    </div>
  </template>
</template>
