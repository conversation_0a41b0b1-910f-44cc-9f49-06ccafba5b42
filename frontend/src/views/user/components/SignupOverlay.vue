<script setup>
import { isEmpty, isEmptyObject, isNullOrUndefined } from '@/@core/utils'
import useCaptcha from '@/composables/useCaptcha'
import router from '@/router'
import ApiService from '@/services/ApiService'
import { processErrors } from '@/utils/errorHandler'
import { formatCurrency, removeKeyFromObject } from '@/utils/helpers'
import AlertError from '@/views/user/components/AlertError.vue'
import { IconCheck, IconEye, IconEyeOff, IconLoader2 } from '@tabler/icons-vue'
import { useSessionStorage } from '@vueuse/core'
import Sidebar from 'primevue/sidebar'
import { ErrorMessage, Field, Form as VForm } from 'vee-validate'
import { computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import * as yup from 'yup'

const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['close'])
const $cookies = inject('$cookies')
const { getRecaptchaToken } = useCaptcha()
const route = useRoute()

const termsConditionsUrl = computed(() => import.meta.env.VITE_TERMS_CONDITIONS_URL)
const privacyPolicyUrl = computed(() => import.meta.env.VITE_PRIVACY_POLICY_URL)

const isVisible = ref(false)
const isLoading = ref(false)
const inputErrors = ref({})
const serverErrors = ref([])
const showMoreFields = ref(false)
const password = ref('')
const isPasswordVisible = ref(false)
const onboardingSession = useSessionStorage('onboardingSession', {})
const passwordRef = ref(null)
const firstOrderDiscount = ref(0)
const skeletonLoading = ref(false)

watch(props, () => {
  isVisible.value = props.show
})

onMounted(async () => {
  await getFirstOrderDiscount()
})

async function getFirstOrderDiscount() {
  try {
    skeletonLoading.value = true

    const { data } = await ApiService.get('/fetch-first-time-discount-amount')

    if (data.status === 200) {
      firstOrderDiscount.value = data.first_time_discount_amount
    }
  } catch (error) {
    console.error(error)
  } finally {
    skeletonLoading.value = false
  }
}

const emailSchema = yup.object().shape({
  email: yup.string()
    .required('Email address is required')
    .matches(/^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,10}$/, 'Invalid email address'),
})

const signupSchema = yup.object().shape({
  email: yup.string()
    .required('Email address is required')
    .matches(/^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,10}$/, 'Invalid email address'),
  password: yup.string()
    .required('Password is required')
    .min(8, 'Password must be at least 8 characters')
    .matches(/(?=.*[A-Z0-9!@#$%^&*])/, 'Include at least one uppercase letter or number or symbol'),
  user_agreement: yup.string().required('Please read and agree to the terms and conditions'),
  beluga_agreement: yup.string().required('Please read and agree to Beluga terms and conditions'),
})

const validationSchema = computed(() => {
  if (showMoreFields.value) {
    return signupSchema
  } else {
    return emailSchema
  }
})

const handleSubmit = async values => {
  if (showMoreFields.value) {
    signup(values)
  } else {
    authenticateEmail(values)
  }
}

async function authenticateEmail(values) {
  try {
    isLoading.value = true
    inputErrors.value = {}
    serverErrors.value = []

    const captchaToken = await getRecaptchaToken('authenticateEmail')

    const postData = {
      ...values,
      'g-recaptcha-response': captchaToken,
    }

    const { data } = await ApiService.post('/email-authentication', postData)

    if (data.status === 200) {
      // user exists with this email
      router.push({ name: 'user-login', query: { login_hint: values.email, redirect_to: route.fullPath } })
    } else if (data.status === 400) {
      // user does not exist with this email
      showMoreFields.value = true
      setTimeout(() => {
        passwordRef.value?.focus()
      }, 100)
    } else {
      // validation errors
      if (data.errors) {
        inputErrors.value = data.errors
      }
    }
  } catch (error) {
    console.error(error)
    serverErrors.value = processErrors(error)
  } finally {
    isLoading.value = false
  }
}

async function signup(values) {
  try {
    isLoading.value = true
    inputErrors.value = {}
    serverErrors.value = []

    const captchaToken = await getRecaptchaToken('signup')

    const postData = {
      ...values,
      'g-recaptcha-response': captchaToken,
    }

    const affiliateCookieKey = `${route.params.visitType}_affiliate_code`
    if ($cookies.get(affiliateCookieKey)) {
      postData['affiliate_reference_code'] = $cookies.get(affiliateCookieKey)
    }

    const { data } = await ApiService.post('/registration', postData)

    if (data.status === 200) {
      onboardingSession.value['userId'] = data.user_id
      router.push({
        name: 'user-signup-profile',
        params: { visitType: route.params.visitType },
        query: { redirect_to: route.fullPath },
      })
    } else {
      if (data.is_user_registered && data.is_user_registered === 1) {
        router.push({ name: 'user-login', query: { login_hint: values.email, redirect_to: route.fullPath } })
      } else {
        isLoading.value = false
        if (data.errors) {
          inputErrors.value = data.errors
        }
      }
    }
  } catch (error) {
    isLoading.value = false
    serverErrors.value = processErrors(error)
    console.error(error)
  }
}

function removeKeyFromInputErrors(key) {
  inputErrors.value = removeKeyFromObject(inputErrors.value, key)
}
</script>

<template>
  <Sidebar
    v-model:visible="isVisible"
    position="right"
    :show-close-icon="false"
    class="login-sidebar"
    :dismissable="false"
    @hide="emit('close')"
  >
    <div
      v-if="skeletonLoading"
      class="flex items-center justify-center py-14 sm:px-16 h-[calc(100vh-4rem)] min-h-[500px]"
    >
      <div class="text-center py-10">
        <IconLoader2 class="inline w-10 h-10 text-gray-800 animate-spin" />
      </div>
    </div>

    <div
      v-else
      class="flex items-center justify-center py-14 sm:px-16 h-[calc(100vh-4rem)] min-h-[500px]"
    >
      <div class="w-full">
        <h2
          v-if="!isEmpty(firstOrderDiscount) && firstOrderDiscount > 0"
          class="text-center text-4xl font-bold leading-tight text-black"
        >
          *Enjoy {{ formatCurrency(firstOrderDiscount) }} off + free shipping on 1st order!
        </h2>

        <AlertError
          v-if="isEmptyObject(inputErrors) && serverErrors.length > 0"
          title="Error!"
          :errors="serverErrors"
        />

        <VForm
          class="mt-8"
          autocomplete="off"
          :validation-schema="validationSchema"
          @submit="handleSubmit"
        >
          <div class="space-y-5">
            <div class="relative mb-4">
              <Field
                id="email"
                type="email"
                name="email"
                class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                placeholder=" "
                autocomplete="off"
                validate-on-input
                autofocus
                required
                @keydown="() => removeKeyFromInputErrors('email')"
              />
              <label
                for="email"
                class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4 cursor-text"
              >Email address</label>
              <ErrorMessage
                name="email"
                class="text-red-500 text-sm ms-5 inline-block"
              />
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.email)"
                class="text-red-500 text-sm ms-5"
              >
                {{ inputErrors.email[0] }}
              </p>
            </div>

            <div v-if="showMoreFields">
              <div class="mb-4">
                <div class="relative">
                  <Field
                    v-slot="{ field }"
                    name="password"
                  >
                    <input
                      v-bind="field"
                      id="password"
                      ref="passwordRef"
                      v-model="password"
                      :type="isPasswordVisible ? 'text' : 'password'"
                      class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                      placeholder=" "
                      autocomplete="new-password"
                      autofocus
                      required
                    >
                  </Field>
                  <label
                    for="password"
                    class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4"
                  >Create Password</label>
                  <div class="absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                    <IconEye
                      v-if="!isPasswordVisible"
                      class="h-5 w-5"
                      stroke-width="2"
                      @click="isPasswordVisible = !isPasswordVisible"
                    />
                    <IconEyeOff
                      v-else
                      class="h-5 w-5"
                      stroke-width="2"
                      @click="isPasswordVisible = !isPasswordVisible"
                    />
                  </div>
                </div>
                <ErrorMessage
                  name="password"
                  class="text-red-500 text-sm ms-5 inline-block"
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.password)"
                  class="text-red-500 text-sm ms-5"
                >
                  {{ inputErrors.password[0] }}
                </p>
              </div>

              <div class="mb-6">
                <p class="flex items-center gap-3 mb-2 text-sm">
                  <IconCheck
                    class="h-5 w-5 text-gray-300"
                    :class="{ 'text-green-300': password.length >= 8 }"
                    stroke-width="2"
                  />
                  <span>Password must be at least 8 characters long</span>
                </p>
                <p class="flex items-center gap-3 text-sm">
                  <IconCheck
                    class="h-5 w-5 text-gray-300"
                    :class="{ 'text-green-300': password.match(/(?=.*[A-Z0-9!@#$%^&*])/) }"
                    stroke-width="2"
                  />
                  <span>Include at least one uppercase letter or number or symbol</span>
                </p>
              </div>
              <div class="form-input mb-1">
                <div class="flex">
                  <Field
                    id="agree-label"
                    type="checkbox"
                    name="user_agreement"
                    class="w-4 h-4 mt-[2px] text-black bg-gray-100 border !border-gray-400 accent-black rounded-sm"
                    value="1"
                    checked
                    required
                  />
                  <label
                    for="agree-label"
                    class="ms-2 text-sm font-normal text-gray-500"
                  >
                    I agree to <a
                      :href="termsConditionsUrl"
                      class="text-gray-600 font-semibold"
                      target="_blank"
                      rel="noopener noreferrer"
                    >Terms of Use</a> and <a
                      :href="privacyPolicyUrl"
                      class="text-gray-600 font-bold"
                      target="_blank"
                      rel="noopener noreferrer"
                    >Privacy Policy</a>.
                  </label>
                </div>
                <ErrorMessage
                  name="user_agreement"
                  class="text-red-500 text-sm ms-5"
                />
              </div>
              <div class="form-input mb-4">
                <div class="flex">
                  <Field
                    id="beluga-agree-label"
                    type="checkbox"
                    name="beluga_agreement"
                    class="w-4 h-4 mt-[2px] text-black bg-gray-100 border !border-gray-400 accent-black rounded-sm"
                    value="1"
                    checked
                    required
                  />
                  <label
                    for="beluga-agree-label"
                    class="ms-2 text-sm font-normal text-gray-500"
                  >
                    I agree to Beluga <a
                      href="https://customerconsents.s3.amazonaws.com/Beluga_Health_PA_Privacy_Policy.pdf"
                      class="text-gray-600 font-bold"
                      target="_blank"
                      rel="noopener noreferrer"
                    >Privacy Policy</a> and consent to <a
                      href="https://customerconsents.s3.amazonaws.com/Beluga_Health_Telemedicine_Informed_Consent.pdf"
                      class="text-gray-600 font-bold"
                      target="_blank"
                      rel="noopener noreferrer"
                    >telehealth</a>.
                  </label>
                </div>
                <ErrorMessage
                  name="beluga_agreement"
                  class="text-red-500 text-sm ms-5"
                />
              </div>
            </div>

            <div class="mt-8">
              <TwButton
                type="submit"
                class="w-full"
                :loading="isLoading"
              >
                Get Started Now
              </TwButton>
            </div>
          </div>
        </VForm>
        <div
          v-if="!isEmpty(firstOrderDiscount) && firstOrderDiscount > 0"
          class="mt-6 text-xs text-center"
        >
          <span class="text-base">*</span> Applies to our pharmacy only
        </div>
      </div>
    </div>
  </Sidebar>
</template>

<style lang="scss">
.login-sidebar {
  .p-sidebar-header {
    display: none;
  }
}
.p-sidebar-right .login-sidebar.p-sidebar {
  width: 36rem;
  font-family: "Konnect", system-ui, -apple-system, BlinkMacSystemFont,
    "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue",
    sans-serif;
}
</style>
