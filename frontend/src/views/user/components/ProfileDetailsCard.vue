<script setup>
import ApiService from '@/services/ApiService'
import AlertError from '@/views/user/components/AlertError.vue'
import { onMounted, ref } from 'vue'
import { processErrors } from '@/utils/errorHandler'
import { IconInfoCircle, IconPencil } from '@tabler/icons-vue'
import { toast } from 'vue-sonner'
import { formattedPhoneNumber, plainPhoneNumber, preventSpacesFromStart, removeKeyFromObject } from '@/utils/helpers'
import { isEmptyObject, isNullOrUndefined } from '@/@core/utils'
import * as yup from 'yup'
import { Form as VForm, Field, ErrorMessage } from 'vee-validate'
import { vMaska } from 'maska'
import ProfilePhoneVerificationModal from '@/views/user/components/ProfilePhoneVerificationModal.vue'
import { useAuthStore } from '@/store/auth'

const authStore = useAuthStore()

const skeletonLoading = ref(false)
const isLoading = ref(false)
const serverErrors = ref([])
const inputErrors = ref({})
const scrollToEle = ref(null)
const user = ref({})
const editMode = ref(false)
const phoneVerificationModal = ref(null)

const formUser = ref({
  first_name: '',
  last_name: '',
  email: '',
  phone_number: '',
})

const isSmsAlertsAllowed = ref(false)
const loadingSmsAlertToggle = ref(false)

const validationSchema = yup.object().shape({
  first_name: yup.string().required('First name is required'),
  last_name: yup.string().required('Last name is required'),
  email: yup.string().required('Email address is required').email('Invalid email address'),
  phone_number: yup.string().required('Phone Number is required'),
})

onMounted(async () => {
  skeletonLoading.value = true
  await fetchUserDetails()
  skeletonLoading.value = false
})

async function fetchUserDetails() {
  try {
    serverErrors.value = []

    const { data } = await ApiService.get('/user-details')

    if (data.status === 200) {
      const userData = data.userData

      user.value = userData

      formUser.value = {
        first_name: userData.first_name,
        last_name: userData.last_name,
        phone_number: userData.phone_number,
        email: userData.email,
      }

      isSmsAlertsAllowed.value = Boolean(userData.is_sms_notification_allowed)

      authStore.updateUserData(userData)
    } else {
      serverErrors.value = processErrors(data)
      scrollToEle.value.scrollIntoView({ behavior: 'smooth' })
    }
  } catch (error) {
    console.error(error)
    serverErrors.value = processErrors(error)
    scrollToEle.value.scrollIntoView({ behavior: 'smooth' })
  }
}

function removeKeyFromInputErrors(key) {
  inputErrors.value = removeKeyFromObject(inputErrors.value, key)
}

async function handleSubmit(values) {
  try {
    isLoading.value = true
    inputErrors.value = {}
    serverErrors.value = []

    const postData = {
      ...values,
      phone_number: plainPhoneNumber(values.phone_number),
    }

    const { data } = await ApiService.post('/update-profile', postData)

    if (data.status === 200) {
      toast.success(data.message)

      if (data.is_phone_number_changed === 1) {
        phoneVerificationModal.value.open(values.phone_number)
      } else {
        discardEdit()
        await fetchUserDetails()
      }
    } else {
      if (data.errors) {
        inputErrors.value = data.errors
      }
    }
  } catch (error) {
    if (error.response.errors) {
      inputErrors.value = error.response.errors
    }
    serverErrors.value = processErrors(error)
    console.error(error)
  } finally {
    isLoading.value = false
  }
}

function discardEdit() {
  editMode.value = false
  formUser.value = {
    first_name: user.value.first_name,
    last_name: user.value.last_name,
    email: user.value.email,
    phone_number: user.value.phone_number,
  }
}

function handlePhoneVerificationResponse() {
  discardEdit()
  fetchUserDetails()
}

async function handleSmsAlertToggleChange() {
  loadingSmsAlertToggle.value = true

  try {
    const { data } = await ApiService.get('/update-sms-notification')

    if (data.status === 200) {
      toast.success(data.message)
      fetchUserDetails()
    } else {
      toast.error(data.message)
    }
  } catch (error) {
    console.error(error)
  } finally {
    loadingSmsAlertToggle.value = false
  }
}
</script>

<template>
  <div class="bg-white py-5 px-6 rounded-[20px]">
    <div class="flex justify-between mb-3">
      <div class="text-xl font-semibold">
        Account Details
      </div>
      <div>
        <button
          v-if="!editMode"
          class="inline-flex gap-x-1 text-sm font-medium border !border-zinc-800 px-4 py-1.5 rounded-full hover:bg-gray-100 transition-all duration-300 disabled:cursor-not-allowed"
          :disabled="skeletonLoading"
          @click="editMode = true"
        >
          <IconPencil
            class="h-4 w-[14px]"
            stroke-width="2"
          />
          <span>Edit</span>
        </button>
      </div>
    </div>

    <div
      ref="scrollToEle"
      class="h-0"
    ></div>

    <AlertError
      v-if="isEmptyObject(inputErrors) && serverErrors.length > 0"
      title="Error!"
      :errors="serverErrors"
    />

    <div
      v-if="skeletonLoading"
      class="space-y-3"
    >
      <div class="space-y-1">
        <div class="h-4 w-20 bg-gray-300 rounded animate-pulse"></div>
        <div class="h-5 w-25 bg-gray-300 rounded animate-pulse"></div>
      </div>
      <div class="space-y-1">
        <div class="h-4 w-20 bg-gray-300 rounded animate-pulse"></div>
        <div class="h-5 w-25 bg-gray-300 rounded animate-pulse"></div>
      </div>
      <div class="space-y-1">
        <div class="h-4 w-20 bg-gray-300 rounded animate-pulse"></div>
        <div class="h-5 w-25 bg-gray-300 rounded animate-pulse"></div>
      </div>
      <div class="space-y-1">
        <div class="h-4 w-20 bg-gray-300 rounded animate-pulse"></div>
        <div class="h-5 w-25 bg-gray-300 rounded animate-pulse"></div>
      </div>
    </div>

    <div v-else>
      <div
        v-if="!editMode"
        class="space-y-5"
      >
        <div>
          <div class="text-sm text-zinc-700">
            Full Name
          </div>
          <div class="font-medium text-zinc-900">
            {{ user.first_name }} {{ user.last_name }}
          </div>
        </div>
        <div>
          <div class="text-sm text-zinc-700">
            Email address
          </div>
          <div class="font-medium text-zinc-900">
            {{ user.email }}
          </div>
        </div>
        <div>
          <div class="text-sm text-zinc-700">
            Phone Number
          </div>
          <div class="font-medium text-zinc-900">
            {{ formattedPhoneNumber(user.phone_number) }}
          </div>
        </div>
        <div>
          <div class="text-sm text-zinc-700 inline-flex items-center">
            Date of birth
            <button
              v-tooltip.focus.top="'Date of birth cannot be changed. Please contact support for assistance.'"
              type="button"
              class="ms-2 cursor-pointer"
              data-tooltip-target="tooltip-birth-date"
              data-tooltip-placement="top"
              data-tooltip-trigger="click"
            >
              <IconInfoCircle
                class="w-4 h-4 mb-1"
                stroke-width="2"
              />
            </button>
          </div>
          <div class="font-medium text-zinc-900">
            {{ user.date_of_birth }}
          </div>
        </div>
        <div v-if="!isLoading">
          <div class="text-sm text-zinc-700">
            Receive SMS alerts
          </div>
          <div class="font-medium text-zinc-900 mt-2">
            <label class="inline-flex items-center me-5 cursor-pointer">
              <input
                type="checkbox"
                value=""
                class="sr-only peer"
                :checked="isSmsAlertsAllowed"
                :disabled="loadingSmsAlertToggle"
                @change="handleSmsAlertToggleChange"
              >
              <div class="relative w-11 h-6 bg-gray-200 rounded-full peer peer-focus:ring-4 peer-focus:ring-green-300 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-400 peer-disabled:cursor-not-allowed"></div>
              <span class="ms-3 text-sm font-medium text-gray-900">
                {{ loadingSmsAlertToggle? 'Updating...' : '' }}
              </span>
            </label>
          </div>
        </div>
      </div>
      <div v-if="editMode">
        <VForm
          class="mt-8"
          autocomplete="off"
          :validation-schema="validationSchema"
          @submit="handleSubmit"
        >
          <div class="space-y-5">
            <div class="flex flex-col md:flex-row gap-5">
              <div class="relative flex-auto">
                <Field
                  id="first_name"
                  v-model="formUser.first_name"
                  type="text"
                  name="first_name"
                  class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                  placeholder=" "
                  autocomplete="off"
                  @keydown="preventSpacesFromStart($event)"
                  @keyup="removeKeyFromInputErrors('first_name')"
                />
                <label
                  for="first_name"
                  class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4 cursor-text"
                >First Name</label>
                <ErrorMessage
                  name="first_name"
                  class="text-red-500 text-sm ms-5"
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.first_name)"
                  class="text-red-500 text-sm ms-5"
                >
                  {{ inputErrors.first_name[0] }}
                </p>
              </div>

              <div class="relative flex-auto">
                <Field
                  id="last_name"
                  v-model="formUser.last_name"
                  type="text"
                  name="last_name"
                  class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                  placeholder=" "
                  autocomplete="off"
                  @keydown="preventSpacesFromStart($event)"
                  @keyup="removeKeyFromInputErrors('last_name')"
                />
                <label
                  for="last_name"
                  class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4 cursor-text"
                >Last Name</label>
                <ErrorMessage
                  name="last_name"
                  class="text-red-500 text-sm ms-5"
                />
                <p
                  v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.last_name)"
                  class="text-red-500 text-sm ms-5"
                >
                  {{ inputErrors.last_name[0] }}
                </p>
              </div>
            </div>

            <div>
              <div class="relative">
                <Field
                  id="email"
                  v-model="formUser.email"
                  type="email"
                  name="email"
                  class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                  placeholder=" "
                  autocomplete="off"
                  required
                  @keyup="removeKeyFromInputErrors('email')"
                />
                <label
                  for="email"
                  class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4 cursor-text"
                >Email</label>
              </div>
              <ErrorMessage
                name="email"
                class="text-red-500 text-sm ms-5"
              />
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.email)"
                class="text-red-500 text-sm ms-5"
              >
                {{ inputErrors.email[0] }}
              </p>
            </div>

            <div>
              <div class="relative">
                <Field
                  id="phone_number"
                  v-model="formUser.phone_number"
                  v-maska
                  type="text"
                  name="phone_number"
                  class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                  placeholder=" "
                  autocomplete="off"
                  data-maska="(###) ###-####"
                  required
                  @keyup="removeKeyFromInputErrors('phone_number')"
                />
                <label
                  for="phone_number"
                  class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4 cursor-text"
                >Phone Number</label>
              </div>
              <ErrorMessage
                name="phone_number"
                class="text-red-500 text-sm ms-5"
              />
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.phone_number)"
                class="text-red-500 text-sm ms-5"
              >
                {{ inputErrors.phone_number[0] }}
              </p>
            </div>

            <div class="mt-8 flex gap-3">
              <button
                type="button"
                class="inline-flex w-full items-center justify-center rounded-full border !border-black px-3.5 py-2.5 font-semibold leading-7 text-black hover:bg-gray-200 text-sm"
                :disabled="isLoading"
                @click="discardEdit"
              >
                Discard
              </button>
              <TwButton
                type="submit"
                class="w-full"
                :loading="isLoading"
              >
                Update
              </TwButton>
            </div>
          </div>
        </VForm>
      </div>
    </div>
  </div>
  <ProfilePhoneVerificationModal
    ref="phoneVerificationModal"
    :close-on-click-outside="false"
    @close-modal="handlePhoneVerificationResponse"
    @phone-verified="handlePhoneVerificationResponse"
  />
</template>
