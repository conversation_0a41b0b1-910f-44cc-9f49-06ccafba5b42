<script setup>
import { ref, onUnmounted } from 'vue'
import { IconVideoOff, IconX } from '@tabler/icons-vue'

const props = defineProps({
  closeOnClickOutside: {
    type: Boolean,
    default: true,
  },
  title: {
    type: String,
    default: 'Capture Image',
  },
  cameraFacingMode: {
    type: String,
    default: 'user',
  },
})

const emit = defineEmits(['imageCaptured'])

const isOpen = ref(false)
const imageCaptured = ref(false)
const imageUrl = ref('')
const video = ref()
const cameraError = ref(null)
const isLoadingCamera = ref(false)

function open() {
  isOpen.value = true
  startCamera()
}

function closeModal() {
  isOpen.value = false
  imageCaptured.value = false
  imageUrl.value = ''
  stopCamera()
}

async function startCamera() {
  try {
    isLoadingCamera.value = true
    cameraError.value = null

    const stream = await navigator.mediaDevices.getUserMedia({
      video: {
        facingMode: props.cameraFacingMode,
        width: { ideal: '1280' },
        height: { ideal: '1280' },
      },
      audio: false,
    })

    video.value.srcObject = stream
    video.value.play()
    isLoadingCamera.value = false
  } catch (err) {
    cameraError.value = err.message || 'Unknown error'
  }
}

function stopCamera() {
  if (video.value && video.value.srcObject) {
    const tracks = video.value.srcObject.getTracks()

    tracks.forEach(track => track.stop())
    video.value.srcObject = null
  }
}

function capture() {
  const canvas = document.createElement('canvas')

  canvas.width = video.value.videoWidth
  canvas.height = video.value.videoHeight

  const ctx = canvas.getContext('2d')

  ctx.drawImage(video.value, 0, 0, canvas.width, canvas.height)
  imageUrl.value = canvas.toDataURL('image/png', 1)
  imageCaptured.value = true

  // stopCamera()
  confirm()
}

function retake() {
  imageCaptured.value = false
  imageUrl.value = ''
  startCamera()
}

function confirm() {
  const file = dataURLtoFile(imageUrl.value, 'captured_image.png')

  emit('imageCaptured', { file, url: imageUrl.value })
  closeModal()
}

// Function to convert data URL to a file
function dataURLtoFile(dataurl, filename) {
  const arr = dataurl.split(',')
  const mime = arr[0].match(/:(.*?);/)[1]
  const bstr = atob(arr[1])
  let n = bstr.length
  const u8arr = new Uint8Array(n)
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n)
  }

  return new File([u8arr], filename, { type: mime })
}

function handleOutsideClick() {
  if (props.closeOnClickOutside) {
    closeModal()
  }
}

onUnmounted(() => {
  stopCamera()
})

defineExpose({ open, close: closeModal })
</script>

<template>
  <div
    v-if="isOpen"
    class="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center overflow-y-auto overflow-x-hidden top-0 right-0 left-0 z-50 w-full md:inset-0 h-full max-h-full"
    @click="handleOutsideClick"
  >
    <div class="relative p-4 w-full max-w-2xl max-h-full">
      <div class="relative inline-block z-[999] top-12 right-[-88%] sm:right-[-94%]">
        <IconX
          class="cursor-pointer h-6 w-6"
          stroke-width="2"
          @click="closeModal"
        />
      </div>
      <div
        v-if="!imageCaptured"
        class="relative bg-white rounded-lg shadow dark:bg-gray-700"
      >
        <div class="px-5 py-8 md:p-10">
          <h2 class="text-center text-2xl md:text-3xl font-bold leading-tight text-black">
            {{ props.title }}
          </h2>
          <div
            v-if="cameraError"
            class="flex flex-col items-center px-5 py-8"
          >
            <IconVideoOff
              class="w-20 h-20 mb-2"
              stroke-width="2"
            />
            <h5 class="text-2xl text-gray-800 font-medium mb-4">
              {{ cameraError }}
            </h5>
            <div class="text-center text-base md:px-10">
              White Label Rx requires access to your camera. Click the camera blocked icon
              <IconVideoOff
                class="inline w-6 h-6"
                stroke-width="2"
              />
              in your browser's address bar. Allow the permissions and reload this page.
            </div>
          </div>
          <video
            v-if="!cameraError"
            ref="video"
            class="w-full max-h-[512px] h-auto my-5 rounded-lg"
            autoplay
          ></video>
          <TwButton
            v-if="!cameraError && !isLoadingCamera"
            class="w-full"
            @click="capture"
          >
            Capture
          </TwButton>
        </div>
      </div>
      <div
        v-else
        class="relative bg-white rounded-lg shadow dark:bg-gray-700"
      >
        <div class="px-5 py-8 md:p-10">
          <h2 class="text-center text-2xl md:text-3xl font-bold leading-tight text-black">
            Preview
          </h2>
          <img
            :src="imageUrl"
            class="w-full max-h-[512px] h-auto my-5 rounded-lg"
            alt="Captured Image"
          >
          <!-- <div class="grid sm:grid-cols-2 gap-2"> -->
          <!--
            <TwButton
            class="w-full uppercase bg-white border !border-gray-900 !text-gray-900 hover:!bg-gray-200"
            @click="retake"
            >
            Retake
            </TwButton>
          -->
          <TwButton
            class="w-full uppercase"
            @click="confirm"
          >
            Confirm
          </TwButton>
          <!-- </div> -->
        </div>
      </div>
    </div>
  </div>
</template>


