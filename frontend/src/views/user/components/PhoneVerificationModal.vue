<script setup>
import { isEmptyObject, isNullOrUndefined } from '@/@core/utils'
import { useAppAbility } from '@/plugins/casl/useAppAbility'
import router from '@/router'
import ApiService from '@/services/ApiService'
import { useAuthStore } from '@/store/auth'
import { processErrors } from '@/utils/errorHandler'
import { plainPhoneNumber } from '@/utils/helpers'
import { IconClockHour4 } from '@tabler/icons-vue'
import { useSessionStorage } from '@vueuse/core'
import { vMaska } from 'maska'
import { ref } from 'vue'
import { useRoute } from 'vue-router'
import { toast } from 'vue-sonner'

const props = defineProps({
  closeOnClickOutside: {
    type: Boolean,
    default: true,
  },
})

const $cookies = inject('$cookies')
const authStore = useAuthStore()
const ability = useAppAbility()
const route = useRoute()

const onboardingSession = useSessionStorage('onboardingSession', {})
const isOpen = ref(false)
const timer = ref(null)
const verificationCode = ref('')
const newPhoneFormActive = ref(false)
const newPhoneNumber = ref('')
const isLoading = ref(false)
const isLoadingResendCode = ref(false)
const inputErrors = ref({})

const verificationCodeInputRef = ref(null)
const newPhoneNumberInputRef = ref(null)

function open() {
  isOpen.value = true
  startTimer()
  setTimeout(() => {
    verificationCodeInputRef.value?.focus()
  }, 100)
}

let intervalId

const startTimer = () => {
  const countDownDate = new Date(Date.now() + 120 * 1000).getTime()
  let mins = 1
  let secs = 59
  timer.value = `0${mins}:${secs}`

  if (intervalId) {
    clearInterval(intervalId)
  }

  intervalId = setInterval(() => {
    const now = new Date().getTime()
    const distance = countDownDate - now

    if (distance < 0) {
      clearInterval(intervalId)
      timer.value = null

      return
    }

    const minutes = Math.floor((distance / (1000 * 60)) % 60)
    const seconds = Math.floor((distance / 1000) % 60)

    mins = minutes < 10 ? '0' + minutes : minutes
    secs = seconds < 10 ? '0' + seconds : seconds

    timer.value = `${mins}:${secs}`
  }, 1000)
}

async function resendNewCode() {
  try {
    isLoadingResendCode.value = true

    const postData = {
      phone_number: plainPhoneNumber(onboardingSession.value['phoneNumber']),
      type: 'registration',
    }

    const { data } = await ApiService.post('/resend-sms-otp', postData)

    if (data.status === 200) {
      toast.success(data.message)
      startTimer()
      setTimeout(() => {
        verificationCodeInputRef.value?.focus()
      }, 100)
    } else if (data.message) {
      toast.error(data.message)
    }
  } catch (error) {
    toast.error(processErrors(error)[0])
    console.error(error)
  } finally {
    isLoadingResendCode.value = false
  }
}

function onChangePhoneClick() {
  newPhoneFormActive.value = true
  setTimeout(() => {
    newPhoneNumberInputRef.value?.focus()
  }, 100)
}

function onDiscardPhoneChange() {
  newPhoneFormActive.value = false
  newPhoneNumber.value = ''
  setTimeout(() => {
    verificationCodeInputRef.value?.focus()
  }, 100)
}

async function handleChangePhoneSubmit() {
  try {
    isLoading.value = true
    inputErrors.value = {}

    const postData = {
      phone_number: newPhoneNumber.value.replace(/\D/g, ''),
      user_id: onboardingSession.value['userId'],
    }

    const { data } = await ApiService.post('/change-phone-number', postData)

    if (data.status === 200) {
      toast.success(data.message)
      startTimer()
      newPhoneFormActive.value = false
      onboardingSession.value['phoneNumber'] = newPhoneNumber.value
      newPhoneNumber.value = ''
    } else {
      if (data.errors) {
        if (data.errors) {
          inputErrors.value = data.errors
        }
      } else if (data.message) {
        toast.error(data.message)
      }
    }
  } catch (error) {
    toast.error(processErrors(error)[0])
    console.error(error)
  } finally {
    isLoading.value = false
  }
}

async function handleVerifySubmit() {
  try {
    isLoading.value = true
    inputErrors.value = {}

    const postData = {
      otp: verificationCode.value,
      user_id: onboardingSession.value['userId'],
      phone_number: plainPhoneNumber(onboardingSession.value['phoneNumber']),
      type: 'register',
      visit_type: route.params.visitType.toLowerCase(),
    }

    const { data } = await ApiService.post('/verify-sms-otp', postData)

    if (data.status === 200) {
      authStore.setAuth(data)

      const userAbility = []

      $cookies.set('userAbilities', userAbility)
      ability.update(userAbility)

      if (!data.is_treatment_available) {
        await router.replace({ name: `${route.params.visitType}-treatment-not-available` })
      } else {
        await router.replace({
          name: 'user-sms-opt-in',
          params: { visitType: route.params.visitType },
          query: { ...route.query },
        })
      }

      // clear onboarding session
      onboardingSession.value = null
      window.sessionStorage.removeItem('onboardingSession')
    } else {
      isLoading.value = false
      if (data.errors) {
        inputErrors.value = data.errors
      } else if (data.message) {
        toast.error(data.message)
      }
    }
  } catch (error) {
    isLoading.value = false
    toast.error(processErrors(error)[0])
    console.error(error)
  }
}

function close() {
  isOpen.value = false
}

function handleOutsideClick() {
  if (props.closeOnClickOutside) {
    close()
  }
}

defineExpose({ open, close })
</script>

<template>
  <div
    v-if="isOpen"
    class="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center overflow-y-auto overflow-x-hidden top-0 right-0 left-0 z-50 w-full md:inset-0 h-full max-h-full"
    @click="handleOutsideClick"
  >
    <div class="relative p-4 w-full max-w-2xl max-h-full">
      <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
        <div class="px-5 py-8 md:p-10">
          <div
            v-if="!newPhoneFormActive"
            class="xl:mx-auto xl:w-full xl:max-w-sm 2xl:max-w-md"
          >
            <h2 class="text-center text-2xl md:text:3xl font-bold leading-tight text-black">
              Verifying your identity to ensure it's genuinely you.
            </h2>
            <p class="mt-2 text-center text-gray-600">
              Please enter the code we sent to <span class="font-medium">{{ onboardingSession['phoneNumber'] ?? '' }}</span>.
            </p>
            <form
              method="POST"
              class="mt-8"
              @submit.prevent="handleVerifySubmit"
            >
              <div class="space-y-5">
                <div class="relative mb-4">
                  <input
                    ref="verificationCodeInputRef"
                    v-model="verificationCode"
                    v-maska
                    type="text"
                    class="block w-full px-5 py-3 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer text-center"
                    placeholder="######"
                    data-maska="######"
                    autofocus
                    required
                  />
                  <p
                    v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.otp)"
                    class="text-red-500 text-sm ms-5"
                  >
                    {{ inputErrors.otp[0] }}
                  </p>
                </div>

                <div class="flex flex-col sm:flex-row justify-center sm:justify-between gap-3">
                  <div class="flex gap-3 justify-center">
                    <button
                      class="underline text-black hover:text-black/80 font-medium disabled:text-gray-400 disabled:cursor-not-allowed"
                      :disabled="timer !== null || isLoadingResendCode"
                      @click="resendNewCode"
                    >
                      Send new code
                    </button>
                    <div
                      v-if="timer"
                      class="flex items-center gap-1 bg-[#FFEF08] px-1.5 py-0.5 text-sm rounded-full"
                    >
                      <span>
                        <IconClockHour4
                          class="w-4 h-4 mb-[2px]"
                          stroke-width="2"
                        />
                      </span>
                      <span>{{ timer }}</span>
                    </div>
                  </div>
                  <button
                    class="underline text-black hover:text-black/80 font-medium"
                    @click="onChangePhoneClick"
                  >
                    Change phone number
                  </button>
                </div>

                <div class="mt-8">
                  <TwButton
                    type="submit"
                    class="w-full"
                    :loading="isLoading"
                  >
                    Submit
                  </TwButton>
                </div>
              </div>
            </form>
          </div>
          <div
            v-if="newPhoneFormActive"
            class="xl:mx-auto xl:w-full xl:max-w-sm 2xl:max-w-md"
          >
            <h2 class="text-center text-2xl md:text:3xl font-bold leading-tight text-black">
              Change phone number
            </h2>
            <p class="mt-2 text-center text-gray-600">
              Please enter new phone number
            </p>
            <form
              method="POST"
              class="mt-8"
              @submit.prevent="handleChangePhoneSubmit"
            >
              <div class="space-y-5">
                <div class="relative mb-4">
                  <input
                    ref="newPhoneNumberInputRef"
                    v-model="newPhoneNumber"
                    v-maska
                    type="text"
                    class="block w-full px-5 py-3 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer text-center"
                    placeholder="Phone Number"
                    data-maska="(###) ###-####"
                    required
                  />
                  <p
                    v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.phone_number)"
                    class="text-red-500 text-sm ms-5"
                  >
                    {{ inputErrors.phone_number[0] }}
                  </p>
                </div>

                <div class="flex gap-3 mt-8">
                  <button
                    type="button"
                    class="inline-flex w-full items-center justify-center rounded-full border !border-black px-3.5 py-2.5 font-semibold leading-7 text-black hover:bg-gray-200 uppercase text-sm"
                    @click="onDiscardPhoneChange"
                  >
                    Discard
                  </button>
                  <TwButton
                    type="submit"
                    class="w-full"
                    :loading="isLoading"
                  >
                    Change
                  </TwButton>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
