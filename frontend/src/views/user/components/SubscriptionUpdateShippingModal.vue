<script setup>
import Sidebar from 'primevue/sidebar'
import ApiService from '@/services/ApiService'
import { Form as VForm, Field, ErrorMessage } from 'vee-validate'
import AlertError from '@/views/user/components/AlertError.vue'
import { processErrors } from '@/utils/errorHandler'
import * as yup from 'yup'
import useMapsApi from '@/composables/useMapsApi'
import AutoComplete from 'primevue/autocomplete'
import { isEmpty, isEmptyObject, isNullOrUndefined } from '@/@core/utils'
import { vMaska } from 'maska'
import { toast } from 'vue-sonner'
import { useRoute } from 'vue-router'

const emit = defineEmits(['updated'])

const { searchPlaces, autoCompleteOptions } = useMapsApi()
const route = useRoute()

const isVisible = ref(false)
const skeletonLoading = ref(false)
const isLoading = ref(false)
const inputErrors = ref({})
const serverErrors = ref([])
const states = ref([])
const countries = ['USA']
const formShippingAddressRef = ref(null)
const subscriptionId = ref(route.params.subscriptionId)

const shippingAddress = ref({
  address_line_1: '',
  address_line_2: '',
  city: '',
  state: '',
  zipcode: '',
  country: 'USA',
})

const statesList = computed(() => states.value?.filter(item => item.type === 'state'))
const territoriesList = computed(() => states.value?.filter(item => item.type === 'territory'))

const validationSchema = yup.object().shape({
  address_line_1: yup.string().required('Street address is required'),
  city: yup.string().required('City is required'),
  state: yup.string().required('State / Territory is required'),
  zipcode: yup.string().required('Zipcode is required'),
  country: yup.string().required('Country is required'),
})

async function fetchStates() {
  try {
    skeletonLoading.value = true

    const { data } = await ApiService.get('/state-list')

    if (data.status === 200) {
      states.value = data.stateData ?? []
    } else {
      console.error(data)
    }
  } catch (error) {
    console.error(error)
  } finally {
    skeletonLoading.value = false
  }
}

const handleShippingAddressSearch = () => {
  if (!isEmpty(shippingAddress.value.address_line_1)) {
    searchPlaces(shippingAddress.value.address_line_1)
  }
}

const handleShippingAddressSelectedItem = $event => {
  const item = $event.value

  shippingAddress.value.address_line_1 = item.streetAddress
  shippingAddress.value.city = item.city

  for (const state of states.value) {
    if (state.code === item.state) {
      shippingAddress.value.state = item.state
    }
  }

  shippingAddress.value.zipcode = item.postalCode
}

async function updateShippingAddress(values) {
  try {
    isLoading.value = true
    inputErrors.value = {}
    serverErrors.value = []

    const postData = { ...values, id: subscriptionId.value }

    const { data } = await ApiService.post('/update-subscription-shipping-address', postData)

    if (data.status === 200) {
      toast.success(data.message)
      emit('updated')
      closeModal()
    } else {
      isLoading.value = false
      if (data.errors) {
        inputErrors.value = data.errors
      }
      serverErrors.value = processErrors(data)
    }
  } catch (error) {
    isLoading.value = false
    if (error.response.errors) {
      inputErrors.value = error.response.errors
    }
    serverErrors.value = processErrors(error)
    console.error(error)
  }
}

async function handleUpdateShipping() {
  const { valid } = await formShippingAddressRef.value.validate()
  if (valid) {
    await updateShippingAddress(shippingAddress.value)
  }
}

function openModal(formData) {
  shippingAddress.value = formData
  inputErrors.value = {}
  serverErrors.value = []
  isVisible.value = true
  isLoading.value = false
  fetchStates()
}

function closeModal() {
  isVisible.value = false
}

defineExpose({ openModal, closeModal })
</script>

<template>
  <Sidebar
    v-model:visible="isVisible"
    position="right"
    show-close-icon
    class="update-shipping-sidebar"
    @hide="closeModal"
  >
    <div class="w-full">
      <h2 class="text-2xl font-bold leading-tight text-black mb-3">
        Update Shipping Address
      </h2>

      <AlertError
        v-if="isEmptyObject(inputErrors) && serverErrors.length > 0"
        title="Error!"
        :errors="serverErrors"
      />

      <div v-if="skeletonLoading">
        <div class="px-4 w-full">
          <div class="h-12 bg-gray-300 rounded mt-6 mb-2 animate-pulse"></div>
          <div class="h-12 bg-gray-300 rounded mt-6 mb-2 animate-pulse"></div>
          <div class="h-12 bg-gray-300 rounded mt-6 mb-2 animate-pulse"></div>
          <div class="h-12 bg-gray-300 rounded mt-6 mb-2 animate-pulse"></div>
          <div class="h-12 bg-gray-300 rounded mt-6 mb-2 animate-pulse"></div>
          <div class="h-10 bg-gray-300 rounded-xl mt-8 mb-2 animate-pulse"></div>
        </div>
      </div>

      <div v-else>
        <VForm
          ref="formShippingAddressRef"
          autocomplete="off"
          :validation-schema="validationSchema"
        >
          <div class="space-y-5">
            <!-- 👉 Street address -->
            <div>
              <div class="relative">
                <AutoComplete
                  id="street_line_1"
                  v-model="shippingAddress.address_line_1"
                  name="address_line_1"
                  option-label="streetAddress"
                  placeholder="Street Address"
                  class="w-full"
                  input-class="block w-full px-5 py-3.5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:!outline-none focus:!ring-1 focus:!ring-black peer"
                  :suggestions="autoCompleteOptions"
                  :delay="500"
                  @complete="handleShippingAddressSearch"
                  @item-select="handleShippingAddressSelectedItem"
                >
                  <template #option="slotProps">
                    <div class="">
                      <p class="mb-0 text-sm font-medium">
                        {{ slotProps.option.displayName }}
                      </p>
                      <small>{{ slotProps.option.formattedAddress }}</small>
                    </div>
                  </template>
                </AutoComplete>
                <Field
                  v-model="shippingAddress.address_line_1"
                  type="hidden"
                  class="hidden h-0 w-0"
                  name="address_line_1"
                  required
                />
              </div>
              <ErrorMessage
                name="address_line_1"
                class="text-red-500 text-sm ms-5"
              />
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.address_line_1)"
                class="text-red-500 text-sm ms-5"
              >
                {{ inputErrors.address_line_1[0] }}
              </p>
            </div>

            <!-- 👉 Street address line 2 -->
            <div>
              <div class="relative">
                <Field
                  id="address_line_2"
                  v-model="shippingAddress.address_line_2"
                  type="text"
                  name="address_line_2"
                  class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                  placeholder=" "
                  autocomplete="off"
                  required
                />
                <label
                  for="address_line_2"
                  class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4 cursor-text"
                >Apt / Suite (optional)</label>
              </div>
              <ErrorMessage
                name="address_line_2"
                class="text-red-500 text-sm ms-5"
              />
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.address_line_2)"
                class="text-red-500 text-sm ms-5"
              >
                {{ inputErrors.address_line_2[0] }}
              </p>
            </div>

            <!-- 👉 City -->
            <div>
              <div class="relative">
                <Field
                  id="city"
                  v-model="shippingAddress.city"
                  type="text"
                  name="city"
                  class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                  placeholder=" "
                  autocomplete="off"
                  required
                />
                <label
                  for="city"
                  class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4 cursor-text"
                >City</label>
              </div>
              <ErrorMessage
                name="city"
                class="text-red-500 text-sm ms-5"
              />
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.city)"
                class="text-red-500 text-sm ms-5"
              >
                {{ inputErrors.city[0] }}
              </p>
            </div>

            <div class="relative">
              <Field
                id="state"
                v-model="shippingAddress.state"
                as="select"
                name="state"
                class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                required
              >
                <option
                  value=""
                  disabled
                  selected
                >
                  Select
                </option>
                <optgroup label="States">
                  <option
                    v-for="state in statesList"
                    :key="state.code"
                    :value="state.code"
                  >
                    {{ state.name }}
                  </option>
                </optgroup>
                <optgroup label="Territories">
                  <option
                    v-for="territory in territoriesList"
                    :key="territory.code"
                    :value="territory.code"
                  >
                    {{ territory.name }}
                  </option>
                </optgroup>
              </Field>
              <label
                for="state"
                class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-focus:scale-75 peer-focus:-translate-y-4"
              >State / Territory</label>
              <ErrorMessage
                name="state"
                class="text-red-500 text-sm ms-5"
              />
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.state)"
                class="text-red-500 text-sm ms-5"
              >
                {{ inputErrors.state[0] }}
              </p>
            </div>

            <div>
              <div class="relative">
                <Field
                  id="zipcode"
                  v-model="shippingAddress.zipcode"
                  v-maska
                  type="text"
                  name="zipcode"
                  class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                  placeholder=" "
                  autocomplete="off"
                  data-maska="#####-####"
                  required
                />
                <label
                  for="zipcode"
                  class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4 cursor-text"
                >Zipcode</label>
              </div>
              <ErrorMessage
                name="zipcode"
                class="text-red-500 text-sm ms-5"
              />
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.zipcode)"
                class="text-red-500 text-sm ms-5"
              >
                {{ inputErrors.zipcode[0] }}
              </p>
            </div>

            <div class="relative">
              <Field
                id="country"
                v-model="shippingAddress.country"
                as="select"
                name="country"
                class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                required
              >
                <option
                  value=""
                  disabled
                  selected
                >
                  Select
                </option>
                <option
                  v-for="item in countries"
                  :key="item"
                  :value="item"
                >
                  {{ item }}
                </option>
              </Field>
              <label
                for="country"
                class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-focus:scale-75 peer-focus:-translate-y-4"
              >Country</label>
              <ErrorMessage
                name="country"
                class="text-red-500 text-sm ms-5"
              />
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.country)"
                class="text-red-500 text-sm ms-5"
              >
                {{ inputErrors.country[0] }}
              </p>
            </div>
          </div>
        </VForm>
        <div>
          <TwButton
            loading-text="Updating..."
            :loading="isLoading"
            class="w-full h-10 mt-5"
            @click="handleUpdateShipping"
          >
            Update
          </TwButton>
        </div>
      </div>
    </div>
  </Sidebar>
</template>

<style lang="scss">
.p-inputtext {
  border: none !important;
  border-color: transparent;
  font-family: "Konnect", system-ui, -apple-system, BlinkMacSystemFont,
    "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue",
    sans-serif;
    font-size: 14px !important;
    font-weight: 500;

  &:enabled:hover {
    border-color: transparent;
  }
  &:enabled:focus {
    outline: none;
    outline-offset: 0;
    box-shadow: none;
    border-color: transparent;
  }
}

.p-autocomplete-panel {
  max-width: 400px;
}
.p-autocomplete-panel .p-autocomplete-items .p-autocomplete-item {
  white-space: pre-line;
}
.p-autocomplete-panel {
  z-index: 2102 !important;
}
.p-sidebar-right .update-shipping-sidebar.p-sidebar {
  width: 30rem;
  font-family: "Konnect", system-ui, -apple-system, BlinkMacSystemFont,
    "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue",
    sans-serif;
}
</style>
