<script setup>
import { isEmpty } from '@/@core/utils'
import ApiService from '@/services/ApiService'
import { processErrors } from '@/utils/errorHandler'
import { IconCheck } from '@tabler/icons-vue'
import { computed, ref } from 'vue'
import { toast } from 'vue-sonner'

const props = defineProps({
  closeOnClickOutside: {
    type: Boolean,
    default: true,
  },
  orderId: {
    type: String,
    required: true,
  },
})

const emit = defineEmits(['updated'])

const orderId = computed(() => props.orderId)
const isOpen = ref(false)
const isLoading = ref(false)
const selectedCancelReason = ref([])

const cancelReasons = [
  'I no longer need this medication.',
  'I ordered the wrong medication or dosage.',
  'I am concerned about potential side effects.',
  'I am switching to a different pharmacy.',
  'I have personal or other reasons for canceling this order.',
]

function open() {
  isOpen.value = true
}

function close() {
  isOpen.value = false
  emit('updated', true)
}

function handleOverlayClick(event) {
  if (props.closeOnClickOutside && event.target === event.currentTarget) {
    close()
  }
}

async function handleCancelReasonSubmit() {
  try {
    isLoading.value = true

    const { data } = await ApiService.post('/update-cancel-order-reason', {
      refill_id: orderId.value,
      cancel_reason: selectedCancelReason.value,
    })

    if (data.status === 200) {
      close()
    } else {
      toast.error(processErrors(data)[0])
    }
  } catch (error) {
    toast.error(processErrors(error)[0])
  } finally {
    isLoading.value = false
  }
}

defineExpose({ open, close })
</script>

<template>
  <div
    v-if="isOpen"
    class="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center overflow-y-auto overflow-x-hidden top-0 right-0 left-0 z-50 w-full md:inset-0 h-full max-h-full"
    @click="handleOverlayClick"
  >
    <div class="relative p-4 w-full max-w-2xl max-h-full">
      <div class="relative bg-white rounded-[20px] shadow dark:bg-gray-700">
        <div class="px-5 py-8 md:p-10">
          <div class="text-center mb-6">
            <h2 class="text-xl md:text-2xl font-bold leading-tight text-black mb-2">
              Your order has been canceled
            </h2>
            <p class="text-sm">
              Can you tell us why you canceled? Select all that apply
            </p>
          </div>

          <ul class="w-full mt-8 space-y-5">
            <li
              v-for="(reason, index) in cancelReasons"
              :key="reason"
            >
              <input
                :id="`cancel_reason_${index}`"
                v-model="selectedCancelReason"
                name="cancel_reason[]"
                :value="reason"
                class="hidden peer"
                type="checkbox"
              />
              <label
                :for="`cancel_reason_${index}`"
                class="inline-flex items-center justify-between w-full px-5 py-3 text-gray-700 bg-white border !border-gray-200 rounded-lg cursor-pointer peer-checked:!border-gray-500 peer-checked:text-black hover:bg-gray-100"
              >
                <span class="block text-start">
                  <span class="w-full text-base font-medium">{{ reason }}</span>
                </span>
                <span class="mx-2 flex items-center justify-center">
                  <span
                    class="w-4 h-4 border-2 rounded-sm flex justify-center items-center"
                    :class="[
                      selectedCancelReason.includes(reason) ? 'border-transparent bg-black' : 'border-gray-400'
                    ]"
                  >
                    <IconCheck
                      class="h-5 w-5 z-50"
                      :stroke-width="selectedCancelReason.includes(reason) ? 4 : 1"
                      :class="[
                        selectedCancelReason.includes(reason) ? 'text-white' : 'text-gray-900'
                      ]"
                    />
                  </span>
                </span>
              </label>
            </li>
          </ul>

          <TwButton
            class="w-full mt-6"
            :loading="isLoading"
            :disabled="isEmpty(selectedCancelReason)"
            @click="handleCancelReasonSubmit"
          >
            Continue
          </TwButton>

          <div class="text-center mt-6">
            <RouterLink
              :to="{ name: 'user-orders' }"
              class="underline text-base font-medium"
            >
              Back to Orders
            </RouterLink>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
