<script setup>
import { isEmpty } from '@/@core/utils'
import router from '@/router'
import ApiService from '@/services/ApiService'
import { IconAlertCircle } from '@tabler/icons-vue'
import { useSessionStorage } from '@vueuse/core'
import Sidebar from 'primevue/sidebar'
import PendingVisitItem from './PendingVisitItem.vue'
import { onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { toast } from 'vue-sonner'
import { clearRetrySession } from '@/utils/sessionHelpers'
import PendingDocumentUploadItem from './PendingDocumentUploadItem.vue'

const route = useRoute()
const edVisitSession = useSessionStorage('edVisitSession', {})
const hlVisitSession = useSessionStorage('hlVisitSession', {})
const pendingVisits = ref([])
const pendingVisitsSidebarVisible = ref(false)
const expiredPrescriptions = ref([])
const expiredOrderSidebarVisible = ref(false)
const btnRenewLoading = ref('')
const pendingDocumentOrders = ref([])

onMounted(async () => {
  await fetchPendingVisits()

  if (route.query['complete-visit'] && pendingVisits.value.length > 0) {
    pendingVisitsSidebarVisible.value = true
  }
})

async function fetchPendingVisits() {
  try {
    const { data } = await ApiService.get('/pending-visits')

    if (data.status === 200) {
      if (!isEmpty(data.pendingVisits)) {
        pendingVisits.value = data.pendingVisits
      }
      if (!isEmpty(data.orders)) {
        pendingDocumentOrders.value = data.orders
      }
      if (!isEmpty(data.expiredPrescriptions)) {
        expiredPrescriptions.value = data.expiredPrescriptions
      }
    } else {
      console.error(data)
    }
  } catch (error) {
    console.error(error)
  }
}

function handleRenewPrescription(visit) {
  clearRetrySession()

  const renewPrescriptionData = visit.renewedPrescriptionData

  btnRenewLoading.value = renewPrescriptionData.previous_subscription_id

  if (!isEmpty(renewPrescriptionData)) {
    if (visit.category.toUpperCase() === 'ED') {
      edVisitSession.value = {}
      edVisitSession.value = {
        visitType: 'renew',
        subscriptionId: renewPrescriptionData.previous_subscription_id,

        // category: renewPrescriptionData.category,
        // productFreq: renewPrescriptionData.frequency,
        // productId: renewPrescriptionData.ed_product_id,
        // strengthId: renewPrescriptionData.ed_product_strength_id,
        // productQty: renewPrescriptionData.ed_product_qty_id,
        // planId: renewPrescriptionData.ed_product_subscription_plan_id,
        // selectedShippingMethod: renewPrescriptionData.pharmacy_shipping_method_id,
        // productType: renewPrescriptionData.product_type,
      }

      router.push({ name: 'ed-questions-renew', params: { slug: 'start' } })

      return
    } else if (visit.category.toUpperCase() === 'HL') {
      hlVisitSession.value = {}
      hlVisitSession.value = {
        visitType: 'renew',
        subscriptionId: renewPrescriptionData.previous_subscription_id,

        // category: renewPrescriptionData.category,
        // productId: renewPrescriptionData.hl_product_id,
        // strengthId: renewPrescriptionData.hl_product_strength_id,
        // productQty: renewPrescriptionData.hl_product_qty_id,
        // planId: renewPrescriptionData.hl_product_subscription_plan_id,
        // selectedShippingMethod: renewPrescriptionData.pharmacy_shipping_method_id,
      }

      router.push({ name: 'hl-questions-renew', params: { slug: 'start' } })

      return
    }
  }

  toast.error('We are unable to renew your prescription at this time. Please try again later.')
}

function handleStartNewVisit(category) {
  edVisitSession.value = {}
  hlVisitSession.value = {}

  router.push({ name: `${category.toLowerCase()}-questions`, params: { slug: 'start' } })
}
</script>

<template>
  <div>
    <!-- Pending Visits alert -->
    <div
      v-if="!isEmpty(pendingVisits) || !isEmpty(pendingDocumentOrders)"
      class="w-full bg-amber-100 text-center p-3 text-base font-medium text-amber-600"
    >
      <div class="flex items-center justify-center gap-2 h-full">
        <IconAlertCircle
          class="h-5 w-5 -mt-1"
          stroke-width="2"
        />
        <span>
          Complete your pending visits.
          <button
            class="font-semibold text-amber-700 underline hover:no-underline"
            @click="pendingVisitsSidebarVisible = true"
          >
            Take Action
          </button>
        </span>
      </div>
    </div>
    <!-- Expired Orders alert -->
    <div
      v-if="!isEmpty(expiredPrescriptions)"
      class="w-full bg-amber-100 text-center p-3 text-base font-medium text-amber-600"
    >
      <div class="flex items-center justify-center gap-2 h-full">
        <IconAlertCircle
          class="h-5 w-5 -mt-1"
          stroke-width="2"
        />
        <span>
          Your prescription is expired.
          <button
            class="font-semibold text-amber-700 underline hover:no-underline"
            @click="expiredOrderSidebarVisible = true"
          >
            Renew Now
          </button>
        </span>
      </div>
    </div>
  </div>

  <!-- pending visits sidebar -->
  <Sidebar
    v-if="!isEmpty(pendingVisits) || !isEmpty(pendingDocumentOrders)"
    v-model:visible="pendingVisitsSidebarVisible"
    position="right"
    show-close-icon
    class="pending-visits-sidebar bg-gray-200"
    @hide="pendingVisitsSidebarVisible = false"
  >
    <div class="w-full">
      <h2 class="text-2xl font-bold leading-tight text-black mb-6">
        Pending Visits
      </h2>
      <div class="mb-3 text-base leading-[1.7] space-y-3">
        <PendingDocumentUploadItem
          v-for="order in pendingDocumentOrders"
          :key="order.id"
          :order="order"
        />
      </div>
      <div class="mb-3 text-base leading-[1.7] space-y-3">
        <PendingVisitItem
          v-for="visit in pendingVisits"
          :key="visit.id"
          :visit="visit"
        />
      </div>
    </div>
  </Sidebar>
  <!-- Expired Orders sidebar -->
  <Sidebar
    v-if="!isEmpty(expiredPrescriptions)"
    v-model:visible="expiredOrderSidebarVisible"
    position="right"
    show-close-icon
    class="pending-visits-sidebar bg-gray-200"
    @hide="expiredOrderSidebarVisible = false"
  >
    <div class="w-full">
      <h2 class="text-2xl font-bold leading-tight text-black mb-6">
        Expired Prescriptions
      </h2>
      <div class="mb-6 text-base leading-[1.7] space-y-3">
        <div
          v-for="visit in expiredPrescriptions"
          :key="visit.id"
          class="bg-white rounded-[20px] p-5"
        >
          <h5 class="text-gray-800 text-base font-semibold uppercase mb-2">
            Renew your {{ visit.category.toUpperCase() === 'HL' ? 'Hair Loss' : visit.category.toUpperCase() }} prescription
          </h5>
          <p class="text-base font-normal mb-5">
            Your prescription for {{ visit.category.toUpperCase() === 'HL' ? 'Hair Treatment' : visit.category.toUpperCase() }} has expired. Please renew your prescription to continue using your plan benefits.
          </p>
          <TwButton
            class="w-full mb-3"
            :loading="btnRenewLoading === visit.renewedPrescriptionData?.previous_subscription_id"
            :disabled="!isEmpty(btnRenewLoading)"
            @click="handleRenewPrescription(visit)"
          >
            Renew Now
          </TwButton>

          <div class="text-center">
            <button
              class="inline-flex items-center text-sm text-black font-medium border-b !border-black hover:text-gray-700 hover:!border-gray-700 disabled:cursor-not-allowed"
              :disabled="!isEmpty(btnRenewLoading)"
              @click="handleStartNewVisit(visit.category.toUpperCase())"
            >
              Start New Visit
            </button>
          </div>
        </div>
      </div>
    </div>
  </Sidebar>
</template>

<style lang="scss">
.p-sidebar-right .pending-visits-sidebar.p-sidebar {
  width: 30rem;
  font-family: "Konnect", system-ui, -apple-system, BlinkMacSystemFont,
    "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue",
    sans-serif;
}
</style>
