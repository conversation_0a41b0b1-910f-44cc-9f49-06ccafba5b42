<script setup>
import ApiService from '@/services/ApiService'
import { processErrors } from '@/utils/errorHandler'
import { computed, ref } from 'vue'
import { toast } from 'vue-sonner'

const props = defineProps({
  closeOnClickOutside: {
    type: Boolean,
    default: true,
  },
  isFollowUpVisit: {
    type: Boolean,
    default: false,
  },
  orderId: {
    type: String,
    required: true,
  },
})

const emit = defineEmits(['canceled'])

const orderId = computed(() => props.orderId)
const isOpen = ref(false)
const isLoading = ref(false)

function open() {
  isOpen.value = true
}

function close() {
  isOpen.value = false
}

function handleOverlayClick(event) {
  if (props.closeOnClickOutside && event.target === event.currentTarget) {
    close()
  }
}

async function handleOrderCancel() {
  try {
    isLoading.value = true

    const { data } = await ApiService.get(`/cancel-order/${orderId.value}`)

    if (data.status === 200) {
      // toast.success(data.message)
      emit('canceled', true)
    } else {
      toast.error(processErrors(data)[0])
    }
  } catch (error) {
    toast.error(processErrors(error)[0])
  } finally {
    isLoading.value = false
    close()
  }
}

defineExpose({ open, close })
</script>

<template>
  <div
    v-if="isOpen"
    class="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center overflow-y-auto overflow-x-hidden top-0 right-0 left-0 z-50 w-full md:inset-0 h-full max-h-full"
    @click="handleOverlayClick"
  >
    <div class="relative p-4 w-full max-w-2xl max-h-full">
      <div class="relative bg-white rounded-[20px] shadow dark:bg-gray-700">
        <div class="px-5 py-8 md:p-10">
          <h2 class="text-center text-2xl md:text:3xl font-bold leading-tight text-black">
            Do you want to cancel this order?
          </h2>

          <div class="flex justify-center gap-3 mt-6">
            <TwButton
              class="w-48"
              variant="secondary"
              :disabled="isLoading"
              @click="close"
            >
              No
            </TwButton>
            <TwButton
              :loading="isLoading"
              class="w-48"
              @click="handleOrderCancel"
            >
              Yes
            </TwButton>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
