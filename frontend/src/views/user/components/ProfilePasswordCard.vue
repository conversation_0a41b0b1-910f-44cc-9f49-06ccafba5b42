<script setup>
import { ref } from 'vue'
import ApiService from '@/services/ApiService'
import AlertError from '@/views/user/components/AlertError.vue'
import { processErrors } from '@/utils/errorHandler'
import { IconCheck, IconEye, IconEyeOff } from '@tabler/icons-vue'
import { toast } from 'vue-sonner'
import { preventSpacesFromStart, removeKeyFromObject } from '@/utils/helpers'
import { isEmpty, isEmptyObject, isNullOrUndefined } from '@/@core/utils'
import * as yup from 'yup'
import { Form as VForm, Field, ErrorMessage } from 'vee-validate'
import { useAuthStore } from '@/store/auth'
import { storeToRefs } from 'pinia'

const authStore = useAuthStore()
const { userData } = storeToRefs(authStore)

const isLoading = ref(false)
const serverErrors = ref([])
const inputErrors = ref({})
const scrollToEle = ref(null)
const editMode = ref(false)
const currentPasswordVisible = ref(false)
const newPasswordVisible = ref(false)
const confirmPasswordVisible = ref(false)

const form = ref({
  current_password: '',
  new_password: '',
  password_confirmation: '',
})

const validationSchema = yup.object().shape({
  current_password: yup.string().required('Current password is required'),
  new_password: yup.string()
    .required('New password is required')
    .min(8, 'New password must be at least 8 characters')
    .matches(/(?=.*[A-Z0-9!@#$%^&*])/, 'Include at least one uppercase letter or number or symbol'),
  password_confirmation: yup.string().required('Confirm password is required').oneOf([yup.ref('new_password'), null], 'Passwords must match'),
})

function removeKeyFromInputErrors(key) {
  inputErrors.value = removeKeyFromObject(inputErrors.value, key)
}

async function handleSubmit(values) {
  try {
    isLoading.value = true
    inputErrors.value = {}
    serverErrors.value = []

    const { data } = await ApiService.post('/change-password', values)

    if (data.status === 200) {
      toast.success(data.message)
      discardEdit()
    } else {
      if (data.errors) {
        inputErrors.value = data.errors
      }
    }
  } catch (error) {
    if (error.response.errors) {
      inputErrors.value = error.response.errors
    }
    serverErrors.value = processErrors(error)
    console.error(error)
  } finally {
    isLoading.value = false
  }
}

function discardEdit() {
  editMode.value = false
  form.value = {
    current_password: '',
    new_password: '',
    password_confirmation: '',
  }
}
</script>

<template>
  <div class="bg-white py-5 px-6 rounded-[20px]">
    <div class="flex justify-between mb-3">
      <div class="text-xl font-semibold">
        Password
      </div>
      <div>
        <button
          v-if="!editMode"
          class="text-sm font-medium border !border-zinc-800 px-4 py-1.5 rounded-full hover:bg-gray-100 transition-all duration-300"
          @click="editMode = true"
        >
          <span class="hidden sm:inline">Change password</span>
          <span class="inline sm:hidden">Change</span>
        </button>
      </div>
    </div>

    <div
      ref="scrollToEle"
      class="h-0"
    ></div>

    <AlertError
      v-if="isEmptyObject(inputErrors) && serverErrors.length > 0"
      title="Error!"
      :errors="serverErrors"
    />

    <div v-if="!editMode">
      <div class="text-sm text-zinc-700">
        Current Password
      </div>
      <div class="font-medium text-zinc-900">
        **********
      </div>
      <div v-if="!isEmpty(userData?.last_updated_password_at)">
        Last update
        <span class="bg-orange-200 text-orange-400 text-sm font-medium px-2 py-1 rounded ms-2">
          {{ userData?.last_updated_password_at }}
        </span>
      </div>
    </div>
    <div v-if="editMode">
      <VForm
        class="mt-8"
        autocomplete="off"
        :validation-schema="validationSchema"
        @submit="handleSubmit"
      >
        <div class="space-y-5">
          <div>
            <div class="relative">
              <Field
                id="current_password"
                v-model="form.current_password"
                :type="currentPasswordVisible ? 'text' : 'password'"
                name="current_password"
                class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                placeholder=" "
                autocomplete="new-password"
                required
                @keydown="preventSpacesFromStart($event)"
                @keyup="removeKeyFromInputErrors('current_password')"
              />
              <label
                for="current_password"
                class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4"
              >Current Password</label>
              <div class="absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                <IconEye
                  v-if="!currentPasswordVisible"
                  class="h-5 w-5"
                  stroke-width="2"
                  @click="currentPasswordVisible = !currentPasswordVisible"
                />
                <IconEyeOff
                  v-else
                  class="h-5 w-5"
                  stroke-width="2"
                  @click="currentPasswordVisible = !currentPasswordVisible"
                />
              </div>
            </div>
            <ErrorMessage
              name="current_password"
              class="text-red-500 text-sm ms-5 inline-block"
            />
            <p
              v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.current_password)"
              class="text-red-500 text-sm ms-5"
            >
              {{ inputErrors.current_password[0] }}
            </p>
          </div>

          <div>
            <div class="relative">
              <Field
                id="new_password"
                v-model="form.new_password"
                :type="newPasswordVisible ? 'text' : 'password'"
                name="new_password"
                class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                placeholder=" "
                autocomplete="new-password"
                required
                @keydown="preventSpacesFromStart($event)"
                @keyup="removeKeyFromInputErrors('last_name')"
              />
              <label
                for="new_password"
                class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4"
              >New Password</label>
              <div class="absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                <IconEye
                  v-if="!newPasswordVisible"
                  class="h-5 w-5"
                  stroke-width="2"
                  @click="newPasswordVisible = !newPasswordVisible"
                />
                <IconEyeOff
                  v-else
                  class="h-5 w-5"
                  stroke-width="2"
                  @click="newPasswordVisible = !newPasswordVisible"
                />
              </div>
            </div>
            <ErrorMessage
              name="new_password"
              class="text-red-500 text-sm ms-5 inline-block"
            />
            <p
              v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.new_password)"
              class="text-red-500 text-sm ms-5"
            >
              {{ inputErrors.new_password[0] }}
            </p>
          </div>

          <div>
            <div class="relative">
              <Field
                id="password_confirmation"
                v-model="form.password_confirmation"
                :type="confirmPasswordVisible ? 'text' : 'password'"
                name="password_confirmation"
                class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                placeholder=" "
                autocomplete="new-password"
                required
                @keydown="preventSpacesFromStart($event)"
                @keyup="removeKeyFromInputErrors('last_name')"
              />
              <label
                for="password_confirmation"
                class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4"
              >Confirm Password</label>
              <div class="absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                <IconEye
                  v-if="!confirmPasswordVisible"
                  class="h-5 w-5"
                  stroke-width="2"
                  @click="confirmPasswordVisible = !confirmPasswordVisible"
                />
                <IconEyeOff
                  v-else
                  class="h-5 w-5"
                  stroke-width="2"
                  @click="confirmPasswordVisible = !confirmPasswordVisible"
                />
              </div>
            </div>
            <ErrorMessage
              name="password_confirmation"
              class="text-red-500 text-sm ms-5 inline-block"
            />
            <p
              v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.password_confirmation)"
              class="text-red-500 text-sm ms-5"
            >
              {{ inputErrors.password_confirmation[0] }}
            </p>
          </div>

          <div class="mt-8 flex gap-3">
            <button
              type="button"
              class="inline-flex w-full items-center justify-center rounded-full border !border-black px-3.5 py-2.5 font-semibold leading-7 text-black hover:bg-gray-200 text-sm"
              :disabled="isLoading"
              @click="discardEdit"
            >
              Discard
            </button>
            <TwButton
              type="submit"
              class="w-full"
              :loading="isLoading"
            >
              Update
            </TwButton>
          </div>
          <div class="mb-6">
            <p class="flex items-center gap-3 mb-2 text-sm">
              <IconCheck
                class="h-5 w-5 text-gray-300"
                :class="{ 'text-green-300': form.new_password.length >= 8 }"
                stroke-width="2"
              />
              <span>Password must be at least 8 characters long</span>
            </p>
            <p class="flex items-center gap-3 text-sm">
              <IconCheck
                class="h-5 w-5 text-gray-300"
                :class="{ 'text-green-300': form.new_password.match(/(?=.*[A-Z0-9!@#$%^&*])/) }"
                stroke-width="2"
              />
              <span>Include at least one uppercase letter or number or symbol</span>
            </p>
          </div>
        </div>
      </VForm>
    </div>
  </div>
</template>
