<script setup>
import { IconMinus, IconPlus } from '@tabler/icons-vue'
import { ref } from 'vue'

defineProps({
  title: {
    type: String,
    required: true,
  },
})

const isOpen = ref(false)

const toggleAccordion = () => {
  isOpen.value = !isOpen.value
}
</script>

<template>
  <div class="border !border-gray-100 !rounded-xl">
    <!-- Title -->
    <div
      class="w-full flex justify-between items-center px-4 py-2.5 !rounded-xl cursor-pointer"
      :class="[
        isOpen ? '' : 'bg-gray-100',
      ]"
      @click="toggleAccordion"
    >
      <span class="font-medium text-gray-900 text-base">{{ title }}</span>
      <div>
        <IconPlus
          v-show="!isOpen"
          class="w-5 h-5 text-gray-500"
          stroke-width="2"
        />
        <IconMinus
          v-show="isOpen"
          class="w-5 h-5 text-gray-500"
          stroke-width="2"
        />
      </div>
    </div>
    <!-- Description -->
    <div
      v-show="isOpen"
      class="px-4 py-2"
    >
      <slot />
    </div>
  </div>
</template>

<style scoped>
/* Add any custom styles here if needed */
</style>
