<script setup>
import Sidebar from 'primevue/sidebar'
import ApiService from '@/services/ApiService'
import AlertError from '@/views/user/components/AlertError.vue'
import { processErrors } from '@/utils/errorHandler'
import { isEmpty } from '@/@core/utils'
import { toast } from 'vue-sonner'
import { IconCheck } from '@tabler/icons-vue'
import { loadScript } from '@paypal/paypal-js'
import router from '@/router'
import { useRoute } from 'vue-router'

const emit = defineEmits(['updated'])

const route = useRoute()
const orderId = ref(null)
const isVisible = ref(false)
const skeletonLoading = ref(false)
const isLoading = ref(false)
const serverErrors = ref([])
const paymentMethods = ref([])
const selectedPaymentMethod = ref(null)
const isCardFormVisible = ref(false)
const cardFieldErrors = ref([])
const cardFieldsRendered = ref(false)

async function openModal(orderObj) {
  isVisible.value = true
  isLoading.value = false
  serverErrors.value = []
  selectedPaymentMethod.value = null
  isCardFormVisible.value = false
  cardFieldErrors.value = []
  cardFieldsRendered.value = false
  await fetchPaymentMethods()

  orderId.value = orderObj.orderId
  selectedPaymentMethod.value = orderObj.currentPaymentMethodId
}

async function fetchPaymentMethods() {
  try {
    skeletonLoading.value = true

    const { data } = await ApiService.get('/list-payment-methods')

    if (data.status === 200) {
      paymentMethods.value = data.paymentMethodList
    }
  } catch (error) {
    console.error(error)
  } finally {
    skeletonLoading.value = false
  }
}

async function initPaypal() {
  serverErrors.value = []
  isCardFormVisible.value = true

  if (cardFieldsRendered.value) return
  isLoading.value = true

  const paypal = await loadScript({
    'client-id': (import.meta.env.VITE_PAYPAL_CLIENT_ID),
    components: 'card-fields',
  })

  const inputStyles = {
    'input': {
      'padding': '0.75rem 1.25rem',
      'font-size': '1rem',
      'font-weight': '500',
      'font-family': 'Konnect',
      'appearance': 'none',
      'outline': 'none',
      'transition': 'border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out',
    },
  }

  const cardFields = paypal.CardFields({
    style: inputStyles,
    createVaultSetupToken: async () => {
      const { data } = await ApiService.get('/paypal/vault/token/create')
      if (data.status === 200) return data.vaultId
      else throw data
    },
    onApprove: async data => {
      const vaultId = data.vaultSetupToken
      const response = await ApiService.get(`/paypal/vault/payment/token/create/${vaultId}`)
      if (response.data.status === 200) {
        selectedPaymentMethod.value = response.data.id
      } else {
        throw response.data
      }
    },
    onError: async error => {
      if (error.message === 'Internal server error') {
        cardFieldErrors.value.push('Please check your card details and try again.')
      } else {
        cardFieldErrors.value.push(error.message || 'Something went wrong. Please try again.')
      }
    },
  })

  if (cardFields.isEligible()) {
    // cardFields.NameField().render("#card-holder-name")
    cardFields.NumberField().render('#card-number')
    cardFields.ExpiryField().render('#expiration-date')
    cardFields.CVVField().render('#cvv')

    isLoading.value = false
    cardFieldsRendered.value = true
  } else {
    // Handle the workflow when credit and debit cards are not available
    toast.error('Credit and debit cards are not available. Please try again later.')
  }

  const submitButton = document.getElementById('checkoutSubmitBtn')

  submitButton.addEventListener('click', () => {
    isLoading.value = true
    cardFieldErrors.value = []

    cardFields
      .submit()
      .then(async () => {
        await retryOrderPayment()
      })
      .catch(error => {
        isLoading.value = false
        if (error.message === 'INVALID_NUMBER') {
          cardFieldErrors.value.push('Please enter a valid card number.')
        }
        if (error.message === 'INVALID_CVV') {
          cardFieldErrors.value.push('Please enter a valid CVV number.')
        }
        if (error.message === 'INVALID_EXPIRY') {
          cardFieldErrors.value.push('Please enter a valid expiration date.')
        }
      })
  })
}

async function retryOrderPayment() {
  try {
    isLoading.value = true
    serverErrors.value = []

    const postData = {
      order_id: orderId.value,
      paypal_card_id: selectedPaymentMethod.value,
    }

    const { data } = await ApiService.post('/order', postData)

    if (data.status === 200) {
      // toast.success(data.message)
      // emit('updated')

      closeModal()
      router.push({
        name: 'ed-visit-order-success',
        params: { orderId: data.orderId },
        query: { ...route.query },
      })
    } else {
      isLoading.value = false
      serverErrors.value = processErrors(data)
    }
  } catch (error) {
    console.error(error)
    isLoading.value = false
    serverErrors.value = processErrors(error)
  }
}

function closeModal() {
  isVisible.value = false
}

function isCardExpired(paymentMethod) {
  const now = new Date()
  const expirationDate = new Date(paymentMethod.card_expiry_year, paymentMethod.card_expiry_month - 1)

  return now > expirationDate
}

defineExpose({ openModal, closeModal })
</script>

<template>
  <Sidebar
    v-model:visible="isVisible"
    position="right"
    show-close-icon
    class="update-shipping-sidebar"
    @hide="closeModal"
  >
    <div class="w-full">
      <h2 class="text-2xl font-bold leading-tight text-black mb-3">
        Retry Order Payment
      </h2>

      <AlertError
        v-if="!isEmpty(serverErrors)"
        title="Error!"
        :errors="serverErrors"
      />

      <div v-if="skeletonLoading">
        <div class="px-4 w-full">
          <div class="h-12 bg-gray-300 rounded mt-6 mb-2 animate-pulse"></div>
          <div class="h-12 bg-gray-300 rounded mt-6 mb-2 animate-pulse"></div>
          <div class="h-12 bg-gray-300 rounded mt-6 mb-2 animate-pulse"></div>
          <div class="h-12 bg-gray-300 rounded mt-6 mb-2 animate-pulse"></div>
          <div class="h-12 bg-gray-300 rounded mt-6 mb-2 animate-pulse"></div>
          <div class="h-10 bg-gray-300 rounded-xl mt-8 mb-2 animate-pulse"></div>
        </div>
      </div>

      <div v-else>
        <div v-if="paymentMethods.length > 0 && !isCardFormVisible">
          <div>
            <h5 class="text-gray-600 text-base font-medium mb-3">
              Select from existing payment methods
            </h5>
          </div>
          <ul class="grid w-full space-y-3 mb-5">
            <li
              v-for="paymentMethod in paymentMethods"
              :key="paymentMethod.id"
            >
              <input
                :id="`payment_method_${paymentMethod.id}`"
                v-model="selectedPaymentMethod"
                type="radio"
                name="shipping_method"
                class="hidden peer"
                :value="paymentMethod.id"
                :disabled="isCardExpired(paymentMethod)"
                required
              />
              <label
                :for="`payment_method_${paymentMethod.id}`"
                class="inline-flex items-center justify-between w-full px-5 py-3 text-gray-700 bg-white border !border-gray-200 rounded-2xl cursor-pointer peer-checked:!border-gray-600 peer-checked:text-black hover:text-gray-800 hover:bg-gray-100 peer-disabled:bg-gray-200 peer-disabled:cursor-not-allowed"
              >
                <div class="block">
                  <div class="w-full text-sm font-medium">{{ paymentMethod.card_brand_type }}</div>
                  <div class="w-full text-sm mb-0.5">xxxx xxxx xxxx {{ paymentMethod.card_last_4_digit }}</div>
                  <div class="w-full text-xs">
                    <span
                      v-if="isCardExpired(paymentMethod)"
                      class="bg-red-300 text-red-700 font-medium  px-2 py-0.5 rounded-full"
                    >Expired</span>
                    <span v-else>
                      Expires {{ paymentMethod.card_expiry_month }}/{{ paymentMethod.card_expiry_year }}
                    </span>
                  </div>
                </div>
                <span class="mx-2 flex items-center justify-center">
                  <span
                    class="w-4 h-4 border-2 rounded-sm flex justify-center items-center"
                    :class="[
                      selectedPaymentMethod === paymentMethod.id ? 'border-transparent bg-black' : 'border-gray-400'
                    ]"
                  >
                    <IconCheck
                      class="h-5 w-5 z-50"
                      :stroke-width="selectedPaymentMethod === paymentMethod.id ? 4 : 1"
                      :class="[
                        selectedPaymentMethod === paymentMethod.id ? 'text-white' : 'text-gray-900'
                      ]"
                    />
                  </span>
                </span>
              </label>
            </li>
          </ul>

          <TwButton
            class="w-full"
            :loading="isLoading"
            :disabled="!selectedPaymentMethod"
            @click="retryOrderPayment"
          >
            Submit
          </TwButton>
        </div>

        <div
          v-if="paymentMethods.length > 0 && !isCardFormVisible"
          class="inline-flex items-center justify-center w-full my-8 relative"
        >
          <hr class="w-64 h-px bg-gray-200 border-0">
          <span class="absolute px-3 font-medium text-gray-900 -translate-x-1/2 bg-white left-1/2">or</span>
        </div>

        <TwButton
          v-if="!isCardFormVisible"
          class="w-full !bg-[#ffef08] border-2 border-solid border-[#BAB010] !text-black hover:!brightness-90 transition-all disabled:cursor-not-allowed disabled:!bg-yellow-100"
          :disabled="isLoading"
          @click="initPaypal"
        >
          Add New Card
        </TwButton>

        <div v-show="isCardFormVisible">
          <AlertError
            v-if="cardFieldErrors.length > 0"
            title="Error!"
            :errors="cardFieldErrors"
          />
          <div>
            <div
              id="card-number"
              class="w-full"
            ></div>
            <div
              id="expiration-date"
              class="w-full"
            ></div>
            <div
              id="cvv"
              class="w-full"
            ></div>
            <TwButton
              id="checkoutSubmitBtn"
              value="submit"
              class="w-full"
              :loading="isLoading"
            >
              Submit
            </TwButton>
            <div
              v-if="paymentMethods.length > 0"
              class="text-center mt-5"
            >
              <button
                class="text-sm font-medium border-b !border-gray-900 text-gray-900 inline-block"
                @click="() => {
                  isCardFormVisible = false
                  cardFieldErrors = []
                }"
              >
                Select existing payment method
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Sidebar>
</template>

<style lang="scss">
.p-inputtext {
  border: none !important;
  border-color: transparent;
  font-family: "Konnect", system-ui, -apple-system, BlinkMacSystemFont,
    "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue",
    sans-serif;
    font-size: 14px !important;
    font-weight: 500;

  &:enabled:hover {
    border-color: transparent;
  }
  &:enabled:focus {
    outline: none;
    outline-offset: 0;
    box-shadow: none;
    border-color: transparent;
  }
}

.p-autocomplete-panel {
  max-width: 400px;
}
.p-autocomplete-panel .p-autocomplete-items .p-autocomplete-item {
  white-space: pre-line;
}
.p-autocomplete-panel {
  z-index: 2102 !important;
}
.p-sidebar-right .update-shipping-sidebar.p-sidebar {
  width: 30rem;
  font-family: "Konnect", system-ui, -apple-system, BlinkMacSystemFont,
    "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue",
    sans-serif;
}
</style>
