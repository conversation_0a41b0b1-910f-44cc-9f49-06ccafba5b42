<script setup>
import { IconHelp, IconMail, IconPhone, IconX } from '@tabler/icons-vue'
import { computed, ref } from 'vue'

const props = defineProps({
  closeOnClickOutside: {
    type: Boolean,
    default: true,
  },
})

const isOpen = ref(false)
const supportEmail = computed(() => import.meta.env.VITE_SUPPORT_EMAIL)
const supportPhone = computed(() => import.meta.env.VITE_SUPPORT_PHONE)

function open() {
  isOpen.value = true
}

function close() {
  isOpen.value = false
}

function handleOverlayClick(event) {
  if (props.closeOnClickOutside && event.target === event.currentTarget) {
    close()
  }
}

defineExpose({ open, close })
</script>

<template>
  <div
    v-if="isOpen"
    class="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center overflow-y-auto overflow-x-hidden top-0 right-0 left-0 z-[100] w-full md:inset-0 h-full max-h-full"
    @click="handleOverlayClick"
  >
    <div
      class="relative p-4 w-full max-w-lg max-h-full"
      @click.stop
    >
      <div class="relative inline-block z-[999] top-12 right-[-85%] sm:right-[-92%]">
        <IconX
          class="cursor-pointer h-6 w-6"
          stroke-width="2"
          @click="close"
        />
      </div>
      <div class="relative bg-white rounded-[20px] shadow">
        <div class="px-5 py-5">
          <IconHelp
            class="h-12 w-12 mx-auto mb-2"
            stroke-width="2"
          />
          <h2 class="text-center text-xl md:text:3xl font-semibold leading-tight text-black">
            Need Help?
          </h2>
          <div class="text-center mt-3">
            <div
              v-if="supportEmail"
              class="text-base font-medium text-gray-700 mb-1"
            >
              <span>
                <IconMail
                  class="inline h-5 w-5 ms-1"
                  stroke-width="2"
                />
                Email:
              </span>
              <a
                :href="`mailto:${supportEmail}`"
                class="text-gray-900 hover:underline"
              >{{ supportEmail }}</a>
            </div>
            <div
              v-if="supportPhone"
              class="text-base font-medium text-gray-700 mb-1"
            >
              <span>
                <IconPhone
                  class="inline h-5 w-5 ms-1"
                  stroke-width="2"
                />
                Phone:
              </span>
              <a
                :href="`tel:${(supportPhone).replace(/[^0-9+]/g, '')}`"
                class="text-gray-900 hover:underline"
              >{{ supportPhone }}</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
