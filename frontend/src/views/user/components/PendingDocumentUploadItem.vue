<script setup>
import { isEmpty } from '@/@core/utils'
import router from '@/router'

const props = defineProps({
  order: {
    type: Object,
    required: true,
  },
})

function handleUpload(order) {
  router.push({
    name: 'visit-photo-upload-id',
    params: { visitType: order.category_name.toLowerCase() },
    query: { order_id: order.order_id },
  })
}
</script>

<template>
  <div
    v-if="order"
    class="bg-white rounded-[20px] p-5"
  >
    <h5 class="text-gray-800 text-base font-semibold uppercase mb-2">
      <span v-if="order.category_name.toUpperCase() === 'HL'">
        Complete your Hair treatment visit
      </span>
      <span v-else-if="order.category_name.toUpperCase() === 'WL'">
        Complete your Weight Loss visit
      </span>
      <span v-else>
        Complete your {{ order.category_name.toUpperCase() }} visit
      </span>
    </h5>
    <p class="text-base font-normal mb-5">
      You recently started a visit for {{ order.category_name.toUpperCase() === 'HL' ? 'Hair treatment' : order.category_name.toUpperCase() === 'WL' ? 'Weight Loss' : order.category_name.toUpperCase() }} and must upload a valid government issued ID (such as a US passport, driver's license, or state ID) to complete your visit.
    </p>
    <TwButton
      class="w-full mb-3"
      @click="handleUpload(order)"
    >
      Upload
    </TwButton>
  </div>
</template>
