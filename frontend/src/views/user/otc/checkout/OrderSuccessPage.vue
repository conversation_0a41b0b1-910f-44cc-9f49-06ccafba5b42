<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import Header from '@/views/user/components/Header.vue'
import { IconArrowRight, IconCheck, IconRosetteDiscount, IconShoppingCart } from '@tabler/icons-vue'
import ConfettiExplosion from 'vue-confetti-explosion'
import { clearOtcSession } from '@/utils/sessionHelpers'
import ApiService from '@/services/ApiService'
import { formatCurrency } from '@/utils/helpers'
import ModalHelp from '@/views/user/components/ModalHelp.vue'

const router = useRouter()
const route = useRoute()
const orderId = ref(route.params.orderId)
const skeletonLoading = ref(true)
const orderSummary = ref({})
const modalHelpRef = ref(null)

async function fetchOrderDetails() {
  skeletonLoading.value = true

  try {
    const { data } = await ApiService.get(`/otc/order-details/${orderId.value}`)

    if (data.status === 200) {
      orderSummary.value = data.order
    } else {
      throw data
    }
  } catch (error) {
    console.error(error)
    router.push({ name: '404' })
  } finally {
    skeletonLoading.value = false
  }
}

onMounted(() => {
  clearOtcSession()
  fetchOrderDetails()
})

const continueShopping = () => {
  router.push({ name: 'otc-products' })
}
</script>

<template>
  <div class="min-h-screen bg-gradient-to-b from-gray-50 to-white print:bg-white">
    <Header class="print:hidden" />

    <div
      v-if="skeletonLoading"
      class="max-w-6xl mx-auto px-4 sm:px-6 py-8 sm:py-12"
    >
      <div class="animate-pulse flex flex-col md:flex-row gap-6">
        <div class="bg-white rounded-lg shadow-sm overflow-hidden p-4 sm:p-8 md:w-3/5 w-full">
          <div class="h-8 bg-gray-200 rounded w-3/4 mb-6"></div>
          <div class="h-4 bg-gray-200 rounded w-1/2 mb-8"></div>
          <div class="h-24 bg-gray-200 rounded mb-6"></div>
          <div class="h-48 sm:h-64 bg-gray-200 rounded mb-6"></div>
        </div>
        <div class="bg-white rounded-lg shadow-sm overflow-hidden p-4 sm:p-8 md:w-2/5 w-full mt-4 md:mt-0">
          <div class="h-8 bg-gray-200 rounded w-1/2 mb-6"></div>
          <div class="h-32 bg-gray-200 rounded mb-6"></div>
          <div class="h-10 bg-gray-200 rounded w-full"></div>
        </div>
      </div>
    </div>

    <div
      v-else
      class="max-w-6xl mx-auto px-4 sm:px-6 py-8 sm:py-12"
    >
      <div class="flex flex-col lg:flex-row gap-6">
        <!-- Left Column - Main Content -->
        <div class="bg-white rounded-2xl shadow-sm overflow-hidden lg:w-3/5 w-full">
          <!-- Header with Logo and Confirmation -->
          <div class="p-4 sm:p-6 border-b !border-gray-100">
            <ConfettiExplosion
              :particle-count="100"
              :particle-size="10"
            />
            <div class="flex items-center mb-4">
              <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center me-3 flex-shrink-0">
                <IconCheck
                  class="h-7 w-7 text-green-600"
                  stroke-width="2"
                />
              </div>
              <div class="overflow-hidden">
                <h3 class="text-sm font-medium text-gray-500">
                  Confirmation <span class="font-medium text-gray-600">#{{ orderSummary.order_reference_no }}</span>
                </h3>
                <h1 class="text-xl sm:text-2xl font-bold text-gray-900">
                  Thank you {{ orderSummary.user_details?.first_name || 'for your order' }}!
                </h1>
              </div>
            </div>
          </div>

          <!-- Order Confirmation -->
          <div class="p-4 sm:p-6 border-b !border-gray-100">
            <div class="mb-1">
              <p class="text-gray-700 font-medium mb-1">
                Your order is confirmed
              </p>
              <p class="text-gray-600 text-sm">
                You'll receive a confirmation email with your order number shortly.
              </p>
            </div>
          </div>

          <!-- Order Updates Section -->
          <div class="p-4 sm:p-6 border-b !border-gray-100">
            <p class="text-gray-700 font-medium mb-1">
              Order updates
            </p>
            <p class="text-gray-600 text-sm">
              You'll get shipping and delivery updates by email.
            </p>
          </div>

          <!-- Customer Information -->
          <div class="p-4 sm:p-6">
            <h2 class="text-lg font-bold text-gray-800 mb-4">
              Customer information
            </h2>

            <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
              <div class="mb-4 sm:mb-0">
                <h3 class="text-sm font-medium text-gray-500 mb-2">
                  Contact information
                </h3>
                <p class="text-gray-700 break-words">
                  {{ orderSummary.user_details?.email }}
                </p>
                <p
                  v-if="orderSummary.user_details?.phone_number"
                  class="text-gray-700"
                >
                  {{ orderSummary.user_details?.phone_number }}
                </p>
              </div>

              <div>
                <h3 class="text-sm font-medium text-gray-500 mb-2">
                  Shipping address
                </h3>
                <p class="text-gray-700">
                  {{ orderSummary.user_details?.full_name }}
                </p>
                <p class="text-gray-700 break-words">
                  {{ orderSummary.shipping_details?.address_line_1 }}
                </p>
                <p
                  v-if="orderSummary.shipping_details?.address_line_2"
                  class="text-gray-700 break-words"
                >
                  {{ orderSummary.shipping_details?.address_line_2 }}
                </p>
                <p class="text-gray-700">
                  {{ orderSummary.shipping_details?.city }}, {{ orderSummary.shipping_details?.state }} {{ orderSummary.shipping_details?.zipcode }}
                </p>
                <p class="text-gray-700">
                  {{ orderSummary.shipping_details?.country }}
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Right Column - Order Summary -->
        <div class="bg-white rounded-2xl shadow-sm overflow-hidden lg:w-2/5 w-full">
          <div class="p-4 sm:p-6 border-b !border-gray-100">
            <div
              v-for="(product, index) in orderSummary.order_products"
              :key="index"
              class="flex items-start mb-4"
            >
              <div class="rounded-lg h-12 w-12 flex items-center justify-center me-3 sm:me-4 flex-shrink-0">
                <img
                  v-if="product.product_details.product_image"
                  :src="product.product_details.product_image"
                  class="h-full w-full object-contain"
                  :alt="product.product_details.product_name"
                >
                <IconShoppingCart
                  v-else
                  class="w-5 h-5 text-gray-500"
                  stroke-width="1.5"
                />
              </div>
              <div class="flex-1 min-w-0">
                <div class="flex flex-col sm:flex-row sm:justify-between sm:items-start md:flex-col lg:flex-row">
                  <div class="mb-1 sm:mb-0 overflow-hidden">
                    <div class="text-gray-600 font-medium">
                      {{ product.product_details.product_name }}
                    </div>
                    <div class="text-sm text-gray-600 flex flex-wrap items-center">
                      <span>{{ product.purchased_qty }} x {{ formatCurrency(product.product_amount) }}</span>
                      <span
                        v-if="product.promo_code_name"
                        class="bg-green-100 text-xs px-1.5 pt-[5px] pb-px rounded-full text-green-700 ms-2 inline-flex items-center font-medium"
                      >
                        <IconRosetteDiscount
                          class="size-4 me-1 -mt-[3px]"
                          stroke-width="2"
                        />
                        <span>{{ product.promo_code_name }}</span>
                        <span v-if="product.promo_code_type === 'percentage'">
                          &nbsp;({{ product.promo_code_value }}%)
                        </span>
                      </span>
                    </div>
                  </div>
                  <div class="flex items-center sm:ms-3 mt-1.5 sm:mt-0 place-self-end">
                    <span
                      v-if="product.total_product_amount !== product.grand_total_amount"
                      class="line-through text-gray-500 me-2"
                    >{{ formatCurrency(product.total_product_amount) }}</span>
                    <span class="text-gray-700 font-medium">
                      {{ formatCurrency(product.grand_total_amount) }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="p-4 sm:p-6">
            <!--
              <div
              v-if="orderSummary.promo_code_name"
              class="flex justify-between mb-2"
              >
              <span class="text-gray-600 grid">
              <span>Promotion applied</span>
              <span class="text-sm font-semibold">
              {{ orderSummary.promo_code_name }}
              <span v-if="orderSummary.promo_code_type === 'percentage'">({{ orderSummary.promo_code_value }}%)</span>
              </span>
              </span>
              <span class="text-green-600">-{{ formatCurrency(orderSummary.promo_code_discount_amount) }}</span>
              </div>
            -->
            <div class="flex justify-between mb-2">
              <span class="text-gray-600">Subtotal</span>
              <span class="text-gray-700">{{ formatCurrency(orderSummary.sub_total) }}</span>
            </div>
            <div class="flex justify-between mb-4">
              <span class="text-gray-600">Shipping</span>
              <span
                v-if="orderSummary.shipping_cost > 0"
                class="text-gray-700"
              >{{ formatCurrency(orderSummary.shipping_cost) }}</span>
              <span
                v-else
                class="text-green-600"
              >FREE</span>
            </div>

            <div class="flex justify-between pt-3 border-t !border-gray-100 text-base sm:text-lg">
              <span class="text-gray-800 font-medium">Total</span>
              <div class="text-right">
                <span class="text-gray-800 font-medium">{{ formatCurrency(orderSummary.grand_total) }}</span>
              </div>
            </div>

            <div class="mt-6">
              <TwButton
                class="w-full print:hidden text-sm sm:text-base"
                @click="continueShopping"
              >
                Continue shopping
                <IconArrowRight
                  class="w-4 h-4 ms-2"
                  stroke-width="2"
                />
              </TwButton>
            </div>

            <p class="mt-6 text-gray-500 text-sm text-center">
              Need help? <a
                href="#"
                class="text-gray-800 hover:text-black font-medium underline"
                @click.prevent="modalHelpRef.open()"
              >Contact us</a>
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Help modal -->
    <ModalHelp ref="modalHelpRef" />
  </div>
</template>

<style>
@media print {
  @page {
    margin: 0.5cm;
  }
  body {
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }
}

/* Mobile responsive styles */
@media (max-width: 640px) {
  .break-words {
    word-wrap: break-word;
    overflow-wrap: break-word;
    word-break: break-word;
  }
}

/* Ensure proper spacing on small screens */
@media (max-width: 480px) {
  .p-4 {
    padding: 0.75rem !important;
  }

  .gap-6 {
    gap: 1rem !important;
  }

  .me-3 {
    margin-right: 0.5rem !important;
  }
}
</style>
