<script setup>
import { ref, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/store/auth'
import { useCartStore } from '@/store/cartStore'
import { useCheckoutStore } from '@/store/checkoutStore'
import { storeToRefs } from 'pinia'
import { loadScript } from '@paypal/paypal-js'
import { IconShieldLock, IconCheck, IconDiscount, IconDiscount2, IconTagStarred, IconRosetteDiscount, IconRosetteDiscountCheckFilled } from '@tabler/icons-vue'
import { toast } from 'vue-sonner'
import ApiService from '@/services/ApiService'
import { formatCurrency } from '@/utils/helpers'

const router = useRouter()
const checkoutStore = useCheckoutStore()
const authStore = useAuthStore()
const cartStore = useCartStore()
const { checkoutSession, isLoading: storeLoading } = storeToRefs(checkoutStore)
const { isAuthenticated } = storeToRefs(authStore)
const { cartItems } = storeToRefs(cartStore)

const isLoading = ref(false)
const cardFieldErrors = ref([])
const paymentMethods = ref([])
const selectedPaymentMethod = ref(null)
const isCardFormVisible = ref(false)
const cardFieldsRendered = ref(false)

const checkoutSummary = ref(null)
const isLoadingProducts = ref(false)
const promoCode = ref('')
const promoCodeError = ref('')
const isPromoAdded = ref(false)
const showPromoInput = ref(false)

const paypalLoaded = ref(false)
const paypalLoadError = ref(null)

const loadPayPalScript = async () => {
  try {
    isLoading.value = true

    const paypal = await loadScript({
      'client-id': import.meta.env.VITE_PAYPAL_CLIENT_ID,
      components: 'card-fields',
    }).catch(error => {
      console.error('PayPal error:', error)
      throw error
    })

    if (paypal) {
      paypalLoaded.value = true
      initializeCardFields(paypal)
    } else {
      throw new Error('Failed to load PayPal')
    }
  } catch (error) {
    console.error('Failed to load PayPal JS SDK:', error)
    paypalLoadError.value = error
    toast.error('Failed to load payment processor. Please try again later.')
  } finally {
    isLoading.value = false
  }
}

const initializeCardFields = paypal => {
  const inputStyles = {
    'input': {
      'padding': '0.75rem 1.25rem',
      'font-size': '1rem',
      'font-weight': '500',
      'font-family': 'Konnect, system-ui, sans-serif',
      'appearance': 'none',
      'outline': 'none',
      'transition': 'border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out',
    },
  }

  const cardFields = paypal.CardFields({
    style: inputStyles,
    createVaultSetupToken: async () => {
      const { data } = await ApiService.get('/paypal/vault/token/create')
      if (data.status === 200) return data.vaultId
      else throw data
    },
    onApprove: async data => {
      const vaultId = data.vaultSetupToken
      const response = await ApiService.get(`/paypal/vault/payment/token/create/${vaultId}`)
      if (response.data.status === 200) {
        selectedPaymentMethod.value = response.data.id
        checkoutSession.value.paymentMethod = response.data.id
        checkoutSession.value.paymentToken = response.data.token

        // Mark step as completed and place order
        checkoutStore.setStepCompleted(3)
        checkoutStore.placeOrder()
      } else {
        throw response.data
      }
    },
    onError: async error => {
      console.error('PayPal Card Fields error:', error)
      if (error.message === 'Internal server error') {
        cardFieldErrors.value.push('Please check your card details and try again.')
      } else {
        cardFieldErrors.value.push(error.message || 'Something went wrong. Please try again.')
      }
      isLoading.value = false
    },
  })

  if (cardFields.isEligible()) {
    // cardFields.NameField().render("#card-holder-name")
    cardFields.NumberField().render('#card-number')
    cardFields.ExpiryField().render('#expiration-date')
    cardFields.CVVField().render('#cvv')

    isLoading.value = false
    cardFieldsRendered.value = true
  } else {
    // Handle the workflow when credit and debit cards are not available
    toast.error('Credit and debit cards are not available. Please try again later.')
  }

  const submitButton = document.getElementById('checkoutSubmitBtn')

  submitButton.addEventListener('click', () => {
    isLoading.value = true
    cardFieldErrors.value = []

    cardFields
      .submit()
      .then(() => {
        // Submission successful, handled by onApprove
      })
      .catch(error => {
        isLoading.value = false
        if (error.message === 'INVALID_NUMBER') {
          cardFieldErrors.value.push('Please enter a valid card number.')
        }
        if (error.message === 'INVALID_CVV') {
          cardFieldErrors.value.push('Please enter a valid CVV number.')
        }
        if (error.message === 'INVALID_EXPIRY') {
          cardFieldErrors.value.push('Please enter a valid expiration date.')
        }
      })
  })
}

const fetchSavedPaymentMethods = async () => {
  if (!isAuthenticated.value || checkoutSession.value.isGuest) return

  try {
    isLoading.value = true

    const { data } = await ApiService.get('/list-payment-methods')

    if (data.status === 200) {
      paymentMethods.value = data.paymentMethodList || []

      if (paymentMethods.value.length > 0) {
        // Find a non-expired default payment method
        const defaultPaymentMethod = paymentMethods.value.find(pm => pm.is_default === 1 && !isCardExpired(pm))
        if (defaultPaymentMethod) {
          selectedPaymentMethod.value = defaultPaymentMethod.id
        } else {
          // Otherwise use the first non-expired payment method
          const firstValidPaymentMethod = paymentMethods.value.find(pm => !isCardExpired(pm))
          if (firstValidPaymentMethod) {
            selectedPaymentMethod.value = firstValidPaymentMethod.id
          }
        }

        if (selectedPaymentMethod.value) {
          isCardFormVisible.value = false
        }
      } else {
        // No payment methods available, show card form
        isCardFormVisible.value = true
        loadPayPalScript()
      }
    }
  } catch (error) {
    console.error(error)
    toast.error('Failed to load saved payment methods')
  } finally {
    isLoading.value = false
  }
}

const selectExistingPaymentMethod = methodId => {
  selectedPaymentMethod.value = methodId
  checkoutSession.value.paymentMethod = methodId
  isCardFormVisible.value = false
  cardFieldErrors.value = []
}

const showNewCardForm = () => {
  // Clear selected payment method
  selectedPaymentMethod.value = null
  isCardFormVisible.value = true

  // If card fields are already rendered, just show the form
  if (cardFieldsRendered.value) {
    return
  }

  // Otherwise, initialize PayPal
  loadPayPalScript()
}

const handlePlaceOrder = () => {
  isLoading.value = true
  checkoutStore.setStepCompleted(3)
  checkoutStore.placeOrder()
}

const fetchProductDetails = async () => {
  try {
    isLoadingProducts.value = true

    const products = cartItems.value.map(item => ({
      product_id: item.id,
      qty: item.quantity,
    }))

    const payload = { products }

    if (promoCode.value) {
      payload.promocode = promoCode.value
    }

    const { data } = await ApiService.post('/otc-product-pre-checkout', payload)

    if (data.status === 200) {
      checkoutSummary.value = data.checkoutSummary

      if (checkoutSummary.value.orderSummary.is_promo_code_valid === 2) {
        promoCodeError.value = checkoutSummary.value.orderSummary.error_message
        isPromoAdded.value = false
      } else if (promoCode.value) {
        promoCodeError.value = ''
        isPromoAdded.value = true
        showPromoInput.value = false
      }
    } else {
      toast.error('Failed to fetch product details')
    }
  } catch (error) {
    console.error('Error fetching product details:', error)
    toast.error('Failed to fetch product details')
  } finally {
    isLoadingProducts.value = false
  }
}

const applyPromoCode = () => {
  promoCodeError.value = ''

  if (!promoCode.value) {
    promoCodeError.value = 'Please enter a promo code'

    return
  }

  checkoutStore.setPromoCode(promoCode.value)
  fetchProductDetails()
}

const removePromoCode = () => {
  promoCode.value = ''
  isPromoAdded.value = false
  checkoutStore.setPromoCode(null)
  fetchProductDetails()
}

const showPromoCodeInput = () => {
  showPromoInput.value = true
}

const cancelPromoCode = () => {
  showPromoInput.value = false
  promoCode.value = ''
  promoCodeError.value = ''
}

watch(promoCode, () => {
  promoCode.value = promoCode.value?.toUpperCase()
})

watch(storeLoading, newValue => {
  isLoading.value = newValue
})

watch(selectedPaymentMethod, newValue => {
  checkoutSession.value.paymentMethod = newValue
})

const isCardExpired = paymentMethod => {
  const now = new Date()
  const expirationDate = new Date(paymentMethod.card_expiry_year, paymentMethod.card_expiry_month - 1)

  return now > expirationDate
}

onMounted(() => {
  checkoutStore.setStep(3)

  if (!checkoutStore.isStepCompleted(2)) {
    router.push({ name: 'otc-checkout-shipping' })

    return
  }

  if (checkoutSession.value.promoCode) {
    promoCode.value = checkoutSession.value.promoCode
    isPromoAdded.value = true
  }

  // Only load PayPal script if we're not authenticated or we don't have payment methods
  if (!isAuthenticated.value || checkoutSession.value.isGuest) {
    loadPayPalScript()
    isCardFormVisible.value = true
  }

  fetchSavedPaymentMethods()
  fetchProductDetails()
})
</script>

<template>
  <div>
    <!-- Product Summary Section -->
    <div class="mb-8">
      <h3 class="text-base font-medium text-gray-900 mb-4">
        Order Summary
      </h3>

      <div
        v-if="isLoadingProducts"
        class="py-4"
      >
        <div class="animate-pulse">
          <div class="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
          <div class="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
          <div class="h-4 bg-gray-200 rounded w-5/6"></div>
        </div>
      </div>

      <div v-else-if="checkoutSummary">
        <!-- Product List -->
        <div class="overflow-hidden">
          <div class="divide-y border-b !border-gray-200">
            <div
              v-for="product in checkoutSummary.products"
              :key="product.id"
              class="p-4"
            >
              <div class="flex items-start gap-4">
                <div class="w-16 h-16 flex-shrink-0">
                  <img
                    :src="product.image"
                    :alt="product.drug_name"
                    class="w-full h-full object-cover rounded"
                  >
                </div>
                <div class="flex-grow">
                  <h4 class="font-medium text-gray-900">
                    {{ product.drug_name }}
                  </h4>
                  <div class="flex justify-between items-center mt-1">
                    <div class="flex items-center">
                      <span class="text-sm text-gray-600">{{ product.qty }} x {{ formatCurrency(product.product_price_after_discount) }}</span>
                      <span
                        v-if="product.promo_code_name"
                        class="bg-green-100 text-xs  px-1.5 pt-[5px] pb-px rounded-full text-green-700 ms-2 inline-flex items-center font-medium"
                      >
                        <IconRosetteDiscount
                          class="size-4 me-1 -mt-[3px]"
                          stroke-width="2"
                        />
                        <span>{{ product.promo_code_name }}</span>
                        <span v-if="product.promocode_type === 'percentage'">
                          &nbsp;({{ product.promocode_value }}%)
                        </span>
                      </span>
                    </div>
                    <div class="text-right">
                      <div class="flex items-center">
                        <span
                          v-if="product.total_price !== product.final_total_price"
                          class="text-sm line-through text-gray-500 me-2"
                        >{{ formatCurrency(product.selling_price * product.qty) }}</span>
                        <span class="text-sm font-medium">{{ formatCurrency(product.final_total_price) }}</span>
                      </div>
                      <!--
                        <div
                        v-if="product.discount_value > 0"
                        class="text-xs text-green-600 font-medium"
                        >
                        Save {{ product.discount_type === 'percentage'
                        ? product.discount_value + '%'
                        : Math.round((product.discount_amount / product.selling_price) * 100) + '%' }}
                        </div>
                      -->
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Order Summary -->
        <div class="overflow-hidden mb-6 mt-2">
          <div class="p-4 space-y-2">
            <!--
              <div class="flex justify-between">
              <span class="text-gray-600">Price ({{ checkoutSummary.products.length }} {{ checkoutSummary.products.length === 1 ? 'item' : 'items' }})</span>
              <span class="font-medium">{{ formatCurrency(checkoutSummary.orderSummary.total_product_price) }}</span>
              </div>
            -->

            <!-- Promo Code Section -->
            <div
              v-if="!isPromoAdded && checkoutSummary.orderSummary.total_product_price !== 0"
              class="py-2"
            >
              <div
                v-if="!showPromoInput"
                class="flex justify-between items-center bg-gray-100 px-4 py-2 rounded-lg"
              >
                <div class="text-sm">
                  Have a Promo code?
                </div>
                <button
                  class="text-[13px] font-medium border-b !border-gray-900 text-gray-900 disabled:text-gray-600"
                  :disabled="isLoading"
                  @click="showPromoCodeInput"
                >
                  Click to Apply
                </button>
              </div>
              <div v-if="showPromoInput">
                <input
                  v-model="promoCode"
                  type="text"
                  placeholder="Enter promo code"
                  class="block w-full px-4 py-2 text-md font-medium appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                  autofocus
                  @keyup.enter="applyPromoCode"
                  @keydown="() => { promoCodeError = '' }"
                >
                <div v-if="promoCodeError">
                  <span class="text-red-500 text-sm ms-2">{{ promoCodeError }}</span>
                </div>
                <div class="mt-3 flex justify-end">
                  <button
                    class="inline-flex items-center justify-center text-black border !border-black hover:bg-gray-100 focus:outline-none font-medium rounded-full text-sm px-4 py-[5px] text-center disabled:bg-zinc-100 disabled:text-gray-600 disabled:cursor-not-allowed me-2"
                    @click="cancelPromoCode"
                  >
                    Discard
                  </button>
                  <TwButton
                    class="!py-1.5"
                    @click="applyPromoCode"
                  >
                    Apply
                  </TwButton>
                </div>
              </div>
            </div>

            <div
              v-if="isPromoAdded && checkoutSummary.orderSummary.promo_code_name"
              class="flex justify-between items-center bg-green-100 px-4 py-2 rounded-lg mb-5"
            >
              <div class="text-sm">
                Promotion applied
              </div>
              <button
                class="text-[13px] font-medium underline text-gray-900 disabled:text-gray-600"
                :disabled="isLoading"
                @click="removePromoCode"
              >
                Remove
              </button>
            </div>

            <!--
              <div
              v-if="isPromoAdded && checkoutSummary.orderSummary.promo_code_name"
              class="flex justify-between py-2"
              >
              <div>
              <div>Promotion applied</div>
              <div class="text-sm font-semibold">
              {{ checkoutSummary.orderSummary.promo_code_name }}
              <span v-if="checkoutSummary.orderSummary.promocode_type === 'percentage'">
              ({{ checkoutSummary.orderSummary.promocode_value }}%)
              </span>
              </div>
              <button
              v-if="isPromoAdded"
              class="underline text-sm font-medium disabled:text-gray-600"
              :disabled="isLoading"
              @click="removePromoCode"
              >
              Remove
              </button>
              </div>
              <div
              v-if="checkoutSummary.orderSummary.promo_code_discount_amount"
              class="font-medium"
              >
              -{{ formatCurrency(checkoutSummary.orderSummary.promo_code_discount_amount) }}
              </div>
              </div>
            -->

            <div class="flex justify-between">
              <span class="text-gray-600">Subtotal</span>
              <span class="font-medium">{{ formatCurrency(checkoutSummary.orderSummary.sub_total) }}</span>
            </div>

            <div class="flex justify-between">
              <span class="text-gray-600">Shipping</span>
              <span class="font-medium">
                <span v-if="checkoutSummary.orderSummary.shipping_cost > 0">
                  {{ formatCurrency(checkoutSummary.orderSummary.shipping_cost) }}
                </span>
                <span
                  v-else
                  class="text-green-600"
                >FREE</span>
              </span>
            </div>

            <div class="pt-3 border-t !border-gray-200">
              <div class="flex justify-between font-medium text-lg">
                <span>Total</span>
                <span>{{ formatCurrency(checkoutSummary.orderSummary.grand_total) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <h3 class="text-base font-medium text-gray-900 mb-4 px-2">
      <span v-if="isAuthenticated && !checkoutSession.isGuest && paymentMethods.length > 0 && !isCardFormVisible">Select from existing payment methods</span>
      <span v-else>Payment Method</span>
    </h3>

    <!-- Saved payment methods section (for logged in users) -->
    <div
      v-if="isAuthenticated && !checkoutSession.isGuest && paymentMethods.length > 0 && !isCardFormVisible"
      class="mb-6"
    >
      <ul class="grid w-full space-y-3 mb-5">
        <li
          v-for="method in paymentMethods"
          :key="method.id"
        >
          <input
            :id="`payment_method_${method.id}`"
            v-model="selectedPaymentMethod"
            type="radio"
            name="payment_method"
            class="hidden peer"
            :value="method.id"
            :disabled="isCardExpired(method)"
            required
            @change="selectExistingPaymentMethod(method.id)"
          />
          <label
            :for="`payment_method_${method.id}`"
            class="inline-flex items-center justify-between w-full px-5 py-3 text-gray-700 bg-white border !border-gray-200 rounded-2xl cursor-pointer peer-checked:!border-gray-600 peer-checked:text-black hover:text-gray-800 hover:bg-gray-100 peer-disabled:bg-gray-200 peer-disabled:cursor-not-allowed"
          >
            <div class="block">
              <div class="w-full">
                <span class="block text-xs uppercase">{{ method.card_brand_type }}</span>
                <span class="block text-base font-medium">xxxx xxxx xxxx {{ method.card_last_4_digit }}</span>
              </div>
              <div class="w-full text-xs">
                <span
                  v-if="isCardExpired(method)"
                  class="bg-red-300 text-red-700 font-medium px-2 py-0.5 rounded-full"
                >Expired</span>
                <span v-else>
                  Expires {{ method.card_expiry_month }}/{{ method.card_expiry_year }}
                </span>
              </div>
            </div>
            <span class="ms-2 flex items-center justify-center">
              <span
                class="w-4 h-4 border-2 rounded-sm flex justify-center items-center"
                :class="[
                  selectedPaymentMethod === method.id ? 'border-transparent bg-black' : 'border-gray-400'
                ]"
              >
                <IconCheck
                  class="h-5 w-5 z-50"
                  :stroke-width="selectedPaymentMethod === method.id ? 4 : 1"
                  :class="[
                    selectedPaymentMethod === method.id ? 'text-white' : 'text-gray-900'
                  ]"
                />
              </span>
            </span>
          </label>
        </li>
      </ul>

      <TwButton
        class="w-full"
        :loading="isLoading"
        :disabled="!selectedPaymentMethod"
        @click="handlePlaceOrder"
      >
        Complete Order
      </TwButton>

      <div class="inline-flex items-center justify-center w-full py-2 my-4">
        <hr class="w-64 h-px bg-gray-200 border-0">
        <span class="absolute px-3 font-medium text-gray-900 -translate-x-1/2 bg-white left-1/2">or</span>
      </div>

      <TwButton
        class="w-full !bg-[#ffef08] border-2 border-solid !border-amber-300 !text-black hover:!brightness-90 transition-all"
        @click="showNewCardForm"
      >
        Add New Card
      </TwButton>
    </div>

    <!-- Card form -->
    <div v-show="isCardFormVisible">
      <div
        v-if="cardFieldErrors.length > 0"
        class="mb-4 p-4 bg-red-50 border border-red-200 rounded-md"
      >
        <h4 class="text-red-700 font-medium mb-2">
          Please correct the following errors:
        </h4>
        <ul class="list-disc ps-5 text-red-600 text-sm">
          <li
            v-for="(error, index) in cardFieldErrors"
            :key="index"
          >
            {{ error }}
          </li>
        </ul>
      </div>

      <div
        v-if="!paypalLoaded && !paypalLoadError"
        class="py-8 text-center"
      >
        <div class="animate-pulse">
          <p class="text-gray-600">
            Loading payment form...
          </p>
        </div>
      </div>

      <div
        v-else-if="paypalLoadError"
        class="py-8 text-center"
      >
        <p class="text-red-600">
          Failed to load payment processor. Please try again.
        </p>
        <button
          class="mt-4 px-4 py-2 bg-gray-600 text-white rounded-md text-sm font-medium hover:bg-gray-700"
          @click="loadPayPalScript"
        >
          Retry
        </button>
      </div>

      <div v-show="paypalLoaded">
        <div class="mb-6">
          <div
            id="card-number"
            class="w-full"
          ></div>
          <div
            id="expiration-date"
            class="w-full"
          ></div>
          <div
            id="cvv"
            class="w-full"
          ></div>
        </div>

        <div class="mb-6 flex items-center text-sm text-gray-500">
          <IconShieldLock class="w-4 h-4 me-2 text-gray-400" />
          <span>Your payment information is secure and encrypted</span>
        </div>

        <TwButton
          id="checkoutSubmitBtn"
          class="w-full"
          :loading="isLoading"
        >
          Complete Order
        </TwButton>

        <div
          v-if="paymentMethods.length > 0"
          class="text-center mt-5"
        >
          <button
            class="text-sm font-medium border-b !border-gray-900 text-gray-900 inline-block"
            @click="selectExistingPaymentMethod(paymentMethods[0].id)"
          >
            Select existing payment method
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
