<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/store/auth'
import { useCheckoutStore } from '@/store/checkoutStore'
import { storeToRefs } from 'pinia'
import { useForm, Field, ErrorMessage } from 'vee-validate'
import * as yup from 'yup'
import { toast } from 'vue-sonner'
import ApiService from '@/services/ApiService'
import { processErrors } from '@/utils/errorHandler'
import { vMaska } from 'maska'
import useMapsApi from '@/composables/useMapsApi'
import AutoComplete from 'primevue/autocomplete'

const router = useRouter()
const checkoutStore = useCheckoutStore()
const authStore = useAuthStore()
const { checkoutSession } = storeToRefs(checkoutStore)
const { isAuthenticated } = storeToRefs(authStore)
const { searchPlaces, autoCompleteOptions } = useMapsApi()

const isLoading = ref(false)
const showAddressForm = ref(!isAuthenticated.value)
const addressLoading = ref(isAuthenticated.value)
const states = ref([])
const validationErrors = ref({})
const serverErrors = ref([])
const serverShippingAddress = ref({})

// Local form state for all users (guest and authenticated)
const localShippingAddress = ref({
  firstName: '',
  lastName: '',
  phone: '',
  address1: '',
  address2: '',
  city: '',
  state: '',
  zipCode: '',
  country: 'USA',
})

const statesList = computed(() => states.value.filter(item => item.type === 'state'))
const territoriesList = computed(() => states.value.filter(item => item.type === 'territory'))

const shippingSchema = computed(() => {
  // Base schema for all users
  const baseSchema = {
    address1: yup.string().required('Address is required'),
    address2: yup.string(),
    city: yup.string().required('City is required'),
    state: yup.string().required('State is required'),
    zipCode: yup.string().required('ZIP code is required').matches(/^\d{5}(-\d{4})?$/, 'Invalid ZIP code format'),
    country: yup.string().required('Country is required'),
  }

  // For guest users, add firstName, lastName, and phone validation
  if (!isAuthenticated.value || checkoutSession.value.isGuest) {
    return yup.object({
      firstName: yup.string().required('First name is required'),
      lastName: yup.string().required('Last name is required'),
      phone: yup.string().required('Phone number is required'),
      ...baseSchema,
    })
  }

  return yup.object(baseSchema)
})

const { handleSubmit } = useForm({
  validationSchema: shippingSchema,
  initialValues: {
    firstName: localShippingAddress.value.firstName,
    lastName: localShippingAddress.value.lastName,
    phone: localShippingAddress.value.phone,
    address1: localShippingAddress.value.address1,
    address2: localShippingAddress.value.address2,
    city: localShippingAddress.value.city,
    state: localShippingAddress.value.state,
    zipCode: localShippingAddress.value.zipCode,
    country: localShippingAddress.value.country,
  },
})

// Function to handle form submission when form is visible
const submitShippingInfo = handleSubmit(async values => {
  try {
    isLoading.value = true
    validationErrors.value = {}
    serverErrors.value = []

    // Update local shipping address with form values for all users
    localShippingAddress.value = {
      firstName: values.firstName,
      lastName: values.lastName,
      phone: values.phone,
      address1: values.address1,
      address2: values.address2,
      city: values.city,
      state: values.state,
      zipCode: values.zipCode,
      country: values.country,
    }

    // For guest users, validate shipping details with the backend
    if (checkoutSession.value.isGuest) {
      try {
        const phoneNumber = values.phone.replace(/\D/g, '')

        const { data } = await ApiService.post('/validate-guest-checkout-details', {
          first_name: values.firstName,
          last_name: values.lastName,
          email: checkoutSession.value.email,
          phone_number: phoneNumber,
          address_line_1: values.address1,
          address_line_2: values.address2 || '',
          city: values.city,
          state: values.state,
          zipcode: values.zipCode,
          country: values.country,
        })

        if (data.status === 200) {
          // Validation successful - now update the checkout session
          checkoutSession.value.shippingAddress = { ...localShippingAddress.value }

          checkoutStore.setStepCompleted(2)
          router.push({ name: 'otc-checkout-payment' })
        } else {
          if (data.errors) {
            validationErrors.value = data.errors
            toast.error('Please check your shipping information')
          } else {
            serverErrors.value = processErrors(data)
            toast.error(serverErrors.value[0] || 'Failed to validate shipping information')
          }
        }
      } catch (error) {
        console.error(error)
        serverErrors.value = processErrors(error)
        toast.error(serverErrors.value[0] || 'Failed to validate shipping information')
      }
    }

    // If user is logged in and not in guest mode, save address to their account
    else if (isAuthenticated.value && !checkoutSession.value.isGuest) {
      try {
        // For authenticated users, update the address on the server
        const { data } = await ApiService.post('/update-shipping-address', {
          address_line_1: values.address1,
          address_line_2: values.address2,
          city: values.city,
          state: values.state,
          zipcode: values.zipCode,
          country: values.country,
        })

        if (data.status === 200) {
          serverShippingAddress.value = { ...localShippingAddress.value }
          checkoutStore.setStepCompleted(2)
          router.push({ name: 'otc-checkout-payment' })
        } else {
          if (data.errors) {
            validationErrors.value = data.errors
          }
          serverErrors.value = processErrors(data)
          toast.error(serverErrors.value[0] || 'Failed to save address')
        }
      } catch (error) {
        console.error(error)
        serverErrors.value = processErrors(error)
        toast.error(serverErrors.value[0] || 'Failed to save address')
      }
    }
  } catch (error) {
    console.error(error)
    toast.error('Failed to process shipping information')
  } finally {
    isLoading.value = false
  }
})

const continueToPayment = async () => {
  try {
    isLoading.value = true

    // For authenticated users, we already have the address saved on the server
    // Just mark the step as completed and proceed to payment
    if (isAuthenticated.value && !checkoutSession.value.isGuest) {
      checkoutStore.setStepCompleted(2)
      router.push({ name: 'otc-checkout-payment' })
    }

    // For guest users, update the checkout session with the current address
    else if (checkoutSession.value.isGuest) {
      checkoutSession.value.shippingAddress = { ...localShippingAddress.value }
      checkoutStore.setStepCompleted(2)
      router.push({ name: 'otc-checkout-payment' })
    }
  } catch (error) {
    console.error(error)
    toast.error('Failed to proceed to payment')
  } finally {
    isLoading.value = false
  }
}

const showNewAddressForm = () => {
  // For authenticated users, populate local state with server address
  if (isAuthenticated.value && !checkoutSession.value.isGuest) {
    localShippingAddress.value = { ...serverShippingAddress.value }
  }

  showAddressForm.value = true
}

const handleAddressSearch = () => {
  if (localShippingAddress.value.address1) {
    searchPlaces(localShippingAddress.value.address1)
  }
}

const handleAddressSelectedItem = event => {
  const item = event.value

  localShippingAddress.value.address1 = item.streetAddress
  localShippingAddress.value.city = item.city
  localShippingAddress.value.state = item.state
  localShippingAddress.value.zipCode = item.postalCode
}

const fetchSavedAddresses = async () => {
  // Initialize local shipping address from checkout session for guest users
  if (!isAuthenticated.value || checkoutSession.value.isGuest) {
    if (checkoutSession.value.shippingAddress) {
      localShippingAddress.value = {
        firstName: checkoutSession.value.shippingAddress.firstName || '',
        lastName: checkoutSession.value.shippingAddress.lastName || '',
        phone: checkoutSession.value.shippingAddress.phone || '',
        address1: checkoutSession.value.shippingAddress.address1 || '',
        address2: checkoutSession.value.shippingAddress.address2 || '',
        city: checkoutSession.value.shippingAddress.city || '',
        state: checkoutSession.value.shippingAddress.state || '',
        zipCode: checkoutSession.value.shippingAddress.zipCode || '',
        country: checkoutSession.value.shippingAddress.country || 'USA',
      }
    }

    showAddressForm.value = true
    addressLoading.value = false

    return
  }

  try {
    addressLoading.value = true

    const { data } = await ApiService.get('/shipping-address-info')

    if (data.status === 200 && data.shippingAddressInfo) {
      const address = data.shippingAddressInfo

      localShippingAddress.value = {
        address1: address.address_line_1 || '',
        address2: address.address_line_2 || '',
        city: address.city || '',
        state: address.state || '',
        zipCode: address.zipcode || '',
        country: address.country || 'USA',
      }
      serverShippingAddress.value = { ...localShippingAddress.value }
      showAddressForm.value = false
    } else {
      showAddressForm.value = true
    }
  } catch (error) {
    console.error(error)
    toast.error('Failed to load saved address')
    showAddressForm.value = true
  } finally {
    addressLoading.value = false
  }
}

async function fetchStates() {
  try {
    const { data } = await ApiService.get('/state-list')

    if (data.status === 200) {
      states.value = data.stateData ?? []
    } else {
      console.error(data)
    }
  } catch (error) {
    console.error(error)
  }
}

onMounted(async () => {
  checkoutStore.setStep(2)

  if (!checkoutStore.isStepCompleted(1)) {
    router.push({ name: 'otc-checkout-account' })

    return
  }

  fetchStates()

  // For authenticated users who are not in guest mode
  if (isAuthenticated.value && !checkoutSession.value.isGuest) {
    await fetchSavedAddresses()
  } else {
    addressLoading.value = false
    showAddressForm.value = true

    if (checkoutSession.value.shippingAddress) {
      localShippingAddress.value = {
        firstName: checkoutSession.value.shippingAddress.firstName || '',
        lastName: checkoutSession.value.shippingAddress.lastName || '',
        phone: checkoutSession.value.shippingAddress.phone || '',
        address1: checkoutSession.value.shippingAddress.address1 || '',
        address2: checkoutSession.value.shippingAddress.address2 || '',
        city: checkoutSession.value.shippingAddress.city || '',
        state: checkoutSession.value.shippingAddress.state || '',
        zipCode: checkoutSession.value.shippingAddress.zipCode || '',
        country: checkoutSession.value.shippingAddress.country || 'USA',
      }
    }
  }
})
</script>

<template>
  <div>
    <!-- Loading state for authenticated users -->
    <div
      v-if="addressLoading"
      class="animate-pulse"
    >
      <h3 class="text-lg font-medium text-gray-900 mb-4">
        Shipping Address
      </h3>
      <div class="border !border-gray-200 rounded-lg p-4 h-32 bg-gray-100"></div>
    </div>

    <!-- Shipping address form -->
    <div v-else-if="showAddressForm || checkoutSession.isGuest">
      <h3 class="text-lg font-medium text-gray-900 mb-4">
        Shipping Address
      </h3>

      <form @submit.prevent="submitShippingInfo">
        <!-- Server errors display -->
        <div
          v-if="serverErrors.length > 0"
          class="mb-4 p-3 bg-red-50 border !border-red-200 rounded-md"
        >
          <p class="text-red-600 font-medium">
            Please correct the following errors:
          </p>
          <ul class="mt-1 list-disc list-inside text-sm text-red-600">
            <li
              v-for="(error, index) in serverErrors"
              :key="index"
            >
              {{ error }}
            </li>
          </ul>
        </div>

        <div class="space-y-5">
          <!-- First Name and Last Name - Only for guest users -->
          <div
            v-if="!isAuthenticated || checkoutSession.isGuest"
            class="grid grid-cols-1 md:grid-cols-2 gap-4"
          >
            <div>
              <div class="relative">
                <Field
                  id="firstName"
                  v-model="localShippingAddress.firstName"
                  name="firstName"
                  type="text"
                  class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                  placeholder=" "
                  autocomplete="off"
                  required
                />
                <label
                  for="firstName"
                  class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4 cursor-text"
                >First Name</label>
              </div>
              <ErrorMessage
                name="firstName"
                class="text-red-500 text-sm ms-5"
              />
              <p
                v-if="validationErrors.first_name && validationErrors.first_name.length > 0"
                class="text-red-500 text-sm ms-5"
              >
                {{ validationErrors.first_name[0] }}
              </p>
            </div>

            <div>
              <div class="relative">
                <Field
                  id="lastName"
                  v-model="localShippingAddress.lastName"
                  name="lastName"
                  type="text"
                  class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                  placeholder=" "
                  autocomplete="off"
                  required
                />
                <label
                  for="lastName"
                  class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4 cursor-text"
                >Last Name</label>
              </div>
              <ErrorMessage
                name="lastName"
                class="text-red-500 text-sm ms-5"
              />
              <p
                v-if="validationErrors.last_name && validationErrors.last_name.length > 0"
                class="text-red-500 text-sm ms-5"
              >
                {{ validationErrors.last_name[0] }}
              </p>
            </div>
          </div>

          <!-- Phone Number - Only for guest users -->
          <div v-if="!isAuthenticated || checkoutSession.isGuest">
            <div class="relative">
              <Field
                id="phone"
                v-model="localShippingAddress.phone"
                v-maska
                name="phone"
                type="tel"
                class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                placeholder=" "
                autocomplete="off"
                data-maska="(###) ###-####"
                required
              />
              <label
                for="phone"
                class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4 cursor-text"
              >Phone Number</label>
            </div>
            <ErrorMessage
              name="phone"
              class="text-red-500 text-sm ms-5"
            />
            <p
              v-if="validationErrors.phone_number && validationErrors.phone_number.length > 0"
              class="text-red-500 text-sm ms-5"
            >
              {{ validationErrors.phone_number[0] }}
            </p>
          </div>

          <!-- Address Line 1 with AutoComplete -->
          <div>
            <div class="relative">
              <AutoComplete
                id="address1"
                v-model="localShippingAddress.address1"
                option-label="streetAddress"
                placeholder="Address Line 1"
                class="w-full"
                input-class="block w-full px-5 py-3.5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:!outline-none focus:!ring-1 focus:!ring-black peer"
                :suggestions="autoCompleteOptions"
                :delay="500"
                @complete="handleAddressSearch"
                @item-select="handleAddressSelectedItem"
              >
                <template #option="slotProps">
                  <div>
                    <p class="mb-0 text-sm font-medium">
                      {{ slotProps.option.displayName }}
                    </p>
                    <small>{{ slotProps.option.formattedAddress }}</small>
                  </div>
                </template>
              </AutoComplete>
              <!-- Hidden field for validation -->
              <Field
                v-model="localShippingAddress.address1"
                type="hidden"
                name="address1"
                required
              />
            </div>
            <ErrorMessage
              name="address1"
              class="text-red-500 text-sm ms-5"
            />
            <p
              v-if="validationErrors.address_line_1 && validationErrors.address_line_1.length > 0"
              class="text-red-500 text-sm ms-5"
            >
              {{ validationErrors.address_line_1[0] }}
            </p>
          </div>

          <!-- Address Line 2 -->
          <div>
            <div class="relative">
              <Field
                id="address2"
                v-model="localShippingAddress.address2"
                name="address2"
                type="text"
                class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                placeholder=" "
                autocomplete="off"
              />
              <label
                for="address2"
                class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4 cursor-text"
              >Apt / Suite (optional)</label>
            </div>
            <ErrorMessage
              name="address2"
              class="text-red-500 text-sm ms-5"
            />
            <p
              v-if="validationErrors.address_line_2 && validationErrors.address_line_2.length > 0"
              class="text-red-500 text-sm ms-5"
            >
              {{ validationErrors.address_line_2[0] }}
            </p>
          </div>

          <!-- City, State, ZIP -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- City -->
            <div>
              <div class="relative">
                <Field
                  id="city"
                  v-model="localShippingAddress.city"
                  name="city"
                  type="text"
                  class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                  placeholder=" "
                  autocomplete="off"
                  required
                />
                <label
                  for="city"
                  class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4 cursor-text"
                >City</label>
              </div>
              <ErrorMessage
                name="city"
                class="text-red-500 text-sm ms-5"
              />
              <p
                v-if="validationErrors.city && validationErrors.city.length > 0"
                class="text-red-500 text-sm ms-5"
              >
                {{ validationErrors.city[0] }}
              </p>
            </div>

            <!-- State -->
            <div class="relative">
              <Field
                id="state"
                v-model="localShippingAddress.state"
                as="select"
                name="state"
                class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                required
              >
                <option
                  value=""
                  disabled
                  selected
                >
                  Select
                </option>
                <optgroup label="States">
                  <option
                    v-for="state in statesList"
                    :key="state.code"
                    :value="state.code"
                  >
                    {{ state.name }}
                  </option>
                </optgroup>
                <optgroup label="Territories">
                  <option
                    v-for="territory in territoriesList"
                    :key="territory.code"
                    :value="territory.code"
                  >
                    {{ territory.name }}
                  </option>
                </optgroup>
              </Field>
              <label
                for="state"
                class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-focus:scale-75 peer-focus:-translate-y-4"
              >State</label>
              <ErrorMessage
                name="state"
                class="text-red-500 text-sm ms-5"
              />
              <p
                v-if="validationErrors.state && validationErrors.state.length > 0"
                class="text-red-500 text-sm ms-5"
              >
                {{ validationErrors.state[0] }}
              </p>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- ZIP Code -->
            <div>
              <div class="relative">
                <Field
                  id="zipCode"
                  v-model="localShippingAddress.zipCode"
                  v-maska
                  name="zipCode"
                  type="text"
                  class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                  placeholder=" "
                  autocomplete="off"
                  data-maska="#####-####"
                  required
                />
                <label
                  for="zipCode"
                  class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4 cursor-text"
                >ZIP Code</label>
              </div>
              <ErrorMessage
                name="zipCode"
                class="text-red-500 text-sm ms-5"
              />
              <p
                v-if="validationErrors.zipcode && validationErrors.zipcode.length > 0"
                class="text-red-500 text-sm ms-5"
              >
                {{ validationErrors.zipcode[0] }}
              </p>
            </div>

            <!-- Country -->
            <div class="relative">
              <Field
                id="country"
                v-model="localShippingAddress.country"
                as="select"
                name="country"
                class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                required
              >
                <option value="USA">
                  United States
                </option>
              </Field>
              <label
                for="country"
                class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-focus:scale-75 peer-focus:-translate-y-4"
              >Country</label>
              <ErrorMessage
                name="country"
                class="text-red-500 text-sm ms-5"
              />
              <p
                v-if="validationErrors.country && validationErrors.country.length > 0"
                class="text-red-500 text-sm ms-5"
              >
                {{ validationErrors.country[0] }}
              </p>
            </div>
          </div>
        </div>

        <div class="mt-8">
          <TwButton
            type="submit"
            class="w-full"
            :loading="isLoading"
          >
            Continue to Payment
          </TwButton>
        </div>
      </form>
    </div>

    <!-- Selected address display -->
    <div v-else-if="!showAddressForm">
      <div class="mb-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">
          Shipping Address
        </h3>

        <!-- Address card with edit button -->
        <div class="border !border-gray-200 rounded-lg p-4 relative">
          <div class="text-gray-600">
            <!-- For authenticated users, don't show first name, last name, and phone -->
            <p
              v-if="!isAuthenticated || checkoutSession.isGuest"
              class="font-medium"
            >
              {{ localShippingAddress.firstName }} {{ localShippingAddress.lastName }}
            </p>

            <!-- For authenticated users, use server shipping address -->
            <template v-if="isAuthenticated && !checkoutSession.isGuest">
              <p>
                {{ serverShippingAddress.address1 }}
                <span v-if="serverShippingAddress.address2"><br>{{ serverShippingAddress.address2 }}</span>
              </p>
              <p>
                {{ serverShippingAddress.city }}, {{ serverShippingAddress.state }} {{ serverShippingAddress.zipCode }}
              </p>
              <p>
                {{ serverShippingAddress.country }}
              </p>
            </template>

            <!-- For guest users, use local shipping address -->
            <template v-else>
              <p>
                {{ localShippingAddress.address1 }}
                <span v-if="localShippingAddress.address2"><br>{{ localShippingAddress.address2 }}</span>
              </p>
              <p>
                {{ localShippingAddress.city }}, {{ localShippingAddress.state }} {{ localShippingAddress.zipCode }}
              </p>
              <p>
                {{ localShippingAddress.country }}
              </p>
              <p v-if="localShippingAddress.phone">
                {{ localShippingAddress.phone }}
              </p>
            </template>
          </div>

          <!-- Edit button -->
          <button
            type="button"
            class="absolute top-4 right-4 font-medium text-gray-700 underline hover:text-gray-900 p-1 rounded-full hover:bg-gray-100"
            @click="showNewAddressForm"
          >
            Edit
          </button>
        </div>
      </div>

      <div class="mt-8">
        <TwButton
          class="w-full"
          :loading="isLoading"
          @click="continueToPayment"
        >
          Continue to Payment
        </TwButton>
      </div>
    </div>
  </div>
</template>

<style lang="scss">
.p-inputtext {
  border: none !important;
  border-color: transparent;
  font-family: "Konnect", system-ui, -apple-system, BlinkMacSystemFont,
    "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue",
    sans-serif;
  font-size: 15px !important;
  font-weight: 500;
  color: black;

  &:enabled:hover {
    border-color: transparent;
  }
  &:enabled:focus {
    outline: none;
    outline-offset: 0;
    box-shadow: none;
    border-color: transparent;
  }
}

.p-autocomplete-panel {
  max-width: 400px;
  z-index: 2102 !important;

  .p-autocomplete-items .p-autocomplete-item {
    white-space: pre-line;
  }
}
</style>
