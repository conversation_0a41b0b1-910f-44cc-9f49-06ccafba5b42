<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/store/auth'
import { useCheckoutStore } from '@/store/checkoutStore'
import { storeToRefs } from 'pinia'
import { isEmptyObject, isNullOrUndefined } from '@/@core/utils'
import useCaptcha from '@/composables/useCaptcha'
import { removeKeyFromObject } from '@/utils/helpers'
import AlertError from '@/views/user/components/AlertError.vue'
import { IconCheck, IconEye, IconEyeOff } from '@tabler/icons-vue'
import useGuestCheckoutSettings from '@/composables/useGuestCheckoutSettings'

import { Form as VForm, Field, ErrorMessage } from 'vee-validate'
import * as yup from 'yup'
import ApiService from '@/services/ApiService'
import { processErrors } from '@/utils/errorHandler'
import { useSessionStorage } from '@vueuse/core'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()
const checkoutStore = useCheckoutStore()
const { getRecaptchaToken } = useCaptcha()
const { isGuestCheckoutEnabled, fetchGuestCheckoutSettings } = useGuestCheckoutSettings()

const { isAuthenticated, userData } = storeToRefs(authStore)
const { checkoutSession } = storeToRefs(checkoutStore)
const onboardingSession = useSessionStorage('onboardingSession', {})

const termsConditionsUrl = computed(() => import.meta.env.VITE_TERMS_CONDITIONS_URL)
const privacyPolicyUrl = computed(() => import.meta.env.VITE_PRIVACY_POLICY_URL)

const showSignupForm = ref(false)
const isLoading = ref(false)
const skeletonLoading = ref(true)
const guestEmail = ref('')
const inputErrors = ref({})
const serverErrors = ref([])
const showMoreFields = ref(false)
const password = ref('')
const isPasswordVisible = ref(false)
const passwordRef = ref(null)

const guestSchema = yup.object({
  email: yup.string().required('Email is required').email('Please enter a valid email'),
})

const emailSchema = yup.object().shape({
  email: yup.string()
    .required('Email address is required')
    .matches(/^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,10}$/, 'Invalid email address'),
})

const signupSchema = yup.object().shape({
  email: yup.string()
    .required('Email address is required')
    .matches(/^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,10}$/, 'Invalid email address'),
  password: yup.string()
    .required('Password is required')
    .min(8, 'Password must be at least 8 characters')
    .matches(/(?=.*[A-Z0-9!@#$%^&*])/, 'Include at least one uppercase letter or number or symbol'),
  user_agreement: yup.string().required('Please read and agree to the terms and conditions'),
})

const validationSchema = computed(() => {
  if (showMoreFields.value) {
    return signupSchema
  } else {
    return emailSchema
  }
})

const handleSubmit = async values => {
  if (showMoreFields.value) {
    signup(values)
  } else {
    authenticateEmail(values)
  }
}

async function authenticateEmail(values) {
  try {
    isLoading.value = true
    inputErrors.value = {}
    serverErrors.value = []

    const captchaToken = await getRecaptchaToken('authenticateEmail')

    const postData = {
      ...values,
      'g-recaptcha-response': captchaToken,
    }

    const { data } = await ApiService.post('/email-authentication', postData)

    if (data.status === 200) {
      // user exists with this email
      router.push({
        name: 'user-login',
        query: {
          login_hint: values.email,
          redirect_to: route.fullPath,
        },
      })
    } else if (data.status === 400) {
      // user does not exist with this email
      showMoreFields.value = true
      setTimeout(() => {
        passwordRef.value?.focus()
      }, 100)
    } else {
      if (data.errors) {
        inputErrors.value = data.errors
      }
    }
  } catch (error) {
    console.error(error)
    serverErrors.value = processErrors(error)
  } finally {
    isLoading.value = false
  }
}

async function signup(values) {
  try {
    isLoading.value = true
    inputErrors.value = {}
    serverErrors.value = []

    const captchaToken = await getRecaptchaToken('signup')

    const postData = {
      ...values,
      'beluga_agreement': 1,
      'g-recaptcha-response': captchaToken,
    }

    const { data } = await ApiService.post('/registration', postData)

    if (data.status === 200) {
      checkoutSession.value.isGuest = false
      checkoutSession.value.email = values.email
      onboardingSession.value['userId'] = data.user_id

      router.push({
        name: 'user-signup-profile',
        params: { visitType: 'otc' },
        query: { redirect_to: route.fullPath },
      })

      // Mark step as completed and navigate to next step
      // checkoutStore.setStepCompleted(1)
      // router.push({ name: 'otc-checkout-shipping' })
    } else {
      if (data.is_user_registered && data.is_user_registered === 1) {
        router.push({
          name: 'user-login',
          query: {
            login_hint: values.email,
            redirect_to: route.fullPath,
          },
        })
      } else {
        isLoading.value = false
        if (data.errors) {
          inputErrors.value = data.errors
        }
      }
    }
  } catch (error) {
    isLoading.value = false
    serverErrors.value = processErrors(error)
    console.error(error)
  }
}

function removeKeyFromInputErrors(key) {
  inputErrors.value = removeKeyFromObject(inputErrors.value, key)
}

const showSignupFormToggle = () => {
  showSignupForm.value = true
}

const showGuestCheckout = () => {
  // Only allow switching to guest checkout if it's enabled
  if (isGuestCheckoutEnabled.value) {
    showSignupForm.value = false
    showMoreFields.value = false
  }
}

const submitGuestCheckout = async values => {
  try {
    isLoading.value = true

    // Check if guest checkout is enabled
    if (!isGuestCheckoutEnabled.value) {
      serverErrors.value = ['Guest checkout is currently disabled. Please create an account or log in.']
      showSignupForm.value = true

      return
    }

    const captchaToken = await getRecaptchaToken('authenticateEmail')

    const postData = {
      ...values,
      'g-recaptcha-response': captchaToken,
    }

    const { data } = await ApiService.post('/email-authentication', postData)

    if (data.status === 200) {
      // User exists with this email
      router.push({
        name: 'user-login',
        query: {
          login_hint: values.email,
          redirect_to: route.fullPath,
        },
      })
    } else {
      checkoutSession.value.email = values.email
      checkoutSession.value.isGuest = true
      guestEmail.value = values.email

      checkoutStore.setStepCompleted(1)
      router.push({ name: 'otc-checkout-shipping' })
    }
  } catch (error) {
    console.error('Error in guest checkout:', error)
    serverErrors.value = ['An error occurred during checkout. Please try again.']
  } finally {
    isLoading.value = false
  }
}

onMounted(async () => {
  try {
    // Ensure the current step is set correctly
    checkoutStore.setStep(1)

    // Fetch guest checkout settings
    await fetchGuestCheckoutSettings()

    if (isAuthenticated.value && userData.value) {
      checkoutSession.value.email = userData.value.email
      checkoutSession.value.isGuest = false

      // Mark step as completed and navigate to next step
      checkoutStore.setStepCompleted(1)
      router.push({ name: 'otc-checkout-shipping' })
    } else {
      if (checkoutSession.value.email) {
        guestEmail.value = checkoutSession.value.email
      }

      // If guest checkout is disabled, show signup form by default
      if (!isGuestCheckoutEnabled.value) {
        showSignupForm.value = true
      }
    }
  } catch (error) {
    console.error('Error loading account step:', error)
  } finally {
    // Set loading to false after all data is loaded
    setTimeout(() => {
      skeletonLoading.value = false
    }, 300) // Small delay to ensure smooth transition
  }
})
</script>

<template>
  <div>
    <!-- Skeleton Loader -->
    <div
      v-if="skeletonLoading"
      class="animate-pulse"
    >
      <!-- Skeleton for Guest Checkout or Create Account -->
      <div class="mb-6">
        <div class="h-7 bg-gray-300 rounded w-48 mb-2"></div>
        <div class="h-5 bg-gray-300 rounded w-full max-w-md"></div>
      </div>

      <!-- Skeleton for email input -->
      <div class="mt-8">
        <div class="h-12 bg-gray-300 rounded-md"></div>
      </div>

      <!-- Skeleton for button -->
      <div class="mt-8">
        <div class="w-full h-10 bg-gray-300 rounded-full"></div>
      </div>

      <!-- Skeleton for links -->
      <div class="mt-4 flex justify-center gap-4">
        <div class="h-5 bg-gray-300 rounded w-16"></div>
        <div class="h-5 bg-gray-300 rounded w-32"></div>
      </div>
    </div>

    <!-- Already logged in -->
    <div
      v-else-if="isAuthenticated"
      class="text-center py-8"
    >
      <div class="animate-pulse">
        <p class="text-lg font-medium text-gray-900 mb-2">
          You are logged in as {{ userData.email }}
        </p>
        <p class="text-gray-600">
          Proceeding to shipping information...
        </p>
      </div>
    </div>

    <!-- Guest checkout form -->
    <div v-else-if="!showSignupForm && isGuestCheckoutEnabled">
      <div class="mb-6">
        <h3 class="text-lg font-medium text-gray-900 mb-2">
          Guest Checkout
        </h3>
        <p class="text-gray-600">
          Continue as a guest or create an account for a faster checkout experience.
        </p>
      </div>

      <AlertError
        v-if="isEmptyObject(inputErrors) && serverErrors.length > 0"
        title="Error!"
        :errors="serverErrors"
      />

      <VForm
        class="mt-8"
        autocomplete="off"
        :validation-schema="guestSchema"
        @submit="submitGuestCheckout"
      >
        <div class="space-y-5">
          <div class="relative">
            <Field
              id="email"
              v-model="guestEmail"
              name="email"
              type="email"
              class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
              placeholder=" "
              autocomplete="off"
              required
            />
            <label
              for="email"
              class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4 cursor-text"
            >Email Address</label>
          </div>
          <ErrorMessage
            name="email"
            class="text-red-500 text-sm ms-5"
          />
        </div>

        <div class="mt-8">
          <TwButton
            type="submit"
            class="w-full"
            :loading="isLoading"
          >
            Continue as Guest
          </TwButton>
        </div>
      </VForm>

      <div class="mt-4 flex justify-center gap-4">
        <router-link
          :to="{ name: 'user-login', query: { redirect_to: route.fullPath } }"
          class="text-gray-900 hover:underline text-sm font-medium"
        >
          Login
        </router-link>
        |
        <button
          type="button"
          class="text-gray-900 hover:underline text-sm font-medium"
          @click="showSignupFormToggle"
        >
          Create Account
        </button>
      </div>
    </div>

    <!-- Guest checkout disabled message -->
    <div v-else-if="!showSignupForm && !isGuestCheckoutEnabled">
      <div class="mb-6">
        <h3 class="text-lg font-medium text-gray-900 mb-2">
          Account Required
        </h3>
        <p class="text-gray-600">
          An account is required to complete your purchase. Please create an account or log in to continue.
        </p>
      </div>

      <div class="mt-8 flex flex-col gap-4">
        <TwButton
          class="w-full"
          @click="showSignupFormToggle"
        >
          Create Account
        </TwButton>

        <router-link
          :to="{ name: 'user-login', query: { redirect_to: route.fullPath } }"
          class="w-full"
        >
          <TwButton
            type="button"
            class="w-full"
            variant="outline"
          >
            Login
          </TwButton>
        </router-link>
      </div>
    </div>

    <!-- Signup form similar to SignupOverlay.vue -->
    <div v-else>
      <div class="mb-6">
        <h3 class="text-lg font-medium text-gray-900 mb-2">
          Create Account
        </h3>
        <p class="text-gray-600">
          Create an account for a faster checkout experience.
        </p>
      </div>

      <AlertError
        v-if="isEmptyObject(inputErrors) && serverErrors.length > 0"
        title="Error!"
        :errors="serverErrors"
      />

      <VForm
        class="mt-8"
        autocomplete="off"
        :validation-schema="validationSchema"
        @submit="handleSubmit"
      >
        <div class="space-y-5">
          <div class="relative mb-4">
            <Field
              id="signup-email"
              type="email"
              name="email"
              class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
              placeholder=" "
              autocomplete="off"
              validate-on-input
              autofocus
              required
              @keydown="() => removeKeyFromInputErrors('email')"
            />
            <label
              for="signup-email"
              class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4 cursor-text"
            >Email address</label>
            <ErrorMessage
              name="email"
              class="text-red-500 text-sm ms-5 inline-block"
            />
            <p
              v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.email)"
              class="text-red-500 text-sm ms-5"
            >
              {{ inputErrors.email[0] }}
            </p>
          </div>

          <div v-if="showMoreFields">
            <div class="mb-4">
              <div class="relative">
                <Field
                  v-slot="{ field }"
                  name="password"
                >
                  <input
                    v-bind="field"
                    id="password"
                    ref="passwordRef"
                    v-model="password"
                    :type="isPasswordVisible ? 'text' : 'password'"
                    class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                    placeholder=" "
                    autocomplete="new-password"
                    autofocus
                    required
                  >
                </Field>
                <label
                  for="password"
                  class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4"
                >Password</label>
                <div class="absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                  <IconEye
                    v-if="!isPasswordVisible"
                    class="h-5 w-5"
                    stroke-width="2"
                    @click="isPasswordVisible = !isPasswordVisible"
                  />
                  <IconEyeOff
                    v-else
                    class="h-5 w-5"
                    stroke-width="2"
                    @click="isPasswordVisible = !isPasswordVisible"
                  />
                </div>
              </div>
              <ErrorMessage
                name="password"
                class="text-red-500 text-sm ms-5 inline-block"
              />
              <p
                v-if="!isEmptyObject(inputErrors) && !isNullOrUndefined(inputErrors.password)"
                class="text-red-500 text-sm ms-5"
              >
                {{ inputErrors.password[0] }}
              </p>
            </div>

            <div class="mb-6">
              <p class="flex items-center gap-3 mb-2 text-sm">
                <IconCheck
                  class="h-5 w-5 text-gray-300"
                  :class="{ 'text-green-300': password.length >= 8 }"
                  stroke-width="2"
                />
                <span>Password must be at least 8 characters long</span>
              </p>
              <p class="flex items-center gap-3 text-sm">
                <IconCheck
                  class="h-5 w-5 text-gray-300"
                  :class="{ 'text-green-300': password.match(/(?=.*[A-Z0-9!@#$%^&*])/) }"
                  stroke-width="2"
                />
                <span>Include at least one uppercase letter or number or symbol</span>
              </p>
            </div>
            <div class="form-input mb-1">
              <div class="flex">
                <Field
                  id="agree-label"
                  type="checkbox"
                  name="user_agreement"
                  class="w-4 h-4 mt-[2px] text-black bg-gray-100 border-gray-300 accent-black rounded-sm"
                  value="1"
                  checked
                  required
                />
                <label
                  for="agree-label"
                  class="ms-2 text-sm font-normal text-gray-500"
                >
                  I agree to <a
                    :href="termsConditionsUrl"
                    class="text-gray-600 font-semibold"
                    target="_blank"
                    rel="noopener noreferrer"
                  >Terms of Use</a> and <a
                    :href="privacyPolicyUrl"
                    class="text-gray-600 font-bold"
                    target="_blank"
                    rel="noopener noreferrer"
                  >Privacy Policy</a>.
                </label>
              </div>
              <ErrorMessage
                name="user_agreement"
                class="text-red-500 text-sm ms-5"
              />
            </div>
          </div>

          <div class="mt-8">
            <TwButton
              type="submit"
              class="w-full"
              :loading="isLoading"
            >
              {{ showMoreFields ? 'Create Account' : 'Continue' }}
            </TwButton>
          </div>
        </div>
      </VForm>

      <div class="mt-4 flex flex-col items-center gap-3">
        <button
          v-if="isGuestCheckoutEnabled"
          type="button"
          class="text-gray-900 hover:underline text-sm font-medium"
          @click="showGuestCheckout"
        >
          Back to Guest Checkout
        </button>

        <router-link
          v-else
          :to="{ name: 'user-login', query: { redirect_to: route.fullPath } }"
          class="text-gray-900 hover:underline text-sm font-medium"
        >
          Already have an account? Login
        </router-link>
      </div>
    </div>
  </div>
</template>
