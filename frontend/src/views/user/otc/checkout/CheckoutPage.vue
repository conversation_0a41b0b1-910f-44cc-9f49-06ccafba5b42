<script setup>
import { computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useCartStore } from '@/store/cartStore'
import { useCheckoutStore } from '@/store/checkoutStore'
import { storeToRefs } from 'pinia'
import Header from '@/views/user/components/Header.vue'
import { IconArrowLeft } from '@tabler/icons-vue'
import AlertError from '@/views/user/components/AlertError.vue'
import CheckoutSteps from './components/CheckoutSteps.vue'

const router = useRouter()
const route = useRoute()
const cartStore = useCartStore()
const checkoutStore = useCheckoutStore()
const { cartItems } = storeToRefs(cartStore)
const { currentStep, serverErrors, isFirstStep, stepTitle } = storeToRefs(checkoutStore)

const isCartEmpty = computed(() => cartItems.value.length === 0)

const backToCart = () => {
  router.push({ name: 'user-cart' })
}

onMounted(() => {
  if (isCartEmpty.value) {
    router.push({ name: 'otc-products' })
  }

  if (route.name === 'otc-checkout') {
    router.replace({ name: 'otc-checkout-account' })
  }
})
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <Header show-back-button />

    <div class="sm:max-w-screen-sm mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <h1 class="text-3xl font-bold text-gray-900 mb-8 text-center">
        {{ stepTitle }}
      </h1>

      <!-- Step indicators -->
      <CheckoutSteps :current-step="currentStep" />

      <AlertError
        v-if="serverErrors.length > 0"
        title="Error!"
        :errors="serverErrors"
      />

      <div>
        <div class="bg-white rounded-2xl shadow-sm overflow-hidden mb-6">
          <div class="p-6">
            <router-view />
          </div>
        </div>

        <!-- Back to cart button -->
        <div class="flex justify-start mt-6">
          <button
            v-if="isFirstStep"
            class="text-sm text-gray-600 hover:text-gray-800 font-medium inline-flex items-center"
            @click="backToCart"
          >
            <IconArrowLeft
              class="w-4 h-4 me-1"
              stroke-width="2"
            />
            <span>Back to Cart</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
