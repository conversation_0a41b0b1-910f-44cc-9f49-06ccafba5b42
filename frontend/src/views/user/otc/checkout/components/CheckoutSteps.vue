<script setup>
import { useRouter } from 'vue-router'
import { useCheckoutStore } from '@/store/checkoutStore'
import { useAuthStore } from '@/store/auth'
import { storeToRefs } from 'pinia'
import { computed } from 'vue'
import { IconUser, IconTruck, IconCreditCard } from '@tabler/icons-vue'

const props = defineProps({
  currentStep: {
    type: Number,
    required: true,
  },
})

const router = useRouter()
const checkoutStore = useCheckoutStore()
const authStore = useAuthStore()
const { currentStep } = storeToRefs(checkoutStore)
const { isAuthenticated } = storeToRefs(authStore)

const baseSteps = [
  {
    id: 1,
    title: 'Account',
    icon: IconUser,
    route: { name: 'otc-checkout-account' },
  },
  {
    id: 2,
    title: 'Shipping',
    icon: IconTruck,
    route: { name: 'otc-checkout-shipping' },
  },
  {
    id: 3,
    title: 'Payment',
    icon: IconCreditCard,
    route: { name: 'otc-checkout-payment' },
  },
]

const steps = computed(() => {
  if (isAuthenticated.value) {
    return baseSteps
      .filter(step => step.id !== 1)
      .map((step, index) => ({
        ...step,
        id: index + 1,
      }))
  }

  return baseSteps
})

const handleStepClick = step => {
  // If it's a previous step, navigate directly
  if (step.id < currentStep.value) {
    router.push(step.route)
  }

  // If it's a completed step, navigate directly
  else if (checkoutStore.isStepCompleted(step.id)) {
    router.push(step.route)
  }
}

const getStepClass = stepId => {
  if (stepId < currentStep.value || checkoutStore.isStepCompleted(stepId)) {
    return 'bg-green-400 text-white'
  } else if (stepId === currentStep.value) {
    return 'bg-gray-500 text-white'
  } else {
    return 'bg-gray-200 text-gray-600'
  }
}

const getLineClass = stepId => {
  if (stepId < currentStep.value || checkoutStore.isStepCompleted(stepId)) {
    return 'border-green-500'
  } else {
    return 'border-gray-300'
  }
}

const getTextClass = stepId => {
  if (stepId === currentStep.value) {
    return 'text-gray-500 font-semibold'
  } else if (stepId < currentStep.value || checkoutStore.isStepCompleted(stepId)) {
    return 'text-green-400'
  } else {
    return 'text-gray-500'
  }
}
</script>

<template>
  <div class="flex justify-center mb-8">
    <div
      class="flex justify-between items-center"
      :class="steps.length === 2 ? 'w-48 sm:w-64' : 'w-64 sm:w-80'"
    >
      <template
        v-for="(step, index) in steps"
        :key="step.id"
      >
        <div
          class="flex flex-col items-center cursor-pointer"
          @click="handleStepClick(step)"
        >
          <div
            :class="getStepClass(step.id)"
            class="rounded-full h-10 w-10 flex items-center justify-center"
          >
            <component
              :is="step.icon"
              class="h-6 w-6"
              stroke-width="2"
            />
          </div>
          <div
            :class="getTextClass(step.id)"
            class="mt-2 text-sm font-medium"
          >
            {{ step.title }}
          </div>
        </div>

        <!-- Connector line between steps -->
        <div
          v-if="index < steps.length - 1"
          :class="getLineClass(step.id)"
          class="w-8 sm:w-12 border-t-2 mx-1 sm:mx-2 -mt-6"
        ></div>
      </template>
    </div>
  </div>
</template>
