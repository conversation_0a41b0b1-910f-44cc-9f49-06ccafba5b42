<script setup>
import { useCartStore } from '@/store/cartStore'
import { storeToRefs } from 'pinia'
import { formatCurrency } from '@/utils/helpers'
import { IconTrash, IconPlus, IconMinus, IconShoppingCart, IconArrowLeft } from '@tabler/icons-vue'
import Header from '@/views/user/components/Header.vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const cartStore = useCartStore()
const { cartItems, totalPrice } = storeToRefs(cartStore)

const incrementQuantity = index => {
  const item = cartItems.value[index]

  cartStore.updateQuantity(index, item.quantity + 1)
}

const decrementQuantity = index => {
  const item = cartItems.value[index]

  cartStore.updateQuantity(index, item.quantity - 1)
}

const removeItem = index => {
  cartStore.removeFromCart(index)
}

const handleCheckout = () => {
  router.push({ name: 'otc-checkout' })
}

const continueShopping = () => {
  router.push({ name: 'otc-products' })
}
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <Header show-back-button />

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <h1 class="text-3xl font-bold text-gray-900 mb-8">
        Shopping Cart
      </h1>

      <div
        v-if="cartItems.length === 0"
        class="bg-white rounded-lg shadow-sm p-8 text-center"
      >
        <div class="max-w-md mx-auto">
          <IconShoppingCart
            class="w-16 h-16 text-gray-400 mx-auto mb-4"
            stroke-width="2"
          />
          <h3 class="text-xl font-semibold text-gray-900 mb-2">
            Your cart is empty
          </h3>
          <p class="text-gray-600 mb-6">
            Looks like you haven't added any products to your cart yet.
          </p>
          <TwButton
            class="!px-6 !py-2.5"
            @click="continueShopping"
          >
            Continue Shopping
          </TwButton>
        </div>
      </div>

      <div
        v-else
        class="lg:grid lg:grid-cols-12 lg:gap-8"
      >
        <div class="lg:col-span-8">
          <div class="bg-white rounded-lg shadow-sm overflow-hidden">
            <div class="p-6">
              <h2 class="text-lg font-medium text-gray-900">
                Items ({{ cartItems.length }})
              </h2>
            </div>

            <div class="divide-y divide-gray-100">
              <div
                v-for="(item, index) in cartItems"
                :key="item.id + item.planType"
                class="p-6 flex flex-col sm:flex-row gap-4"
              >
                <div class="w-24 h-24 flex-shrink-0 rounded-md overflow-hidden">
                  <img
                    v-if="item.image"
                    :src="item.image"
                    :alt="item.name"
                    class="w-full h-full object-cover"
                    onerror="this.src='/src/assets/images/placeholder.png'"
                  >
                  <div v-else>
                    <IconShoppingCart
                      class="w-16 h-16 mx-auto text-gray-400"
                      stroke-width="1.5"
                    />
                  </div>
                </div>

                <div class="flex-1">
                  <div class="flex justify-between">
                    <h3 class="text-base font-medium text-gray-900">
                      {{ item.name }}
                    </h3>
                    <p class="text-base font-medium text-gray-900">
                      {{ formatCurrency(item.price * item.quantity) }}
                    </p>
                  </div>

                  <p class="mt-1 text-sm text-gray-500">
                    {{ item.planType === 'one_time' ? 'One-time Purchase' : 'Subscription' }}
                  </p>

                  <div class="mt-4 flex justify-between items-center">
                    <div class="flex items-center border !border-gray-300 rounded">
                      <button
                        class="px-3 py-1 text-gray-600 hover:bg-gray-100"
                        :disabled="item.quantity <= 1"
                        @click="decrementQuantity(index)"
                      >
                        <IconMinus class="w-4 h-4" />
                      </button>
                      <span class="px-4 py-1 text-gray-800">{{ item.quantity }}</span>
                      <button
                        class="px-3 py-1 text-gray-600 hover:bg-gray-100"
                        :disabled="item.quantity >= item.maxQuantity"
                        @click="incrementQuantity(index)"
                      >
                        <IconPlus class="w-4 h-4" />
                      </button>
                    </div>

                    <button
                      class="text-sm text-gray-500 hover:text-red-500 flex items-center gap-1"
                      @click="removeItem(index)"
                    >
                      <IconTrash class="w-4 h-4" />
                      <span class="-mb-0.5">Remove</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="lg:col-span-4 mt-8 lg:!mt-0">
          <div class="bg-white rounded-lg shadow-sm p-6">
            <h2 class="text-lg font-medium text-gray-900 mb-4">
              Order Summary
            </h2>

            <div class="space-y-3 border-b border-gray-200 pb-4">
              <div class="flex justify-between">
                <p class="text-sm text-gray-600">
                  Subtotal
                </p>
                <p class="text-sm font-medium text-gray-900">
                  {{ formatCurrency(totalPrice) }}
                </p>
              </div>
              <div class="flex justify-between">
                <p class="text-sm text-gray-600">
                  Shipping
                </p>
                <p class="text-sm font-medium text-gray-900">
                  Calculated at checkout
                </p>
              </div>
            </div>

            <div class="flex justify-between pt-4 mb-6">
              <p class="text-base font-medium text-gray-900">
                Total
              </p>
              <p class="text-base font-medium text-gray-900">
                {{ formatCurrency(totalPrice) }}
              </p>
            </div>

            <TwButton
              class="w-full"
              @click="handleCheckout"
            >
              Proceed to Checkout
            </TwButton>
            <div class="mt-6 text-center">
              <button
                class="text-sm text-gray-600 hover:text-gray-800 font-medium inline-flex"
                @click="continueShopping"
              >
                <IconArrowLeft
                  class="w-4 h-4 me-1"
                  stroke-width="2"
                />
                <span>Continue Shopping</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
