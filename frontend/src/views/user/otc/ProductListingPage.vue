<script setup>
import { formatCurrency } from '@/utils/helpers'
import Header from '@/views/user/components/Header.vue'
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useOtcProductsStore } from '@/store/otcProductListingStore'
import { storeToRefs } from 'pinia'
import { refDebounced } from '@vueuse/core'
import { IconChevronLeft, IconChevronRight, IconChevronDown, IconChevronUp, IconLayoutGrid, IconMenu2, IconMoodSad, IconSearch, IconX } from '@tabler/icons-vue'

const route = useRoute()
const router = useRouter()
const otcProductsStore = useOtcProductsStore()
const { categories, products, loading, pagination } = storeToRefs(otcProductsStore)

const searchQuery = ref('')
const debouncedSearchQuery = refDebounced(searchQuery, 300)
const selectedCategory = ref(route.params.category || null)
const selectedSubCategory = ref(route.params.subCategory || null)
const layoutType = ref('grid') // 'grid' or 'list'
const currentPage = ref(1)
const pageSize = ref(12)
const isMobileCategoryMenuOpen = ref(false)
const expandedCategories = ref({})

const totalPages = computed(() => {
  return pagination.value.totalPages
})

const toggleMobileCategoryMenu = () => {
  isMobileCategoryMenuOpen.value = !isMobileCategoryMenuOpen.value
}

const closeMobileCategoryMenu = () => {
  setTimeout(() => {
    isMobileCategoryMenuOpen.value = false
  }, 50)
}

const toggleCategoryExpansion = categorySlug => {
  expandedCategories.value = {
    ...expandedCategories.value,
    [categorySlug]: !expandedCategories.value[categorySlug],
  }
}

const selectCategory = category => {
  selectedCategory.value = category
  selectedSubCategory.value = null
  searchQuery.value = ''
  currentPage.value = 1
  router.push({
    name: 'otc-products',
    params: {
      category: category,
      subCategory: null,
    },
  })
  closeMobileCategoryMenu()
  fetchProducts()
}

const selectSubCategory = (category, subCategory) => {
  selectedCategory.value = category
  selectedSubCategory.value = subCategory
  searchQuery.value = ''
  currentPage.value = 1
  router.push({
    name: 'otc-products',
    params: {
      category,
      subCategory,
    },
  })
  closeMobileCategoryMenu()
  fetchProducts()
}

function clearFilters() {
  selectedCategory.value = null
  selectedSubCategory.value = null
  searchQuery.value = ''
  currentPage.value = 1
  router.push({
    name: 'otc-products',
    params: {
      category: null,
      subCategory: null,
    },
  })
}

const changePage = page => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page
    fetchProducts()
  }
}

const fetchProducts = async () => {
  await otcProductsStore.fetchProducts({
    search: searchQuery.value,
    perPage: pageSize.value,
    page: currentPage.value,
    categorySlug: selectedCategory.value,
    subCategorySlug: selectedSubCategory.value,
  })
}

watch(debouncedSearchQuery, () => {
  if (debouncedSearchQuery.value) {
    selectedCategory.value = null
    selectedSubCategory.value = null
    router.push({
      name: 'otc-products',
      params: {
        category: null,
        subCategory: null,
      },
    })
  }
  currentPage.value = 1
  fetchProducts()
})

onMounted(async () => {
  if (route.params.category) {
    selectedCategory.value = route.params.category

    // Auto-expand the selected category
    expandedCategories.value[route.params.category] = true
  }
  if (route.params.subCategory) {
    selectedSubCategory.value = route.params.subCategory
  }

  await Promise.all([
    otcProductsStore.fetchCategories(),
    fetchProducts(),
  ])
})
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <Header />

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Search Section -->
      <div class="mb-8">
        <div class="relative max-w-2xl mx-auto">
          <input
            v-model="searchQuery"
            class="w-full px-6 py-4 text-lg border !border-gray-200 rounded-full shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-600 focus:!border-transparent transition-all duration-200"
            type="text"
            placeholder="Search for products..."
          />
          <div class="absolute right-4 top-1/2 -translate-y-1/2">
            <IconSearch
              class="w-6 h-6 text-gray-400"
              stroke-width="2"
            />
          </div>
        </div>
      </div>

      <div class="flex flex-col lg:flex-row gap-8">
        <!-- Desktop Categories Sidebar -->
        <div class="hidden lg:block lg:w-72 lg:sticky lg:top-24 lg:h-[calc(100vh-8rem)]">
          <div class="bg-white rounded-xl shadow-sm p-6 h-full overflow-y-auto">
            <h3 class="text-xl font-semibold text-gray-900 mb-6">
              Categories
            </h3>
            <ul class="space-y-2">
              <li
                v-for="category in categories"
                :key="category.slug"
                class="space-y-2"
              >
                <div
                  class="flex items-center justify-between px-4 py-3 cursor-pointer rounded-lg transition-all duration-200"
                  :class="{
                    'bg-gray-100 text-gray-800 font-semibold': selectedCategory === category.slug && !selectedSubCategory,
                    'text-gray-600 hover:bg-gray-50': selectedCategory !== category.slug || selectedSubCategory
                  }"
                  @click="selectCategory(category.slug)"
                >
                  <span>{{ category.name }}</span>
                  <button
                    v-if="category.subCategories.length > 0"
                    class="p-1 bg-gray-100 text-gray-500 hover:text-gray-700 focus:outline-none rounded-full"
                    @click.stop="toggleCategoryExpansion(category.slug)"
                  >
                    <IconChevronUp
                      v-if="expandedCategories[category.slug]"
                      class="w-4 h-4"
                      stroke-width="2"
                    />
                    <IconChevronDown
                      v-else
                      class="w-4 h-4"
                      stroke-width="2"
                    />
                  </button>
                </div>
                <transition
                  enter-active-class="transition-all duration-300 ease-out"
                  enter-from-class="max-h-0 opacity-0"
                  enter-to-class="max-h-[500px] opacity-100"
                  leave-active-class="transition-all duration-200 ease-in"
                  leave-from-class="max-h-[500px] opacity-100"
                  leave-to-class="max-h-0 opacity-0"
                >
                  <ul
                    v-if="category.subCategories.length > 0 && expandedCategories[category.slug]"
                    class="ps-6 space-y-1 overflow-hidden"
                  >
                    <li
                      v-for="subCategory in category.subCategories"
                      :key="subCategory.slug"
                      class="px-4 py-2 cursor-pointer rounded-lg transition-all duration-200 text-sm"
                      :class="{
                        'bg-gray-100 text-gray-800 font-semibold': selectedCategory === category.slug && selectedSubCategory === subCategory.slug,
                        'text-gray-600 hover:bg-gray-50': selectedCategory !== category.slug || selectedSubCategory !== subCategory.slug
                      }"
                      @click="selectSubCategory(category.slug, subCategory.slug)"
                    >
                      {{ subCategory.name }}
                    </li>
                  </ul>
                </transition>
              </li>
            </ul>
          </div>
        </div>

        <!-- Main Content Area -->
        <div class="flex-grow lg:w-[calc(100%-18rem)]">
          <!-- Top Strip -->
          <div class="bg-white rounded-xl shadow-sm p-4 mb-6 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 sm:gap-0">
            <div class="flex items-center gap-2 w-full sm:w-auto overflow-hidden">
              <div class="flex items-center gap-1">
                <button
                  class="lg:hidden p-1 text-gray-600 hover:text-gray-900 focus:outline-none"
                  @click="toggleMobileCategoryMenu"
                >
                  <IconMenu2
                    class="w-5 h-5"
                    stroke-width="2"
                  />
                </button>
                <span class="text-gray-600 whitespace-nowrap">Category:</span>
              </div>
              <span class="font-semibold text-gray-900 truncate">
                {{ categories.find(c => c.slug === selectedCategory)?.name || 'All' }}
                <span v-if="selectedSubCategory">
                  / {{ categories.find(c => c.slug === selectedCategory)?.subCategories.find(sc => sc.slug === selectedSubCategory)?.name }}
                </span>
              </span>
            </div>
            <div class="hidden sm:flex items-center gap-2 self-end sm:self-auto">
              <button
                class="p-2 rounded-lg"
                :class="layoutType === 'grid' ? 'bg-gray-100 text-gray-900' : 'text-gray-600 hover:bg-gray-50'"
                @click="layoutType = 'grid'"
              >
                <IconLayoutGrid
                  class="w-5 h-5"
                  stroke-width="2"
                />
              </button>
              <button
                class="p-2 rounded-lg"
                :class="layoutType === 'list' ? 'bg-gray-100 text-gray-900' : 'text-gray-600 hover:bg-gray-50'"
                @click="layoutType = 'list'"
              >
                <IconMenu2
                  class="w-5 h-5"
                  stroke-width="2"
                />
              </button>
            </div>
          </div>

          <!-- Loading State -->
          <div
            v-if="loading"
            class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6"
          >
            <div
              v-for="i in 6"
              :key="i"
              class="bg-white rounded-2xl shadow-sm overflow-hidden"
            >
              <div class="animate-pulse">
                <div class="pt-[100%] bg-gray-200"></div>
                <div class="p-4 sm:p-6">
                  <div class="h-6 bg-gray-200 rounded w-3/4 mb-4"></div>
                  <div class="h-4 bg-gray-200 rounded w-full mb-4"></div>
                  <div class="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
                  <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2">
                    <div class="h-6 bg-gray-200 rounded w-1/4"></div>
                    <div class="h-10 bg-gray-200 rounded w-1/3 hidden sm:block"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Products Grid/List -->
          <div
            v-else
            :class="{
              'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6': layoutType === 'grid',
              'space-y-4': layoutType === 'list'
            }"
            :data-layout="layoutType"
          >
            <RouterLink
              v-for="product in products"
              :key="product.id"
              class="group bg-white rounded-2xl shadow-sm overflow-hidden hover:shadow-lg transition-all duration-300 border !border-gray-200 cursor-pointer"
              :class="{
                'flex flex-col sm:flex-row': layoutType === 'list'
              }"
              :to="{ name: 'otc-product-details', params: { productSlug: product.slug } }"
            >
              <div
                class="relative w-full"
                :class="{
                  'pt-[100%] max-h-[250px] sm:max-h-[300px] md:max-h-[350px]': layoutType === 'grid',
                  'pt-[60%] sm:pt-0 sm:w-48 sm:h-auto sm:flex-shrink-0': layoutType === 'list'
                }"
              >
                <img
                  :src="product.image"
                  :alt="product.drug_name"
                  class="absolute top-0 left-0 w-full h-full object-contain group-hover:scale-105 transition-transform duration-300 max-h-[250px] sm:max-h-[300px] md:max-h-[350px]"
                />
              </div>
              <div
                class="p-4 sm:p-6"
                :class="{
                  'flex-grow': layoutType === 'list'
                }"
              >
                <h4 class="text-base font-semibold text-gray-900 mb-2">
                  {{ product.drug_name }}
                </h4>
                <p class="text-gray-600 mb-4 line-clamp-2 text-sm">
                  {{ product.summary }}
                </p>
                <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2">
                  <span class="text-xl font-medium text-gray-800">
                    {{ formatCurrency(product.product_price_after_discount) }}
                    <span
                      v-if="product.product_price_after_discount !== product.selling_price"
                      class="line-through text-gray-700 font-normal text-sm ms-1"
                    >{{ formatCurrency(product.selling_price) }}</span>
                    <span
                      v-if="product.discount_value > 0"
                      class="px-2 pt-1 pb-0.5 bg-green-100 text-green-800 rounded text-xs font-semibold ms-2"
                    >
                      {{ product.discount_type === 'percentage' ? product.discount_value + '%' : formatCurrency(product.discount_value) }} OFF
                    </span>
                  </span>
                </div>
              </div>
            </RouterLink>
          </div>

          <!-- Empty State -->
          <div
            v-if="!loading && products.length === 0"
            class="bg-white rounded-2xl shadow-sm p-12 text-center"
          >
            <div class="max-w-md mx-auto">
              <IconMoodSad
                class="w-16 h-16 text-gray-400 mx-auto mb-4"
                stroke-width="2"
              />
              <h3 class="text-xl font-semibold text-gray-900 mb-2">
                No products found
              </h3>
              <p class="text-gray-600 mb-6">
                Try adjusting your search or category filters to find what you're looking for.
              </p>
              <TwButton
                class="!px-6 !py-2.5"
                @click="clearFilters"
              >
                Clear Filters
              </TwButton>
            </div>
          </div>

          <!-- Pagination Strip -->
          <div
            v-if="!loading && products.length > 0"
            class="bg-white rounded-xl shadow-sm p-4 mt-6 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 sm:gap-0"
          >
            <div class="flex items-center gap-2 sm:gap-4">
              <span class="text-gray-600 text-sm sm:text-base">Show</span>
              <select
                v-model="pageSize"
                class="w-14 px-2 py-1.5 border !border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-600 focus:!border-transparent"
                @change="fetchProducts"
              >
                <option value="12">
                  12
                </option>
                <option value="24">
                  24
                </option>
                <option value="36">
                  36
                </option>
              </select>
              <span class="text-gray-600 text-sm sm:text-base">per page</span>
            </div>
            <div class="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-4 w-full sm:w-auto">
              <span class="text-gray-600 text-sm sm:text-base order-2 sm:order-1">
                {{ (currentPage - 1) * pageSize + 1 }} - {{ Math.min(currentPage * pageSize, pagination.totalRecords) }} of {{ pagination.totalRecords }}
              </span>
              <div class="flex gap-2 self-end sm:self-auto order-1 sm:order-2">
                <button
                  class="p-2 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
                  :class="currentPage === 1 ? 'text-gray-400' : 'text-gray-600 hover:bg-gray-50'"
                  :disabled="currentPage === 1"
                  @click="changePage(currentPage - 1)"
                >
                  <IconChevronLeft
                    class="w-5 h-5"
                    stroke-width="2"
                  />
                </button>
                <button
                  class="p-2 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
                  :class="currentPage === totalPages ? 'text-gray-400' : 'text-gray-600 hover:bg-gray-50'"
                  :disabled="currentPage === totalPages"
                  @click="changePage(currentPage + 1)"
                >
                  <IconChevronRight
                    class="w-5 h-5"
                    stroke-width="2"
                  />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Mobile Category Menu Overlay -->
    <transition
      enter-active-class="transition-opacity duration-300 ease-out"
      enter-from-class="opacity-0"
      enter-to-class="opacity-100"
      leave-active-class="transition-opacity duration-200 ease-in"
      leave-from-class="opacity-100"
      leave-to-class="opacity-0"
    >
      <div
        v-if="isMobileCategoryMenuOpen"
        class="fixed inset-0 bg-gray-900 bg-opacity-50 z-50 lg:hidden flex flex-col"
      >
        <div class="bg-white h-full w-full overflow-auto animate-slide-in-right">
          <div class="p-4 flex justify-between items-center border-b border-gray-200">
            <h3 class="text-xl font-semibold text-gray-900">
              Categories
            </h3>
            <button
              class="p-2 text-gray-600 hover:text-gray-900 focus:outline-none"
              @click="closeMobileCategoryMenu"
            >
              <IconX
                class="w-6 h-6"
                stroke-width="2"
              />
            </button>
          </div>

          <div class="p-4 overflow-y-auto max-h-[calc(100vh-4rem)]">
            <ul class="space-y-2">
              <li
                v-for="category in categories"
                :key="category.slug"
                class="space-y-2"
              >
                <div
                  class="flex items-center justify-between px-4 py-3 cursor-pointer rounded-lg transition-all duration-200 active:bg-gray-200"
                  :class="{
                    'bg-gray-100 text-gray-800 font-semibold': selectedCategory === category.slug && !selectedSubCategory,
                    'text-gray-600 hover:bg-gray-50': selectedCategory !== category.slug || selectedSubCategory
                  }"
                  @click="selectCategory(category.slug)"
                >
                  <span>{{ category.name }}</span>
                  <button
                    v-if="category.subCategories.length > 0"
                    class="p-1 bg-gray-100 text-gray-500 hover:text-gray-700 focus:outline-none rounded-full"
                    @click.stop="toggleCategoryExpansion(category.slug)"
                  >
                    <IconChevronUp
                      v-if="expandedCategories[category.slug]"
                      class="w-4 h-4"
                      stroke-width="2"
                    />
                    <IconChevronDown
                      v-else
                      class="w-4 h-4"
                      stroke-width="2"
                    />
                  </button>
                </div>
                <transition
                  enter-active-class="transition-all duration-300 ease-out"
                  enter-from-class="max-h-0 opacity-0"
                  enter-to-class="max-h-[500px] opacity-100"
                  leave-active-class="transition-all duration-200 ease-in"
                  leave-from-class="max-h-[500px] opacity-100"
                  leave-to-class="max-h-0 opacity-0"
                >
                  <ul
                    v-if="category.subCategories.length > 0 && expandedCategories[category.slug]"
                    class="ps-6 space-y-1 overflow-hidden"
                  >
                    <li
                      v-for="subCategory in category.subCategories"
                      :key="subCategory.slug"
                      class="px-4 py-2 cursor-pointer rounded-lg transition-all duration-200 text-sm active:bg-gray-200"
                      :class="{
                        'bg-gray-100 text-gray-800 font-semibold': selectedCategory === category.slug && selectedSubCategory === subCategory.slug,
                        'text-gray-600 hover:bg-gray-50': selectedCategory !== category.slug || selectedSubCategory !== subCategory.slug
                      }"
                      @click="selectSubCategory(category.slug, subCategory.slug)"
                    >
                      {{ subCategory.name }}
                    </li>
                  </ul>
                </transition>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<style scoped>
@keyframes slide-in-right {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

.animate-slide-in-right {
  animation: slide-in-right 0.3s ease-out forwards;
}

/* Custom scrollbar styling for the categories sidebar */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 10px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Collapsible category transitions */
.transition-all {
  transition-property: all;
}

.overflow-hidden {
  overflow: hidden;
}

/* Ensure grid images maintain aspect ratio while respecting max-height */
@media (max-width: 640px) {
  [data-layout="grid"] .relative {
    height: auto;
    max-height: 200px;
    padding-top: 0 !important;
    aspect-ratio: 1/1;
  }

  [data-layout="grid"] .relative img {
    position: relative;
    object-fit: contain;
  }
}
</style>
