<script setup>
import { formatCurrency } from '@/utils/helpers'
import Header from '@/views/user/components/Header.vue'
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useOtcProductsStore } from '@/store/otcProductListingStore'
import { useCartStore } from '@/store/cartStore'
import { storeToRefs } from 'pinia'
import { IconMinus, IconMoodSad, IconPlus, IconShoppingBag, IconShoppingCart, IconChevronRight } from '@tabler/icons-vue'

const route = useRoute()
const router = useRouter()
const otcProductsStore = useOtcProductsStore()
const cartStore = useCartStore()
const { productDetails, productLoading: loading } = storeToRefs(otcProductsStore)

const quantity = ref(1)
const selectedPlanType = ref('one_time')

const maxQuantity = computed(() => {
  return productDetails.value?.max_qty_per_order || 10
})

const itemPrice = computed(() => {
  return productDetails.value?.product_price_after_discount || 0
})

const totalPrice = computed(() => {
  return itemPrice.value * quantity.value
})

const incrementQuantity = () => {
  if (quantity.value < maxQuantity.value) {
    quantity.value++
  }
}

const decrementQuantity = () => {
  if (quantity.value > 1) {
    quantity.value--
  }
}

const addToCart = () => {
  if (productDetails.value && productDetails.value.stock_availability) {
    cartStore.addToCart(productDetails.value, quantity.value, selectedPlanType.value)
  }
}

const buyNow = () => {
  if (productDetails.value && productDetails.value.stock_availability) {
    cartStore.addToCart(productDetails.value, quantity.value, selectedPlanType.value, true)
    router.push({ name: 'otc-checkout' })
  }
}

onMounted(async () => {
  const productSlug = route.params.productSlug
  if (!productSlug) {
    router.push({ name: 'otc-products' })

    return
  }

  await otcProductsStore.fetchProductDetails(productSlug)

  if (!productDetails.value) {
    router.push({ name: 'otc-products' })
  }
})
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <Header show-back-button />

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Breadcrumbs -->
      <nav
        v-if="!loading && productDetails"
        aria-label="Breadcrumb"
        class="mb-4"
      >
        <ol class="flex items-center text-sm">
          <li class="flex items-center">
            <RouterLink
              class="text-gray-600 hover:text-gray-900 font-medium transition-colors"
              :aria-label="`Go to ${productDetails.main_category_name} category`"
              :to="{
                name: 'otc-products',
                params: {
                  category: productDetails.main_category_slug,
                },
              }"
            >
              {{ productDetails.main_category_name }}
            </RouterLink>
          </li>

          <li class="flex items-center">
            <IconChevronRight
              class="mx-2 text-gray-400 w-4 h-4"
              stroke-width="2"
              aria-hidden="true"
            />
          </li>

          <template v-if="productDetails.sub_category_name">
            <li class="flex items-center">
              <RouterLink
                class="text-gray-600 hover:text-gray-900 font-medium transition-colors"
                :aria-label="`Go to ${productDetails.sub_category_name} subcategory`"
                :to="{
                  name: 'otc-products',
                  params: {
                    category: productDetails.main_category_slug,
                    subCategory: productDetails.sub_category_slug,
                  },
                }"
              >
                {{ productDetails.sub_category_name }}
              </RouterLink>
            </li>

            <!--
              <li class="flex items-center">
              <IconChevronRight
              class="mx-2 text-gray-400 w-4 h-4"
              stroke-width="2"
              aria-hidden="true"
              />
              </li>
            -->
          </template>

          <!--
            <li class="flex items-center">
            <span class="text-gray-900 font-semibold">
            {{ productDetails.drug_name }}
            </span>
            </li>
          -->
        </ol>
      </nav>

      <!-- Loading State -->
      <div
        v-if="loading"
        class="bg-white rounded-2xl shadow-sm p-8"
      >
        <div class="animate-pulse">
          <div class="flex flex-col md:flex-row gap-8">
            <div class="w-full md:w-1/2 lg:w-2/5">
              <div class="bg-gray-200 rounded-xl aspect-square"></div>
            </div>
            <div class="w-full md:w-1/2 lg:w-3/5">
              <div class="h-8 bg-gray-200 rounded w-3/4 mb-4"></div>
              <div class="h-6 bg-gray-200 rounded w-1/4 mb-6"></div>
              <div class="h-6 bg-gray-200 rounded w-1/2 mb-4"></div>
              <div class="h-4 bg-gray-200 rounded w-full mb-2"></div>
              <div class="h-4 bg-gray-200 rounded w-full mb-2"></div>
              <div class="h-4 bg-gray-200 rounded w-3/4 mb-6"></div>
              <div class="h-10 bg-gray-200 rounded w-full mb-6"></div>
              <div class="h-12 bg-gray-200 rounded w-full"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Product Details -->
      <div
        v-else-if="productDetails"
        class="bg-white rounded-2xl overflow-hidden border !border-gray-200"
      >
        <div class="flex flex-col md:flex-row">
          <!-- Product Image -->
          <div class="w-full md:w-1/2 lg:w-2/5 p-8 flex justify-center">
            <img
              :src="productDetails.image"
              :alt="productDetails.drug_name"
              class="max-w-full max-h-[400px] object-contain"
            />
          </div>

          <!-- Product Info -->
          <div class="w-full md:w-1/2 lg:w-3/5 p-8">
            <h1 class="text-2xl font-semibold text-gray-800 mb-2">
              {{ productDetails.drug_name }} <span v-if="productDetails.packaging">- {{ productDetails.packaging }}</span>
            </h1>

            <!--
              <div class="flex items-center gap-2 mb-4">
              <span class="px-3 pt-1 pb-0.5 bg-gray-100 text-gray-700 rounded-full text-xs font-medium">
              {{ productDetails.main_category_name }}
              </span>
              <span
              v-if="productDetails.sub_category_name"
              class="px-3 pt-1 pb-0.5 bg-gray-100 text-gray-700 rounded-full text-xs font-medium"
              >
              {{ productDetails.sub_category_name }}
              </span>
              </div>
            -->

            <div class="flex items-baseline gap-2 mb-6">
              <span class="text-2xl font-medium text-gray-900">
                {{ formatCurrency(productDetails.product_price_after_discount) }}
              </span>
              <span
                v-if="productDetails.product_price_after_discount !== productDetails.selling_price"
                class="text-lg text-gray-500 line-through"
              >
                {{ formatCurrency(productDetails.selling_price) }}
              </span>
              <span
                v-if="productDetails.discount_value > 0"
                class="px-2 pt-1 pb-0.5 bg-green-100 text-green-800 rounded text-xs font-semibold"
              >
                {{ productDetails.discount_type === 'percentage' ? productDetails.discount_value + '%' : formatCurrency(productDetails.discount_value) }} OFF
              </span>
            </div>

            <!-- <div class="flex flex-col gap-4 mb-6"> -->
            <!--
              <div
              v-if="productDetails.drug_form"
              class="flex items-center gap-4"
              >
              <span class="text-gray-700 w-24">Form:</span>
              <span class="font-medium">{{ productDetails.drug_form }}</span>
              </div>
            -->
            <!--
              <div
              v-if="productDetails.strength"
              class="flex items-center gap-4"
              >
              <span class="text-gray-700 w-24">Strength:</span>
              <span class="font-medium">{{ productDetails.strength }}</span>
              </div>
            -->
            <!--
              <div class="flex items-center gap-4">
              <span class="text-gray-700 w-24">Availability:</span>
              <span
              v-if="productDetails.stock_availability"
              class="font-medium text-green-600"
              >In Stock</span>
              <span
              v-else
              class="font-medium text-red-600"
              >Out of Stock</span>
              </div>
            -->
            <!-- </div> -->

            <!-- Quantity Selector -->
            <div class="flex flex-wrap items-center gap-4 mb-6">
              <span class="text-gray-700">Quantity:</span>
              <div class="flex items-center border !border-gray-300 rounded-lg">
                <button
                  class="px-3 py-2 text-gray-600 hover:bg-gray-100 focus:outline-none"
                  :disabled="quantity <= 1"
                  @click="decrementQuantity"
                >
                  <IconMinus
                    class="w-4 h-4"
                    stroke-width="2"
                  />
                </button>
                <span class="px-4 py-2 text-gray-800 font-medium">{{ quantity }}</span>
                <button
                  class="px-3 py-2 text-gray-600 hover:bg-gray-100 focus:outline-none"
                  :disabled="quantity >= maxQuantity"
                  @click="incrementQuantity"
                >
                  <IconPlus
                    class="w-4 h-4"
                    stroke-width="2"
                  />
                </button>
              </div>
              <span class="text-sm text-gray-500 italic">Max {{ maxQuantity }} per order</span>
            </div>

            <!-- Plan Type Selection -->
            <!--
              <div
              v-if="productDetails.plan_type && productDetails.plan_type.length > 0"
              class="mb-6"
              >
              <div class="text-gray-700 mb-2">
              Plan Type:
              </div>
              <div class="flex flex-wrap gap-3">
              <button
              v-for="planType in productDetails.plan_type"
              :key="planType"
              class="px-4 py-2 rounded-lg border transition-colors"
              :class="selectedPlanType === planType
              ? '!border-gray-400 bg-gray-200 text-gray-900 font-medium'
              : '!border-gray-200 hover:border-gray-400'"
              @click="selectedPlanType = planType"
              >
              {{ planType === 'one_time' ? 'One-time Purchase' : 'Subscription' }}
              </button>
              </div>
              </div>
            -->

            <!-- Pricing Summary -->
            <div class="bg-gray-50 p-4 rounded-lg mb-6">
              <div class="flex justify-between mb-2">
                <span class="text-gray-700">Price per item:</span>
                <span class="font-medium">{{ formatCurrency(itemPrice) }}</span>
              </div>
              <div class="flex justify-between font-medium text-lg">
                <span>Total:</span>
                <span>{{ formatCurrency(totalPrice) }}</span>
              </div>
            </div>

            <div class="flex flex-col sm:flex-row gap-3">
              <!-- Add to Cart Button -->
              <TwButton
                class="w-full"
                :disabled="!productDetails.stock_availability"
                @click="addToCart"
              >
                <IconShoppingCart
                  class="w-5 h-5 me-2"
                  stroke-width="2"
                />
                <span v-if="productDetails.stock_availability">
                  Add to Cart
                </span>
                <span v-else>Out of Stock</span>
              </TwButton>
              <TwButton
                class="w-full"
                variant="accent"
                :disabled="!productDetails.stock_availability"
                @click="buyNow"
              >
                <IconShoppingBag
                  class="w-5 h-5 me-2"
                  stroke-width="2"
                />
                <span v-if="productDetails.stock_availability">
                  Buy Now
                </span>
                <span v-else>Out of Stock</span>
              </TwButton>
            </div>
          </div>
        </div>

        <div class="p-8 ">
          <!-- Product Summary -->
          <div class="border-t !border-gray-200 pt-6">
            <h4 class="text-lg font-medium text-gray-900 mb-2">
              Product summary
            </h4>
            <div
              v-if="productDetails.summary"
              class="text-gray-700"
            >
              {{ productDetails.summary }}
            </div>
          </div>
          <!-- Product Description -->
          <div class="border-t !border-gray-200 pt-6 mt-10">
            <div
              class="text-gray-700 prose max-w-none"
              v-html="productDetails.description"
            ></div>
          </div>
        </div>
      </div>

      <!-- Product Not Found -->
      <div
        v-else
        class="bg-white rounded-2xl shadow-sm p-12 text-center"
      >
        <div class="max-w-md mx-auto">
          <IconMoodSad
            class="w-16 h-16 text-gray-400 mx-auto mb-4"
            stroke-width="2"
          />
          <h3 class="text-xl font-semibold text-gray-900 mb-2">
            Product not found
          </h3>
          <p class="text-gray-600 mb-6">
            The product you are looking for does not exist or has been removed.
          </p>
          <TwButton @click="router.push({ name: 'otc-products' })">
            Browse Products
          </TwButton>
        </div>
      </div>
    </div>
  </div>
</template>
