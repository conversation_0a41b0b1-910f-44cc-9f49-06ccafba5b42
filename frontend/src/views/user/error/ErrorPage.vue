<script setup>
import { isEmpty, isEmptyObject } from '@/@core/utils'
import router from '@/router'
import Header from '@/views/user/components/Header.vue'
import { IconMoodSad } from '@tabler/icons-vue'
import { useSessionStorage } from '@vueuse/core'
import { useRoute } from 'vue-router'

const route = useRoute()
const edVisitSession = useSessionStorage('edVisitSession', {})
const hlVisitSession = useSessionStorage('hlVisitSession', {})
const wlVisitSession = useSessionStorage('wlVisitSession', {})

const loading = ref(false)

const btnAction = () => {
  loading.value = true

  if (route.query.visit === 'ed') {
    if (!isEmptyObject(edVisitSession.value) && !isEmpty(edVisitSession.value['questionSessionId'])) {
      router.replace({ name: 'ed-visit-product-recommended', query: { ...route.query } })
    } else {
      router.push({ name: 'ed-start-consult' })
    }
  } else if (route.query.visit === 'hl') {
    if (!isEmptyObject(hlVisitSession.value) && !isEmpty(hlVisitSession.value['questionSessionId'])) {
      router.replace({ name: 'hl-visit-product-recommended', query: { ...route.query } })
    } else {
      router.push({ name: 'user-subscription' })
    }
  } else if (route.query.visit === 'wl') {
    if (!isEmptyObject(wlVisitSession.value) && !isEmpty(wlVisitSession.value['questionSessionId'])) {
      router.replace({ name: 'wl-products', query: { ...route.query } })
    } else {
      router.push({ name: 'user-subscription' })
    }
  }
}
</script>

<template>
  <div>
    <Header />
    <div class="grid place-items-center">
      <div class="w-full sm:max-w-lg grid place-items-center h-[calc(100vh-15rem)]">
        <div class="px-4 py-12 sm:px-20 flex flex-col items-center">
          <IconMoodSad
            class="w-28 mb-4 text-gray-800"
            stroke-width="1.5"
          />
          <div class="mb-6 text-center">
            <h1 class="text-2xl sm:text-3xl text-gray-800 font-semibold mb-4">
              Oops, an error occurred.
            </h1>
            <p class="text-base sm:text-lg text-gray-600">
              Please refresh the page. We're actively working to resolve the issue quickly.
            </p>
          </div>
          <TwButton
            class="px-8"
            :loading="loading"
            @click="btnAction"
          >
            Reload
          </TwButton>
        </div>
      </div>
    </div>
  </div>
</template>
