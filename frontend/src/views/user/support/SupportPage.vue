<script setup>
import { computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { supportQues } from './data'
import router from '@/router'
import Header from '@/views/user/components/Header.vue'
import { IconHeartHandshake, IconMail, IconPhone } from '@tabler/icons-vue'

const route = useRoute()

const slug = computed(() => route.params.slug)
const supportEmail = computed(() => import.meta.env.VITE_SUPPORT_EMAIL)
const supportPhone = computed(() => import.meta.env.VITE_SUPPORT_PHONE)

onMounted(() => {
  if (!slug.value) {
    router.replace({ name: 'account-support', params: { slug: supportQues[0].slug } })
  }
})

const tableOfIndex = computed(() => {
  return supportQues.map(item => ({ title: item.title, slug: item.slug }))
})

const questions = computed(() => {
  return supportQues.find(item => item.slug === slug.value)?.questions
})
</script>

<template>
  <section>
    <Header show-back-button />
    <div class="container px-6 mx-auto max-w-screen-lg">
      <h1 class="text-lg lg:text-3xl font-semibold text-center text-gray-800 lg:hidden">
        Have any Questions?
      </h1>

      <div class="mt-8 xl:mt-16 lg:flex lg:-mx-12">
        <div class="lg:mx-12 lg:mt-14">
          <div class="block mt-4 space-y-2 lg:mt-8 text-base text-center border !border-gray-100 rounded-lg p-2 lg:!border-none lg:!text-start">
            <RouterLink
              v-for="item in tableOfIndex"
              :key="item.slug"
              :to="{ name: 'account-support', params: { slug: item.slug } }"
              replace
              class="block text-gray-500 px-3 py-1 rounded-lg hover:lg:underline"
              active-class="!bg-black !text-white lg:!bg-white lg:!text-black lg:underline font-medium"
            >
              {{ item.title }}
            </RouterLink>
          </div>
        </div>

        <div class="flex-1 lg:mx-12">
          <h1 class="text-lg lg:text-3xl font-semibold text-gray-800 hidden lg:block mb-12">
            Have any Questions?
          </h1>

          <div class="mt-4 space-y-8">
            <div
              v-for="item in questions"
              :key="item.question"
              class="p-6 bg-gray-100 rounded-lg"
            >
              <h2 class="text-lg md:text-2xl font-medium text-gray-800">
                {{ item.question }}
              </h2>

              <div
                class="mt-3 text-base text-gray-600 [&>p]:mb-2 [&>div]:mb-2 [&>strong]:text-black"
                v-html="item.answer"
              >
              </div>
            </div>
          </div>

          <div class="inline-flex items-center justify-center w-full my-14 relative">
            <hr class="w-64 h-px bg-gray-200 border-0">
            <span class="absolute px-3 font-medium text-gray-900 -translate-x-1/2 bg-white left-1/2">or</span>
          </div>

          <div class="px-5 py-5 bg-gray-100 rounded-[16px] mb-10">
            <IconHeartHandshake
              class="h-12 w-12 mx-auto mb-2"
              stroke-width="1.5"
            />
            <h2 class="text-center text-xl md:text:3xl font-semibold leading-tight text-black">
              Contact Us
            </h2>
            <div class="text-center mt-3">
              <div
                v-if="supportEmail"
                class="text-base font-medium text-gray-700 mb-1"
              >
                <span>
                  <IconMail
                    class="inline h-5 w-5 ms-1"
                    stroke-width="2"
                  />
                  Email:
                </span>
                <a
                  :href="`mailto:${supportEmail}`"
                  class="text-gray-900 hover:underline"
                >{{ supportEmail }}</a>
              </div>
              <div
                v-if="supportPhone"
                class="text-base font-medium text-gray-700 mb-1"
              >
                <span>
                  <IconPhone
                    class="inline h-5 w-5 ms-1"
                    stroke-width="2"
                  />
                  Phone:
                </span>
                <a
                  :href="`tel:${(supportPhone).replace(/[^0-9+]/g, '')}`"
                  class="text-gray-900 hover:underline"
                >{{ supportPhone }}</a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<style lang="scss">
@import '@/assets/user/css/style.css';
</style>
