<script setup>
import { isEmpty } from '@/@core/utils'
import { usePreventNavigation } from '@/composables/usePreventNavigation'
import router from '@/router'
import ApiService from '@/services/ApiService'
import { IconArrowRight, IconStethoscope } from '@tabler/icons-vue'
import { useSessionStorage } from '@vueuse/core'
import { onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import Header from '../components/Header.vue'
import useLogOrderSession from '@/composables/useLogOrderSession'

usePreventNavigation(true)

const route = useRoute()
const hlVisitSession = useSessionStorage('hlVisitSession', {})
const { logSession } = useLogOrderSession()
const products = ref([])
const isLoading = ref(false)
const btnLoading = ref(null)

onMounted(async () => {
  await fetchRecommendedProducts()
})

async function fetchRecommendedProducts() {
  try {
    isLoading.value = true

    const { data } = await ApiService.get('/hl-recommended-products')

    if (data.status === 200) {
      if (isEmpty(data.recommendhl_product_lists)) {
        router.replace({ name: 'hl-visit-select-product' })
      }
      products.value = data.recommendhl_product_lists
    } else {
      console.error(data)
      router.replace({ name: 'hl-visit-select-product' })
    }
  } catch (error) {
    console.log(error)
    router.replace({ name: 'error-something-wrong', query: { visit: 'hl' } })
  } finally {
    isLoading.value = false
  }
}

async function continueWithRecommended(product) {
  btnLoading.value = product.hl_product_id

  hlVisitSession.value['productId'] = product.hl_product_id
  hlVisitSession.value['strengthId'] = product.hl_product_strength_id

  if (product.hl_product_strength_qty_id) {
    hlVisitSession.value['productQty'] = product.hl_product_strength_qty_id
  }

  await router.push({
    name: 'visit-shipping',
    params: { visitType: 'hl' },
    query: { ...route.query },
  })

  logSession({
    id: hlVisitSession.value['questionSessionId'],
    category: 'HL',
    product: {
      id: hlVisitSession.value['productId'] ?? null,
      strength_id: hlVisitSession.value['strengthId'] ?? null,
      qty_id: hlVisitSession.value['productQty'] ?? null,
    },
  })
}

function exploreOtherOptions() {
  hlVisitSession.value['productId'] = null
  hlVisitSession.value['strengthId'] = null
  hlVisitSession.value['productQty'] = null
  router.push({ name: 'hl-visit-select-product' })
}

function bestForArr(str) {
  return str.split(',').map(s => s.trim())
}
</script>

<template>
  <div>
    <Header />

    <div class="flex items-center justify-center py-10 sm:px-16">
      <div class="px-4 w-full sm:max-w-[480px]">
        <div
          v-if="isLoading"
          class="h-7 bg-gray-300 rounded mb-2 animate-pulse"
        ></div>
        <div
          v-else
          class="mb-2"
        >
          <h2 class="text-xl md:text-3xl font-bold leading-tight text-black">
            Recommended Treatment
          </h2>
          <p class="text-sm text-zinc-700 mt-2">
            Based on your assessment, our providers recommend this treatment
          </p>
        </div>

        <div
          v-if="isLoading"
          class="flex flex-col items-center"
        >
          <div class="flex flex-col p-5 mt-6 w-full bg-white rounded-3xl border border-solid !border-zinc-300">
            <!-- Product Info Skeleton Loader -->
            <div class="animate-pulse">
              <div class="justify-between py-1">
                <div class="flex gap-5 max-md:flex-col max-md:gap-0">
                  <div class="flex flex-col w-full max-md:ml-0 max-md:w-full">
                    <div class="flex flex-col-reverse sm:flex-row gap-5 grow justify-between items-top text-sm leading-5 text-black">
                      <div class="grow">
                        <div class="h-6 w-4/5 bg-gray-300 rounded mb-2"></div> <!-- Title Skeleton Loader -->
                        <div class="h-5 w-36 bg-gray-300 rounded mb-2"></div> <!-- Price Skeleton Loader -->
                        <div class="h-3 w-40 bg-gray-300 rounded mb-2"></div> <!-- Description Skeleton Loader -->
                        <div class="h-3 w-32 bg-gray-300 rounded"></div> <!-- Description Skeleton Loader -->
                      </div>
                      <div>
                        <div class="min-h-24 min-w-24 w-full sm:h-24 sm:w-24 bg-gray-300 rounded-lg"></div> <!-- Image Skeleton Loader -->
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="mt-5 animate-pulse">
              <div class="h-10 w-full bg-gray-300 rounded"></div>
            </div>
          </div>
        </div>

        <div
          v-else
          class="flex flex-col items-center"
        >
          <div
            v-for="product in products"
            :key="product.id"
            class="flex flex-col p-5 mt-6 w-full bg-white rounded-3xl border border-solid !border-gray-300"
          >
            <div class="justify-between py-1">
              <div class="text-sm font-medium text-zinc-600 -mt-5 mb-4 inline-flex items-center gap-2">
                <IconStethoscope
                  class="size-5 inline-block"
                  stroke-width="2"
                />
                <span>Provider Recommended</span>
              </div>
              <div class="flex gap-5 max-md:flex-col max-md:gap-0">
                <div class="flex flex-col w-full max-md:ml-0 max-md:w-full">
                  <div class="flex flex-col-reverse sm:flex-row gap-5 grow justify-between items-top text-sm leading-5 text-black">
                    <div class="grow">
                      <div class="text-2xl font-semibold tracking-tight leading-5 mb-3">
                        {{ product.product_name }}
                      </div>
                      <div class="flex flex-wrap gap-2 mb-4">
                        <span class="bg-gray-500 px-2 py-0.5 rounded-full text-sm font-medium text-white">
                          Most Popular
                        </span>
                        <span class="bg-gray-200 px-2 py-0.5 rounded-full text-sm font-medium text-black">
                          {{ product.strength }} {{ product.strength_unit }}
                        </span>
                        <span class="bg-gray-200 px-2 py-0.5 rounded-full text-sm font-medium text-black">
                          <span>{{ product.qty }} units/mo</span>
                        </span>
                        <span
                          v-if="product.base_price_per_pill"
                          class="bg-gray-200 px-2 py-0.5 rounded-full text-sm font-medium text-black"
                        >
                          {{ formatCurrency(product.base_price_per_pill) }}/unit
                        </span>
                      </div>
                      <div
                        v-if="product.product_description"
                        class="text-zinc-600 custom-prose mb-4"
                        v-html="product.product_description"
                      ></div>
                      <div v-if="!isEmpty(product.best_for)">
                        <div class="text-sm font-medium text-gray-700 mb-1">
                          Perfect For
                        </div>
                        <div class="flex gap-2">
                          <span
                            v-for="bestFor in bestForArr(product.best_for)"
                            :key="bestFor"
                            class="bg-green-200 text-sm font-medium px-2 py-0.5 rounded-lg"
                          >{{ bestFor }}</span>
                        </div>
                      </div>
                    </div>
                    <div class="!rounded-xl bg-gray-100 sm:bg-transparent">
                      <img
                        :src="product.strength_image"
                        class="min-h-24 min-w-24 w-full sm:h-24 sm:w-24 object-contain rounded-lg max-h-52"
                        alt=""
                      >
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <TwButton
              class="mt-6 w-full"
              :disabled="!isEmpty(btnLoading)"
              :loading="btnLoading === product.id"
              @click="continueWithRecommended(product)"
            >
              Continue
            </TwButton>
          </div>

          <div class="inline-flex items-center justify-center w-full my-10">
            <hr class="w-64 h-px bg-gray-200 border-0">
            <span class="absolute px-3 font-medium text-gray-900 -translate-x-1/2 bg-white left-1/2">or</span>
          </div>

          <button
            class="flex justify-center items-center p-5 w-full bg-gradient-to-r from-[#fff8f1] to-white rounded-full border border-solid !border-zinc-300 hover:from-white hover:to-[#fff8f1] transition-all"
            @click="exploreOtherOptions"
          >
            <span class="font-medium text-base">Explore all treatments</span>
            <IconArrowRight
              class="h-5 w-5 ms-2"
              stroke-width="2"
            />
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
