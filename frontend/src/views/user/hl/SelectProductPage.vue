<script setup>
import { isEmpty } from '@/@core/utils'
import { usePreventNavigation } from '@/composables/usePreventNavigation'
import router from '@/router'
import { useHlProductsStore } from '@/store/hlProducts'
import { IconBolt } from '@tabler/icons-vue'
import { useSessionStorage } from '@vueuse/core'
import { storeToRefs } from 'pinia'
import { onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import Header from '../components/Header.vue'
import { formatCurrency } from '@/utils/helpers'
import useLogOrderSession from '@/composables/useLogOrderSession'

usePreventNavigation(true)

const route = useRoute()
const hlProductsStore = useHlProductsStore()
const { logSession } = useLogOrderSession()

const {
  products,
  hasRecommendations,
  loading: isLoading,
  errors: serverErrors,
} = storeToRefs(hlProductsStore)

const hlVisitSession = useSessionStorage('hlVisitSession', {})
const btnLoading = ref('')

onMounted(async () => {
  if (products.value.length === 0) {
    await hlProductsStore.fetchProducts()
  }
})

async function handleProductSelection(product) {
  btnLoading.value = product.id
  hlVisitSession.value['productId'] = product.id

  await router.push({ name: 'hl-visit-select-strength', query: { ...route.query } })

  logSession({
    id: hlVisitSession.value['questionSessionId'],
    category: 'HL',
    product: {
      id: hlVisitSession.value['productId'] ?? null,
      strength_id: hlVisitSession.value['strengthId'] ?? null,
      qty_id: hlVisitSession.value['productQty'] ?? null,
    },
  })
}

function seeRecommendation() {
  hlVisitSession.value['productId'] = null
  hlVisitSession.value['strengthId'] = null
  hlVisitSession.value['productQty'] = null
  router.push({ name: 'hl-visit-product-recommended' })
}

function bestForArr(str) {
  return str.split(',').map(s => s.trim())
}
</script>

<template>
  <div>
    <Header />

    <div class="flex items-center justify-center py-10 sm:px-16">
      <div class="px-4 w-full sm:max-w-[480px]">
        <div
          v-if="isLoading"
          class="h-7 bg-gray-300 rounded mb-2 animate-pulse"
        ></div>
        <h2
          v-else
          class="text-center text-xl sm:text-2xl md:text-3xl font-bold leading-tight text-black mb-3"
        >
          Which treatment option best fits your preference?
        </h2>

        <div
          v-if="isLoading"
          class="flex flex-col items-center"
        >
          <div
            v-for="i in 2"
            :key="i"
            class="flex flex-col p-5 mt-6 w-full bg-white rounded-3xl border border-solid !border-zinc-300"
          >
            <!-- Product Info Skeleton Loader -->
            <div class="animate-pulse">
              <div class="justify-between py-1">
                <div class="flex gap-5 max-md:flex-col max-md:gap-0">
                  <div class="flex flex-col w-full max-md:ml-0 max-md:w-full">
                    <div class="flex flex-col gap-5 grow justify-between items-top text-sm leading-5 text-black">
                      <div class="bg-gray-300 rounded-lg flex justify-center align-center h-48"></div> <!-- Image Skeleton Loader -->
                      <div class="grow">
                        <div class="h-6 w-4/5 bg-gray-300 rounded mb-2"></div> <!-- Title Skeleton Loader -->
                        <div class="h-5 w-36 bg-gray-300 rounded mb-2"></div> <!-- Price Skeleton Loader -->
                        <div class="h-3 w-40 bg-gray-300 rounded mb-2"></div> <!-- Description Skeleton Loader -->
                        <div class="h-3 w-32 bg-gray-300 rounded"></div> <!-- Description Skeleton Loader -->
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="mt-5 animate-pulse">
              <div class="h-10 w-full bg-gray-300 rounded"></div>
            </div>
          </div>
        </div>

        <div
          v-else
          class="flex flex-col items-center"
        >
          <div
            v-for="product in products"
            :key="product.id"
            class="flex flex-col p-5 mt-6 w-full bg-white rounded-3xl border border-solid !border-zinc-300"
          >
            <div class="justify-between py-1">
              <div class="flex gap-5 max-md:flex-col max-md:gap-0">
                <div class="flex flex-col w-full max-md:ml-0 max-md:w-full">
                  <div class="flex flex-col gap-5 grow justify-between items-top text-sm leading-5 text-black">
                    <div class="bg-gray-100 rounded-lg flex justify-center align-center h-48 p-4">
                      <img
                        :src="product.product_image"
                        class="h-full w-full object-contain rounded-lg"
                        alt=""
                      >
                    </div>
                    <div class="grow">
                      <div class="text-2xl font-semibold tracking-tight leading-5 mb-3">
                        {{ product.product_name }}
                      </div>
                      <div
                        v-if="product.product_description"
                        class="text-zinc-600 custom-prose mb-4"
                        v-html="product.product_description"
                      ></div>
                      <div v-if="!isEmpty(product.best_for)">
                        <div class="text-sm font-medium text-gray-700 mb-1">
                          Perfect For
                        </div>
                        <div class="flex gap-2">
                          <span
                            v-for="bestFor in bestForArr(product.best_for)"
                            :key="bestFor"
                            class="bg-green-200 text-sm font-medium px-2 py-0.5 rounded-lg"
                          >{{ bestFor }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <TwButton
              type="button"
              class="mt-5 w-full"
              :loading="btnLoading === product.id"
              :disabled="!isEmpty(btnLoading)"
              @click="handleProductSelection(product)"
            >
              Continue&nbsp;<span v-if="product.base_price_per_pill">({{ formatCurrency(product.base_price_per_pill) }}/unit)</span>
            </TwButton>
          </div>

          <div
            v-if="hasRecommendations"
            class="inline-flex items-center justify-center w-full my-10"
          >
            <hr class="w-64 h-px bg-gray-200 border-0">
            <span class="absolute px-3 font-medium text-gray-900 -translate-x-1/2 bg-white left-1/2">or</span>
          </div>

          <div
            v-if="hasRecommendations"
            class="flex flex-col p-5 w-full bg-gradient-to-r from-[#fff8f1] to-white rounded-3xl border border-solid !border-zinc-300"
          >
            <div class="">
              <div class="text-xl tracking-tight leading-6 text-black font-semibold">
                Indifferent to medication&nbsp;choices?
              </div>
              <div class="text-sm leading-5 text-black">
                View popular suggestions.
              </div>
              <button
                class="w-full flex gap-1.5 justify-center px-4 py-3 text-base font-semibold leading-3 text-black bg-[#ffef08] border-2 border-solid border-[#BAB010] rounded-full hover:brightness-90 transition-all mt-5"
                @click="seeRecommendation"
              >
                <div class="font-semibold leading-4">
                  Get&nbsp;recommendation
                </div>
                <IconBolt
                  class="h-4 w-4"
                  stroke-width="1"
                  fill="currentColor"
                />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
