<script setup>
import { isEmpty, isEmptyObject } from '@/@core/utils'
import router from '@/router'
import { useHlProductsStore } from '@/store/hlProducts'
import { useSessionStorage } from '@vueuse/core'
import { storeToRefs } from 'pinia'
import { onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import Header from '../components/Header.vue'
import useLogOrderSession from '@/composables/useLogOrderSession'
import { formatCurrency } from '@/utils/helpers'

const route = useRoute()
const hlProductsStore = useHlProductsStore()
const { logSession } = useLogOrderSession()

const {
  products,
  loading: isLoading,
  errors: serverErrors,
} = storeToRefs(hlProductsStore)

const hlVisitSession = useSessionStorage('hlVisitSession', {})
const btnLoading = ref('')

onMounted(async () => {
  if (products.value.length === 0) {
    await hlProductsStore.fetchProducts()
  }
  if (isEmptyObject(hlVisitSession.value)) {
    router.replace({ name: 'hl-visit-select-product', query: { ...route.query } })
  }
})

const currentProduct = computed(() => {
  return products.value.find(p => p.id === hlVisitSession.value['productId'])
})

const strengths = computed(() => {
  return currentProduct.value?.hl_product_strengths
})

const recommendedStrengths = computed(() => strengths.value?.filter(s => s.is_recommended === 1))

const otherStrengths = computed(() => strengths.value?.filter(s => s.is_recommended === 0))

async function handleStrengthSelection(strength) {
  btnLoading.value = strength.id
  hlVisitSession.value['strengthId'] = strength.id
  hlVisitSession.value['productQty'] = strength.hl_product_strength_qty_id

  await router.push({
    name: 'visit-shipping',
    params: { visitType: 'hl' },
    query: { ...route.query },
  })

  logSession({
    id: hlVisitSession.value['questionSessionId'],
    category: 'HL',
    product: {
      id: hlVisitSession.value['productId'] ?? null,
      strength_id: hlVisitSession.value['strengthId'] ?? null,
      qty_id: hlVisitSession.value['productQty'] ?? null,
    },
  })
}
</script>

<template>
  <div>
    <Header show-back-button />

    <div class="flex items-center justify-center py-10 sm:px-16">
      <div class="px-4 w-full sm:max-w-[480px]">
        <div
          v-if="isLoading"
          class="h-7 bg-gray-300 rounded mb-2 animate-pulse"
        ></div>
        <h2
          v-else
          class=" text-xl md:text-3xl font-bold leading-tight text-black mb-3"
        >
          Do you prefer a specific dosage for your medication?
        </h2>

        <p
          v-if="!isLoading"
          class="text-sm text-zinc-700 font-medium mb-6"
        >
          Your preference will influence our provider's treatment choice.
        </p>

        <div
          v-if="isLoading"
          class="flex flex-col items-center"
        >
          <div
            v-for="i in 2"
            :key="i"
            class="flex flex-col p-5 mt-6 w-full bg-white rounded-3xl border border-solid !border-zinc-300"
          >
            <!-- Product Info Skeleton Loader -->
            <div class="animate-pulse">
              <div class="justify-between py-1">
                <div class="flex gap-5 max-md:flex-col max-md:gap-0">
                  <div class="flex flex-col w-full max-md:ml-0 max-md:w-full">
                    <div class="flex grow justify-between items-top text-sm leading-5 text-black">
                      <div class="grow">
                        <div class="h-6 w-4/5 bg-gray-300 rounded mb-2"></div> <!-- Title Skeleton Loader -->
                        <div class="h-6 w-[50px] bg-gray-300 rounded mb-2"></div> <!-- Title Skeleton Loader -->
                        <div class="h-5 w-36 bg-gray-300 rounded"></div> <!-- Price Skeleton Loader -->
                      </div>
                      <div>
                        <div class="h-24 w-24 bg-gray-300 rounded-lg"></div> <!-- Image Skeleton Loader -->
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="mt-5 animate-pulse">
              <div class="h-10 w-full bg-gray-300 rounded"></div>
            </div>
          </div>
        </div>

        <div v-else>
          <div
            v-if="recommendedStrengths && recommendedStrengths.length > 0"
            class="w-full flex flex-col"
          >
            <div class="text-base font-medium italic text-zinc-600 mb-3">
              If you're new to treatment, start with:
            </div>
            <div
              v-for="strength in recommendedStrengths"
              :key="strength.id"
              class="flex flex-col p-5 mb-6 w-full bg-gradient-to-r from-[#fff8f1] to-white rounded-3xl border border-solid !border-zinc-300"
            >
              <div class="justify-between py-1">
                <div class="flex gap-5 max-md:flex-col max-md:gap-0">
                  <div class="flex flex-col w-full max-md:ml-0 max-md:w-full">
                    <div class="flex grow justify-between items-top text-sm leading-5 text-black">
                      <div class="grow">
                        <div class="text-xl font-semibold tracking-tight leading-5 mb-1">
                          {{ currentProduct.product_name }}
                        </div>
                        <p class="text-zinc-600 text-lg font-medium">
                          {{ strength.strength }} {{ strength.strength_unit }}
                        </p>
                        <div class="justify-center mt-3">
                          <span class="px-3 py-1.5 bg-sky-100 rounded-lg text-sm text-gray-700 font-medium">
                            {{ formatCurrency(strength.base_price_per_pill) }}/unit
                          </span>
                        </div>
                      </div>
                      <div class="ms-2">
                        <img
                          :src="strength.strength_image"
                          class="min-h-24 min-w-24 h-24 w-24 object-contain rounded-lg"
                          alt=""
                        >
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <TwButton
                type="button"
                class="mt-5 w-full"
                :loading="btnLoading === strength.id"
                :disabled="!isEmpty(btnLoading)"
                @click="handleStrengthSelection(strength)"
              >
                Select
              </TwButton>
            </div>
          </div>

          <div
            v-if="otherStrengths && otherStrengths.length > 0"
            class="w-full flex flex-col mt-5"
          >
            <div
              v-if="recommendedStrengths && recommendedStrengths.length > 0"
              class="text-base font-medium italic text-zinc-600 mb-3"
            >
              If you know what works for you:
            </div>
            <div
              v-for="strength in otherStrengths"
              :key="strength.id"
              class="flex flex-col p-5 mb-6 w-full bg-white rounded-3xl border border-solid !border-zinc-300"
            >
              <div class="justify-between py-1">
                <div class="flex gap-5 max-md:flex-col max-md:gap-0">
                  <div class="flex flex-col w-full max-md:ml-0 max-md:w-full">
                    <div class="flex grow justify-between items-top text-sm leading-5 text-black">
                      <div class="grow">
                        <div class="text-xl font-semibold tracking-tight leading-5 mb-1">
                          {{ currentProduct.product_name }}
                        </div>
                        <p class="text-zinc-600 text-lg font-medium">
                          {{ strength.strength }} {{ strength.strength_unit }}
                        </p>
                        <div class="justify-center mt-3">
                          <span class="px-3 py-1.5 bg-sky-100 rounded-lg text-sm text-gray-700 font-medium">
                            {{ formatCurrency(strength.base_price_per_pill) }}/unit
                          </span>
                        </div>
                      </div>
                      <div class="ms-2">
                        <img
                          :src="strength.strength_image"
                          class="min-h-24 min-w-24 h-24 w-24 object-contain rounded-lg"
                          alt=""
                        >
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <TwButton
                type="button"
                class="mt-5 w-full"
                :loading="btnLoading === strength.id"
                :disabled="!isEmpty(btnLoading)"
                @click="handleStrengthSelection(strength)"
              >
                Continue
              </TwButton>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
