<script setup>
import { isEmpty, isEmptyObject } from '@/@core/utils'
import useGlobalSettings from '@/composables/useGlobalSettings'
import useLogOrderSession from '@/composables/useLogOrderSession'
import router from '@/router'
import ApiService from '@/services/ApiService'
import { useHlProductsStore } from '@/store/hlProducts'
import { processErrors } from '@/utils/errorHandler'
import { ceilToDecimalPlaces, formatCurrency, scrollToTop } from '@/utils/helpers'
import { clearOrderSession } from '@/utils/sessionHelpers'
import AlertError from '@/views/user/components/AlertError.vue'
import Header from '@/views/user/components/Header.vue'
import { loadScript } from '@paypal/paypal-js'
import { IconCheck, IconCreditCardOff, IconInfoCircle, IconShieldLock, IconVideo } from '@tabler/icons-vue'
import { useSessionStorage } from '@vueuse/core'
import Sidebar from 'primevue/sidebar'
import { ErrorMessage, Field, Form as VForm } from 'vee-validate'
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { toast } from 'vue-sonner'
import * as yup from 'yup'
import ConsultFeeSidebar from '@/views/user/components/ConsultFeeSidebar.vue'
import useDocuments from '@/composables/useDocuments'

// import PaymentRefundSidebar from '@/views/user/components/PaymentRefundSidebar.vue'

const $cookies = inject('$cookies')
const route = useRoute()
const hlProductsStore = useHlProductsStore()
const { logSession } = useLogOrderSession()
const { documentUploadStage } = useGlobalSettings()
const { isIdUploaded } = useDocuments()

const {
  products,
  loading: productLoading,
  errors: productErrors,
} = storeToRefs(hlProductsStore)

const hlVisitSession = useSessionStorage('hlVisitSession', {})
const shippingDetailsSession = useSessionStorage('shippingDetailsSession', {})
const isLoading = ref(false)
const skeletonLoading = ref(false)
const planLoading = ref(false)
const serverErrors = ref([])
const subscriptionPlans = ref([])
const selectedSubscriptionPlanId = ref(null)
const isCardFormVisible = ref(false)
const isEditDrugSidebarVisible = ref(false)
const editStrengthId = ref(null)
const editQtyId = ref(null)

// const paymentAndRefundSidebarVisible = ref(false)
const consultFeeSidebarVisible = ref(false)
const checkoutSummary = ref({})
const paymentMethods = ref([])
const selectedPaymentMethod = ref(null)
const promoCode = useSessionStorage('hlCheckoutPromoCode', null)
const showPromoInput = ref(false)
const promoCodeError = ref('')
const isPromoAdded = ref(false)
const promoCodeInputRef = ref(null)
const retryOrderId = useSessionStorage('hlRetryOrderId', null)
const retryOrderSummary = ref(null)
const showPaymentMethods = ref(true)
const showBackButton = ref(true)

const isLocalPharmacySelected = computed(() => {
  return shippingDetailsSession.value['selectedShippingMethod'] === 'local_pickup'
})

onMounted(async () => {
  skeletonLoading.value = true

  if (isEmpty(retryOrderId.value) && isEmptyObject(hlVisitSession.value)) {
    router.replace({ name: 'hl-visit-product-recommended', query: { ...route.query } })
  }

  if (retryOrderId.value) {
    preventBackNavigation()
    await getFailedOrderSummary()
  } else {
    if (products.value.length === 0) {
      hlProductsStore.fetchProducts()
    }
    await getSubscriptionPlans()
  }

  skeletonLoading.value = false
})

async function getSubscriptionPlans() {
  try {
    serverErrors.value = []

    const { data } = await ApiService.get(`/fetch-hl-product-subscription-plans/${hlVisitSession.value['productQty']}`)

    if (data.status === 200) {
      if (isEmpty(data.subscriptionPlans)) {
        router.push({ name: 'error-something-wrong', query: { visit: 'hl' } })

        return
      }

      subscriptionPlans.value = data.subscriptionPlans

      const recommendedPlan = subscriptionPlans.value.find(plan => plan.is_recommended === 1)

      selectedSubscriptionPlanId.value = recommendedPlan ? recommendedPlan.id : subscriptionPlans.value[0].id

      await preCheckout()
    } else {
      serverErrors.value = processErrors(data)
    }
  } catch (error) {
    serverErrors.value = processErrors(error)
    console.error(error)
  }
}

watch(promoCode, () => {
  promoCode.value = promoCode.value?.toUpperCase()
})

function applyPromoCode() {
  promoCodeError.value = ''

  if (isEmpty(promoCode.value)) {
    promoCodeError.value = 'Please enter the promo code'
  } else {
    preCheckout()
  }
}

function discardPromoApply() {
  showPromoInput.value = false
  promoCode.value = null
  promoCodeError.value = ''
}

function removePromoCode() {
  promoCode.value = null
  isPromoAdded.value = false
  preCheckout()
}

async function preCheckout() {
  try {
    planLoading.value = true
    serverErrors.value = []

    const postData = {}

    postData['category'] = 'HL'
    postData['qa_id'] = hlVisitSession.value['questionSessionId']
    postData['pharmacy_shipping_method_id'] = shippingDetailsSession.value['selectedShippingMethod']
    postData['treatment_visit_type'] = hlVisitSession.value['visitType']
    postData['followup_visit_id'] = hlVisitSession.value['visitType'] === 'followup' ? hlVisitSession.value['subscriptionId'] : undefined

    if (!isEmpty(promoCode.value)) {
      postData['promocode'] = promoCode.value
    }

    if (isLocalPharmacySelected.value) {
      // local pharmacy order
      postData['local_pickup_pharmacy_address'] = extractLocalPharmacyAddress()
      postData['hl_product_strength_id'] = hlVisitSession.value['strengthId']
    } else {
      // normal subscription order
      postData['subscription_plan_id'] = selectedSubscriptionPlanId.value
    }

    const { data } = await ApiService.post('/hl-pre-checkout', postData)

    if (data.status === 200) {
      checkoutSummary.value = data.subscriptionPlanSummary

      if (
        !isEmpty(data.subscriptionPlanSummary.is_promocode_invalid)
        && data.subscriptionPlanSummary.is_promocode_invalid === 1
      ) {
        promoCode.value = null
        promoCodeError.value = data.subscriptionPlanSummary.invalid_promocode_msg
        isPromoAdded.value = false
      } else if (!isEmpty(data.subscriptionPlanSummary.promocode)) {
        promoCodeError.value = ''
        showPromoInput.value = false
        isPromoAdded.value = true
      } else {
        promoCode.value = null
        promoCodeError.value = ''
        isPromoAdded.value = false
      }

      if (!isEmpty(data.paymentMethods)) {
        paymentMethods.value = data.paymentMethods

        const defaultPaymentMethod = paymentMethods.value.find(pm => pm.is_default === 1 && !isCardExpired(pm))
        if (defaultPaymentMethod) {
          selectedPaymentMethod.value = defaultPaymentMethod.id
        } else {
          const firstPaymentMethod = paymentMethods.value[0]
          if (!isCardExpired(firstPaymentMethod)) {
            selectedPaymentMethod.value = firstPaymentMethod.id
          }
        }
      }
    } else {
      if (data.status === 400) {
        if (Boolean(data.treatment_not_available)) {
          return router.push({ name: 'hl-treatment-not-available' })
        } else if (Boolean(data.user_has_an_active_plan)) {
          return router.push({ name: 'hl-visit-exists' })
        }
      }
      serverErrors.value = processErrors(data)
    }
  } catch (error) {
    serverErrors.value = processErrors(error)
    console.error(error)
  } finally {
    planLoading.value = false
  }
}

async function getFailedOrderSummary() {
  try {
    showPaymentMethods.value = false
    serverErrors.value = []

    const { data } = await ApiService.get(`/hl-failed-payment-order-summary/${retryOrderId.value}`)

    if (data.status === 200) {
      retryOrderSummary.value = data.orderSummary

      if (!isEmpty(data.paymentMethods)) {
        paymentMethods.value = data.paymentMethods

        const defaultPaymentMethod = paymentMethods.value.find(pm => pm.is_default === 1 && !isCardExpired(pm))
        if (defaultPaymentMethod) {
          selectedPaymentMethod.value = defaultPaymentMethod.id
        } else {
          const firstPaymentMethod = paymentMethods.value[0]
          if (!isCardExpired(firstPaymentMethod)) {
            selectedPaymentMethod.value = firstPaymentMethod.id
          }
        }
      }
    } else {
      serverErrors.value = processErrors(data)
    }
  } catch (error) {
    serverErrors.value = processErrors(error)
    console.error(error)
  }
}

const switchToOurPharmacy = () => {
  shippingDetailsSession.value['selectedShippingMethod'] = shippingDetailsSession.value['switchToId']
  preCheckout()
  scrollToTop()

  logSession({
    id: hlVisitSession.value['questionSessionId'],
    category: 'HL',
    shipping: {
      id: shippingDetailsSession.value['selectedShippingMethod'],
    },
  })
}

function extractLocalPharmacyAddress() {
  const selectedPharmacy = shippingDetailsSession.value['selectedPharmacy']

  return {
    'pharmacyId': selectedPharmacy['PharmacyId'],
    'pharmacy_name': selectedPharmacy['StoreName'],
    'pharmacy_zipcode': selectedPharmacy['ZipCode'],
  }
}

const currentProduct = computed(() => {
  return products.value.find(p => p.id === hlVisitSession.value['productId'])
})

const selectedStrength = computed(() => {
  return currentProduct.value?.hl_product_strengths?.find(s => s.id === hlVisitSession.value['strengthId'])
})

const selectedPlan = computed(() => {
  return subscriptionPlans.value.find(plan => plan.id === selectedSubscriptionPlanId.value)
})

function calculateAnnualSavings(plan) {
  if (plan.interval <= 0) {
    return 0
  }

  const intervalsPerYear = 12 / plan.interval
  const savingsPerInterval = plan.plan_interval_price - plan.subscription_plan_price_after_discount
  const annualSavings = intervalsPerYear * savingsPerInterval

  return Math.ceil(annualSavings)
}

const percentageSaved = computed(() => {
  const totalPrice = selectedPlan.value?.plan_interval_price
  const discountedPrice = selectedPlan.value?.subscription_plan_price_after_discount

  if (
    checkoutSummary.value.subscription_plan_amount < totalPrice &&
    shippingDetailsSession.value['selectedShippingMethod'] !== 'local_pickup'
  ) {
    const amountSaved = totalPrice - discountedPrice
    const percentage = (amountSaved / totalPrice) * 100

    return `You're saving ${percentage.toFixed(0)}%`
  } else {
    return null
  }
})

const cardFieldErrors = ref([])
const cardFieldsRendered = ref(false)

async function initPaypal() {
  isCardFormVisible.value = true
  selectedPaymentMethod.value = null

  if (cardFieldsRendered.value) return
  isLoading.value = true

  const paypal = await loadScript({
    'client-id': (import.meta.env.VITE_PAYPAL_CLIENT_ID),
    components: 'card-fields',
  }).catch(error => {
    console.error('paypal error :::', error)
  })

  const inputStyles = {
    'input': {
      'padding': '0.75rem 1.25rem',
      'font-size': '1rem',
      'font-weight': '500',
      'font-family': 'Konnect, system-ui, sans-serif',
      'appearance': 'none',
      'outline': 'none',
      'transition': 'border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out',
    },
  }

  const cardFields = paypal.CardFields({
    style: inputStyles,
    createVaultSetupToken: async () => {
      const { data } = await ApiService.get('/paypal/vault/token/create')
      if (data.status === 200) return data.vaultId
      else throw data
    },
    onApprove: async data => {
      const vaultId = data.vaultSetupToken
      const response = await ApiService.get(`/paypal/vault/payment/token/create/${vaultId}`)
      if (response.data.status === 200) {
        selectedPaymentMethod.value = response.data.id
        await placeOrder()
      } else {
        throw response.data
      }
    },
    onError: async error => {
      // console.log(error)
      if (!retryOrderId.value && !retryOrderSummary.value) {
        // when not retry call then calling place order API on card failure
        await placeOrder()
      } else {
        // if card failure is on retry flow then show error message
        if (error.message === 'Internal server error') {
          cardFieldErrors.value.push('Please check your card details and try again.')
        } else {
          cardFieldErrors.value.push(error.message || 'Something went wrong. Please try again.')
        }
      }
    },
  })

  if (cardFields.isEligible()) {
    // cardFields.NameField().render("#card-holder-name")
    cardFields.NumberField().render('#card-number')
    cardFields.ExpiryField().render('#expiration-date')
    cardFields.CVVField().render('#cvv')

    isLoading.value = false
    cardFieldsRendered.value = true
  } else {
    // Handle the workflow when credit and debit cards are not available
    toast.error('Credit and debit cards are not available. Please try again later.')
  }

  const submitButton = document.getElementById('checkoutSubmitBtn')

  submitButton.addEventListener('click', () => {
    isLoading.value = true
    serverErrors.value = []
    cardFieldErrors.value = []

    cardFields
      .submit()
      .then(() => {
        // console.log("submit was successful")
      })
      .catch(error => {
        isLoading.value = false
        if (error.message === 'INVALID_NUMBER') {
          cardFieldErrors.value.push('Please enter a valid card number.')
        }
        if (error.message === 'INVALID_CVV') {
          cardFieldErrors.value.push('Please enter a valid CVV number.')
        }
        if (error.message === 'INVALID_EXPIRY') {
          cardFieldErrors.value.push('Please enter a valid expiration date.')
        }
      })
  })
}

function selectExistingPaymentMethod() {
  isCardFormVisible.value = false
  cardFieldErrors.value = []
  if (paymentMethods.value.length > 0) {
    selectedPaymentMethod.value = paymentMethods.value[0].id
  }
}

async function placeOrder() {
  try {
    isLoading.value = true
    serverErrors.value = []

    const postData = {}

    if (retryOrderId.value) {
      // payment has failed once so only send this data to server
      postData['order_id'] = retryOrderId.value
      postData['paypal_card_id'] = selectedPaymentMethod.value
    } else {
      // new order postData
      postData['category'] = 'HL'
      postData['qa_id'] = hlVisitSession.value['questionSessionId']
      postData['paypal_card_id'] = selectedPaymentMethod.value
      postData['pharmacy_shipping_method_id'] = shippingDetailsSession.value['selectedShippingMethod']
      postData['treatment_visit_type'] = hlVisitSession.value['visitType']
      postData['followup_visit_id'] = hlVisitSession.value['visitType'] === 'followup' ? hlVisitSession.value['subscriptionId'] : undefined
      postData['previous_subscription_id'] = hlVisitSession.value['visitType'] === 'renew' ? hlVisitSession.value['subscriptionId'] : undefined

      if (isLocalPharmacySelected.value) {
        // local pharmacy order
        postData['local_pickup_pharmacy_address'] = extractLocalPharmacyAddress()
        postData['hl_product_strength_id'] = hlVisitSession.value['strengthId']
      } else {
        // normal subscription order
        postData['subscription_plan_id'] = selectedSubscriptionPlanId.value
      }

      if (!isEmpty(promoCode.value)) {
        postData['promocode'] = promoCode.value
      }

      if ($cookies.isKey('hl_affiliate_code')) {
        postData['affiliate_reference_code'] = $cookies.get('hl_affiliate_code')
      }
    }

    const { data } = await ApiService.post('/hl-place-order', postData)

    if (data.status === 200) {
      if (documentUploadStage.value === 'before') {
        if (data.is_sync_visit) {
          router.push({
            name: 'schedule-video-visit',
            params: { visitType: 'hl', orderId: data.orderId },
            query: { ...route.query },
          })
        } else {
          router.push({
            name: 'hl-visit-order-success',
            params: { orderId: data.orderId },
            query: { ...route.query },
          })
        }
      } else {
        if (isIdUploaded.value) {
          if (data.is_sync_visit) {
            router.push({
              name: 'schedule-video-visit',
              params: { visitType: 'hl', orderId: data.orderId },
              query: { ...route.query },
            })
          } else {
            router.push({
              name: 'hl-visit-order-success',
              params: { orderId: data.orderId },
              query: { ...route.query },
            })
          }
        } else {
          router.push({
            name: 'visit-purchase-success',
            params: { visitType: 'hl' },
            query: { ...route.query, order_id: data.orderId, is_sync_visit: data.is_sync_visit },
          })
        }
      }
    } else {
      if (data.status === 400) {
        if (Boolean(data.treatment_not_available)) {
          return router.push({ name: 'hl-treatment-not-available' })
        } else if (Boolean(data.user_has_an_active_plan)) {
          return router.push({ name: 'hl-visit-exists' })
        }
      }

      if (data.failedPayment === 1 && !isEmpty(data.order_id)) {
        retryOrderId.value = data.order_id
        preventBackNavigation()
        await getFailedOrderSummary()
      } else {
        serverErrors.value = processErrors(data)
        scrollToTop()
      }
      isLoading.value = false
    }
  } catch (error) {
    isLoading.value = false
    console.error(error)
    serverErrors.value = processErrors(error)
    scrollToTop()
  }
}

function preventBackNavigation() {
  showBackButton.value = false
  window.addEventListener('beforeunload', onLoadListener)
  window.addEventListener('popstate', async () => {
    await router.replace({ name: 'user-orders' })
    clearOrderSession()
  })
}

function onLoadListener(e) {
  e.preventDefault()

  const message = 'You have unsaved changes. Are you sure you wish to leave?'

  e.returnValue = message

  return message
}

onUnmounted(() => {
  window.removeEventListener('beforeunload', onLoadListener)
})

const editDrugValidationSchema = yup.object().shape({
  edit_strength: yup.string().required('Strength is required'),
})

const availableStrengths = computed(() => {
  return currentProduct.value?.hl_product_strengths
})

function openEditMedicationSidebar() {
  isEditDrugSidebarVisible.value = true
  editStrengthId.value = selectedStrength.value?.id
  editQtyId.value = selectedStrength.value?.hl_product_strength_qty_id
}

function handleMedicationChange() {
  hlVisitSession.value['strengthId'] = editStrengthId.value
  hlVisitSession.value['productQty'] = editQtyId.value
  isEditDrugSidebarVisible.value = false
  planLoading.value = true
  getSubscriptionPlans()
}

function isCardExpired(paymentMethod) {
  const now = new Date()
  const expirationDate = new Date(paymentMethod.card_expiry_year, paymentMethod.card_expiry_month - 1)

  return now > expirationDate
}
</script>

<template>
  <div>
    <Header :show-back-button="showBackButton" />

    <div
      v-if="skeletonLoading || productLoading"
      class="flex items-center justify-center py-8 sm:px-16"
    >
      <div class="px-4 w-full sm:max-w-[480px]">
        <div class="h-7 bg-gray-300 rounded mb-3 animate-pulse"></div>
        <div class="flex flex-col sm:p-5 mt-6 w-full bg-white rounded-3xl sm:border sm:border-solid sm:!border-gray-300">
          <div class="h-14 bg-gray-300 rounded mb-2 animate-pulse"></div>
          <hr>
          <div class="h-5 bg-gray-300 w-[50%] rounded mt-6 mb-4 animate-pulse"></div>
          <div class="h-12 bg-gray-300 rounded mb-4 animate-pulse"></div>
          <div class="h-12 bg-gray-300 rounded mb-4 animate-pulse"></div>
          <div class="h-12 bg-gray-300 rounded mb-6 animate-pulse"></div>
          <div class="h-10 bg-gray-300 rounded mb-4 animate-pulse"></div>
          <div class="h-10 bg-gray-300 rounded mb-4 animate-pulse"></div>
          <div class="h-12 bg-gray-300 rounded-xl mt-8 mb-2 animate-pulse"></div>
        </div>
      </div>
    </div>

    <div
      v-else
      class="flex items-center justify-center py-6 sm:px-16"
    >
      <div class="px-4 w-full sm:max-w-[480px]">
        <div class="text-center mb-3">
          <h2 class="text-xl md:text-3xl font-bold leading-tight text-black">
            Complete Your Order
          </h2>
          <p class="text-sm text-zinc-700 mt-2">
            Secure checkout for your erectile dysfunction treatment
          </p>
        </div>

        <AlertError
          v-if="serverErrors.length > 0"
          title="Error!"
          :errors="serverErrors"
        />

        <div class="flex flex-col sm:p-5 mt-6 w-full bg-white rounded-3xl sm:border sm:border-solid sm:!border-gray-300">
          <!-- Retry Order Product Summary -->
          <div
            v-if="retryOrderId && retryOrderSummary"
            class="justify-between py-1"
          >
            <div class="flex gap-5 max-md:flex-col max-md:gap-0">
              <div class="flex flex-col w-full max-md:ml-0 max-md:w-full">
                <div class="flex grow gap-3 justify-between items-top text-sm leading-5 text-black">
                  <div class="ms-2">
                    <img
                      :src="retryOrderSummary.image"
                      class="min-h-12 min-w-12 h-12 w-12 object-contain rounded-lg"
                      alt=""
                    >
                  </div>
                  <div class="grow">
                    <div class="text-xl font-semibold tracking-tight leading-5 mb-2">
                      {{ retryOrderSummary.product_name }}
                    </div>
                    <div class="flex flex-wrap gap-2">
                      <span class="bg-black px-2 py-1 rounded-md text-xs font-medium text-white">
                        {{ retryOrderSummary.strength }}
                      </span>
                      <span class="bg-black px-2 py-1 rounded-md text-xs font-medium text-white">
                        {{ retryOrderSummary.qty }} units
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- New Order Summary -->
          <div
            v-else
            class="justify-between py-1"
          >
            <div class="flex gap-5 max-md:flex-col max-md:gap-0">
              <div class="flex flex-col w-full max-md:ml-0 max-md:w-full">
                <div class="flex grow gap-3 justify-between items-top text-sm leading-5 text-black">
                  <div class="ms-2">
                    <img
                      :src="selectedStrength?.strength_image"
                      class="min-h-12 min-w-12 h-12 w-12 object-contain rounded-lg"
                      alt=""
                    >
                  </div>
                  <div class="grow">
                    <div class="text-xl font-semibold tracking-tight leading-5 mb-2">
                      {{ currentProduct?.product_name }}
                    </div>
                    <div class="flex flex-wrap gap-2">
                      <span class="bg-black px-2 py-1 rounded-sm text-xs font-semibold text-white">
                        {{ selectedStrength?.strength }}{{ selectedStrength?.strength_unit }}
                      </span>
                      <span class="bg-black px-2 py-1 rounded-sm text-xs font-semibold text-white">
                        {{ selectedStrength?.qty }} units/mo
                      </span>
                    </div>
                  </div>
                  <div class="ms-2">
                    <button
                      v-if="Array.isArray(availableStrengths) && availableStrengths.length > 1"
                      class="text-sm font-semibold underline hover:no-underline"
                      @click="openEditMedicationSidebar"
                    >
                      Change
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <hr
            v-if="!retryOrderId && !retryOrderSummary"
            class="my-5"
          >

          <!-- Normal flow: Shipping Frequency -->
          <div
            v-if="!retryOrderId && !isLocalPharmacySelected"
            class="mb-6"
          >
            <div class="ms-2">
              <h5 class="text-gray-700 text-base font-medium mb-3">
                Choose shipping frequency
              </h5>
            </div>
            <div
              v-if="planLoading"
              class="grid w-full space-y-3"
            >
              <div class="h-14 bg-gray-300 rounded animate-pulse"></div>
              <div class="h-14 bg-gray-300 rounded animate-pulse"></div>
              <div class="h-14 bg-gray-300 rounded animate-pulse"></div>
            </div>
            <ul
              v-else
              class="grid w-full space-y-3"
            >
              <li
                v-for="plan in subscriptionPlans"
                :key="plan.id"
              >
                <input
                  :id="`subscription_plan_${plan.id}`"
                  v-model="selectedSubscriptionPlanId"
                  type="radio"
                  name="subscription_plan"
                  class="hidden peer"
                  :value="plan.id"
                  required
                  @change="preCheckout"
                />
                <label
                  :for="`subscription_plan_${plan.id}`"
                  class="inline-flex items-center justify-between w-full px-5 py-3 text-gray-700 bg-white border !border-gray-200 rounded-2xl cursor-pointer peer-checked:!border-gray-600 peer-checked:text-black peer-checked:rounded-b-none peer-checked:!border-2 peer-checked:!border-b-0 hover:text-gray-800 hover:bg-gray-100"
                >
                  <div class="block">
                    <div class="w-full text-base font-medium">Every {{ plan.interval === 1 ? 'month' : `${plan.interval} months` }}</div>
                    <div
                      v-if="calculateAnnualSavings(plan) !== 0"
                      class="bg-green-100 px-2 py-[2px] rounded-sm text-xs inline-block font-semibold text-green-800"
                    >
                      Save {{ formatCurrency(calculateAnnualSavings(plan)) }} per year
                    </div>
                  </div>
                  <span class="ms-2 flex items-center justify-center">
                    <div class="text-base font-semibold">
                      {{ formatCurrency(ceilToDecimalPlaces(plan.subscription_plan_price_after_discount / plan.interval)) }}/mo
                    </div>
                  </span>
                </label>
                <div
                  v-if="selectedSubscriptionPlanId === plan.id"
                  class="w-[100%] bg-gray-100 px-5 py-1 !border-2 !border-t-0 !border-gray-600 rounded-b-2xl mx-auto text-center"
                >
                  <span class="text-sm">
                    {{ plan.interval }} month plan billed {{ formatCurrency(plan.subscription_plan_price_after_discount) }} every {{ plan.interval === 1 ? '1 month' : `${plan.interval} months` }}, {{ formatCurrency(plan.price_per_pill) }}/unit
                  </span>
                </div>
              </li>
            </ul>
          </div>

          <!-- Pharmacy Details when local pharmacy selected -->
          <div
            v-if="!retryOrderId && !retryOrderSummary && isLocalPharmacySelected"
            class="mx-1.5 mb-6"
          >
            <div class="text-gray-700 text-xs uppercase font-medium mb-2">
              Prescription pickup location
            </div>
            <div class="mb-3">
              <span class="block font-medium">{{ shippingDetailsSession?.selectedPharmacy?.StoreName }}</span>
              <span class="block">
                <span v-if="shippingDetailsSession?.selectedPharmacy?.Address2">
                  {{ shippingDetailsSession?.selectedPharmacy?.Address1 }}, {{ shippingDetailsSession?.selectedPharmacy?.Address2 }}
                </span>
                <span v-else>{{ shippingDetailsSession?.selectedPharmacy?.Address1 }}</span>
                <span>, {{ shippingDetailsSession?.selectedPharmacy?.City }}</span>
                <span>, {{ shippingDetailsSession?.selectedPharmacy?.State }}</span>
                <span>, {{ shippingDetailsSession?.selectedPharmacy?.ZipCode }}</span>
              </span>
              <span class="block text-sm italic mt-2">You'll pickup your prescription at this pharmacy.</span>
            </div>
            <div>
              <button
                class="text-base font-semibold border-b-2 border-solid !border-black hover:text-gray-600 hover:!border-gray-600"
                type="button"
                @click="switchToOurPharmacy"
              >
                Ship with our pharmacy
              </button>
            </div>
          </div>

          <!-- Retry Order Local Pharmacy Summary -->
          <div
            v-if="retryOrderId && retryOrderSummary && retryOrderSummary.is_medicine_pickup_at_local_pharmacy === 1"
            class="mx-1.5"
          >
            <div class="text-gray-700 text-xs uppercase font-medium mb-2">
              Prescription pickup location
            </div>
            <div class="mb-4">
              <span class="block font-medium">{{ retryOrderSummary.PharmacyName }}</span>
              <span class="block">{{
                retryOrderSummary.PharmacyAddress +
                  ", " +
                  retryOrderSummary.PharmacyCity +
                  ", " +
                  retryOrderSummary.PharmacyState +
                  ", " +
                  retryOrderSummary.PharmacyZipCode
              }}</span>
              <span class="block text-sm italic mt-2">You'll pickup your prescription at this pharmacy.</span>
            </div>
          </div>

          <div
            v-if="planLoading"
            class="grid w-full space-y-3"
          >
            <div
              v-for="i in 4"
              :key="i"
            >
              <div class="px-2 py-3 border-t !border-gray-200">
                <div class="flex justify-between">
                  <div class="h-5 min-w-[180px] bg-gray-300 rounded animate-pulse"></div>
                  <div class="h-5 min-w-[25px] bg-gray-300 rounded animate-pulse"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- Retry Order Payment Summary -->
          <div v-else-if="retryOrderId && retryOrderSummary">
            <!-- local pharmacy retry summary -->
            <div v-if="retryOrderSummary.is_medicine_pickup_at_local_pharmacy === 1">
              <div
                v-if="!isEmpty(retryOrderSummary.sub_total)"
                class="flex justify-between px-2 py-4 border-t !border-gray-200"
              >
                <div>Medication</div>
                <div class="font-medium">
                  {{ formatCurrency(retryOrderSummary.sub_total) }}
                </div>
              </div>

              <div
                v-if="!isEmpty(retryOrderSummary.provider_consultation_fee)"
                class="flex flex-col px-2 py-4 border-t !border-gray-200"
              >
                <div class="flex justify-between">
                  <div class="flex items-center">
                    <span>Physician and Processing Fee</span>
                    <button
                      v-if="retryOrderSummary.provider_consultation_fee > 0"
                      type="button"
                      class="ms-2 cursor-pointer"
                      @click="consultFeeSidebarVisible = true"
                    >
                      <IconInfoCircle
                        class="w-4 h-4"
                        stroke-width="2"
                      />
                    </button>
                  </div>
                  <div class="font-medium">
                    <span
                      v-if="retryOrderSummary.provider_consultation_fee === 0"
                      class="text-green-400"
                    >FREE</span>
                    <span v-else>{{ formatCurrency(retryOrderSummary.provider_consultation_fee) }}</span>
                  </div>
                </div>
                <div
                  v-if="retryOrderSummary.is_synchronous_visit"
                  class="inline-flex items-center gap-2 text-sm text-gray-600 mt-1 bg-amber-100 px-2 py-2 rounded-lg"
                >
                  <IconVideo
                    class="w-4 h-4"
                    stroke-width="2"
                  />
                  Video visit is required in {{ retryOrderSummary.state }} by state law.
                </div>
              </div>

              <div
                v-if="!isEmpty(retryOrderSummary.convenience_fee)"
                class="flex justify-between px-2 py-4 border-t !border-gray-200"
              >
                <div>Convenience fee</div>
                <div class="font-medium">
                  {{ formatCurrency(retryOrderSummary.convenience_fee) }}
                </div>
              </div>

              <div
                v-if="!isEmpty(retryOrderSummary.total_amount)"
                class="flex justify-between px-2 py-4 border-t !border-gray-200"
              >
                <div class="text-base font-bold">
                  Total
                </div>
                <div class="text-base font-bold">
                  {{ formatCurrency(retryOrderSummary.total_amount) }}
                </div>
              </div>
            </div>
            <!-- normal order retry summary -->
            <div
              v-else
              class="mt-5"
            >
              <div
                v-if="!isEmpty(retryOrderSummary.medicine_amount)"
                class="px-2 py-4 border-t !border-gray-200"
              >
                <div class="flex justify-between">
                  <div>{{ retryOrderSummary.subscription_interval }} month plan</div>
                  <div class="ms-1 font-medium">
                    {{ formatCurrency(retryOrderSummary.medicine_amount) }}
                  </div>
                </div>
              </div>

              <div
                v-if="!isEmpty(retryOrderSummary.promo_code_name)"
                class="flex justify-between px-2 py-4 border-t !border-gray-200"
              >
                <div>
                  <div>Promotion applied</div>
                  <div class="text-sm font-semibold">
                    {{ retryOrderSummary.promo_code_name }} <span v-if="retryOrderSummary.promo_code_type === 'percentage'">({{ retryOrderSummary.promo_code_value }}%)</span>
                  </div>
                </div>
                <div class="font-medium">
                  -{{ formatCurrency(retryOrderSummary.promo_code_discount_amount) }}
                </div>
              </div>

              <div
                v-if="!isEmpty(retryOrderSummary.first_time_order_discount_amount)"
                class="flex justify-between px-2 py-4 border-t !border-gray-200"
              >
                <div>First purchase discount applied</div>
                <div class="font-medium">
                  -{{ formatCurrency(retryOrderSummary.first_time_order_discount_amount) }}
                </div>
              </div>

              <div
                v-if="!isEmpty(retryOrderSummary.sub_total) && retryOrderSummary.sub_total > 0"
                class="flex justify-between px-2 py-4 border-t !border-gray-200"
              >
                <div>Subtotal</div>
                <div class="font-medium">
                  {{ formatCurrency(retryOrderSummary.sub_total) }}
                </div>
              </div>

              <div
                v-if="!isEmpty(retryOrderSummary.provider_consultation_fee)"
                class="flex flex-col px-2 py-4 border-t !border-gray-200"
              >
                <div class="flex justify-between">
                  <div class="flex items-center">
                    <span>Physician and Processing Fee</span>
                    <button
                      v-if="retryOrderSummary.provider_consultation_fee > 0"
                      type="button"
                      class="ms-2 cursor-pointer"
                      @click="consultFeeSidebarVisible = true"
                    >
                      <IconInfoCircle
                        class="w-4 h-4"
                        stroke-width="2"
                      />
                    </button>
                  </div>
                  <div class="font-medium">
                    <span
                      v-if="retryOrderSummary.provider_consultation_fee === 0"
                      class="text-green-400"
                    >FREE</span>
                    <span v-else>{{ formatCurrency(retryOrderSummary.provider_consultation_fee) }}</span>
                  </div>
                </div>
                <div
                  v-if="retryOrderSummary.is_synchronous_visit"
                  class="inline-flex items-center gap-2 text-sm text-gray-600 mt-1 bg-amber-100 px-2 py-2 rounded-lg"
                >
                  <IconVideo
                    class="w-4 h-4"
                    stroke-width="2"
                  />
                  Video visit is required in {{ retryOrderSummary.state }} by state law.
                </div>
              </div>

              <div
                v-if="!isEmpty(retryOrderSummary.shipping_cost)"
                class="flex justify-between px-2 py-4 border-t !border-gray-200"
              >
                <div>Shipping & handling cost</div>
                <div class="font-medium">
                  <span
                    v-if="retryOrderSummary.shipping_cost === 0"
                    class="text-green-400"
                  >FREE</span>
                  <span v-else>{{ formatCurrency(retryOrderSummary.shipping_cost) }}</span>
                </div>
              </div>

              <div
                v-if="!isEmpty(retryOrderSummary.total_amount)"
                class="flex justify-between px-2 py-4 border-t !border-gray-200"
              >
                <div class="text-base font-bold">
                  Total due if prescribed
                </div>
                <div class="text-base font-bold">
                  {{ formatCurrency(retryOrderSummary.total_amount) }}
                </div>
              </div>
            </div>
          </div>

          <!-- Normal Order Payment Summary -->
          <div v-else>
            <div
              v-if="!isEmpty(checkoutSummary.subscription_plan_amount) && !isLocalPharmacySelected"
              class="px-2 py-4 border-t !border-gray-200"
            >
              <div class="flex justify-between">
                <div>{{ selectedPlan?.interval }} month plan</div>
                <div>
                  <span
                    v-if="checkoutSummary.subscription_plan_amount !== (selectedPlan?.plan_interval_price)"
                    class="line-through text-gray-700"
                  >{{ formatCurrency(selectedPlan?.plan_interval_price) }}</span>
                  <span class="ms-1 font-medium">{{ formatCurrency(checkoutSummary.subscription_plan_amount) }}</span>
                </div>
              </div>
              <div
                v-if="percentageSaved"
                class="text-sm text-green-400 font-medium"
              >
                {{ percentageSaved }}
              </div>
            </div>

            <div
              v-if="!isPromoAdded && checkoutSummary.sub_total !== 0"
              class="py-2 border-t !border-gray-200"
            >
              <div
                v-if="!showPromoInput"
                class="flex justify-between items-center bg-gray-100 px-4 py-2 rounded-lg"
              >
                <div class="text-sm">
                  Have a Promo code?
                </div>
                <button
                  class="text-[13px] font-medium border-b !border-gray-900 text-gray-900 disabled:text-gray-600"
                  :disabled="isLoading"
                  @click="showPromoInput = true"
                >
                  Click to Apply
                </button>
              </div>
              <div v-if="showPromoInput">
                <input
                  ref="promoCodeInputRef"
                  v-model="promoCode"
                  type="text"
                  placeholder="Enter promo code"
                  class="block w-full px-4 py-2 text-md font-medium appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
                  autofocus
                  @keyup.enter="applyPromoCode"
                  @keydown="() => {
                    promoCodeError = ''
                  }"
                >
                <div v-if="!isEmpty(promoCodeError)">
                  <span class="text-red-500 text-sm ms-2">{{ promoCodeError }}</span>
                </div>
                <div class="mt-3 flex justify-end">
                  <button
                    class="inline-flex items-center justify-center text-black border !border-black hover:bg-gray-100 focus:outline-none font-medium rounded-full text-sm px-4 py-[5px] text-center disabled:bg-zinc-100 disabled:text-gray-600 disabled:cursor-not-allowed me-2"
                    @click="discardPromoApply"
                  >
                    Discard
                  </button>
                  <TwButton
                    class="!py-1.5"
                    @click="applyPromoCode"
                  >
                    Apply
                  </TwButton>
                </div>
              </div>
            </div>

            <div
              v-if="!isEmpty(checkoutSummary.promocode)"
              class="flex justify-between px-2 py-4 border-t !border-gray-200"
            >
              <div>
                <div>Promotion applied</div>
                <div class="text-sm font-semibold">
                  {{ checkoutSummary.promocode }} <span v-if="checkoutSummary.promocode_type === 'percentage'">({{ checkoutSummary.promocode_value }}%)</span>
                </div>
                <button
                  v-if="isPromoAdded"
                  class="underline text-sm font-medium disabled:text-gray-600"
                  :disabled="isLoading"
                  @click="removePromoCode"
                >
                  Remove
                </button>
              </div>
              <div class="font-medium">
                -{{ formatCurrency(checkoutSummary.promocode_discount_amount) }}
              </div>
            </div>

            <div
              v-if="!isEmpty(checkoutSummary.discount_amount)"
              class="flex justify-between px-2 py-4 border-t !border-gray-200"
            >
              <div>First purchase discount applied</div>
              <div class="font-medium">
                -{{ formatCurrency(checkoutSummary.discount_amount) }}
              </div>
            </div>

            <div
              v-if="!isEmpty(checkoutSummary.sub_total) && checkoutSummary.sub_total > 0"
              class="flex justify-between px-2 py-4 border-t !border-gray-200"
            >
              <div>Subtotal</div>
              <div class="font-medium">
                {{ formatCurrency(checkoutSummary.sub_total) }}
              </div>
            </div>

            <div
              v-if="!isEmpty(checkoutSummary.provider_consultation_fee)"
              class="flex flex-col px-2 py-4 border-t !border-gray-200"
            >
              <div class="flex justify-between">
                <div class="flex items-center">
                  <span>Physician and Processing Fee</span>
                  <button
                    v-if="checkoutSummary.provider_consultation_fee > 0"
                    type="button"
                    class="ms-2 cursor-pointer"
                    @click="consultFeeSidebarVisible = true"
                  >
                    <IconInfoCircle
                      class="w-4 h-4"
                      stroke-width="2"
                    />
                  </button>
                </div>
                <div class="font-medium">
                  <span
                    v-if="checkoutSummary.provider_consultation_fee === 0"
                    class="text-green-400"
                  >FREE</span>
                  <span v-else>{{ formatCurrency(checkoutSummary.provider_consultation_fee) }}</span>
                </div>
              </div>
              <div
                v-if="checkoutSummary.is_synchronous_visit"
                class="inline-flex items-center gap-2 text-sm text-gray-600 mt-1 bg-amber-100 px-2 py-2 rounded-lg"
              >
                <IconVideo
                  class="w-4 h-4"
                  stroke-width="2"
                />
                Video visit is required in {{ checkoutSummary.state }} by state law.
              </div>
            </div>

            <div
              v-if="!isEmpty(checkoutSummary.convenience_fee)"
              class="flex justify-between px-2 py-4 border-t !border-gray-200"
            >
              <div>Convenience fee</div>
              <div class="font-medium">
                {{ formatCurrency(checkoutSummary.convenience_fee) }}
              </div>
            </div>

            <div
              v-if="!isEmpty(checkoutSummary.shipping_handling_cost)"
              class="flex justify-between px-2 py-4 border-t !border-gray-200"
            >
              <div>Shipping & handling cost</div>
              <div class="font-medium">
                <span
                  v-if="checkoutSummary.shipping_handling_cost === 0"
                  class="text-green-400"
                >FREE</span>
                <span v-else>{{ formatCurrency(checkoutSummary.shipping_handling_cost) }}</span>
              </div>
            </div>

            <div
              v-if="!isEmpty(checkoutSummary.grand_total)"
              class="flex justify-between px-2 py-4 border-t !border-gray-200"
            >
              <div class="text-base font-bold">
                Total due if prescribed
              </div>
              <div class="text-base font-bold">
                {{ formatCurrency(checkoutSummary.grand_total) }}
              </div>
            </div>

            <div
              v-if="!isEmpty(checkoutSummary.full_refund_if_not_prescribed) && !isLocalPharmacySelected"
              class="flex justify-between px-2 py-1"
            >
              <div>
                <div class="font-semibold mb-1">
                  Due Today
                </div>
                <!--
                  <button
                  class="text-sm font-medium border-b !border-gray-900 text-gray-900"
                  @click="paymentAndRefundSidebarVisible = true"
                  >
                  When am I charged?
                  </button>
                -->
              </div>
              <div class="font-medium">
                {{ formatCurrency(checkoutSummary.full_refund_if_not_prescribed) }}
              </div>
            </div>
            <div class="text-sm text-gray-600 px-2 border-b !border-gray-200 pb-4 mt-2">
              <h5 class="font-semibold text-sm text-gray-700 mb-0.5">
                Only charged if prescribed by a licensed physician
              </h5>
              <p>We'll securely hold your payment method. No charge until prescribed.</p>
            </div>
          </div>

          <!-- Payment Failed Card -->
          <div v-if="retryOrderId && !showPaymentMethods">
            <div class="bg-red-50 p-6 rounded-lg">
              <IconCreditCardOff
                class="size-16 mx-auto mb-3"
                stroke-width="1.5"
              />
              <div class="text-gray-600 text-[15px]">
                <h5 class="mb-2 text-lg font-medium text-gray-900">
                  Oops! Your payment didn't go through.
                </h5>
                <p>
                  This could be because:
                </p>
                <ul class="list-disc ps-5">
                  <li>You have insufficient funds in your account</li>
                  <li>Your payment card has expired</li>
                  <li>There's a problem with your bank</li>
                </ul>
                <p class="mt-2">
                  Please try again using a different payment method.
                </p>
              </div>
              <TwButton
                class="w-full mt-5"
                @click="showPaymentMethods = true"
              >
                Retry Payment
              </TwButton>
            </div>
          </div>

          <!-- Payment methods -->
          <div
            v-if="showPaymentMethods"
            class="mt-4 space-y-6"
          >
            <div v-if="paymentMethods.length > 0 && !isCardFormVisible">
              <div>
                <h5 class="text-gray-700 text-base font-medium mb-3">
                  Select from existing payment methods
                </h5>
              </div>
              <ul class="grid w-full space-y-3 mb-5">
                <li
                  v-for="paymentMethod in paymentMethods"
                  :key="paymentMethod.id"
                >
                  <input
                    :id="`payment_method_${paymentMethod.id}`"
                    v-model="selectedPaymentMethod"
                    type="radio"
                    name="shipping_method"
                    class="hidden peer"
                    :value="paymentMethod.id"
                    :disabled="isCardExpired(paymentMethod)"
                    required
                  />
                  <label
                    :for="`payment_method_${paymentMethod.id}`"
                    class="inline-flex items-center justify-between w-full px-5 py-3 text-gray-700 bg-white border !border-gray-200 rounded-2xl cursor-pointer peer-checked:!border-gray-600 peer-checked:text-black hover:text-gray-800 hover:bg-gray-100 peer-disabled:bg-gray-200 peer-disabled:cursor-not-allowed"
                  >
                    <div class="block">
                      <div class="w-full">
                        <span class="block text-xs uppercase">{{ paymentMethod.card_brand_type }}</span>
                        <span class="block text-base font-medium">xxxx xxxx xxxx {{ paymentMethod.card_last_4_digit }}</span>
                      </div>
                      <div class="w-full text-xs">
                        <span
                          v-if="isCardExpired(paymentMethod)"
                          class="bg-red-300 text-red-700 font-medium  px-2 py-0.5 rounded-full"
                        >Expired</span>
                        <span v-else>
                          Expires {{ paymentMethod.card_expiry_month }}/{{ paymentMethod.card_expiry_year }}
                        </span>
                      </div>
                    </div>
                    <span class="mx-2 flex items-center justify-center">
                      <span
                        class="w-4 h-4 border-2 rounded-sm flex justify-center items-center"
                        :class="[
                          selectedPaymentMethod === paymentMethod.id ? 'border-transparent bg-black' : 'border-gray-400'
                        ]"
                      >
                        <IconCheck
                          class="h-5 w-5 z-50"
                          :stroke-width="selectedPaymentMethod === paymentMethod.id ? 4 : 1"
                          :class="[
                            selectedPaymentMethod === paymentMethod.id ? 'text-white' : 'text-gray-900'
                          ]"
                        />
                      </span>
                    </span>
                  </label>
                </li>
              </ul>

              <TwButton
                class="w-full"
                :loading="isLoading"
                :disabled="planLoading || isEmpty(selectedPaymentMethod)"
                @click="placeOrder"
              >
                <span v-if="retryOrderId && retryOrderSummary && retryOrderSummary.is_medicine_pickup_at_local_pharmacy === 1">
                  Pay {{ formatCurrency(retryOrderSummary.total_amount) }}
                </span>
                <span v-else-if="isLocalPharmacySelected">
                  Pay {{ formatCurrency(checkoutSummary.grand_total) }}
                </span>
                <span v-else>Continue</span>
              </TwButton>
            </div>

            <div
              v-if="paymentMethods.length > 0 && !isCardFormVisible"
              class="inline-flex items-center justify-center w-full py-2"
            >
              <hr class="w-64 h-px bg-gray-200 border-0">
              <span class="absolute px-3 font-medium text-gray-900 -translate-x-1/2 bg-white left-1/2">or</span>
            </div>

            <TwButton
              v-if="!isCardFormVisible"
              class="w-full !bg-[#ffef08] border-2 border-solid !border-amber-300 !text-black hover:!brightness-90 transition-all"
              :disabled="planLoading"
              @click="initPaypal"
            >
              Add Credit or Debit Card
            </TwButton>
          </div>

          <div v-show="isCardFormVisible && showPaymentMethods">
            <AlertError
              v-if="cardFieldErrors.length > 0"
              title="Error!"
              :errors="cardFieldErrors"
            />
            <div>
              <div
                id="card-number"
                class="w-full"
              ></div>
              <div
                id="expiration-date"
                class="w-full"
              ></div>
              <div
                id="cvv"
                class="w-full"
              ></div>
              <!--
                <div
                id="card-holder-name"
                class="w-full"
                ></div>
              -->
              <TwButton
                id="checkoutSubmitBtn"
                class="w-full mt-2"
                value="submit"
                :loading="isLoading"
                :disabled="planLoading"
              >
                <span v-if="retryOrderId && retryOrderSummary && retryOrderSummary.is_medicine_pickup_at_local_pharmacy === 1">
                  Pay {{ formatCurrency(retryOrderSummary.total_amount) }}
                </span>
                <span v-else-if="isLocalPharmacySelected">
                  Pay {{ formatCurrency(checkoutSummary.grand_total) }}
                </span>
                <span v-else>Continue</span>
              </TwButton>
              <div
                v-if="paymentMethods.length > 0"
                class="text-center mt-5"
              >
                <button
                  class="text-sm font-medium border-b !border-gray-900 text-gray-900 inline-block"
                  @click="selectExistingPaymentMethod"
                >
                  Select existing payment method
                </button>
              </div>
            </div>
          </div>

          <hr
            v-if="!isLocalPharmacySelected && !retryOrderId"
            class="w-full h-px bg-gray-200 border-0 mt-8 mb-4"
          >

          <div
            v-if="!isLocalPharmacySelected && !retryOrderId"
            class="text-[13px] italic text-gray-600 bg-gray-100 !rounded-xl p-4"
          >
            You won't be charged anything for now. If approved, we will charge the provided card {{ formatCurrency(checkoutSummary.initial_refill_charge) }} for the first month and {{ formatCurrency(checkoutSummary.next_refill_charge) }} every {{ selectedPlan?.interval === 1 ? 'month' : `${selectedPlan?.interval} months` }} thereafter, discreetly delivering your prescription to your doorstep.
          </div>
        </div>

        <div class="mt-8">
          <div class="grid gap-2 text-xs text-gray-600">
            <div>
              <span class="font-semibold">Payment Authorization:</span> We'll securely pre-authorize your payment method for the amount shown. You'll only be charged if a licensed physician prescribes your medication after reviewing your medical information.
            </div>
            <div>
              <span class="font-semibold">Medical Disclaimer:</span> By submitting this form, I confirm that all information provided is accurate and complete to the best of my knowledge. I understand that providing complete and honest medical information is essential for safe treatment.
            </div>
          </div>
          <p class="text-xs text-gray-600 flex gap-2 justify-center items-center font-medium mt-5">
            <span>
              <IconShieldLock
                class="h-[18px] w-[18px] mb-px"
                stroke-width="2"
              />
            </span>
            <span>256-BIT TLS SECURITY</span>
          </p>
        </div>
      </div>
    </div>
  </div>

  <!-- 👉 edit medication -->
  <Sidebar
    v-model:visible="isEditDrugSidebarVisible"
    position="right"
    show-close-icon
    class="edit-drug-sidebar"
    @hide="isEditDrugSidebarVisible = false"
  >
    <div class="w-full">
      <h2 class="text-xl font-bold leading-tight text-black mb-5">
        Change medication preferences
      </h2>
      <VForm
        :validation-schema="editDrugValidationSchema"
        @submit="handleMedicationChange"
      >
        <div class="space-y-5">
          <div class="relative mb-4">
            <Field
              id="edit_strength"
              v-model="editStrengthId"
              as="select"
              name="edit_strength"
              class="block w-full px-5 pb-2.5 pt-5 text-md font-semibold appearance-none rounded-lg shadow-[inset_0_0_4px] shadow-gray-300 focus:outline-none focus:ring-1 focus:ring-black peer"
              placeholder=" "
              autocomplete="off"
              required
              @change="() => {
                editQtyId = availableStrengths.find(s => s.id === editStrengthId)?.hl_product_strength_qty_id
              }"
            >
              <option
                value=""
                disabled
              >
                Select
              </option>
              <option
                v-for="strength in availableStrengths"
                :key="strength.id"
                :value="strength.id"
              >
                {{ strength.strength }}{{ strength.strength_unit }} <span v-if="!isLocalPharmacySelected">(from {{ formatCurrency(strength.base_price_per_pill) }}/unit)</span>
              </option>
            </Field>
            <label
              for="edit_strength"
              class="absolute text-sm font-semibold text-gray-500 duration-300 transform -translate-y-4 scale-75 top-4 z-10 origin-[0] left-5 peer-focus:text-black peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-4 cursor-text"
            >Select Strength</label>
            <ErrorMessage
              name="edit_strength"
              class="text-red-500 text-sm ms-3"
            />
          </div>

          <TwButton
            class="w-full mt-3"
            type="submit"
          >
            Update
          </TwButton>
        </div>
      </VForm>
    </div>
  </Sidebar>

  <!-- 👉 payment and refund sidebar -->
  <!--
    <PaymentRefundSidebar
    :visible="paymentAndRefundSidebarVisible"
    @update:visible="paymentAndRefundSidebarVisible = $event"
    />
  -->

  <!-- 👉 consult fee sidebar -->
  <ConsultFeeSidebar
    :visible="consultFeeSidebarVisible"
    @update:visible="consultFeeSidebarVisible = $event"
  />
</template>

<style lang="scss">
.p-sidebar-right .edit-drug-sidebar.p-sidebar {
  width: 26rem;
  font-family: "Konnect", system-ui, -apple-system, BlinkMacSystemFont,
    "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue",
    sans-serif;
}
.p-sidebar-right .payment-and-refund-sidebar.p-sidebar {
  width: 30rem;
  font-family: "Konnect", system-ui, -apple-system, BlinkMacSystemFont,
    "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue",
    sans-serif;
}
</style>
