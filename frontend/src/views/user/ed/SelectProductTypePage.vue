<script setup>
import { isEmpty, isEmptyObject } from '@/@core/utils'
import router from '@/router'
import { useEdProductsStore } from '@/store/edProducts'
import { useSessionStorage } from '@vueuse/core'
import { storeToRefs } from 'pinia'
import { computed, onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import Header from '../components/Header.vue'
import useLogOrderSession from '@/composables/useLogOrderSession'

const route = useRoute()
const edProductsStore = useEdProductsStore()
const { logSession } = useLogOrderSession()

const {
  products,
  loading: isLoading,
  errors: serverErrors,
} = storeToRefs(edProductsStore)

const edVisitSession = useSessionStorage('edVisitSession', {})
const btnLoading = ref('')

onMounted(async () => {
  if (products.value.length === 0) {
    await edProductsStore.fetchProducts()
  }
  if (isEmptyObject(edVisitSession.value)) {
    router.replace({ name: 'ed-visit-select-product', query: { ...route.query } })
  }
})

const currentProduct = computed(() => {
  return products.value.find(p => p.id === edVisitSession.value['productId'])
})

const basePriceForGeneric = computed(() => {
  const strengths = currentProduct.value?.ed_product_strengths?.filter(s =>
    s.product_type?.toLowerCase() === 'generic',
  )

  if (!strengths || strengths.length === 0) return undefined

  return strengths.reduce((minPrice, s) =>
    Math.min(minPrice, s.base_price_per_pill), strengths[0].base_price_per_pill,
  )
})

const basePriceForBranded = computed(() => {
  const strengths = currentProduct.value?.ed_product_strengths?.filter(s =>
    s.product_type?.toLowerCase() === 'branded',
  )

  if (!strengths || strengths.length === 0) return undefined

  return strengths.reduce((minPrice, s) =>
    Math.min(minPrice, s.base_price_per_pill), strengths[0].base_price_per_pill,
  )
})


async function handleProductTypeSelection(productType) {
  btnLoading.value = productType
  edVisitSession.value['productType'] = productType

  const hasDailyFreq = currentProduct.value?.ed_product_strengths?.some(
    s => s.frequency?.includes('daily') && s.product_type?.toLowerCase() === productType,
  )

  const hasAsNeededFreq = currentProduct.value?.ed_product_strengths?.some(
    s => s.frequency?.includes('as_needed') && s.product_type?.toLowerCase() === productType,
  )

  if (hasDailyFreq && hasAsNeededFreq) {
    await router.push({ name: 'ed-visit-select-frequency', query: { ...route.query } })
  } else {
    edVisitSession.value['productFreq'] = hasDailyFreq ? 'daily' : 'as_needed'
    await router.push({ name: 'ed-visit-select-strength', query: { ...route.query } })
  }

  logSession({
    id: edVisitSession.value['questionSessionId'],
    category: 'ED',
    product: {
      id: edVisitSession.value['productId'] ?? null,
      strength_id: edVisitSession.value['strengthId'] ?? null,
      qty_id: edVisitSession.value['productQty'] ?? null,
      frequency: edVisitSession.value['productFreq'] ?? null,
      type: edVisitSession.value['productType'] ?? null,
    },
  })
}
</script>

<template>
  <div>
    <Header show-back-button />

    <div class="flex items-center justify-center py-10 sm:px-16">
      <div class="px-4 w-full sm:max-w-[480px]">
        <div
          v-if="isLoading"
          class="h-7 bg-gray-300 rounded mb-2 animate-pulse"
        ></div>
        <div
          v-else
          class="mb-6"
        >
          <h2 class="text-xl md:text-3xl font-bold leading-tight text-black mb-2">
            Choose the one that best fits your preference
          </h2>
          <p class="text-sm text-zinc-700 font-medium">
            Both Generic and Branded options have the same active ingredients, and are equally effective.
          </p>
        </div>

        <div
          v-if="isLoading"
          class="flex flex-col items-center"
        >
          <ul class="grid w-full space-y-3 mb-8 animate-pulse mt-6">
            <li
              v-for="i in 4"
              :key="i"
            >
              <div class="w-full px-5 py-3 text-gray-700 text-start bg-gray-100 border border-solid !border-zinc-300 rounded-2xl">
                <div>
                  <!-- <div class="h-4 bg-gray-300 rounded w-3/4 mb-1"></div> -->
                  <div class="h-4 bg-gray-300 rounded w-full mb-1"></div>
                  <div class="h-4 bg-gray-300 rounded w-2/3"></div>
                </div>
              </div>
            </li>
          </ul>
        </div>

        <div
          v-else
          class="w-full flex flex-col items-center"
        >
          <ul class="grid w-full space-y-3 mb-8">
            <!-- Generic -->
            <li v-if="!isEmpty(currentProduct?.ed_product_strengths) && currentProduct?.ed_product_strengths?.some(s => s.product_type?.toLowerCase() === 'generic')">
              <button
                type="button"
                class="w-full px-5 py-3 text-gray-700 text-start bg-gradient-to-r from-[#fff8f1] to-white border border-solid !border-zinc-300 rounded-2xl cursor-pointer hover:text-gray-800 hover:from-white hover:to-[#fff8f1] transition-colors"
                :loading="btnLoading === 'generic'"
                :disabled="!isEmpty(btnLoading)"
                @click="handleProductTypeSelection('generic')"
              >
                <div>
                  <div class="text-lg">
                    Generic {{ currentProduct.product_name }}
                  </div>
                  <div
                    v-if="basePriceForGeneric"
                    class="text-sm"
                  >
                    From <span class="font-medium">{{ formatCurrency(basePriceForGeneric) }}</span>/unit
                  </div>
                </div>
              </button>
            </li>
            <!-- Branded -->
            <li v-if="!isEmpty(currentProduct?.ed_product_strengths) && currentProduct?.ed_product_strengths?.some(s => s.product_type?.toLowerCase() === 'branded')">
              <button
                type="button"
                class="w-full px-5 py-3 text-gray-700 text-start bg-gradient-to-r from-[#fff8f1] to-white border border-solid !border-zinc-300 rounded-2xl cursor-pointer hover:text-gray-800 hover:from-white hover:to-[#fff8f1] transition-colors"
                :loading="btnLoading === 'branded'"
                :disabled="!isEmpty(btnLoading)"
                @click="handleProductTypeSelection('branded')"
              >
                <div>
                  <div class="text-lg">
                    Branded {{ currentProduct.product_name }}
                  </div>
                  <div
                    v-if="basePriceForBranded"
                    class="text-sm"
                  >
                    From <span class="font-medium">{{ formatCurrency(basePriceForBranded) }}</span>/unit
                  </div>
                </div>
              </button>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>
