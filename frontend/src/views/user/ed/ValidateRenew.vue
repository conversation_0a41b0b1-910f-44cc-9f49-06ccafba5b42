<script setup>
import { isEmpty } from '@/@core/utils'
import router from '@/router'
import ApiService from '@/services/ApiService'
import Header from '@/views/user/components/Header.vue'
import { IconLoader } from '@tabler/icons-vue'
import { useSessionStorage } from '@vueuse/core'
import { onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { toast } from 'vue-sonner'

const route = useRoute()
const subscriptionId = computed(() => route.params.subscriptionId)
const edVisitSession = useSessionStorage('edVisitSession', {})

onMounted(async () => {
  await validateRenew()
})

async function validateRenew() {
  try {
    const { data } = await ApiService.get(`/validate-ed-renew-subscription/${subscriptionId.value}`)

    if (data.status === 200) {
      handleRenewPrescription(data.renewedPrescription)
    } else {
      router.replace({ name: 'user-subscription' })
    }
  } catch (error) {
    console.error(error)
    router.replace({ name: 'user-subscription' })
  }
}

function handleRenewPrescription(data) {
  if (!isEmpty(data)) {
    edVisitSession.value = {
      visitType: 'renew',
      subscriptionId: data.expired_subscription_id,

      // category: data.category,
      // productFreq: data.frequency,
      // productId: data.ed_product_id,
      // strengthId: data.ed_product_strength_id,
      // productQty: data.ed_product_qty_id,
      // planId: data.ed_product_subscription_plan_id,
      // selectedShippingMethod: data.pharmacy_shipping_method_id,
      // productType: data.product_type,
    }

    router.replace({ name: 'ed-questions-renew', params: { slug: 'start' } })
  } else {
    toast.error('We are unable to renew your prescription at this time. Please try again later.')
  }
}
</script>

<template>
  <div>
    <Header />
    <div class="grid place-items-center h-[calc(100vh-15rem)]">
      <div class="flex flex-col items-center gap-4">
        <IconLoader
          class="h-12 w-12 animate-spin"
          stroke-width="1.2"
        />
        <span>Please wait...</span>
      </div>
    </div>
  </div>
</template>
