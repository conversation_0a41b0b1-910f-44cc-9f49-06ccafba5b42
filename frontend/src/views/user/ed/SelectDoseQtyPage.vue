<script setup>
import { isEmpty, isEmptyObject } from '@/@core/utils'
import router from '@/router'
import { useEdProductsStore } from '@/store/edProducts'
import { useSessionStorage } from '@vueuse/core'
import { storeToRefs } from 'pinia'
import { onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import Header from '../components/Header.vue'
import useLogOrderSession from '@/composables/useLogOrderSession'

const route = useRoute()
const edProductsStore = useEdProductsStore()
const { logSession } = useLogOrderSession()

const {
  products,
  loading: isLoading,
  errors: serverErrors,
} = storeToRefs(edProductsStore)

const edVisitSession = useSessionStorage('edVisitSession', {})
const btnLoading = ref('')

onMounted(async () => {
  if (products.value.length === 0) {
    await edProductsStore.fetchProducts()
  }
  if (isEmptyObject(edVisitSession.value)) {
    router.replace({ name: 'ed-visit-select-product', query: { ...route.query } })
  }
  if (edVisitSession.value['productFreq'] === 'daily') {
    router.push({
      name: 'visit-shipping',
      params: { visitType: 'ed' },
      query: { ...route.query },
    })
  }
})

const currentProduct = computed(() => {
  return products.value.find(p => p.id === edVisitSession.value['productId'])
})

const productQtys = computed(() => {
  return currentProduct.value?.ed_product_strengths?.find(strength => {
    return strength.id === edVisitSession.value['strengthId']
  })?.ed_product_qtys_data
})

const recommendedQtys = computed(() => {
  return productQtys.value
    ?.filter(qty => qty.is_recommended === 1)
    ?.sort((a, b) => a.base_price_per_pill - b.base_price_per_pill)
})

const otherQtys = computed(() => {
  return productQtys.value
    ?.filter(qty => qty.is_recommended === 0)
    ?.sort((a, b) => a.base_price_per_pill - b.base_price_per_pill)
})

async function handleQtySelection(qtyId) {
  btnLoading.value = qtyId
  edVisitSession.value['productQty'] = qtyId

  await router.push({
    name: 'visit-shipping',
    params: { visitType: 'ed' },
    query: { ...route.query },
  })

  logSession({
    id: edVisitSession.value['questionSessionId'],
    category: 'ED',
    product: {
      id: edVisitSession.value['productId'] ?? null,
      strength_id: edVisitSession.value['strengthId'] ?? null,
      qty_id: edVisitSession.value['productQty'] ?? null,
      frequency: edVisitSession.value['productFreq'] ?? null,
      type: edVisitSession.value['productType'] ?? null,
    },
  })
}
</script>

<template>
  <div>
    <Header show-back-button />

    <div class="flex items-center justify-center py-10 sm:px-16">
      <div class="px-4 w-full sm:max-w-[480px]">
        <div
          v-if="isLoading"
          class="h-7 bg-gray-300 rounded mb-2 animate-pulse"
        ></div>
        <div
          v-else
          class="mb-6"
        >
          <h2 class="text-xl md:text-3xl font-bold leading-tight text-black mb-2">
            How many times per month do you expect to use it?
          </h2>
          <p class="text-sm text-zinc-700 font-medium">
            If you are prescribed this medication, how frequently do you anticipate using it for sexual activity?
          </p>
        </div>

        <div
          v-if="isLoading"
          class="flex flex-col items-center"
        >
          <ul class="grid w-full space-y-3 mb-8 animate-pulse mt-6">
            <li
              v-for="i in 4"
              :key="i"
            >
              <div class="w-full px-5 py-3 text-gray-700 text-start bg-gray-100 border border-solid !border-zinc-300 rounded-2xl">
                <div>
                  <!-- <div class="h-4 bg-gray-300 rounded w-3/4 mb-1"></div> -->
                  <div class="h-4 bg-gray-300 rounded w-full mb-1"></div>
                  <div class="h-4 bg-gray-300 rounded w-2/3"></div>
                </div>
              </div>
            </li>
          </ul>
        </div>

        <div v-else>
          <div
            v-if="recommendedQtys && recommendedQtys.length > 0"
            class="w-full flex flex-col items-center"
          >
            <div class="text-base font-medium italic text-zinc-600 mb-3">
              Most Popular
            </div>
            <ul class="grid w-full space-y-3 mb-8">
              <li
                v-for="qty in recommendedQtys"
                :key="qty.id"
              >
                <button
                  type="button"
                  class="w-full px-5 py-3 text-gray-700 text-start bg-gradient-to-r from-[#fff8f1] to-white border border-solid !border-zinc-300 rounded-2xl cursor-pointer hover:text-gray-800 hover:from-white hover:to-[#fff8f1] transition-colors"
                  :loading="btnLoading === qty.id"
                  :disabled="!isEmpty(btnLoading)"
                  @click="handleQtySelection(qty.id)"
                >
                  <div>
                    <!--
                      <div class="text-xs uppercase font-medium">
                      {{ currentProduct.product_name }}
                      </div>
                    -->
                    <div class="text-lg">
                      {{ qty.qty }} units per month
                    </div>
                    <div class="text-sm">
                      From <span class="font-medium">{{ formatCurrency(qty.base_price_per_pill) }}</span>/unit
                    </div>
                  </div>
                </button>
              </li>
            </ul>
          </div>

          <div
            v-if="otherQtys && otherQtys.length > 0"
            class="w-full flex flex-col items-center"
          >
            <div
              v-if="recommendedQtys && recommendedQtys.length > 0"
              class="text-base font-medium italic text-zinc-600 mb-3"
            >
              Other Options
            </div>
            <ul class="grid w-full space-y-3 mb-8">
              <li
                v-for="qty in otherQtys"
                :key="qty.id"
              >
                <button
                  type="button"
                  class="w-full px-5 py-3 text-gray-700 text-start bg-white border border-solid !border-zinc-300 rounded-2xl cursor-pointer hover:text-gray-800 hover:bg-gray-50 transition-colors"
                  :loading="btnLoading === qty.id"
                  :disabled="!isEmpty(btnLoading)"
                  @click="handleQtySelection(qty.id)"
                >
                  <div>
                    <!--
                      <div class="text-xs uppercase font-medium">
                      {{ currentProduct.product_name }}
                      </div>
                    -->
                    <div class="text-lg">
                      {{ qty.qty }} units per month
                    </div>
                    <div class="text-sm">
                      From <span class="font-medium">{{ formatCurrency(qty.base_price_per_pill) }}</span>/unit
                    </div>
                  </div>
                </button>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
