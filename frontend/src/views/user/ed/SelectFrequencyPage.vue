<script setup>
import Header from '../components/Header.vue'
import { onMounted, ref } from 'vue'
import router from '@/router'
import { useRoute } from 'vue-router'
import { useEdProductsStore } from '@/store/edProducts'
import { storeToRefs } from 'pinia'
import { useSessionStorage } from '@vueuse/core'
import { isEmpty, isEmptyObject } from '@/@core/utils'
import useLogOrderSession from '@/composables/useLogOrderSession'

const route = useRoute()
const edProductsStore = useEdProductsStore()
const { logSession } = useLogOrderSession()

const {
  products,
  loading: isLoading,
  errors: serverErrors,
} = storeToRefs(edProductsStore)

const edVisitSession = useSessionStorage('edVisitSession', {})
const btnLoading = ref('')

onMounted(async () => {
  if (products.value.length === 0) {
    await edProductsStore.fetchProducts()
  }
  if (isEmptyObject(edVisitSession.value)) {
    router.replace({ name: 'ed-visit-select-product', query: { ...route.query } })
  }
})

const currentProduct = computed(() => {
  return products.value.find(p => p.id === edVisitSession.value['productId'])
})

const getBasePrice = frequencyType => {
  const strengths = currentProduct.value?.ed_product_strengths?.filter(
    s => s.frequency?.includes(frequencyType) && s.product_type === edVisitSession.value['productType'],
  )

  if (!strengths || strengths.length === 0) return 0

  const basePriceKey = frequencyType === 'daily'? 'daily_base_price' : 'as_needed_base_price'

  return strengths.reduce(
    (minPrice, strength) => Math.min(minPrice, strength[basePriceKey]),
    strengths[0][basePriceKey],
  )
}

const dailyStartsFrom = computed(() => getBasePrice('daily'))
const asNeededStartsFrom = computed(() => getBasePrice('as_needed'))

async function handleFrequencySelection(productFreq) {
  btnLoading.value = productFreq
  edVisitSession.value['productFreq'] = productFreq

  await router.push({ name: 'ed-visit-select-strength', query: { ...route.query } })

  logSession({
    id: edVisitSession.value['questionSessionId'],
    category: 'ED',
    product: {
      id: edVisitSession.value['productId'] ?? null,
      strength_id: edVisitSession.value['strengthId'] ?? null,
      qty_id: edVisitSession.value['productQty'] ?? null,
      frequency: edVisitSession.value['productFreq'] ?? null,
      type: edVisitSession.value['productType'] ?? null,
    },
  })
}
</script>

<template>
  <div>
    <Header show-back-button />

    <div class="flex items-center justify-center py-10 sm:px-16">
      <div class="px-4 w-full sm:max-w-[480px]">
        <div
          v-if="isLoading"
          class="h-7 bg-gray-300 rounded mb-2 animate-pulse"
        ></div>
        <h2
          v-else
          class="text-center text-xl md:text-3xl font-bold leading-tight text-black mb-3"
        >
          What time works best for you to take your medication?
        </h2>

        <div
          v-if="isLoading"
          class="flex flex-col items-center"
        >
          <div
            v-for="i in 2"
            :key="i"
            class="flex flex-col p-5 mt-6 w-full bg-white rounded-3xl border border-solid !border-zinc-300"
          >
            <div class="animate-pulse">
              <div class="justify-between py-1">
                <div class="flex gap-5 max-md:flex-col max-md:gap-0">
                  <div class="flex flex-col w-[74%] max-md:ml-0 max-md:w-full">
                    <div class="flex flex-col grow justify-center text-sm leading-5 text-black">
                      <div class="h-6 w-3/4 bg-gray-300 rounded mb-2"></div>
                      <div class="h-4 w-5/6 bg-gray-200 rounded mb-3"></div>
                      <!-- <div class="h-5 w-24 bg-gray-300 rounded"></div> -->
                    </div>
                  </div>
                </div>
              </div>
              <div class="shrink-0 mt-4 h-px border border-solid bg-slate-200 !border-slate-200"></div>
              <div class="flex gap-4 justify-between mt-4 text-sm leading-5 text-center text-black">
                <div
                  v-for="j in 3"
                  :key="j"
                  class="justify-center px-3 py-1.5 rounded-lg bg-slate-100"
                >
                  <div class="h-4 w-12 bg-gray-300 rounded"></div>
                </div>
              </div>
            </div>
            <div class="mt-5 animate-pulse">
              <div class="h-10 bg-gray-300 rounded-xl"></div>
            </div>
          </div>
        </div>

        <div
          v-else
          class="flex flex-col items-center"
        >
          <div
            v-if="!isEmpty(currentProduct?.ed_product_strengths) && currentProduct?.ed_product_strengths.some(s => s.frequency.includes('daily'))"
            class="mt-6"
          >
            <div class="bg-yellow-200 font-medium rounded-t-lg text-center w-fit px-2 sm:!px-8 mx-auto pt-1">
              Ideal for Unplanned Moments
            </div>
            <div class="flex flex-col p-5 w-full bg-white rounded-3xl border border-solid !border-zinc-300">
              <div class="justify-between py-1">
                <div class="flex gap-5 max-md:flex-col max-md:gap-0">
                  <div class="flex flex-col w-[74%] max-md:ml-0 max-md:w-full">
                    <div class="flex flex-col grow justify-center text-sm leading-5 text-black">
                      <div class="text-2xl font-semibold tracking-tight leading-5 mb-2">
                        Daily
                      </div>
                      <p class="text-zinc-600">
                        Be ready and stay ready whenever the mood strikes—no planning needed.
                      </p>
                      <div
                        v-if="dailyStartsFrom"
                        class="justify-center mt-3"
                      >
                        <span class="px-3 py-1.5 bg-sky-100 rounded-lg text-sm">
                          From <span class="font-medium">{{ formatCurrency(dailyStartsFrom) }}/unit</span>
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <TwButton
                type="button"
                class="mt-5 w-full"
                :loading="btnLoading === 'daily'"
                :disabled="!isEmpty(btnLoading)"
                @click="handleFrequencySelection('daily')"
              >
                Continue
              </TwButton>
            </div>
          </div>

          <div
            v-if="!isEmpty(currentProduct?.ed_product_strengths) && currentProduct?.ed_product_strengths.some(s => s.frequency.includes('as_needed'))"
            class="flex flex-col p-4 mt-4 w-full bg-white rounded-3xl border border-solid !border-zinc-300"
          >
            <div class="justify-between py-1">
              <div class="flex gap-5 max-md:flex-col max-md:gap-0">
                <div class="flex flex-col w-[74%] max-md:ml-0 max-md:w-full">
                  <div class="flex flex-col grow justify-center text-sm leading-5 text-black">
                    <div class="text-2xl font-semibold tracking-tight leading-5 mb-2">
                      Before Sex / As Needed
                    </div>
                    <p class="text-zinc-600">
                      Be ready before the moment arrives.
                    </p>
                    <div
                      v-if="asNeededStartsFrom"
                      class="justify-center mt-3"
                    >
                      <span class="px-3 py-1.5 bg-sky-100 rounded-lg text-sm">
                        From <span class="font-medium">{{ formatCurrency(asNeededStartsFrom) }}/unit</span>
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <TwButton
              type="button"
              class="mt-5 w-full"
              :loading="btnLoading === 'as_needed'"
              :disabled="!isEmpty(btnLoading)"
              @click="handleFrequencySelection('as_needed')"
            >
              Continue
            </TwButton>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
