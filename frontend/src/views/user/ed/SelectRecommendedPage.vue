<script setup>
import { isEmpty } from '@/@core/utils'
import { usePreventNavigation } from '@/composables/usePreventNavigation'
import router from '@/router'
import ApiService from '@/services/ApiService'
import { IconArrowRight, IconStethoscope } from '@tabler/icons-vue'
import { useSessionStorage } from '@vueuse/core'
import { onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import Header from '../components/Header.vue'
import { capitalizeFirstChar, formatCurrency } from '@/utils/helpers'
import useLogOrderSession from '@/composables/useLogOrderSession'

usePreventNavigation(true)

const route = useRoute()
const { logSession } = useLogOrderSession()
const edVisitSession = useSessionStorage('edVisitSession', {})
const products = ref([])
const isLoading = ref(false)
const btnLoading = ref(null)

onMounted(async () => {
  await fetchRecommendedProducts()
})

async function fetchRecommendedProducts() {
  try {
    isLoading.value = true

    const { data } = await ApiService.get('/ed-recommended-products')

    if (data.status === 200) {
      if (isEmpty(data.recommended_product_lists)) {
        router.replace({ name: 'ed-visit-select-product' })
      }
      products.value = data.recommended_product_lists
    } else {
      console.error(data)
      router.replace({ name: 'ed-visit-select-product' })
    }
  } catch (error) {
    console.log(error)
    router.replace({ name: 'error-something-wrong', query: { visit: 'ed' } })
  } finally {
    isLoading.value = false
  }
}

async function continueWithRecommended(product) {
  btnLoading.value = product.ed_product_id

  edVisitSession.value['productId'] = product.ed_product_id
  edVisitSession.value['productFreq'] = product.frequency
  edVisitSession.value['productType'] = product.product_type
  edVisitSession.value['strengthId'] = product.ed_product_strength_id

  if (product.ed_product_strength_qty_id) {
    edVisitSession.value['productQty'] = product.ed_product_strength_qty_id
  }

  await router.push({
    name: 'visit-shipping',
    params: { visitType: 'ed' },
    query: { ...route.query },
  })

  logSession({
    id: edVisitSession.value['questionSessionId'],
    category: 'ED',
    product: {
      id: edVisitSession.value['productId'] ?? null,
      strength_id: edVisitSession.value['strengthId'] ?? null,
      qty_id: edVisitSession.value['productQty'] ?? null,
      frequency: edVisitSession.value['productFreq'] ?? null,
      type: edVisitSession.value['productType'] ?? null,
    },
  })
}

function exploreOtherOptions() {
  edVisitSession.value['productFreq'] = null
  edVisitSession.value['productId'] = null
  edVisitSession.value['strengthId'] = null
  edVisitSession.value['productQty'] = null
  router.push({ name: 'ed-visit-select-product' })
}
</script>

<template>
  <div>
    <Header />

    <div class="flex items-center justify-center py-10 sm:px-16">
      <div class="px-4 w-full sm:max-w-[480px]">
        <div
          v-if="isLoading"
          class="h-7 bg-gray-300 rounded mb-2 animate-pulse"
        ></div>
        <div
          v-else
          class="mb-2"
        >
          <h2 class="text-xl md:text-3xl font-bold leading-tight text-black">
            Recommended Treatment
          </h2>
          <p class="text-sm text-zinc-700 mt-2">
            Based on your assessment, our providers recommend this treatment
          </p>
        </div>

        <div
          v-if="isLoading"
          class="flex flex-col items-center"
        >
          <div class="flex flex-col p-5 mt-6 w-full bg-white rounded-3xl border border-solid !border-zinc-300">
            <!-- Product Info Skeleton Loader -->
            <div class="animate-pulse">
              <div class="justify-between py-1">
                <div class="flex gap-5 max-md:flex-col max-md:gap-0">
                  <div class="flex flex-col w-full max-md:ml-0 max-md:w-full">
                    <div class="flex flex-col-reverse sm:flex-row gap-5 grow justify-between items-top text-sm leading-5 text-black">
                      <div class="grow">
                        <div class="h-6 w-4/5 bg-gray-300 rounded mb-2"></div> <!-- Title Skeleton Loader -->
                        <div class="h-5 w-36 bg-gray-300 rounded mb-2"></div> <!-- Price Skeleton Loader -->
                        <div class="h-3 w-40 bg-gray-300 rounded mb-2"></div> <!-- Description Skeleton Loader -->
                        <div class="h-3 w-32 bg-gray-300 rounded"></div> <!-- Description Skeleton Loader -->
                      </div>
                      <div>
                        <div class="min-h-24 min-w-24 w-full sm:h-24 sm:w-24 bg-gray-300 rounded-lg"></div> <!-- Image Skeleton Loader -->
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="mt-5 animate-pulse">
              <div class="h-10 w-full bg-gray-300 rounded"></div>
            </div>
          </div>
        </div>

        <div
          v-else
          class="flex flex-col items-center"
        >
          <div
            v-for="product in products"
            :key="product.id"
            class="flex flex-col p-5 mt-6 w-full rounded-3xl border border-solid !border-gray-300"
            :class="[
              product.product_type === 'branded' ? 'bg-gradient-to-r from-[#fff8f1] to-white' : 'bg-white'
            ]"
          >
            <div class="justify-between py-1">
              <div class="text-sm font-medium text-zinc-600 -mt-5 mb-4 inline-flex items-center gap-2">
                <IconStethoscope
                  class="size-5 inline-block"
                  stroke-width="2"
                />
                <span>Provider Recommended</span>
              </div>
              <div class="flex gap-5 max-md:flex-col max-md:gap-0">
                <div class="flex flex-col w-full max-md:ml-0 max-md:w-full">
                  <div class="flex flex-col-reverse sm:flex-row gap-5 grow justify-between items-top text-sm leading-5 text-black">
                    <div class="grow">
                      <div class="text-2xl font-semibold tracking-tight leading-5 mb-3">
                        {{ capitalizeFirstChar(product.product_type) }} {{ product.product_name }}
                      </div>
                      <div class="flex flex-wrap gap-2 mb-4">
                        <span class="bg-gray-500 px-2 py-0.5 rounded-full text-sm font-medium text-white">
                          Most Popular
                        </span>
                        <span class="bg-gray-200 px-2 py-0.5 rounded-full text-sm font-medium text-black">
                          {{ product.strength }} {{ product.strength_unit }}
                        </span>
                        <span class="bg-gray-200 px-2 py-0.5 rounded-full text-sm font-medium text-black">
                          <span>{{ product.qty }} units/mo</span>
                        </span>
                      </div>
                      <div
                        v-if="product.product_description"
                        class="text-zinc-600 custom-prose mb-4"
                        v-html="product.product_description"
                      ></div>
                    </div>
                    <div
                      class="!rounded-xl sm:bg-transparent"
                      :class="[
                        product.product_type === 'branded' ? 'bg-[#fff8f1]' : 'bg-gray-100'
                      ]"
                    >
                      <img
                        :src="product.strength_image"
                        class="min-h-24 min-w-24 w-full sm:h-24 sm:w-24 object-contain rounded-lg max-h-60"
                        alt=""
                      >
                    </div>
                  </div>
                  <div class="flex flex-wrap gap-3">
                    <div
                      v-if="!isEmpty(product.works_in)"
                      class="flex flex-1 flex-col bg-gray-50 border !border-gray-200 rounded-lg px-3 py-1.5"
                    >
                      <span class="text-xs font-medium text-gray-700">Works&nbsp;In</span>
                      <span class="text-base font-medium">{{ product.works_in }}</span>
                    </div>
                    <div
                      v-if="!isEmpty(product.lasts_up)"
                      class="flex flex-1 flex-col bg-gray-50 border !border-gray-200 rounded-lg px-3 py-1.5"
                    >
                      <span class="text-xs font-medium text-gray-700">Lasts&nbsp;for</span>
                      <span class="text-base font-medium">{{ product.lasts_up }}</span>
                    </div>
                    <div
                      v-if="product.base_price_per_pill"
                      class="flex flex-1 flex-col bg-gray-50 border !border-gray-200 rounded-lg px-3 py-1.5"
                    >
                      <span class="text-xs font-medium text-gray-700">Starts&nbsp;at</span>
                      <span class="text-base font-medium">{{ formatCurrency(product.base_price_per_pill) }}/unit</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <TwButton
              class="mt-6 w-full"
              :disabled="!isEmpty(btnLoading)"
              :loading="btnLoading === product.id"
              @click="continueWithRecommended(product)"
            >
              Continue
            </TwButton>
          </div>

          <div class="inline-flex items-center justify-center w-full my-10">
            <hr class="w-64 h-px bg-gray-200 border-0">
            <span class="absolute px-3 font-medium text-gray-900 -translate-x-1/2 bg-white left-1/2">or</span>
          </div>

          <button
            class="flex justify-center items-center p-5 w-full bg-gradient-to-r from-[#fff8f1] to-white rounded-full border border-solid !border-zinc-300 hover:from-white hover:to-[#fff8f1] transition-all"
            @click="exploreOtherOptions"
          >
            <span class="font-medium text-base">Explore all treatments</span>
            <IconArrowRight
              class="h-5 w-5 ms-2"
              stroke-width="2"
            />
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
