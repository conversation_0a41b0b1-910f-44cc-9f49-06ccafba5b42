<script setup>
import { isEmpty } from '@/@core/utils'
import ApiService from '@/services/ApiService'
import { IconBolt } from '@tabler/icons-vue'
import { onMounted } from 'vue'
import { RouterLink, useRoute } from 'vue-router'

const $cookies = inject('$cookies')
const route = useRoute()
const frontendUrl = computed(() => import.meta.env.VITE_MARKETING_SITE_URL)

onMounted(async () => {
  validateAffiliateCode()
})

async function validateAffiliateCode() {
  if (isEmpty(route.query.ref)) {
    return
  }

  const refCode = route.query.ref

  if (refCode !== $cookies.get('ed_affiliate_code')) {
    try {
      const { data } = await ApiService.get(`/validate-affiliate-ed-reference-id/${refCode}`)

      if (data.status === 200) {
        $cookies.set('ed_affiliate_code', refCode, '60D')
      } else {
        console.log('Invalid affiliate code')
      }
    } catch (error) {
      console.error(error)
    }
  } else if (refCode === $cookies.get('ed_affiliate_code')) {
    $cookies.set('ed_affiliate_code', refCode, '60D')
  }
}
</script>

<template>
  <div class="mx-auto max-w-[1920px]">
    <nav class="flex justify-center items-center px-11 py-6 w-full bg-white max-md:px-5 max-md:max-w-full relative">
      <a :href="frontendUrl ?? '#'">
        <AppLogo />
      </a>
    </nav>

    <div class="flex justify-center items-center px-2 py-5 w-full bg-white">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div class="mx-auto max-w-2xl text-center">
          <div class="text-lg font-semibold bg-gradient-to-r from-[#fff456] to-[#ff5454] bg-clip-text text-transparent max-md:max-w-full">
            You're in the right spot
          </div>
          <p class="mt-3 text-3xl text-gray-gray-700 font-medium">
            Supercharge Your Sex Life in 4 Easy Steps
          </p>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 text-center py-12">
          <div class="bg-white px-3 py-6 rounded-2xl shadow-sm border-2 border-gray-100">
            <div class="relative mx-auto h-44 w-44 flex items-center justify-center rounded-full bg-gray-100">
              <img
                src="/assets/images/step-1-ed.png"
                alt="slide 1"
                class="h-full w-full rounded-full object-cover"
              />
              <div class="absolute bottom-[-10px]">
                <span class="bg-gradient-to-br from-[#fff456] to-[#ff5454] px-4 py-1.5 rounded-full text-xs font-medium">
                  3 minutes
                </span>
              </div>
            </div>
            <h3 class="mt-6 text-lg font-semibold text-gray-800">
              1. Start visit
            </h3>
            <p class="mt-1 text-[15px] text-gray-600">
              Begin by answering questions about your symptoms, medical history, and lifestyle.
            </p>
          </div>
          <div class="bg-white px-3 py-6 rounded-2xl shadow-sm border-2 border-gray-100">
            <div class="relative mx-auto h-44 w-44 flex items-center justify-center rounded-full bg-gray-100">
              <img
                src="/assets/images/step-2.webp"
                alt="slide 1"
                class="h-full w-full rounded-full object-cover"
              />
              <div class="absolute bottom-[-10px]">
                <span class="bg-gradient-to-br from-[#fff456] to-[#ff5454] px-4 py-1.5 rounded-full text-xs font-medium">
                  1 minute
                </span>
              </div>
            </div>
            <h3 class="mt-6 text-lg font-semibold text-gray-800">
              2. Recommendation
            </h3>
            <p class="mt-1 text-[15px] text-gray-600">
              Based on your answers, explore the treatments you may qualify for.
            </p>
          </div>
          <div class="bg-white px-3 py-6 rounded-2xl shadow-sm border-2 border-gray-100">
            <div class="relative mx-auto h-44 w-44 flex items-center justify-center rounded-full bg-gray-100">
              <img
                src="/assets/images/doctor.png"
                alt="slide 1"
                class="h-full w-full rounded-full object-cover scale-[1.035]"
              />
              <div class="absolute bottom-[-10px]">
                <span class="bg-gradient-to-br from-[#fff456] to-[#ff5454] px-4 py-1.5 rounded-full text-xs font-medium">
                  24 hours
                </span>
              </div>
            </div>
            <h3 class="mt-6 text-lg font-semibold text-gray-800">
              3. Provider review
            </h3>
            <p class="mt-1 text-[15px] text-gray-600">
              A clinician will review your responses and prescribe treatment if it's appropriate for you.
            </p>
          </div>
          <div class="bg-white px-3 py-6 rounded-2xl shadow-sm border-2 border-gray-100">
            <div class="relative mx-auto h-44 w-44 flex items-center justify-center rounded-full bg-gray-100">
              <img
                src="/assets/images/step-4.webp"
                alt="slide 1"
                class="h-full w-full rounded-full object-cover"
              />
              <div class="absolute bottom-[-10px]">
                <span class="bg-gradient-to-br from-[#fff456] to-[#ff5454] px-4 py-1.5 rounded-full text-xs font-medium">
                  24-48 hours
                </span>
              </div>
            </div>
            <h3 class="mt-6 text-lg font-semibold text-gray-800">
              4. Treatment
            </h3>
            <p class="mt-1 text-[15px] text-gray-600">
              If prescribed, your medication will be delivered directly and discreetly to your doorstep.
            </p>
          </div>
        </div>
        <div class="flex justify-center mb-6">
          <RouterLink
            :to="{ name: 'ed-questions', params: { slug: 'start' } }"
            class="flex gap-1.5 justify-center px-5 py-4 text-base font-semibold leading-3 text-black bg-[#ffef08] border-2 border-[#BAB010] border-solid rounded-full hover:brightness-90 transition-all"
          >
            <div class="font-semibold leading-4">
              Get Started
            </div>
            <IconBolt
              class="h-4 w-4"
              stroke-width="1"
              fill="currentColor"
            />
          </RouterLink>
        </div>
      </div>
    </div>
  </div>
</template>
