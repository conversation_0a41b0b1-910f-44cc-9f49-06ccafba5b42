<script setup>
import { isEmpty } from '@/@core/utils'
import { useAuthStore } from '@/store/auth'
import { useChatStore } from '@/store/chatStore'
import ModalHelp from '@/views/user/components/ModalHelp.vue'
import { IconArrowRight, IconBolt, IconHelp, IconInfoCircle, IconLoader, IconUser } from '@tabler/icons-vue'
import { storeToRefs } from 'pinia'
import { computed, onMounted, onUnmounted, ref } from 'vue'
import VueMarkdown from 'vue-markdown-render'
import SpeechToText from './components/SpeechToText.vue'

const authStore = useAuthStore()
const chatStore = useChatStore()

const {
  prompt,
  messages,
  isLoading,
  isMessagesLoading,
  chatContainer,
} = storeToRefs(chatStore)

const {
  setPrompt,
  sendPrompt,
  loadMessages,
  clearChat,
} = chatStore

const promptInput = ref(null)
const showInfoBanner = ref(true)
const modalHelpRef = ref(null)
const isListening = ref(false)

const suggestedPrompts = ref([
  {
    label: 'Help me improve my sexual health',
    prompt: 'I would like to improve my erectile health and gain a better understanding of the key factors that influence sexual well-being. Specifically, I am looking for insights on how diet, physical activity, lifestyle habits, and potential medical interventions can contribute to achieving harder erections and enhancing overall sexual performance. Please provide personalized recommendations and evidence-based strategies to help boost my confidence and optimize my sexual health.',
  },
  {
    label: 'Help me maintain and regrow my hair',
    prompt: 'I am focused on maintaining and regrowing my hair, aiming to promote healthy growth and prevent further hair loss. I am seeking advice on effective treatments, products, and lifestyle changes that can help strengthen my hair and keep it full. Please provide recommendations for evidence-based solutions, including any dietary, environmental, or medical factors that can contribute to achieving healthier, stronger hair.',
  },
  {
    label: 'Help me lose weight',
    prompt: 'I am committed to adopting a healthier lifestyle by achieving sustainable weight loss. My goal is to lose weight through a tailored approach that integrates balanced nutrition, regular physical activity, and the cultivation of long-term healthy habits. I am seeking personalized, evidence-based guidance that aligns with my unique lifestyle, focusing on strategies that promote motivation, consistency, and long-term success. Please provide practical, research-backed recommendations for a structured weight loss plan, including tips to help me stay focused, overcome challenges, and achieve my health and fitness objectives.',
  },
])

watch(prompt, () => {
  setTimeout(() => {
    const el = promptInput.value

    if (el) {
      el.style.height = 'auto'
      el.style.height = `${el.scrollHeight}px`
    }
  }, 0)
})

let intervalId

onMounted(async () => {
  if (promptInput.value) {
    promptInput.value.focus()
  }
  await loadMessages()
})

onUnmounted(() => {
  clearTimeout(intervalId)
})

function applyBlankTarget() {
  const links = chatContainer.value.querySelectorAll('a')

  if (links) {
    links.forEach(link => {
      link.setAttribute('target', '_blank')
      link.setAttribute('rel', 'noopener noreferrer')
    })
  }
}

watch(messages, () => {
  setTimeout(() => {
    applyBlankTarget()
  }, 100)
})

const reversedMessages = computed(() => {
  return [...messages.value].reverse()
})

async function handleSubmit() {
  const promptText = prompt.value.trim()
  if (!isEmpty(promptText)) {
    await sendPrompt(promptText)
  }
}
</script>

<template>
  <div>
    <div class="flex flex-col w-full h-screen">
      <!-- Header -->
      <header class="flex justify-center items-center px-11 py-5 w-full bg-zinc-900 max-md:px-5 max-md:max-w-full">
        <!--
          <button
          class="absolute top-0 left-0 cursor-pointer py-6 px-5 sm:!px-10 disabled:cursor-not-allowed disabled:text-gray-500"
          @click="$router.go(-1)"
          >
          <IconChevronLeft
          stroke-width="2.5"
          class="w-6 h-6 text-white"
          />
          </button>
        -->
        <AppLogo variant="white" />
        <button
          v-tooltip.bottom="'Help'"
          type="button"
          class="absolute top-[10px] right-[10px] cursor-pointer p-4 disabled:cursor-not-allowed disabled:text-gray-500"
          @click="modalHelpRef.open()"
        >
          <IconHelp
            class="h-6 w-6 cursor-pointer text-white"
            stroke-width="2"
          />
        </button>
      </header>

      <!-- Loading -->
      <div
        v-if="isMessagesLoading"
        class="flex-1 grid place-items-center"
      >
        <div>
          <IconLoader
            class="w-10 h-10 animate-spin mx-auto mb-3"
            stroke-width="2"
          />
          <span class="text-center">
            Please Wait...
          </span>
        </div>
      </div>

      <!-- Title and Suggested actions -->
      <div
        v-if="!isMessagesLoading && isEmpty(messages)"
        class="flex-1 flex flex-col gap-10 items-start w-full overflow-y-auto"
      >
        <!-- Title and Suggested actions -->
        <div class="w-full max-w-[992px] mx-auto space-y-10 py-10 px-5 md:!px-10">
          <!-- Title -->
          <div class="flex flex-col grow shrink py-1.5 text-6xl tracking-tighter leading-none min-w-[240px] w-full max-md:max-w-full max-md:text-4xl">
            <h1
              v-if="authStore.isAuthenticated && authStore.userData?.first_name"
              class="text-[#FF8D08] font-bold max-md:text-4xl"
            >
              Hello, {{ authStore.userData.first_name }}!
            </h1>
            <h1
              v-else
              class="text-[#FF8D08] font-bold max-md:text-4xl"
            >
              Hello!
            </h1>
            <p class="mt-5 text-black text-opacity-30 max-md:max-w-full max-md:text-4xl font-medium">
              How may I assist you?
            </p>
          </div>

          <!-- Suggested actions -->
          <section class="hidden sm:flex flex-wrap gap-7 justify-center items-start w-full">
            <div
              v-for="suggestedPrompt in suggestedPrompts"
              :key="suggestedPrompt.label"
              class="flex sm:flex-col flex-1 justify-between p-6 bg-orange-100 rounded-2xl sm:min-h-[170px] min-w-[240px] max-md:px-5 cursor-pointer hover:brightness-[95%] transition-colors"
              @click="setPrompt(suggestedPrompt.prompt)"
            >
              <h2 class="text-sm sm:text-xl font-medium tracking-tight leading-7 text-black">
                {{ suggestedPrompt.label }}
              </h2>
              <div class="flex justify-center items-center mt-1 bg-black w-[22px] sm:w-9 h-[22px] sm:h-9 min-h-[22px] rounded-[650px]">
                <IconBolt class="my-auto w-12 fill-white" />
              </div>
            </div>
          </section>

          <!-- Info Card -->
          <section
            v-if="showInfoBanner"
            class="flex flex-col justify-center items-center sm:flex-row sm:items-start gap-4 sm:gap-6 px-6 py-6 bg-gray-100 rounded-2xl w-full"
          >
            <div>
              <IconInfoCircle
                class="my-auto h-9 w-9"
                stroke-width="1.5"
              />
            </div>
            <div class="flex-1">
              <p class="text-sm tracking-tight leading-[1.6] text-gray-600">
                Your conversations are processed by human reviewers to improve the technologies powering White Label Rx. Don't enter anything that you wouldn't want to be reviewed or used.
              </p>
            </div>
            <div>
              <button
                class="gap-1.5 px-5 pt-1.5 pb-1 text-sm text-black font-semibold leading-none border !border-black rounded-full hover:bg-gray-200"
                @click="showInfoBanner = false"
              >
                Dismiss
              </button>
            </div>
          </section>
        </div>
      </div>

      <!-- Messages -->
      <div
        v-show="!isMessagesLoading && reversedMessages.length > 0"
        id="chatContainer"
        ref="chatContainer"
        class="flex-1 flex flex-col space-y-4 overflow-y-auto w-full py-10 px-2 md:!px-10"
      >
        <div
          v-for="message in reversedMessages"
          :key="message.id"
          class="w-full max-w-[880px] mx-auto"
          :class="`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`"
        >
          <div
            v-if="message.role === 'assistant'"
            class="bg-[#ffef08] rounded-full w-8 h-8 min-w-8 min-h-8 flex items-center justify-center p-1.5 me-2"
          >
            <img
              src="@/assets/logo/logo-small.svg"
              class="w-full h-full"
              alt="avatar"
            />
          </div>
          <div :class="`max-w-3xl p-4 shadow-sm ${message.role === 'user' ? 'bg-gray-200 text-gray-800 rounded-l-2xl rounded-br-2xl ms-10' : 'bg-orange-100 text-gray-800 rounded-r-2xl rounded-bl-2xl me-10'}`">
            <div class="prose prose-p:my-0">
              <vue-markdown :source="message.content[0]?.text?.value" />
            </div>

            <!-- <span class="text-xs text-gray-300 mt-1 block">{{ Date(message.created_at).toString() }}</span> -->
          </div>
          <div
            v-if="message.role === 'user'"
            class="bg-gray-200 rounded-full w-8 h-8 min-w-8 min-h-8 flex items-center justify-center p-1.5 ms-2"
          >
            <IconUser
              class="my-auto h-full w-full"
              stroke-width="2"
            />
          </div>
        </div>
        <div
          v-if="isLoading"
          class="w-full max-w-[880px] mx-auto flex justify-start"
        >
          <div class="bg-[#ffef08] rounded-full w-8 h-8 min-w-8 min-h-8 flex items-center justify-center p-1.5 me-2">
            <img
              src="@/assets/logo/logo-small.svg"
              class="w-full h-full"
              alt="avatar"
            />
          </div>
          <div class="max-w-3xl p-4 shadow-sm bg-orange-100 text-gray-800 rounded-r-2xl rounded-bl-2xl">
            <div class="typing">
              <div class="dot"></div>
              <div class="dot"></div>
              <div class="dot"></div>
            </div>
          </div>
        </div>
        <div
          v-if="!isLoading && !isEmpty(messages)"
          class="text-center mt-6"
        >
          <TwButton
            variant="secondary"
            class="!py-1.5"
            @click="clearChat"
          >
            Clear Chat
          </TwButton>
        </div>
      </div>

      <!-- Input Section -->
      <div class="px-2 w-full max-w-[992px] mx-auto pb-4 md:!px-6">
        <!-- Suggested actions -->
        <section
          v-if="!isMessagesLoading && isEmpty(messages)"
          class="sm:hidden flex gap-4 w-full py-3 overflow-x-auto no-scrollbar"
        >
          <div
            v-for="suggestedPrompt in suggestedPrompts"
            :key="suggestedPrompt.label"
            class="p-3 bg-orange-100 rounded-2xl min-w-[200px] max-md:px-5 cursor-pointer hover:brightness-[95%] transition-colors"
            @click="setPrompt(suggestedPrompt.prompt)"
          >
            <h2 class="text-sm font-medium text-black">
              {{ suggestedPrompt.label }}
            </h2>
          </div>
        </section>

        <form
          class="flex gap-2 justify-between items-center px-2 py-2 lg:!px-4 lg:!py-4 bg-zinc-900 rounded-[40px] w-full"
          @submit.prevent="handleSubmit"
        >
          <textarea
            ref="promptInput"
            v-model="prompt"
            :placeholder="isListening ? 'Listening...' : 'Ask Dr. Smith'"
            class="self-stretch my-auto text-base font-medium leading-none text-white bg-transparent border-none w-full focus:ring-0 overflow-hidden resize-none"
            rows="1"
            @keyup.enter="() => {
              if (!isLoading) handleSubmit()
            }"
          ></textarea>
          <SpeechToText
            v-model="prompt"
            language="en-US"
          />
          <button
            type="submit"
            aria-label="Send"
            class="flex items-center justify-center rounded-full bg-white p-1 disabled:cursor-not-allowed disabled:bg-gray-400"
            :disabled="isLoading || isEmpty(prompt)"
          >
            <IconArrowRight
              class="my-auto w-8 stroke-black p-0.5"
              stroke-width="2"
            />
          </button>
        </form>
      </div>
    </div>

    <!-- Help modal -->
    <ModalHelp ref="modalHelpRef" />
  </div>
</template>

<style lang="scss">
.typing {
  align-items: center;
  display: flex;
  padding: 4px 8px;
}
.typing .dot {
  animation: mercuryTypingAnimation 1.8s infinite ease-in-out;
  background-color: #0f172a;
  border-radius: 50%;
  height: 7px;
  margin-right: 4px;
  vertical-align: middle;
  width: 7px;
  display: inline-block;
}
.typing .dot:nth-child(1) {
  animation-delay: 200ms;
}
.typing .dot:nth-child(2) {
  animation-delay: 300ms;
}
.typing .dot:nth-child(3) {
  animation-delay: 400ms;
}
.typing .dot:last-child {
  margin-right: 0;
}

@keyframes mercuryTypingAnimation {
  0% {
    transform: translateY(0px);
    background-color: #0f172a;
  }
  28% {
    transform: translateY(-7px);
    background-color: #334155;
  }
  44% {
    transform: translateY(0px);
    background-color: #94a3b8;
  }
}
</style>
