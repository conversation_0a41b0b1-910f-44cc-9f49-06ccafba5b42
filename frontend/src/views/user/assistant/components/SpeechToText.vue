<script setup>
import { IconMicrophone } from '@tabler/icons-vue'
import { ref, onMounted, onUnmounted } from 'vue'
import { toast } from 'vue-sonner'

const props = defineProps({
  modelValue: {
    type: String,
    required: true,
  },
  language: {
    type: String,
    default: 'en-US',
  },
})

const emit = defineEmits(['update:modelValue'])

const isListening = ref(false)
const hasError = ref(false)
const recognition = ref(null)

onMounted(() => {
  // Check browser support
  if ('webkitSpeechRecognition' in window) {
    recognition.value = new webkitSpeechRecognition()
    setupRecognition()
  } else {
    hasError.value = true
    toast.error('Speech recognition is not supported in this browser.')
  }
})

onUnmounted(() => {
  if (recognition.value) {
    recognition.value.stop()
  }
})

const setupRecognition = () => {
  const recognitionInstance = recognition.value

  // Configure for continuous speech recognition
  recognitionInstance.continuous = true
  recognitionInstance.interimResults = true
  recognitionInstance.lang = props.language

  // Set high accuracy
  recognitionInstance.maxAlternatives = 1

  recognitionInstance.onstart = () => {
    isListening.value = true
    hasError.value = false
  }

  recognitionInstance.onend = () => {
    isListening.value = false

    // Restart if was listening (handles auto-stop after ~60s)
    if (isListening.value) {
      recognitionInstance.start()
    }
  }

  recognitionInstance.onresult = event => {
    let finalTranscript = ''
    let interimTranscript = ''

    for (let i = event.resultIndex; i < event.results.length; i++) {
      const transcript = event.results[i][0].transcript
      if (event.results[i].isFinal) {
        finalTranscript += transcript
      } else {
        interimTranscript += transcript
      }
    }

    // Emit final transcript
    if (finalTranscript) {
      emit('update:modelValue', props.modelValue + finalTranscript)
    }
  }

  recognitionInstance.onerror = event => {
    hasError.value = true
    isListening.value = false
    toast.error(`Error: ${event.error}`)
  }
}

const toggleListening = () => {
  if (!recognition.value) return

  if (isListening.value) {
    recognition.value.stop()
    isListening.value = false
  } else {
    recognition.value.start()
  }
}
</script>

<template>
  <div class="speech-to-text-container">
    <button
      type="button"
      aria-label="Voice Input"
      class="flex items-center justify-center rounded-full p-1 disabled:cursor-not-allowed disabled:bg-gray-400"
      :disabled="hasError"
      @click="toggleListening"
    >
      <IconMicrophone
        class="my-auto w-8 p-1"
        :class="[isListening ? 'text-red-500 rounded-full bg-white animate-pulse' : 'text-white']"
        stroke-width="2"
      />
    </button>
  </div>
</template>
