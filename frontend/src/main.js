/* eslint-disable import/order */
import '@/@iconify/icons-bundle'
import App from '@/App.vue'
import ability from '@/plugins/casl/ability'
import layoutsPlugin from '@/plugins/layouts'
import vuetify from '@/plugins/vuetify'
import { loadFonts } from '@/plugins/webfontloader'
import router from '@/router'
import { abilitiesPlugin } from '@casl/vue'
import '@core/scss/template/index.scss'
import '@styles/styles.scss'
import { createPinia } from 'pinia'
import PrimeVue from 'primevue/config'
import 'primevue/resources/themes/lara-light-indigo/theme.css'
import Skeleton from 'primevue/skeleton'
import Tooltip from 'primevue/tooltip'
import 'sweetalert2/dist/sweetalert2.min.css'
import { createApp } from 'vue'
import VueCookies from 'vue-cookies'
import { VueReCaptcha } from 'vue-recaptcha-v3'
import VueSweetalert2 from 'vue-sweetalert2'
import axiosIns from './plugins/axios'
import { formatCurrency } from './utils/helpers'

loadFonts()

// Create vue app
const app = createApp(App)

// Use plugins
app.use(vuetify)
app.use(createPinia())
app.use(router)
app.use(layoutsPlugin)
app.use(abilitiesPlugin, ability, {
  useGlobalProperties: true,
})
app.use(PrimeVue)
app.use(VueSweetalert2)
app.use(VueCookies)
app.component('Skeleton', Skeleton)
app.use(VueReCaptcha, {
  siteKey: import.meta.env.VITE_RECAPTCHA_SITE_KEY,
  loaderOptions: {
    autoHideBadge: true,
  },
})
app.directive('tooltip', Tooltip)

app.config.errorHandler = (error, instance, info) => {
  // log error to slack
  const appEnv = import.meta.env.VITE_APP_ENV
  if (appEnv !== 'local') {
    axiosIns.post('/error-logger', {
      appUrl: window.location.href,
      error: error?.message,
      stackTrace: error?.stack,
      info: info,
    })
  }
}

app.config.globalProperties.formatCurrency = formatCurrency

// Mount vue app
app.mount('#app')
