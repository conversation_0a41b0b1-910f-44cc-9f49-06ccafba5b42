import cookies from 'vue-cookies'

const USER_DATA_KEY = 'userData'
const USER_ABILITY_KEY = 'userAbilities'

export const setUserData = data => {
  cookies.set(USER_DATA_KEY, data)
}

export const getUserData = () => {
  return cookies.get(USER_DATA_KEY)
}

export const destroyUserData = () => {
  cookies.remove(USER_DATA_KEY)
}

export const setUserAbilities = abilities => {
  cookies.set(USER_ABILITY_KEY, abilities)
}

export const getUserAbilities = () => {
  return cookies.get(USER_ABILITY_KEY)
}

export const destroyUserAbilities = () => {
  cookies.remove(USER_ABILITY_KEY)
}
