import { useAuthStore } from '@/store/auth'
import ApiService from '@/services/ApiService'
import { ref } from 'vue'

export default function useUserDetails() {
  const authStore = useAuthStore()
  const user = ref({})
  const errors = ref([])

  async function fetchUserDetails() {
    try {
      errors.value = []

      const { data } = await ApiService.get('/user-details')

      if (data.status === 200) {
        user.value = data.userData
        authStore.updateUserData(data.userData)
      } else {
        errors.value = data
      }
    } catch (error) {
      console.error(error)
      errors.value = error
    }
  }

  return {
    user,
    fetchUserDetails,
    errors,
  }
}
