import { ref } from 'vue'
import { useGlobalData } from '@/store/global'
import ApiService from '@/services/ApiService'
import { processErrors } from '@/utils/errorHandler'

export default function useListCount() {
  const globalData = useGlobalData()
  const { showSnackbar } = globalData

  const loading = ref(false)
  const countData = ref({})

  const getListCount = async routeParam => {
    try {
      loading.value = true

      const { data } = await ApiService.get(`/admin/statistics/${routeParam}`)

      if (data.status === 200) {
        countData.value = data.statistics
      } else {
        showSnackbar(data.message, 'error')
      }
    } catch (error) {
      console.error(error)
      showSnackbar(processErrors(error)[0], 'error')
    } finally {
      loading.value = false
    }
  }

  return { loading, getListCount, countData }
}
