import ApiService from '@/services/ApiService'

export default function useLogOrderSession() {

  /**
   * Log the order session
   * @param {Object} requestBody
   * @returns {Promise<void>}
   */
  async function logSession(requestBody) {
    try {
      const { data } = await ApiService.post('/treatment-checkout-logs', requestBody)

      if (data.status === 200) {
        // console.log('Order session logged')
      } else {
        console.error('Failed to log order session', data.message)
      }
    } catch (error) {
      console.error('Failed to log order session', error)
    }
  }

  return {
    logSession,
  }
}
