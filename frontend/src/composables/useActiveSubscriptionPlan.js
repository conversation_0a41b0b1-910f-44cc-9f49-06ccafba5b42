import ApiService from '@/services/ApiService'
import { ref } from 'vue'

const subscriptionPlanData = ref(null)
const isLoading = ref(false)
const error = ref(null)

export function useActiveSubscriptionPlan() {
  const validateActiveSubscriptionPlan = async () => {
    try {
      isLoading.value = true
      error.value = null

      const { data } = await ApiService.get('/validate-active-subscription-plan')

      if (data.status === 200) {
        subscriptionPlanData.value = data

        return data
      }

      throw new Error(data.message || 'Failed to validate subscription plan')
    } catch (err) {
      error.value = err
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const isSubscriptionActive = category => {
    if (!subscriptionPlanData.value) {
      return false
    }

    return subscriptionPlanData.value[`is_${category}_active`] || false
  }

  const isTreatmentAvailable = category => {
    if (!subscriptionPlanData.value) {
      return false
    }

    return subscriptionPlanData.value[`is_${category}_treatment_available`] || false
  }

  const isPrescriptionActive = category => {
    if (!subscriptionPlanData.value) {
      return false
    }

    return subscriptionPlanData.value[`is_${category}_prescription_active`] || false
  }

  /**
   * @returns {string|null} 'new' | 'followup' | null
   */
  const getTreatmentType = category => {
    if (!subscriptionPlanData.value) {
      return null
    }

    return subscriptionPlanData.value[`${category}_treatment_type`] || null
  }

  const getLastSubscriptionId = category => {
    if (!subscriptionPlanData.value) {
      return null
    }

    return subscriptionPlanData.value[`${category}_followup_visit_id`] || null
  }

  return {
    subscriptionPlanData,
    isLoading,
    error,
    validateActiveSubscriptionPlan,
    isSubscriptionActive,
    isTreatmentAvailable,
    isPrescriptionActive,
    getTreatmentType,
    getLastSubscriptionId,
  }
}
