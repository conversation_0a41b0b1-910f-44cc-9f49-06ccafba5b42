import { ref } from 'vue'
import ApiService from '@/services/ApiService'

export default function useGuestCheckoutSettings() {
  const isGuestCheckoutEnabled = ref(false)
  const isLoading = ref(false)
  const error = ref(null)

  /**
   * Fetch guest checkout settings from the server
   * @returns {Promise<boolean>} Whether guest checkout is enabled
   */
  const fetchGuestCheckoutSettings = async () => {
    try {
      isLoading.value = true
      error.value = null

      const { data } = await ApiService.get('/setting/guest-checkout')

      if (data.status === 200) {
        // Set to true if info is 1, false otherwise
        isGuestCheckoutEnabled.value = data.info === 1

        return isGuestCheckoutEnabled.value
      } else {
        throw new Error('Failed to fetch guest checkout settings')
      }
    } catch (err) {
      console.error('Error fetching guest checkout settings:', err)
      error.value = err

      return false
    } finally {
      isLoading.value = false
    }
  }

  return {
    isGuestCheckoutEnabled,
    isLoading,
    error,
    fetchGuestCheckoutSettings,
  }
}
