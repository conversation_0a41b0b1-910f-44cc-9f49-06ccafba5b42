export function useClarity() {
  function addScript() {
    const projectId = import.meta.env.VITE_MS_CLARITY_PROJECT_ID

    if (!projectId) return

    const script = document.createElement('script')

    script.type = 'text/javascript'
    script.async = true
    script.src = `https://www.clarity.ms/tag/${projectId}`
    script.id = 'clarity-script'

    document.head.appendChild(script)

    window.clarity = window.clarity || function () {
      (window.clarity.q = window.clarity.q || []).push(arguments)
    }
  }

  function removeScript() {
    const script = document.getElementById('clarity-script')
    if (script) {
      document.head.removeChild(script)
    }
  }

  return {
    addScript,
    removeScript,
  }
}
