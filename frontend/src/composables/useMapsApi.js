import axios from 'axios'
import { ref } from 'vue'

export default function useMapsApi() {
  const autoCompleteOptions = ref([])

  const searchPlaces = async query => {
    const requestData = {
      textQuery: query,
    }

    const headers = {
      'Content-Type': 'application/json',
      'X-Goog-Api-Key': import.meta.env.VITE_GOOGLE_MAPS_API_KEY,
      'X-Goog-FieldMask': 'places.displayName,places.formattedAddress,places.addressComponents',
    }

    axios
      .post('https://places.googleapis.com/v1/places:searchText', requestData, { headers })
      .then(response => {
        autoCompleteOptions.value = extractAddressDetails(response?.data?.places)

        // console.log(autoCompleteOptions.value)
      })
      .catch(error => {
        // console.error(error)
        autoCompleteOptions.value = []
      })
  }

  function extractAddressDetails(places) {
    if (!places || places.length === 0) {
      throw new Error('Invalid response or no places found.')
    }

    const addressDetails = []

    places.forEach(place => {
      const addressComponents = place.addressComponents

      // let streetAddress = ""
      let city = ''
      let state = ''
      let postalCode = ''
      let country = ''

      addressComponents.forEach(component => {
        if (component.types.includes('locality') || component.types.includes('sublocality')) {
          city = component.shortText
        } else if (component.types.includes('administrative_area_level_1')) {
          state = component.shortText
        } else if (component.types.includes('postal_code')) {
          postalCode = component.shortText
        } else if (component.types.includes('postal_code_suffix')) {
          postalCode += '-' + component.shortText
        } else if (component.types.includes('country')) {
          country = component.shortText
        }
      })

      addressDetails.push({
        streetAddress: place.displayName.text,
        city: city,
        state: state,
        postalCode: postalCode,
        country: country === 'US' ? 'USA' : country,
        displayName: place.displayName.text,
        formattedAddress: place.formattedAddress,
      })
    })

    return addressDetails
  }

  return {
    searchPlaces,
    autoCompleteOptions,
  }
}
