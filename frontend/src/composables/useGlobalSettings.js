import ApiService from '@/services/ApiService'
import { onMounted } from 'vue'

export default function useGlobalSettings() {
  const documentUploadStage = ref('after')

  async function fetchDocumentSettings() {
    try {
      const { data } = await ApiService.get('/fetch-document-settings')

      if (data.status === 200) {
        documentUploadStage.value = data.info
      } else {
        throw data
      }
    } catch (error) {
      console.error(error)
    }
  }

  onMounted(async () => {
    await fetchDocumentSettings()
  })

  return {
    documentUploadStage,
  }
}
