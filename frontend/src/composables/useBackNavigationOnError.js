import { useAuthStore } from '@/store/auth'
import { useRouter } from 'vue-router'

export default function useBackNavigationOnError() {
  const router = useRouter()
  const authStore = useAuthStore()

  const loading = ref(false)

  const handleClick = () => {
    loading.value = true

    if (authStore.isAuthenticated) {
      if (authStore.userData.role === 'admin' || authStore.userData.role === 'superadmin') {
        router.replace({ name: 'admin-dashboard' })
      } else if (authStore.userData.role === 'affiliate') {
        router.replace({ name: 'affiliate-dashboard' })
      } else {
        router.replace({ name: 'user-subscription' })
      }
    } else {
      router.replace({ name: 'user-login' })
    }
  }

  return { handleClick, loading }
}
