import { isEmpty } from '@/@core/utils'
import ApiService from '@/services/ApiService'
import { onMounted } from 'vue'

export default function useDocuments() {
  const isIdUploaded = ref(false)

  async function fetchUploadedDocuments() {
    try {
      const { data } = await ApiService.get('/fetch-identity-document')

      if (data.status === 200) {
        if (!isEmpty(data.IdentityDocument) && !isEmpty(data.IdentityDocument.government_document)) {
          isIdUploaded.value = true
        }
      } else {
        throw data
      }
    } catch (error) {
      console.error(error)
    }
  }

  onMounted(async () => {
    await fetchUploadedDocuments()
  })

  return { isIdUploaded }
}
