import { isEmpty } from '@/@core/utils'
import ApiService from '@/services/ApiService'
import { processErrors } from '@/utils/errorHandler'
import { onMounted } from 'vue'

export default function usePharmacyList() {
  const pharmacyList = ref([])
  const errors = ref(null)

  async function fetchPharmacyList() {
    try {
      const { data } = await ApiService.get('/admin/pharmacy-lists')

      if (data.status === 200) {
        if (!isEmpty(data.PharmacyList)) {
          pharmacyList.value = data.PharmacyList.map(p => ({ title: p.name, value: p.id }))
        }
      } else {
        throw data
      }
    } catch (error) {
      console.log(error)
      errors.value = processErrors(error)
    }
  }

  onMounted(() => {
    fetchPharmacyList()
  })

  return {
    pharmacyList,
    errors,
    fetchPharmacyList,
  }
}
