export function useFacebookPixel() {
  const pixelId = import.meta.env.VITE_META_PIXEL_ID
  const scriptId = 'facebook-pixel-script'
  const noScriptId = 'facebook-pixel-noscript'

  const loadPixel = () => {
    if (!pixelId) return

    if (document.getElementById(scriptId)) return

    const script = document.createElement('script')

    script.id = scriptId
    script.async = true
    script.innerHTML = `
      !function(f,b,e,v,n,t,s)
      {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
      n.callMethod.apply(n,arguments):n.queue.push(arguments)};
      if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
      n.queue=[];t=b.createElement(e);t.async=!0;
      t.src=v;s=b.getElementsByTagName(e)[0];
      s.parentNode.insertBefore(t,s)}(window, document,'script',
      'https://connect.facebook.net/en_US/fbevents.js');
      fbq('init', '${pixelId}');
      fbq('track', 'PageView');
    `

    document.head.appendChild(script)

    const noscript = document.createElement('noscript')

    noscript.id = noScriptId
    noscript.innerHTML = `
      <img height="1" width="1" style="display:none"
           src="https://www.facebook.com/tr?id=${pixelId}&ev=PageView&noscript=1"/>
    `
    document.body.appendChild(noscript)
  }

  const removePixel = () => {
    const script = document.getElementById(scriptId)
    if (script) script.remove()

    const noscript = document.getElementById(noScriptId)
    if (noscript) noscript.remove()

    if (window.fbq) {
      delete window.fbq
      delete window._fbq
    }
  }

  return { loadPixel, removePixel }
}
