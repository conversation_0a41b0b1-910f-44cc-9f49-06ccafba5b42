import ApiService from '@/services/ApiService'
import { processErrors } from '@/utils/errorHandler'
import { ref } from 'vue'

export default function useProductTypes() {
  const productTypes = ref([])
  const error = ref(null)
  const isLoading = ref(false)

  async function fetchProductTypes(category) {
    try {
      isLoading.value = true
      error.value = null

      const { data } = await ApiService.get(`/admin/get-product-types/${category}`)

      if (data.status === 200) {
        productTypes.value = data.productTypes || []
      } else {
        throw data
      }
    } catch (err) {
      console.error(err)
      error.value = processErrors(err)[0]
    } finally {
      isLoading.value = false
    }
  }

  return {
    productTypes,
    fetchProductTypes,
    error,
    isLoading,
  }
}
