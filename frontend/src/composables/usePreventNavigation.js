import { onMounted, onUnmounted } from 'vue'

export function usePreventNavigation(shouldPrevent) {
  const preventNavigation = event => {
    if (shouldPrevent) {
      event.preventDefault()
      event.returnValue = ''
    }
  }

  const handlePopState = () => {
    if (shouldPrevent) {
      window.history.pushState(null, '', window.location.href)
    }
  }

  onMounted(() => {
    window.history.pushState(null, '', window.location.href)
    window.addEventListener('popstate', handlePopState)
    window.addEventListener('beforeunload', preventNavigation)
  })

  onUnmounted(() => {
    window.removeEventListener('popstate', handlePopState)
    window.removeEventListener('beforeunload', preventNavigation)
  })
}
