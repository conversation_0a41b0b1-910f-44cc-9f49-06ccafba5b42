import { initialAbility } from '@/plugins/casl/ability'
import { useAppAbility } from '@/plugins/casl/useAppAbility'
import ApiService from '@/services/ApiService'
import { destroyUserAbilities } from '@/services/UserService'
import { useAuthStore } from '@/store/auth'
import { useRouter } from 'vue-router'

export default function useLogout() {
  const router = useRouter()
  const authStore = useAuthStore()
  const ability = useAppAbility()

  const handleLogout = async () => {
    try {
      await ApiService.get('/logout')

      const userRole = authStore.userData.role

      authStore.purgeAuth()
      destroyUserAbilities()
      ability.update(initialAbility)
      localStorage.clear()
      sessionStorage.clear()

      if (userRole === 'superadmin' || userRole === 'admin') {
        await router.push({ name: 'admin-login' })
      } else {
        await router.push({ name: 'user-login' })
      }
    } catch (error) {
      console.log(error)
    }
  }

  const handleAffiliateLogout = async () => {
    try {
      await ApiService.get('/affiliate/logout')
      authStore.purgeAuth()
      destroyUserAbilities()
      ability.update(initialAbility)
      localStorage.clear()
      sessionStorage.clear()
      router.replace({ name: 'affiliate-login' })
    } catch (error) {
      console.log(error)
    }
  }

  return {
    handleLogout,
    handleAffiliateLogout,
  }
}
