/* eslint-disable indent */
export function resolveSubscriptionStatusColor(status) {
  switch (status) {
    case 0: return 'warning' // On Hold
    case 1: return 'success' // Active
    case 2: return 'error' // Canceled
    case 3: return 'secondary' // Expired
    default: return 'default'
  }
}

export function resolvePaymentStatusColor(status) {
  if (status === 1) { // Paid
    return 'success'
  } else if (status === 2) { // Failed
    return 'error'
  } else {
    return 'default'
  }
}

export function resolveTransactionTypeColor(status) {
  const statusColors = {
    1: 'success', // Captured
    2: 'error',   // Refund
    3: 'info',    // Authorize
    4: 'error',   // Void
  }

  return statusColors[status] || 'default'
}

export const categoryList = [
  { title: 'ED', value: 'ED' },
  { title: 'HL', value: 'HL' },
  { title: 'WL', value: 'WL' },
  { title: 'OTC', value: 'OTC' },
]

export function getShippingActivityColor(status) {
  const statusColors = {
    pending_shipping_details: 'warning',
    pre_transit: 'default',
    in_transit: 'primary',
    out_for_delivery: 'info',
    delivered: 'success',
  }

  return statusColors[status] || 'default'
}

export function resolvePrescriptionStatus(status) {
  if (status === 1) {
    return { label: 'Valid', color: 'success' }
  } else if (status === 2) {
    return { label: 'Expired', color: 'default' }
  }
}

export function resolveRefundStatusColor(status) {
  switch (status) {
    case 0: return 'warning' // Pending
    case 1: return 'success' // Approved
    case 2: return 'error' // Canceled
    case 3: return 'error' // Refund Failed
    default: return 'default'
  }
}

export const gogomedsShipmentMethodIds = [
  { title: 'Standard with Tracking', value: 1 },
  { title: 'Next Day Air', value: 2 },
  { title: '2nd Day Air', value: 3 },
  { title: 'Priority Mail', value: 8 },
  { title: 'Ground', value: 10 },
  { title: 'Saturday Delivery', value: 14 },
  { title: 'Free USPS Express Priority', value: 15 },
]

export const hallandaleShipmentMethodIds = [
  { title: 'Pharmacy pickup', value: 9 },
  { title: 'Delivery', value: 999 },
  { title: 'Fedex 2 Day', value: 6223 },
  { title: 'Fedex Express Saver', value: 6224 },
  { title: 'Fedex Ground', value: 6225 },
  { title: 'Fedex First Overnight', value: 6226 },
  { title: 'Fedex Ground Home Delivery', value: 6227 },
  { title: 'Fedex Priority Overnight', value: 6228 },
  { title: 'Fedex Standard Overnight', value: 6230 },
  { title: 'Fedex 2 Day AM', value: 6231 },
]

const shipmentMethodName = {
  // gogomeds
  1: 'Standard with Tracking',
  2: 'Next Day Air',
  3: '2nd Day Air',
  8: 'Priority Mail',
  10: 'Ground',
  14: 'Saturday Delivery',
  15: 'Free USPS Express Priority',

  // hallandale
  // 9: 'Pharmacy pickup',
  // 999: 'Delivery',
  // 6223: 'Fedex 2 Day',
  // 6224: 'Fedex Express Saver',
  // 6225: 'Fedex Ground',
  // 6226: 'Fedex First Overnight',
  // 6227: 'Fedex Ground Home Delivery',
  // 6228: 'Fedex Priority Overnight',
  // 6230: 'Fedex Standard Overnight',
  // 6231: 'Fedex 2 Day AM',
}

export function resolveShipmentMethodName(id) {
  return shipmentMethodName[id]
}

export function resolveVisitStatus(status) {
  switch (status) {
    case 0:
      return { label: 'Pending', color: 'warning' }
    case 1:
      return { label: 'Awaiting for Rx', color: 'primary' }
    case 2:
      return { label: 'Canceled', color: 'error' }
    case 3:
      return { label: 'Awaiting for cancelation', color: 'warning' }
    case 4:
      return { label: 'Prescribed', color: 'success' }
    case 5:
      return { label: 'RX not issued', color: 'warning' }
    case 6:
      return { label: 'Canceled', color: 'error' }
    case 7:
      return { label: 'Failed', color: 'error' }
    case 8:
      return { label: 'Canceled', color: 'error' }
    default:
      return { label: 'Unknown', color: 'default' }
  }
}
