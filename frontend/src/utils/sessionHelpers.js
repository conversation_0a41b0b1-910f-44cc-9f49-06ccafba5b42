export function clearOrderSession() {
  // ed
  window.sessionStorage.removeItem('edVisitSession')
  window.sessionStorage.removeItem('edQuestionsSession')
  window.sessionStorage.removeItem('shippingDetailsSession')
  window.sessionStorage.removeItem('checkoutPromoCode')
  window.sessionStorage.removeItem('retryOrderId')

  // hl
  window.sessionStorage.removeItem('hlVisitSession')
  window.sessionStorage.removeItem('hlQuestionsSession')
  window.sessionStorage.removeItem('hlCheckoutPromoCode')
  window.sessionStorage.removeItem('hlRetryOrderId')

  // wl
  window.sessionStorage.removeItem('wlVisitSession')
  window.sessionStorage.removeItem('wlQuestionsSession')
  window.sessionStorage.removeItem('isFullBodyImgLatest')
  window.sessionStorage.removeItem('wlRetryOrderId')

  window.sessionStorage.removeItem('start_new_ed_visit')
  window.sessionStorage.removeItem('start_new_hl_visit')
}

export function clearRetrySession() {
  window.sessionStorage.removeItem('retryOrderId')
  window.sessionStorage.removeItem('hlRetryOrderId')
  window.sessionStorage.removeItem('wlRetryOrderId')
}

export function resolveVisitSessionKey(visitType) {
  visitType = visitType.toLowerCase()
  if (visitType === 'ed') {
    return 'edVisitSession'
  } else if (visitType === 'hl') {
    return 'hlVisitSession'
  } else if (visitType === 'wl') {
    return 'wlVisitSession'
  } else if (visitType === 'wl-followup') {
    return 'wlVisitSession'
  } else {
    return 'edVisitSession'
  }
}

export function clearOtcSession() {
  window.sessionStorage.removeItem('checkout_current_step')
  window.sessionStorage.removeItem('checkout_completed_steps')
  window.sessionStorage.removeItem('otcCheckoutSession')
}
