/**
 * Processes error data and returns an array of error messages.
 *
 * @param {Object} errorData - The error information to process. This can be an object
 *                             containing an 'errors' property, a 'response' property with
 *                             nested 'data' and 'message', or a direct 'message' property.
 * @returns {string[]} An array of error messages extracted from the errorData.
 */
export function processErrors(errorData) {
  let errors = []

  if (errorData.errors && typeof errorData.errors === 'object') {
    for (let key in errorData.errors) {
      errors.push(...errorData.errors[key])
    }
  } else if (errorData.response && errorData.response.data && errorData.response.data.message) {
    errors.push(errorData.response.data.message)
  } else if (errorData.message) {
    errors.push(errorData.message)
  } else {
    errors.push('An unexpected error occurred. Please try again later.')
  }

  return errors
}
