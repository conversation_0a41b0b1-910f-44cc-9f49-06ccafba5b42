import {
  IconCheck, IconClock, IconX, IconPrescription, IconPackage, IconTruckDelivery, IconCircleDot,
  IconCheckupList,
  IconExclamationMark,
} from '@tabler/icons-vue'

export function getOrderStatus(status) {
  let bgColor, textColor, icon

  switch (status) {
    case 0: // Pending Visit
    case 1: // Awaiting for Rx
      bgColor = 'bg-amber-500'
      textColor = 'text-amber-500'
      icon = IconClock
      break

    case 2: // Canceled
      bgColor = 'bg-red-600'
      textColor = 'text-red-600'
      icon = IconX
      break

    case 3: // Prescription Issued
      bgColor = 'bg-lime-700'
      textColor = 'text-lime-700'
      icon = IconPrescription
      break

    case 4: // Payment Failed
    case 16: // Payment Failed
      bgColor = 'bg-red-600'
      textColor = 'text-red-600'
      icon = IconX
      break

    case 5: // Payment Pending
    case 17: // Payment Processing
      bgColor = 'bg-amber-500'
      textColor = 'text-amber-500'
      icon = IconExclamationMark
      break

    case 6: // Consult concluded
      bgColor = 'bg-amber-500'
      textColor = 'text-amber-500'
      icon = IconCheckupList
      break

    case 7: // Preparing for shipment
    case 11: // Preparing for shipment
    case 12: // Preparing for shipment
    case 13: // Preparing for shipment
    case 14: // Preparing for shipment
    case 15: // Preparing for shipment
      bgColor = 'bg-cyan-600'
      textColor = 'text-cyan-600'
      icon = IconPackage
      break

    case 8: // In transit
    case 9: // Out for Delivery
      bgColor = 'bg-blue-600'
      textColor = 'text-blue-600'
      icon = IconTruckDelivery
      break

    case 10: // Delivered
      bgColor = 'bg-green-600'
      textColor = 'text-green-600'
      icon = IconCheck
      break

    default:
      bgColor = 'bg-gray-600'
      textColor = 'text-gray-600'
      icon = IconCircleDot
  }

  return { bgColor, textColor, icon }
}
