export function blockSpaces(event) {
  if (event.key === ' ') {
    event.preventDefault()
  }
}

export function toUpperCaseValue(value) {
  return value.toUpperCase()
}

export function isNumber(event, allowNegative = false) {
  if (event.key === 'Backspace' || event.key === 'Delete' || event.key === 'ArrowLeft' || event.key === 'ArrowRight' || event.key === 'Tab') {
    return true
  }
  const keysAllowed = allowNegative ? ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '.', '-'] : ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '.']
  const keyPressed = event.key
  const currentValue = event.target.value

  if (!keysAllowed.includes(keyPressed)) {
    event.preventDefault()
  } else if (keyPressed === '.' && currentValue.includes('.')) {
    event.preventDefault()
  } else if (keyPressed === '-' && (currentValue.includes('-') || currentValue.length > 0)) {
    event.preventDefault()
  }
}

export function preventSpacesFromStart(event) {
  const keyPressed = event.key
  const currentValue = event.target.value

  if (keyPressed === ' ' && currentValue.trim() === '') {
    event.preventDefault()
  }
}

export function preventAllSpaces(event) {
  if (event.key === ' ') {
    event.preventDefault()
  }
}

export function scrollToTop() {
  window.scrollTo({ top: 0, left: 0, behavior: 'smooth' })
}

export function scrollToElement(elementId, valueToAdd = 0) {
  const element = document.getElementById(elementId)

  window.scrollTo({ top: element.offsetTop + valueToAdd, behavior: 'smooth' })
}

export function scrollIntoView(selector) {
  const element = document.querySelector(selector)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' })
  }
}

export function formattedPhoneNumber(phoneNumberString) {
  let cleaned = ('' + phoneNumberString).replace(/\D/g, '')
  let match = cleaned.match(/^(\d{3})(\d{3})(\d{4})$/)
  if (match) {
    return '(' + match[1] + ') ' + match[2] + '-' + match[3]
  }

  return null
}

export function plainPhoneNumber(phoneNumberString) {
  let cleaned = ('' + phoneNumberString).replace(/\D/g, '')

  return cleaned
}

export function createIdFromString(value) {
  return value.toLowerCase().replace(/ /g, '-')
}

export async function createFileFromUrl(url) {
  const response = await fetch(url)
  const blob = await response.blob()
  const fileName = url.split('/').pop().split('?')[0]

  return new File([blob], fileName, { type: 'image/jpeg' })
}

export function removeKeyFromObject(object, key) {
  const { [key]: _, ...newObject } = object

  return newObject
}

export function calculateStartIndex(pageNo, pageSize) {
  if (pageNo <= 0 || pageSize <= 0) {
    return 1
  }

  return (pageNo - 1) * pageSize + 1
}

export function resolveInitials(name) {
  const nameArray = name.split(' ')

  if (nameArray.length >= 2) {
    return nameArray[0][0] + nameArray[1][0]
  } else if (nameArray.length === 1) {
    return nameArray[0][0]
  } else {
    return '※'
  }
}

export function isCardExpired(year, month) {
  const now = new Date()
  const expirationDate = new Date(year, month - 1)

  return now > expirationDate
}

export function ceilToDecimalPlaces(number, decimalPlaces = 2) {
  const factor = Math.pow(10, decimalPlaces)

  return Math.ceil(number * factor) / factor
}

export function capitalizeFirstChar(word) {
  if (!word) return ''

  return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
}

export function formatCurrency(value) {
  return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(value ?? 0)
}

export async function readAsDataURL(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()

    reader.onload = () => resolve(reader.result)
    reader.onerror = reject
    reader.readAsDataURL(file)
  })
}

export function sleep(ms) {
  return new Promise((resolve => setTimeout(resolve, ms)))
}
