/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
import '@vue/runtime-core'

export {}

declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    AffiliateApproveProfileDrawer: typeof import('./src/components/dialogs/AffiliateApproveProfileDrawer.vue')['default']
    AffiliateEditAdditionalDrawer: typeof import('./src/components/dialogs/AffiliateEditAdditionalDrawer.vue')['default']
    AffiliateEditAddressDrawer: typeof import('./src/components/dialogs/AffiliateEditAddressDrawer.vue')['default']
    AffiliateEditCommissionDrawer: typeof import('./src/components/dialogs/AffiliateEditCommissionDrawer.vue')['default']
    AffiliateEditPayoutMethodDrawer: typeof import('./src/components/dialogs/AffiliateEditPayoutMethodDrawer.vue')['default']
    AffiliateRejectProfileDialog: typeof import('./src/components/dialogs/AffiliateRejectProfileDialog.vue')['default']
    AffiliateUserInfoEditDialog: typeof import('./src/components/dialogs/AffiliateUserInfoEditDialog.vue')['default']
    AppAutocomplete: typeof import('./src/@core/components/app-form-elements/AppAutocomplete.vue')['default']
    AppBarSearch: typeof import('./src/@core/components/AppBarSearch.vue')['default']
    AppCardActions: typeof import('./src/@core/components/cards/AppCardActions.vue')['default']
    AppCardCode: typeof import('./src/@core/components/cards/AppCardCode.vue')['default']
    AppCombobox: typeof import('./src/@core/components/app-form-elements/AppCombobox.vue')['default']
    AppDateTimePicker: typeof import('./src/@core/components/app-form-elements/AppDateTimePicker.vue')['default']
    AppDrawerHeaderSection: typeof import('./src/@core/components/AppDrawerHeaderSection.vue')['default']
    AppLogo: typeof import('./src/components/AppLogo.vue')['default']
    AppOtpInput: typeof import('./src/@core/components/app-form-elements/AppOtpInput.vue')['default']
    AppSelect: typeof import('./src/@core/components/app-form-elements/AppSelect.vue')['default']
    AppStepper: typeof import('./src/@core/components/AppStepper.vue')['default']
    AppTextarea: typeof import('./src/@core/components/app-form-elements/AppTextarea.vue')['default']
    AppTextField: typeof import('./src/@core/components/app-form-elements/AppTextField.vue')['default']
    CardStatisticsHorizontal: typeof import('./src/@core/components/cards/CardStatisticsHorizontal.vue')['default']
    CardStatisticsVertical: typeof import('./src/@core/components/cards/CardStatisticsVertical.vue')['default']
    CardStatisticsVerticalSimple: typeof import('./src/@core/components/CardStatisticsVerticalSimple.vue')['default']
    CircularProgress: typeof import('./src/components/CircularProgress.vue')['default']
    ConfirmDialog: typeof import('./src/components/dialogs/ConfirmDialog.vue')['default']
    CustomCheckboxes: typeof import('./src/@core/components/app-form-elements/CustomCheckboxes.vue')['default']
    CustomCheckboxesWithIcon: typeof import('./src/@core/components/app-form-elements/CustomCheckboxesWithIcon.vue')['default']
    CustomCheckboxesWithImage: typeof import('./src/@core/components/app-form-elements/CustomCheckboxesWithImage.vue')['default']
    CustomizerSection: typeof import('./src/@core/components/CustomizerSection.vue')['default']
    CustomRadios: typeof import('./src/@core/components/app-form-elements/CustomRadios.vue')['default']
    CustomRadiosWithIcon: typeof import('./src/@core/components/app-form-elements/CustomRadiosWithIcon.vue')['default']
    CustomRadiosWithImage: typeof import('./src/@core/components/app-form-elements/CustomRadiosWithImage.vue')['default']
    DialogCloseBtn: typeof import('./src/@core/components/DialogCloseBtn.vue')['default']
    EditAddressDialog: typeof import('./src/components/dialogs/EditAddressDialog.vue')['default']
    EditAffiliateOrderNoDrawer: typeof import('./src/components/dialogs/EditAffiliateOrderNoDrawer.vue')['default']
    EditHallandaleOrderIdDrawer: typeof import('./src/components/dialogs/EditHallandaleOrderIdDrawer.vue')['default']
    EditHallandaleOrderStatusDrawer: typeof import('./src/components/dialogs/EditHallandaleOrderStatusDrawer.vue')['default']
    EditRefundAmountDialog: typeof import('./src/components/dialogs/EditRefundAmountDialog.vue')['default']
    EditShippingStatusDrawer: typeof import('./src/components/dialogs/EditShippingStatusDrawer.vue')['default']
    ErrorHeader: typeof import('./src/components/ErrorHeader.vue')['default']
    HallandaleEventDetailsDialog: typeof import('./src/components/dialogs/HallandaleEventDetailsDialog.vue')['default']
    HLEditShippingStatusDrawer: typeof import('./src/components/dialogs/HLEditShippingStatusDrawer.vue')['default']
    HLOrderActivities: typeof import('./src/components/dialogs/HLOrderActivities.vue')['default']
    HLOrderCancelDialog: typeof import('./src/components/dialogs/HLOrderCancelDialog.vue')['default']
    HLOrderChangePaymentMethod: typeof import('./src/components/dialogs/HLOrderChangePaymentMethod.vue')['default']
    HLSubscriptionCancelDialog: typeof import('./src/components/dialogs/HLSubscriptionCancelDialog.vue')['default']
    HLSubscriptionChangeNextRefillDate: typeof import('./src/components/dialogs/HLSubscriptionChangeNextRefillDate.vue')['default']
    HLSubscriptionChangePaymentMethod: typeof import('./src/components/dialogs/HLSubscriptionChangePaymentMethod.vue')['default']
    HLSubscriptionChangeShipmentMethod: typeof import('./src/components/dialogs/HLSubscriptionChangeShipmentMethod.vue')['default']
    HLSubscriptionEditShippingAddress: typeof import('./src/components/dialogs/HLSubscriptionEditShippingAddress.vue')['default']
    HLSubscriptionRecentEvents: typeof import('./src/components/dialogs/HLSubscriptionRecentEvents.vue')['default']
    I18n: typeof import('./src/@core/components/I18n.vue')['default']
    InputError: typeof import('./src/components/InputError.vue')['default']
    LoadingProgress: typeof import('./src/components/LoadingProgress.vue')['default']
    MoreBtn: typeof import('./src/@core/components/MoreBtn.vue')['default']
    Notifications: typeof import('./src/@core/components/Notifications.vue')['default']
    OrderActivities: typeof import('./src/components/dialogs/OrderActivities.vue')['default']
    OrderCancelDialog: typeof import('./src/components/dialogs/OrderCancelDialog.vue')['default']
    OrderChangePaymentMethod: typeof import('./src/components/dialogs/OrderChangePaymentMethod.vue')['default']
    PaymentHistoryDialog: typeof import('./src/components/dialogs/PaymentHistoryDialog.vue')['default']
    PayoutRequestDeclineDialog: typeof import('./src/components/dialogs/PayoutRequestDeclineDialog.vue')['default']
    PayoutRequestDetailsDialog: typeof import('./src/components/dialogs/PayoutRequestDetailsDialog.vue')['default']
    PharmacyEditOrderStatusDrawer: typeof import('./src/components/dialogs/PharmacyEditOrderStatusDrawer.vue')['default']
    PharmacyEventDetailsDialog: typeof import('./src/components/dialogs/PharmacyEventDetailsDialog.vue')['default']
    PharmacyUpdateOrderNoteDialog: typeof import('./src/components/dialogs/PharmacyUpdateOrderNoteDialog.vue')['default']
    PharmacyUpdateShippingAddressDialog: typeof import('./src/components/dialogs/PharmacyUpdateShippingAddressDialog.vue')['default']
    RefundDeclineDialog: typeof import('./src/components/dialogs/RefundDeclineDialog.vue')['default']
    RefundDetailsDialog: typeof import('./src/components/dialogs/RefundDetailsDialog.vue')['default']
    RefundRequestDetailsDialog: typeof import('./src/components/dialogs/RefundRequestDetailsDialog.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    ScrollToTop: typeof import('./src/@core/components/ScrollToTop.vue')['default']
    ShippingActivityDialog: typeof import('./src/components/dialogs/ShippingActivityDialog.vue')['default']
    Shortcuts: typeof import('./src/@core/components/Shortcuts.vue')['default']
    SubscriptionCancelDialog: typeof import('./src/components/dialogs/SubscriptionCancelDialog.vue')['default']
    SubscriptionChangeNextFollowupDate: typeof import('./src/components/dialogs/SubscriptionChangeNextFollowupDate.vue')['default']
    SubscriptionChangeNextRefillDate: typeof import('./src/components/dialogs/SubscriptionChangeNextRefillDate.vue')['default']
    SubscriptionChangePaymentMethod: typeof import('./src/components/dialogs/SubscriptionChangePaymentMethod.vue')['default']
    SubscriptionChangeShipmentMethod: typeof import('./src/components/dialogs/SubscriptionChangeShipmentMethod.vue')['default']
    SubscriptionEditShippingAddress: typeof import('./src/components/dialogs/SubscriptionEditShippingAddress.vue')['default']
    SubscriptionRecentEvents: typeof import('./src/components/dialogs/SubscriptionRecentEvents.vue')['default']
    TheCustomizer: typeof import('./src/@core/components/TheCustomizer.vue')['default']
    ThemeSwitcher: typeof import('./src/@core/components/ThemeSwitcher.vue')['default']
    TiptapEditor: typeof import('./src/components/TiptapEditor.vue')['default']
    TwButton: typeof import('./src/components/ui/TwButton.vue')['default']
    UserInfoEditDrawer: typeof import('./src/components/dialogs/UserInfoEditDrawer.vue')['default']
    VisitEventDetailsDialog: typeof import('./src/components/dialogs/VisitEventDetailsDialog.vue')['default']
    WLEditShippingStatusDrawer: typeof import('./src/components/dialogs/WLEditShippingStatusDrawer.vue')['default']
    WLOrderActivities: typeof import('./src/components/dialogs/WLOrderActivities.vue')['default']
    WLOrderCancelDialog: typeof import('./src/components/dialogs/WLOrderCancelDialog.vue')['default']
    WLSubscriptionCancelDialog: typeof import('./src/components/dialogs/WLSubscriptionCancelDialog.vue')['default']
    WLSubscriptionChangePaymentMethod: typeof import('./src/components/dialogs/WLSubscriptionChangePaymentMethod.vue')['default']
    WLSubscriptionEditShippingAddress: typeof import('./src/components/dialogs/WLSubscriptionEditShippingAddress.vue')['default']
    WLSubscriptionRecentEvents: typeof import('./src/components/dialogs/WLSubscriptionRecentEvents.vue')['default']
  }
}
